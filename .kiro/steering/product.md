# Product Overview

## Bookify - Beauty Salon Booking System

A comprehensive beauty salon booking platform built on top of an existing e-commerce system. The system provides online appointment booking, payment processing, and management features for beauty services.

### Core Features
- **Service Management**: Browse beauty services with detailed descriptions, pricing, and categories
- **Technician Profiles**: View technician information, specialties, ratings, and availability
- **Booking Flow**: Complete appointment booking process (service → technician → time → confirmation)
- **User Management**: Customer profiles, booking history, reviews, and favorites
- **Payment Integration**: Support for multiple payment methods including WeChat Pay and Alipay
- **Admin Dashboard**: Booking management, technician management, and analytics

### Target Users
- **Customers**: Book beauty services, manage appointments, leave reviews
- **Technicians**: Manage schedules, view customer information, track earnings
- **Administrators**: Oversee operations, manage services and staff

### Business Model
- Service-based booking platform
- Commission-based technician payments
- Membership and loyalty programs
- Marketing and promotional campaigns

The system extends an existing e-commerce platform (wn商城) with specialized beauty salon functionality while reusing core infrastructure like user management, payments, and distribution systems.