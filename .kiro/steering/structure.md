# Project Structure & Organization

## Root Directory Layout
```
├── bookify/           # Frontend uniapp application
├── server/            # Backend Go application  
├── database/          # Database schema and test data
├── docs/              # Project documentation
└── .kiro/             # Kiro AI assistant configuration
```

## Frontend Structure (bookify/)

### Core Directories
- **`pages/`** - All application pages following uniapp conventions
  - `beauty/` - Beauty salon specific pages (main feature)
  - `user/` - User management pages
  - `order/` - Order and payment pages
  - `goods/` - Product/service listing pages

- **`components/`** - Reusable Vue components
  - `beauty/` - Beauty-specific components (service-card, technician-card, booking-card)
  - `popup/` - Modal and popup components
  - `layout/` - Layout components

- **`api/`** - API integration layer
  - `beauty/` - Beauty service APIs with mock data
  - Individual service files (user.js, order.js, etc.)
  - `request.js` - HTTP client configuration

- **`utils/`** - Utility functions
  - `auth.js` - Authentication helpers
  - `errorHandler.js` - Error handling
  - `routerGuard.js` - Navigation guards

### Configuration Files
- `pages.json` - Page routing and navigation configuration
- `manifest.json` - App metadata and platform settings
- `uni.scss` - Global styles and variables

## Backend Structure (server/)

### Core Directories
- **`app/`** - Main application code
  - `controller/` - HTTP request handlers
  - `service/` - Business logic layer
  - `model/` - Data models and database entities
  - `route/` - API route definitions
  - `middleware/` - HTTP middleware (CORS, auth, logging)
  - `config/` - Configuration management

- **`etc/`** - Configuration files
  - `app.yaml` - Application configuration
  - `db.yaml` - Database configuration

- **`migrations/`** - Database migration scripts
- **`uploads/`** - Static file storage
- **`logs/`** - Application logs

## Documentation Structure (docs/)
- **`beauty/`** - Beauty system documentation
  - Architecture, API design, database design
- **`api-doc/`** - API documentation
- **`sql/`** - Database scripts and migrations
- **`specification/`** - Feature specifications

## Naming Conventions

### Files & Directories
- **Frontend**: kebab-case for files (`service-card.vue`, `booking-form.vue`)
- **Backend**: camelCase for Go files, snake_case for database
- **Pages**: Follow uniapp conventions (`pages/beauty/service/list.vue`)

### Components
- **Vue Components**: PascalCase for component names
- **Props**: camelCase
- **Events**: kebab-case

### API Endpoints
- RESTful conventions: `/api/beauty/services`, `/api/beauty/bookings`
- Use plural nouns for collections
- Use HTTP methods appropriately (GET, POST, PUT, DELETE)

### Database
- **Tables**: snake_case (`beauty_services`, `booking_records`)
- **Columns**: snake_case (`created_at`, `user_id`)
- **Foreign Keys**: `{table}_id` format

## Module Organization

### Beauty System (Primary Feature)
- **Frontend**: `bookify/pages/beauty/` + `bookify/components/beauty/`
- **Backend**: `server/app/controller/beauty/` + `server/app/service/beauty/`
- **Database**: Tables prefixed with `beauty_`

### Shared Systems
- **User Management**: Reused from existing e-commerce system
- **Payment**: Integrated WeChat Pay and Alipay
- **File Upload**: Centralized in `server/uploads/`

## Development Workflow
1. **Feature Development**: Create in respective `beauty/` subdirectories
2. **API First**: Define API contracts before implementation
3. **Component Reuse**: Leverage existing components where possible
4. **Documentation**: Update relevant docs/ files for new features