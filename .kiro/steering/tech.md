# Technology Stack & Build System

## Frontend (bookify/)
- **Framework**: uniapp + Vue.js 3
- **UI Library**: uView 2.0 + uni-ui components
- **Styling**: SCSS with CSS variables
- **State Management**: Built-in state management (stores/)
- **HTTP Client**: Axios
- **Target Platforms**: WeChat Mini Program, H5, App

### Frontend Structure
- Pages follow uniapp conventions with `pages.json` configuration
- Custom navigation style used throughout (`navigationStyle: "custom"`)
- Component library in `components/` with beauty-specific components
- API layer in `api/` with mock data support
- Utilities in `utils/` for auth, routing, error handling

## Backend (server/)
- **Language**: Go 1.20+
- **Framework**: Gin (HTTP router)
- **ORM**: GORM v2
- **Database**: MySQL 8.0
- **Cache**: Redis
- **Config**: Viper (YAML configuration)
- **Logging**: Logrus
- **Authentication**: JWT tokens

### Key Dependencies
- **Payment**: WeChat Pay, Alipay integration
- **File Storage**: Aliyun OSS
- **Database Drivers**: MySQL, SQL Server support
- **Middleware**: CORS, logging, authentication

## Database
- **Primary**: MySQL 8.0
- **Schema**: Located in `database/` and `docs/sql/`
- **Migrations**: SQL files in `server/migrations/`

## Development Commands

### Frontend (bookify/)
```bash
# Install dependencies
npm install

# Development (specific platform)
npm run dev:mp-weixin    # WeChat Mini Program
npm run dev:h5           # H5 web version
npm run dev:app          # App development

# Build for production
npm run build:mp-weixin
npm run build:h5
npm run build:app
```

### Backend (server/)
```bash
# Run development server
go run main.go

# Build for production
go build -o shop main.go

# Run with specific config
./shop -c etc/app.yaml

# Database migrations
# Execute SQL files in migrations/ directory manually
```

## Configuration Files
- **Frontend**: `bookify/manifest.json`, `bookify/pages.json`
- **Backend**: `server/etc/app.yaml`, `server/etc/db.yaml`
- **Environment**: `.env` files in bookify/ for different environments

## API Integration
- Mock data available in `bookify/api/beauty/mock.js`
- Real API endpoints defined in `server/app/route/`
- Request handling in `bookify/api/request.js`

## Deployment
- **Frontend**: Build and deploy to respective platforms (WeChat, web hosting)
- **Backend**: Docker support available (`server/Dockerfile`)
- **Static Files**: Served from `server/uploads/` directory