import request from '../request.js'

// 获取可购买套餐卡列表
export function listCard(params) {
  return request.get('/beauty/card/list', { params })
}

// 获取套餐卡详情
export function getCardDetail(params) {
  return request.get('/beauty/card/detail', { params })
}

// 获取我的套餐卡
export function getMyCards(params) {
  return request.get('/beauty/card/my', { params })
}

// 获取套餐卡使用记录
export function getCardRecord(params) {
  return request.get('/beauty/card/record', { params })
}

// 购买套餐卡
export function purchaseCard(data) {
  return request.post('/beauty/card/purchase', data)
}

// 套餐卡支付
export function payCard(data) {
  return request.post('/beauty/card/pay', data)
} 