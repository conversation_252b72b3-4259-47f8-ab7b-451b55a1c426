import request from '../request'

// ==================== 首页相关 ====================

// 获取首页数据
export function getHomeData() {
  return request.get('/beauty/home')
}

// ==================== 服务相关 ====================

// 获取服务分类列表
export function getServiceCategories() {
  return request.get('/beauty/service/categories')
}

// 获取服务列表
export function getServiceList(params) {
  return request.get('/beauty/service/list', { params })
}

// 获取服务详情
export function getServiceDetail(id) {
  return request.get(`/beauty/service/${id}`)
}

// 获取服务的可用技师
export function getServiceTechnicians(params) {
  return request.get('/beauty/service/technicians', { params })
}

// ==================== 技师相关 ====================

// 获取技师列表
export function getTechnicianList(params) {
  return request.get('/beauty/technician/list', { params })
}

// 获取技师详情
export function getTechnicianDetail(id) {
  return request.get(`/beauty/technician/${id}`)
}

// 获取技师可用时间
export function getTechnicianAvailableTime(params) {
  const { technician_id, ...otherParams } = params
  return request.get(`/beauty/technician/${technician_id}/available-time`, { params: otherParams })
}

// ==================== 技师设置相关 ====================

// 获取技师个人信息
export function getTechnicianProfile() {
  return request.get('/beauty/technician-settings/profile')
}

// 更新技师个人信息
export function updateTechnicianProfile(data) {
  return request.put('/beauty/technician-settings/profile', data)
}

// 获取技师工作设置
export function getTechnicianWorkSettings() {
  return request.get('/beauty/technician-settings/work-settings')
}

// 更新技师工作设置
export function updateTechnicianWorkSettings(data) {
  return request.put('/beauty/technician-settings/work-settings', data)
}





// ==================== 预约相关 ====================

// 获取可用预约时间
export function getAvailableTime(params) {
  return request.get('/beauty/booking/available-time', { params })
}

// 创建预约
export function createBooking(data) {
  return request.post('/beauty/booking', data)
}

// 获取我的预约列表
export function getMyBookingList(params) {
  return request.get('/beauty/booking/list', { params })
}

// 获取预约详情
export function getBookingDetail(id) {
  return request.get(`/beauty/booking/${id}`)
}

// 取消预约
export function cancelBooking(data) {
  return request.post('/beauty/booking/cancel', data)
}

// 更新预约状态
export function updateBookingStatus(data) {
  return request.post('/beauty/booking/update-status', data)
}

// 修改预约时间
export function rescheduleBooking(data) {
  return request.post('/beauty/booking/reschedule', data)
}

// 将预约转换为订单
export function createOrderFromBooking(bookingId) {
  return request.post('/beauty/booking/create-order', { booking_id: bookingId })
}

// 美容预约支付
export function payBeautyBooking(data) {
  return request.post('/beauty/booking/pay', data)
}

// ==================== 评价相关 ====================

// 获取服务评价列表
export function getServiceReviews(params) {
  return request.get('/beauty/review/service', { params })
}

// 获取技师评价列表
export function getTechnicianReviews(params) {
  return request.get('/beauty/review/technician', { params })
}

// 获取技师评价统计
export function getTechnicianReviewStats(TechnicianUserID) {
  return request.get(`/beauty/review/technician/${TechnicianUserID}/stats`)
}

// 获取服务评价统计
export function getServiceReviewStats(serviceId) {
  return request.get(`/beauty/review/service/${serviceId}/stats`)
}

// 提交评价
export function submitReview(data) {
  return request.post('/beauty/review/submit', data)
}

// ==================== 优惠券相关 ====================

// 获取可用优惠券
export function getAvailableCoupons(params) {
  return request.get('/beauty/coupon/available', { params })
}

// 获取我的优惠券
export function getMyCoupons(params) {
  return request.get('/beauty/coupon/my', { params })
}

// 领取优惠券
export function receiveCoupon(data) {
  return request.post('/beauty/coupon/receive', data)
}

// ==================== 收藏相关 ====================

// 获取收藏的服务列表
export function getCollectedServices(params) {
  return request.get('/beauty/collect/service', { params })
}

// 获取收藏的技师列表
export function getCollectedTechnicians(params) {
  return request.get('/beauty/collect/technician', { params })
}

// 收藏服务
export function collectService(data) {
  return request.post('/collect/action', data)
}

// 收藏/取消收藏技师
export function collectTechnician(data) {
  return request.post('/beauty/collect/technician', data)
}

// ==================== 统计相关 ====================

// 获取用户消费统计
export function getUserStats() {
  return request.get('/beauty/stats/user')
}

// 获取服务统计
export function getServiceStats(params) {
  return request.get('/beauty/stats/service', { params })
}

// 获取预约统计
export function getBookingStats() {
  return request.get('/beauty/booking/stats')
}

// ==================== 通知相关 ====================

// 获取通知列表
export function getNotifications(params) {
  return request.get('/beauty/notification/list', { params })
}

// 获取未读通知数量
export function getUnreadNotificationCount() {
  return request.get('/beauty/notification/unread-count')
}

// 标记通知为已读
export function markNotificationRead(data) {
  return request.post('/beauty/notification/read', data)
}

// ==================== 搜索相关 ====================

// 搜索服务
export function searchServices(params) {
  return request.get('/beauty/search/service', { params })
}

// 搜索技师
export function searchTechnicians(params) {
  return request.get('/beauty/search/technician', { params })
}

// 获取搜索历史
export function getSearchHistory() {
  return request.get('/beauty/search/history')
}

// 清除搜索历史
export function clearSearchHistory() {
  return request.post('/beauty/search/history/clear')
}

// ==================== 地址相关 ====================

// 获取服务地址列表
export function getServiceAddresses() {
  return request.get('/beauty/address/list')
}

// 获取附近的服务点
export function getNearbyServices(params) {
  return request.get('/beauty/address/nearby', { params })
}

// 获取技师今日预约列表
export function getTechnicianTodayBookings() {
  return request.get('/beauty/booking/technician-today')
}

// 获取技师今日统计
export function getTechnicianTodayStats() {
  return request.get('/beauty/booking/technician-today-stats')
}

// 获取技师指定日期预约列表
export function getTechnicianDateBookings(date) {
  return request.get('/beauty/booking/technician-date', { params: { date } })
}

// 获取技师指定日期统计
export function getTechnicianDateStats(date) {
  return request.get('/beauty/booking/technician-date-stats', { params: { date } })
}

// 获取技师全部预约列表
export function getTechnicianBookingList(params) {
  return request.get('/beauty/booking/technician-list', { params })
} 