# 美容预约系统 API 接口

## 概述

本目录包含美容预约系统的所有API接口定义。

## 文件说明

- `index.js` - 标准API接口文件，用于实际的后端API调用
- `mock.js` - Mock数据文件，用于开发和测试，保留作为接口示例标准

## 使用方法

### 1. 导入API接口

```javascript
// 导入单个接口
import { getHomeData, getServiceList } from '@/api/beauty/index.js'

// 或者导入所有接口
import * as beautyApi from '@/api/beauty/index.js'
```

### 2. 调用API接口

```javascript
// 获取首页数据
const response = await getHomeData()

// 获取服务列表（带参数）
const response = await getServiceList({
  categoryId: 1,
  page: 1,
  pageSize: 10
})

// 创建预约
const response = await createBooking({
  serviceId: 1,
  TechnicianUserID: 1,
  bookingDate: '2024-01-15',
  bookingTime: '14:00'
})
```

### 3. 错误处理

```javascript
try {
  const response = await getServiceList()
  if (response.code === 200) {
    // 处理成功响应
    const data = response.data
  } else {
    // 处理业务错误
    console.error('API错误:', response.message)
  }
} catch (error) {
  // 处理网络错误
  console.error('网络错误:', error)
}
```

## API接口列表

### 首页相关
- `getHomeData()` - 获取首页数据

### 服务相关
- `getServiceCategories()` - 获取服务分类列表
- `getServiceList(params)` - 获取服务列表
- `getServiceDetail(id)` - 获取服务详情
- `getServiceTechnicians(params)` - 获取服务的可用技师

### 技师相关
- `getTechnicianList(params)` - 获取技师列表
- `getTechnicianDetail(id)` - 获取技师详情
- `getTechnicianAvailableTime(params)` - 获取技师可用时间

### 预约相关
- `getAvailableTime(params)` - 获取可用预约时间
- `createBooking(data)` - 创建预约
- `getMyBookingList(params)` - 获取我的预约列表
- `getBookingDetail(id)` - 获取预约详情
- `cancelBooking(data)` - 取消预约
- `rescheduleBooking(data)` - 修改预约时间

### 评价相关
- `getServiceReviews(params)` - 获取服务评价列表
- `getTechnicianReviews(params)` - 获取技师评价列表
- `submitReview(data)` - 提交评价

### 优惠券相关
- `getAvailableCoupons(params)` - 获取可用优惠券
- `getMyCoupons(params)` - 获取我的优惠券
- `receiveCoupon(data)` - 领取优惠券

### 收藏相关
- `getCollectedServices(params)` - 获取收藏的服务列表
- `getCollectedTechnicians(params)` - 获取收藏的技师列表
- `collectService(data)` - 收藏/取消收藏服务
- `collectTechnician(data)` - 收藏/取消收藏技师

### 其他功能
- `getUserStats()` - 获取用户消费统计
- `getNotifications(params)` - 获取通知列表
- `searchServices(params)` - 搜索服务
- `searchTechnicians(params)` - 搜索技师

## 后端API地址

- 开发环境：`http://127.0.0.1:8081/api`
- 生产环境：`http://demo.wcore.top/api`

## 注意事项

1. 所有API接口都遵循统一的响应格式：
   ```json
   {
     "code": 200,
     "message": "success",
     "data": {...}
   }
   ```

2. 参数传递：
   - GET请求：使用`params`对象传递查询参数
   - POST请求：使用`data`对象传递请求体数据

3. 错误处理：
   - 网络错误：使用try-catch捕获
   - 业务错误：检查response.code是否为200

4. 分页参数：
   - `page`: 页码（从1开始）
   - `pageSize`: 每页数量（默认10）

5. 时间格式：
   - 日期：`YYYY-MM-DD`
   - 时间：`HH:mm`
   - 日期时间：`YYYY-MM-DD HH:mm:ss` 