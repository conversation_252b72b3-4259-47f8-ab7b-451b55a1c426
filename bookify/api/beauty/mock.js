// 美容预约系统 Mock API
import { generateOrderNo } from '@/utils/mock.js'

// 服务分类数据
const serviceCategories = [
  { id: 1, name: '面部护理', icon: '🧴', code: 'facial' },
  { id: 2, name: '美甲服务', icon: '💅', code: 'nail' },
  { id: 3, name: '美发造型', icon: '💇', code: 'hair' },
  { id: 4, name: '美睫服务', icon: '👁️', code: 'eyelash' },
  { id: 5, name: '身体护理', icon: '💆', code: 'body' },
  { id: 6, name: '美容仪器', icon: '🔬', code: 'device' },
  { id: 7, name: '纹绣服务', icon: '🎨', code: 'tattoo' }
]

// 服务数据
const services = [
  {
    id: 1,
    name: '深层清洁面部护理',
    categoryId: 1,
    price: 198,
    originalPrice: 298,
    duration: 90,
    rating: 4.8,
    reviewCount: 256,
    sold: 1289,
    description: '专业深层清洁，去除黑头粉刺，收缩毛孔，改善肤质',
    effects: ['深层清洁', '收缩毛孔', '改善肤质', '控油平衡'],
    suitable: ['油性肌肤', '混合性肌肤', '毛孔粗大者'],
    notice: ['术前避免剧烈运动', '术后24小时内避免化妆', '术后一周内加强防晒'],
    process: ['面部清洁', '蒸汽软化', '专业吸黑头', '收缩毛孔', '补水修复'],
    // 新增详细数据结构
    detailedProcess: [
      {
        step: 1,
        title: '面部深层清洁',
        description: '使用专业洁面产品，彻底清除面部污垢和化妆品残留',
        duration: 10,
        tools: ['专业洁面乳', '洁面刷']
      },
      {
        step: 2,
        title: '蒸汽软化角质',
        description: '利用温和蒸汽打开毛孔，软化角质层，为后续清洁做准备',
        duration: 15,
        tools: ['蒸汽仪', '热毛巾']
      },
      {
        step: 3,
        title: '专业吸黑头',
        description: '使用专业工具安全去除黑头粉刺，避免毛孔损伤',
        duration: 25,
        tools: ['黑头导出液', '专业吸头工具']
      },
      {
        step: 4,
        title: '收缩毛孔护理',
        description: '使用收敛水和专业手法，有效收缩毛孔，防止再次堵塞',
        duration: 20,
        tools: ['收敛水', '冷敷面膜']
      },
      {
        step: 5,
        title: '补水修复',
        description: '深层补水，修复受损肌肤，恢复肌肤健康状态',
        duration: 20,
        tools: ['补水精华', '修复面膜']
      }
    ],
    // 分类注意事项
    precautions: {
      before: [
        '术前24小时内避免使用去角质产品',
        '术前避免剧烈运动和大量出汗',
        '如有皮肤过敏史请提前告知技师',
        '术前清洁面部，卸除所有化妆品'
      ],
      during: [
        '如感到不适请及时告知技师',
        '配合技师的专业操作',
        '保持面部放松，避免过度紧张'
      ],
      after: [
        '术后24小时内避免化妆',
        '术后一周内加强防晒保护',
        '避免使用刺激性护肤品',
        '多喝水促进新陈代谢',
        '术后3天内避免桑拿和高温环境'
      ]
    },
    // 服务亮点
    highlights: [
      '专业级深层清洁技术',
      '无创伤黑头去除',
      '个性化肌肤分析',
      '天然植物精华护理',
      '专业技师一对一服务'
    ],
    // 服务保障
    guarantee: {
      quality: '100%正品保障，专业技师操作',
      safety: '严格消毒，一人一套专用工具',
      effect: '7天内无效果可免费重做',
      service: '24小时客服支持，随时解答疑问'
    },
    // 适用人群详细说明
    suitableDetails: {
      primary: ['油性肌肤', '混合性肌肤', '毛孔粗大者'],
      secondary: ['黑头粉刺严重', '肌肤暗沉无光泽', '经常熬夜的人群'],
      notSuitable: ['敏感肌肤', '正在使用刺激性药物', '面部有炎症或伤口']
    },
    // 护理建议
    careAdvice: [
      '建议每月进行1-2次深层清洁',
      '日常使用温和洁面产品',
      '定期使用收敛水收缩毛孔',
      '避免用手挤压黑头',
      '保持规律作息，减少熬夜'
    ],
    images: [
      'https://picsum.photos/300/200?random=11',
      '/static/beauty/service-detail1.jpg',
      '/static/beauty/service-detail2.jpg'
    ],
    isHot: true,
    bookingCount: 1289,
    tags: ['深层清洁', '去黑头', '收缩毛孔', '补水保湿']
  },
  {
    id: 2,
    name: '日式美甲套餐',
    categoryId: 2,
    price: 88,
    originalPrice: 128,
    duration: 60,
    rating: 4.9,
    reviewCount: 189,
    sold: 876,
    description: '精致日式美甲，持久不脱落，多种款式可选',
    effects: ['美化指甲', '持久不易脱落', '多款式选择'],
    suitable: ['想要美化指甲的人群', '参加重要场合的人士'],
    notice: ['术后1小时内避免碰水', '避免使用含有丙酮的卸甲水'],
    process: ['指甲修整', '甲面处理', '涂抹底胶', '涂抹色胶', '涂抹封层胶', '卸除黏胶'],
    // 新增详细数据结构
    detailedProcess: [
      {
        step: 1,
        title: '指甲修整造型',
        description: '专业修剪指甲长度，打磨成理想形状',
        duration: 10,
        tools: ['指甲剪', '指甲锉', '抛光条']
      },
      {
        step: 2,
        title: '甲面清洁处理',
        description: '清洁甲面油脂，推拭死皮，为美甲做准备',
        duration: 8,
        tools: ['死皮推', '清洁液', '酒精棉']
      },
      {
        step: 3,
        title: '涂抹底胶',
        description: '涂抹专业底胶，增强附着力，保护指甲',
        duration: 5,
        tools: ['底胶', 'LED灯']
      },
      {
        step: 4,
        title: '涂抹色胶',
        description: '均匀涂抹选定颜色，确保色彩饱满持久',
        duration: 15,
        tools: ['色胶', 'LED灯', '专业刷具']
      },
      {
        step: 5,
        title: '涂抹封层胶',
        description: '涂抹封层胶，增加光泽度和持久性',
        duration: 8,
        tools: ['封层胶', 'LED灯']
      },
      {
        step: 6,
        title: '清洁完成',
        description: '清除残留黏胶，涂抹营养油，完成美甲',
        duration: 14,
        tools: ['清洁液', '营养油', '护手霜']
      }
    ],
    // 分类注意事项
    precautions: {
      before: [
        '术前修剪指甲至适当长度',
        '清洁双手，卸除旧甲油',
        '如有甲沟炎等问题请提前告知',
        '避免使用含油分的护手霜'
      ],
      during: [
        '保持手部稳定，配合技师操作',
        '如感到不适请及时告知',
        '避免触碰未干的甲油'
      ],
      after: [
        '术后1小时内避免碰水',
        '避免使用含丙酮的卸甲水',
        '做家务时建议戴手套',
        '定期涂抹营养油保养',
        '避免用指甲作为工具使用'
      ]
    },
    // 服务亮点
    highlights: [
      '日式精致工艺',
      '持久不脱落配方',
      '50+款式任选',
      '专业级LED灯固化',
      '天然植物营养油护理'
    ],
    // 服务保障
    guarantee: {
      quality: '进口优质甲油，安全无害',
      safety: '一人一套工具，严格消毒',
      effect: '正常使用可保持2-3周',
      service: '7天内免费修补，满意保证'
    },
    // 适用人群详细说明
    suitableDetails: {
      primary: ['想要美化指甲的人群', '参加重要场合的人士', '职场女性'],
      secondary: ['指甲容易断裂', '经常做家务', '追求时尚的人群'],
      notSuitable: ['孕妇', '指甲严重受损', '对化学品过敏者']
    },
    // 护理建议
    careAdvice: [
      '建议每2-3周重新美甲',
      '日常使用营养油保养',
      '做家务时戴手套保护',
      '避免用指甲开启物品',
      '定期修剪保持形状'
    ],
    images: [
      'https://picsum.photos/300/200?random=12',
      '/static/beauty/service-detail3.jpg',
      '/static/beauty/service-detail4.jpg'
    ],
    isHot: false,
    bookingCount: 876,
    tags: ['美甲', '日式', '持久', '多款式']
  },
  {
    id: 3,
    name: '韩式半永久眉毛',
    categoryId: 7,
    price: 688,
    originalPrice: 888,
    duration: 120,
    rating: 4.7,
    reviewCount: 98,
    sold: 432,
    description: '自然韩式眉型，专业纹绣师操作，持久自然',
    effects: ['塑造自然眉形', '节省化妆时间', '持久不易褪色'],
    suitable: ['眉毛稀疏者', '想要定型眉形的人士'],
    notice: ['术前避免饮酒', '术后一周内避免碰水', '术后避免强光照射'],
    process: ['面部清洁', '设计眉形', '麻醉', '纹绣操作', '修整', '术后护理'],
    // 新增详细数据结构
    detailedProcess: [
      {
        step: 1,
        title: '面部清洁消毒',
        description: '彻底清洁眉部区域，进行专业消毒处理',
        duration: 10,
        tools: ['清洁液', '消毒液', '无菌棉片']
      },
      {
        step: 2,
        title: '眉形设计',
        description: '根据面部轮廓和个人喜好设计最适合的眉形',
        duration: 20,
        tools: ['眉笔', '测量尺', '设计模板']
      },
      {
        step: 3,
        title: '局部麻醉',
        description: '涂抹专业麻醉膏，确保操作过程舒适无痛',
        duration: 15,
        tools: ['麻醉膏', '保鲜膜']
      },
      {
        step: 4,
        title: '纹绣操作',
        description: '使用专业纹绣针具，按设计眉形进行精细操作',
        duration: 50,
        tools: ['纹绣机', '专业色料', '一次性针头']
      },
      {
        step: 5,
        title: '细节修整',
        description: '检查眉形对称性，进行细节调整和完善',
        duration: 15,
        tools: ['修整工具', '镜子']
      },
      {
        step: 6,
        title: '术后护理',
        description: '涂抹修复膏，讲解术后护理注意事项',
        duration: 10,
        tools: ['修复膏', '护理说明']
      }
    ],
    // 分类注意事项
    precautions: {
      before: [
        '术前一周避免饮酒和服用抗凝血药物',
        '术前避免修眉和使用眉部护理产品',
        '如有眉部皮肤问题请提前告知',
        '女性避开生理期进行操作',
        '术前充分休息，保持良好状态'
      ],
      during: [
        '保持放松状态，配合技师操作',
        '如感到疼痛或不适请及时告知',
        '避免频繁眨眼和面部表情',
        '严格遵循无菌操作流程'
      ],
      after: [
        '术后一周内避免碰水',
        '避免强光照射和暴晒',
        '不要用手抠抓结痂',
        '避免使用化妆品和护肤品',
        '术后3天内避免剧烈运动',
        '按时涂抹修复膏',
        '一个月内避免游泳和桑拿'
      ]
    },
    // 服务亮点
    highlights: [
      '韩式自然眉型设计',
      '专业纹绣师操作',
      '进口安全色料',
      '个性化定制服务',
      '持久自然效果'
    ],
    // 服务保障
    guarantee: {
      quality: '韩国进口色料，安全无害',
      safety: '一人一针，严格无菌操作',
      effect: '正常可保持1-2年',
      service: '30天内免费补色，终身维护'
    },
    // 适用人群详细说明
    suitableDetails: {
      primary: ['眉毛稀疏者', '想要定型眉形的人士', '经常化妆的女性'],
      secondary: ['眉形不对称', '工作繁忙没时间化妆', '运动爱好者'],
      notSuitable: ['孕妇和哺乳期女性', '疤痕体质', '眉部有炎症或伤口', '未成年人']
    },
    // 护理建议
    careAdvice: [
      '术后严格按照护理说明执行',
      '定期回店检查和补色',
      '避免使用含酸性成分的护肤品',
      '注意防晒保护',
      '建议1-2年后进行补色维护'
    ],
    images: [
      'https://picsum.photos/300/200?random=13',
      '/static/beauty/service-detail5.jpg',
      '/static/beauty/service-detail6.jpg'
    ],
    isHot: true,
    bookingCount: 432,
    tags: ['纹绣', '韩式', '半永久', '眉毛']
  },
  {
    id: 4,
    name: '蔓时光补水保湿面膜',
    categoryId: 1,
    price: 128,
    originalPrice: 168,
    duration: 60,
    rating: 4.6,
    reviewCount: 142,
    sold: 980,
    description: '深层补水，改善肌肤干燥，提升肌肤弹性',
    effects: ['深层补水', '改善干燥', '提升弹性', '提亮肤色'],
    suitable: ['干性肌肤', '敏感肌肤', '缺水肌肤'],
    notice: ['术前清洁面部', '术后避免使用刺激性产品', '多喝水促进新陈代谢'],
    process: ['面部清洁', '蒸汽开毛孔', '精华导入', '面膜敷用', '按摩吸收', '保湿锁水'],
    images: [
      'https://picsum.photos/300/200?random=14',
      '/static/beauty/service-detail7.jpg',
      '/static/beauty/service-detail8.jpg'
    ],
    isHot: false,
    bookingCount: 980,
    tags: ['补水', '保湿', '提亮肤色', '抗衰老']
  },
  {
    id: 5,
    name: '精油SPA全身按摩',
    categoryId: 5,
    price: 298,
    originalPrice: 398,
    duration: 120,
    rating: 4.9,
    reviewCount: 203,
    sold: 760,
    description: '舒缓压力，改善睡眠，缓解疲劳',
    effects: ['舒缓压力', '改善睡眠', '缓解疲劳', '促进血液循环'],
    suitable: ['工作压力大', '睡眠质量差', '肌肉紧张者'],
    notice: ['术前避免饱食', '术后多喝水', '术后避免剧烈运动'],
    process: ['环境准备', '精油调配', '全身按摩', '穴位按压', '放松冥想', '护理结束'],
    images: [
      'https://picsum.photos/300/200?random=15',
      '/static/beauty/service-detail9.jpg',
      '/static/beauty/service-detail10.jpg'
    ],
    isHot: true,
    bookingCount: 760,
    tags: ['精油', 'SPA', '全身按摩', '舒缓压力']
  },
  {
    id: 6,
    name: '超音波洁面仪护理',
    categoryId: 6,
    price: 158,
    originalPrice: 228,
    duration: 45,
    rating: 4.5,
    reviewCount: 87,
    sold: 523,
    description: '利用超音波技术深层清洁，温和去除老化角质',
    effects: ['深层清洁', '去除角质', '促进吸收', '改善肤质'],
    suitable: ['角质层厚', '肌肤暗沉', '毛孔堵塞者'],
    notice: ['术前避免去角质', '术后加强保湿', '敏感肌慎用'],
    process: ['面部清洁', '仪器准备', '超音波清洁', '精华导入', '舒缓修复', '防护隔离'],
    images: [
      'https://picsum.photos/300/200?random=16',
      '/static/beauty/service-detail11.jpg',
      '/static/beauty/service-detail12.jpg'
    ],
    isHot: false,
    bookingCount: 523,
    tags: ['超音波', '洁面仪', '深层清洁', '科技美容']
  },
  {
    id: 7,
    name: '法式水晶美甲',
    categoryId: 2,
    price: 138,
    originalPrice: 188,
    duration: 90,
    rating: 4.8,
    reviewCount: 156,
    sold: 642,
    description: '经典法式设计，水晶质感，优雅大方',
    effects: ['优雅造型', '水晶质感', '持久亮泽', '修饰手型'],
    suitable: ['商务场合', '正式活动', '追求优雅的人士'],
    notice: ['术后避免碰撞', '定期保养', '避免接触化学品'],
    process: ['指甲修整', '甲面打磨', '底胶涂抹', '法式线条', '水晶装饰', '封层固化'],
    images: [
      'https://picsum.photos/300/200?random=17',
      '/static/beauty/service-detail13.jpg',
      '/static/beauty/service-detail14.jpg'
    ],
    isHot: false,
    bookingCount: 642,
    tags: ['法式', '水晶', '优雅', '商务']
  },
  {
    id: 8,
    name: '韩式洗剪吹造型',
    categoryId: 3,
    price: 168,
    originalPrice: 218,
    duration: 120,
    rating: 4.7,
    reviewCount: 234,
    sold: 1156,
    description: '时尚韩式造型，根据脸型设计，打造完美发型',
    effects: ['修饰脸型', '时尚造型', '蓬松自然', '易于打理'],
    suitable: ['想要改变形象', '追求时尚的人士'],
    notice: ['术前洗头', '术后避免立即洗头', '使用专业护发产品'],
    process: ['发质分析', '造型设计', '精准剪发', '专业洗护', '造型吹风', '定型护理'],
    images: [
      'https://picsum.photos/300/200?random=18',
      '/static/beauty/service-detail15.jpg',
      '/static/beauty/service-detail16.jpg'
    ],
    isHot: true,
    bookingCount: 1156,
    tags: ['韩式', '洗剪吹', '造型', '修饰脸型']
  },
  {
    id: 9,
    name: '3D立体睫毛嫁接',
    categoryId: 4,
    price: 218,
    originalPrice: 288,
    duration: 90,
    rating: 4.9,
    reviewCount: 178,
    sold: 567,
    description: '3D立体效果，自然卷翘，让眼睛更加迷人',
    effects: ['立体效果', '自然卷翘', '放大眼睛', '持久美丽'],
    suitable: ['睫毛稀疏', '想要放大眼睛', '追求自然美的人士'],
    notice: ['术后24小时避免碰水', '避免揉眼睛', '定期补睫毛'],
    process: ['眼部清洁', '睫毛分离', '胶水准备', '逐根嫁接', '造型调整', '护理指导'],
    images: [
      'https://picsum.photos/300/200?random=19',
      '/static/beauty/service-detail17.jpg',
      '/static/beauty/service-detail18.jpg'
    ],
    isHot: true,
    bookingCount: 567,
    tags: ['3D', '立体', '睫毛嫁接', '自然卷翘']
  },
  {
    id: 10,
    name: '中式传统刮痧',
    categoryId: 5,
    price: 188,
    originalPrice: 238,
    duration: 60,
    rating: 4.4,
    reviewCount: 89,
    sold: 345,
    description: '传统中式刮痧，疏通经络，排毒养颜',
    effects: ['疏通经络', '排毒养颜', '改善循环', '缓解疲劳'],
    suitable: ['气血不畅', '面色暗沉', '肩颈疲劳者'],
    notice: ['术前避免饮酒', '术后多喝温水', '避免风寒'],
    process: ['穴位定位', '刮痧油涂抹', '手法刮痧', '经络疏通', '热敷放松', '护理建议'],
    images: [
      'https://picsum.photos/300/200?random=20',
      '/static/beauty/service-detail19.jpg',
      '/static/beauty/service-detail20.jpg'
    ],
    isHot: false,
    bookingCount: 345,
    tags: ['中式', '传统', '刮痧', '疏通经络']
  },
  {
    id: 11,
    name: '射频紧肤抗衰',
    categoryId: 6,
    price: 588,
    originalPrice: 788,
    duration: 90,
    rating: 4.6,
    reviewCount: 67,
    sold: 234,
    description: '射频技术紧致肌肤，抗衰老，提升面部轮廓',
    effects: ['紧致肌肤', '抗衰老', '提升轮廓', '改善松弛'],
    suitable: ['肌肤松弛', '面部下垂', '抗衰老需求者'],
    notice: ['术前清洁', '术后避免高温', '加强防晒'],
    process: ['肌肤检测', '清洁准备', '射频治疗', '冷却舒缓', '精华导入', '防护修复'],
    images: [
      'https://picsum.photos/300/200?random=21',
      '/static/beauty/service-detail21.jpg',
      '/static/beauty/service-detail22.jpg'
    ],
    isHot: false,
    bookingCount: 234,
    tags: ['射频', '紧肤', '抗衰老', '提升轮廓']
  },
  {
    id: 12,
    name: '光子嫩肤祛斑',
    categoryId: 6,
    price: 398,
    originalPrice: 498,
    duration: 60,
    rating: 4.5,
    reviewCount: 123,
    sold: 456,
    description: '光子技术嫩肤祛斑，改善肌肤色素沉着',
    effects: ['嫩肤祛斑', '改善色素', '提亮肤色', '收缩毛孔'],
    suitable: ['色斑问题', '肌肤暗沉', '毛孔粗大者'],
    notice: ['术前避免暴晒', '术后加强防晒', '避免刺激性产品'],
    process: ['肌肤评估', '清洁准备', '光子治疗', '冷敷舒缓', '修复护理', '防晒指导'],
    images: [
      'https://picsum.photos/300/200?random=22',
      '/static/beauty/service-detail23.jpg',
      '/static/beauty/service-detail24.jpg'
    ],
    isHot: true,
    bookingCount: 456,
    tags: ['光子', '嫩肤', '祛斑', '提亮肤色']
  },
  {
    id: 13,
    name: '欧式挑染造型',
    categoryId: 3,
    price: 288,
    originalPrice: 388,
    duration: 180,
    rating: 4.8,
    reviewCount: 92,
    sold: 278,
    description: '欧式挑染技术，层次分明，时尚个性',
    effects: ['层次分明', '时尚个性', '修饰肤色', '增加质感'],
    suitable: ['追求个性', '想要时尚造型的人士'],
    notice: ['术前测试过敏', '术后护色', '使用专业洗护'],
    process: ['发质检测', '颜色设计', '挑染操作', '护理修复', '造型定型', '护理指导'],
    images: [
      'https://picsum.photos/300/200?random=23',
      '/static/beauty/service-detail25.jpg',
      '/static/beauty/service-detail26.jpg'
    ],
    isHot: false,
    bookingCount: 278,
    tags: ['欧式', '挑染', '个性', '时尚']
  },
  {
    id: 14,
    name: '日式睫毛烫',
    categoryId: 4,
    price: 98,
    originalPrice: 138,
    duration: 45,
    rating: 4.7,
    reviewCount: 167,
    sold: 789,
    description: '日式睫毛烫技术，自然卷翘，持久定型',
    effects: ['自然卷翘', '持久定型', '放大眼睛', '免化妆'],
    suitable: ['睫毛直硬', '想要自然卷翘的人士'],
    notice: ['术后24小时避免碰水', '避免使用睫毛夹', '定期护理'],
    process: ['睫毛清洁', '卷度选择', '烫发操作', '定型处理', '营养护理', '效果检查'],
    images: [
      'https://picsum.photos/300/200?random=24',
      '/static/beauty/service-detail27.jpg',
      '/static/beauty/service-detail28.jpg'
    ],
    isHot: true,
    bookingCount: 789,
    tags: ['日式', '睫毛烫', '自然卷翘', '持久定型']
  },
  {
    id: 15,
    name: '足部SPA护理',
    categoryId: 5,
    price: 158,
    originalPrice: 198,
    duration: 90,
    rating: 4.6,
    reviewCount: 134,
    sold: 423,
    description: '足部深层护理，去除死皮，舒缓疲劳',
    effects: ['深层护理', '去除死皮', '舒缓疲劳', '改善循环'],
    suitable: ['足部疲劳', '死皮较多', '需要放松的人士'],
    notice: ['术前泡脚', '术后保持干燥', '穿透气鞋袜'],
    process: ['足部清洁', '去角质', '按摩护理', '精油滋养', '热敷放松', '保湿护理'],
    images: [
      'https://picsum.photos/300/200?random=25',
      '/static/beauty/service-detail29.jpg',
      '/static/beauty/service-detail30.jpg'
    ],
    isHot: false,
    bookingCount: 423,
    tags: ['足部', 'SPA', '护理', '舒缓疲劳']
  },
  {
    id: 16,
    name: '肩颈理疗按摩',
    categoryId: 5,
    price: 198,
    originalPrice: 268,
    duration: 60,
    rating: 4.8,
    reviewCount: 198,
    sold: 672,
    description: '专业肩颈理疗，缓解颈椎疲劳，改善睡眠',
    effects: ['缓解疲劳', '改善睡眠', '舒缓压力', '活血通络'],
    suitable: ['颈椎疲劳', '肩膀酸痛', '睡眠不佳者'],
    notice: ['术前避免饱食', '术后多喝水', '避免剧烈运动'],
    process: ['问诊评估', '穴位定位', '手法按摩', '理疗仪器', '热敷放松', '护理建议'],
    images: [
      'https://picsum.photos/300/200?random=26',
      '/static/beauty/service-detail31.jpg',
      '/static/beauty/service-detail32.jpg'
    ],
    isHot: true,
    bookingCount: 672,
    tags: ['肩颈', '理疗', '按摩', '缓解疲劳']
  },
  {
    id: 17,
    name: '水光针补水',
    categoryId: 6,
    price: 688,
    originalPrice: 888,
    duration: 60,
    rating: 4.7,
    reviewCount: 89,
    sold: 234,
    description: '水光针深层补水，改善肌肤干燥，提升光泽',
    effects: ['深层补水', '提升光泽', '改善干燥', '紧致肌肤'],
    suitable: ['肌肤干燥', '缺水严重', '想要水光肌的人士'],
    notice: ['术前清洁', '术后避免化妆', '加强防晒'],
    process: ['肌肤检测', '麻醉准备', '水光注射', '冷敷舒缓', '修复护理', '护理指导'],
    images: [
      'https://picsum.photos/300/200?random=27',
      '/static/beauty/service-detail33.jpg',
      '/static/beauty/service-detail34.jpg'
    ],
    isHot: true,
    bookingCount: 234,
    tags: ['水光针', '补水', '提升光泽', '紧致肌肤']
  },
  {
    id: 18,
    name: '艺术美甲定制',
    categoryId: 2,
    price: 168,
    originalPrice: 228,
    duration: 120,
    rating: 4.9,
    reviewCount: 76,
    sold: 189,
    description: '个性化艺术美甲设计，独一无二的创意作品',
    effects: ['个性设计', '艺术创意', '独特美观', '彰显个性'],
    suitable: ['追求个性', '艺术爱好者', '特殊场合需求'],
    notice: ['提前预约', '沟通设计', '精心保养'],
    process: ['需求沟通', '设计草图', '基础处理', '艺术创作', '细节完善', '保养指导'],
    images: [
      'https://picsum.photos/300/200?random=28',
      '/static/beauty/service-detail35.jpg',
      '/static/beauty/service-detail36.jpg'
    ],
    isHot: false,
    bookingCount: 189,
    tags: ['艺术', '定制', '个性', '创意']
  },
  {
    id: 19,
    name: '头皮深层清洁',
    categoryId: 3,
    price: 128,
    originalPrice: 168,
    duration: 60,
    rating: 4.5,
    reviewCount: 156,
    sold: 567,
    description: '头皮深层清洁，去除油脂和污垢，改善头皮环境',
    effects: ['深层清洁', '去油控油', '改善头皮', '促进生长'],
    suitable: ['头皮油腻', '头屑问题', '头皮敏感者'],
    notice: ['术前避免洗头', '术后避免抓挠', '使用温和洗发水'],
    process: ['头皮检测', '深层清洁', '按摩护理', '精华导入', '舒缓修复', '护理建议'],
    images: [
      'https://picsum.photos/300/200?random=29',
      '/static/beauty/service-detail37.jpg',
      '/static/beauty/service-detail38.jpg'
    ],
    isHot: false,
    bookingCount: 567,
    tags: ['头皮', '深层清洁', '去油', '改善环境']
  },
  {
    id: 20,
    name: '眼部抗衰护理',
    categoryId: 1,
    price: 258,
    originalPrice: 328,
    duration: 75,
    rating: 4.8,
    reviewCount: 112,
    sold: 345,
    description: '专业眼部抗衰护理，淡化细纹，紧致眼周',
    effects: ['淡化细纹', '紧致眼周', '消除浮肿', '提亮眼部'],
    suitable: ['眼部细纹', '眼袋浮肿', '黑眼圈问题'],
    notice: ['术前卸妆', '术后避免揉眼', '使用眼霜'],
    process: ['眼部清洁', '热敷开毛孔', '精华导入', '按摩护理', '眼膜敷用', '保湿锁水'],
    images: [
      'https://picsum.photos/300/200?random=30',
      '/static/beauty/service-detail39.jpg',
      '/static/beauty/service-detail40.jpg'
    ],
    isHot: true,
    bookingCount: 345,
    tags: ['眼部', '抗衰', '淡化细纹', '紧致眼周']
  }
]

// 技师数据
const technicians = [
  {
    id: 1,
    name: '李美美',
    level: '首席美容师',
    avatar: 'https://picsum.photos/200/200?random=1',
    rating: 4.9,
    reviewCount: 328,
    experience: 8,
    price: 50,
    workTime: '09:00-21:00',
    restDay: ['周一'],
    specialty: ['面部护理', '身体SPA', '皮肤管理'],
    introduction: '拥有8年美容经验，精通各类面部护理和身体SPA，曾获得全国美容技能大赛金奖。擅长解决各类皮肤问题，为顾客提供专业的美容护理方案。',
    certificate: ['国家高级美容师证', '国际CIDESCO美容文凭', 'SPA高级技师证'],
    gallery: [
      '/static/beauty/technician-work1.jpg',
      '/static/beauty/technician-work2.jpg',
      '/static/beauty/technician-work3.jpg'
    ],
    services: [1, 4, 5, 6, 12, 17, 20],
    isTop: true
  },
  {
    id: 2,
    name: '张小雅',
    level: '高级美甲师',
    avatar: 'https://picsum.photos/200/200?random=2',
    rating: 4.8,
    reviewCount: 256,
    experience: 5,
    price: 30,
    workTime: '10:00-20:00',
    restDay: ['周三'],
    specialty: ['日式美甲', '法式美甲', '手部护理'],
    introduction: '专注美甲5年，精通日式、韩式、法式等多种美甲风格，擅长手绘和各类饰品搭配。追求精致和细节，为每位顾客打造独特的美甲作品。',
    certificate: ['日式美甲认证', '国家高级美甲师证'],
    gallery: [
      '/static/beauty/technician-work4.jpg',
      '/static/beauty/technician-work5.jpg',
      '/static/beauty/technician-work6.jpg'
    ],
    services: [2, 7, 18],
    isTop: false
  },
  {
    id: 3,
    name: '王丽丽',
    level: '资深美发师',
    avatar: 'https://picsum.photos/200/200?random=3',
    rating: 4.9,
    reviewCount: 412,
    experience: 10,
    price: 60,
    workTime: '09:30-21:30',
    restDay: ['周二'],
    specialty: ['剪发造型', '染发', '烫发', '头皮护理'],
    introduction: '10年美发经验，曾在国际知名发廊工作，擅长根据顾客脸型和气质设计发型。精通各类染烫技术，追求时尚与实用的完美结合。',
    certificate: ['国际发型师认证', 'TONI&GUY剪发认证', '欧莱雅专业染发认证'],
    gallery: [
      '/static/beauty/technician-work7.jpg',
      '/static/beauty/technician-work8.jpg',
      '/static/beauty/technician-work9.jpg'
    ],
    services: [8, 13, 19],
    isTop: true
  },
  {
    id: 4,
    name: '陈晓敏',
    level: '纹绣师',
    avatar: 'https://picsum.photos/200/200?random=4',
    rating: 4.7,
    reviewCount: 187,
    experience: 6,
    price: 80,
    workTime: '10:00-19:00',
    restDay: ['周六', '周日'],
    specialty: ['韩式半永久眉毛', '美瞳线', '唇妆'],
    introduction: '专业纹绣师，擅长韩式、日式半永久妆容，注重自然效果。严格执行消毒标准，使用进口色料，确保安全和效果。',
    certificate: ['韩国半永久化妆认证', '纹绣师资格证'],
    gallery: [
      '/static/beauty/technician-work10.jpg',
      '/static/beauty/technician-work11.jpg',
      '/static/beauty/technician-work12.jpg'
    ],
    services: [3, 9, 14],
    isTop: false
  },
  {
    id: 5,
    name: '刘小芳',
    level: '高级按摩师',
    avatar: 'https://picsum.photos/200/200?random=5',
    rating: 4.8,
    reviewCount: 198,
    experience: 6,
    price: 40,
    workTime: '09:30-20:30',
    restDay: ['周二'],
    specialty: ['身体按摩', '足疗SPA', '肩颈理疗'],
    introduction: '专业按摩师，擅长各种身体按摩和理疗，对肩颈疲劳、腰背酸痛有丰富的治疗经验。',
    certificate: ['高级按摩师证', '中医理疗师证'],
    gallery: [
      '/static/beauty/technician-work13.jpg',
      '/static/beauty/technician-work14.jpg',
      '/static/beauty/technician-work15.jpg'
    ],
    services: [10, 15, 16],
    isTop: true
  },
  {
    id: 6,
    name: '周美玲',
    level: '美容仪器师',
    avatar: 'https://picsum.photos/200/200?random=6',
    rating: 4.7,
    reviewCount: 145,
    experience: 4,
    price: 60,
    workTime: '10:00-19:00',
    restDay: ['周日'],
    specialty: ['美容仪器操作', '光子嫩肤', '射频抗衰'],
    introduction: '专业美容仪器操作师，熟练掌握各种先进美容仪器的使用，为顾客提供安全有效的科技美容服务。',
    certificate: ['美容仪器操作证', '激光美容师证'],
    gallery: [
      '/static/beauty/technician-work16.jpg',
      '/static/beauty/technician-work17.jpg',
      '/static/beauty/technician-work18.jpg'
    ],
    services: [11, 12, 17],
    isTop: false
  }
]

// 评价数据
const reviews = [
  {
    id: 1,
    serviceId: 1,
    TechnicianUserID: 1,
    userId: 101,
    userName: '张女士',
    userAvatar: 'https://picsum.photos/100/100?random=21',
    rating: 5,
    content: '服务非常专业，皮肤明显改善，毛孔变小了，很满意！',
    images: [
      'https://picsum.photos/150/150?random=31',
      'https://picsum.photos/150/150?random=32'
    ],
    createTime: '2024-01-15 15:30:00'
  },
  {
    id: 2,
    serviceId: 1,
    TechnicianUserID: 1,
    userId: 102,
    userName: '李先生',
    userAvatar: 'https://picsum.photos/100/100?random=22',
    rating: 4,
    content: '美容师技术很好，服务态度也不错，就是店里有点吵。',
    images: [],
    createTime: '2024-01-12 10:15:00'
  },
  {
    id: 3,
    serviceId: 2,
    TechnicianUserID: 2,
    userId: 103,
    userName: '王小姐',
    userAvatar: 'https://picsum.photos/100/100?random=23',
    rating: 5,
    content: '美甲师很耐心，设计很漂亮，持久度也很好，两周了还是很完美！',
    images: [
      'https://picsum.photos/150/150?random=33'
    ],
    createTime: '2024-01-10 14:20:00'
  }
]

// 可用时间段数据
const availableTimeSlots = [
  {
    date: '2024-01-20',
    slots: ['09:00', '10:30', '13:00', '14:30', '16:00', '17:30']
  },
  {
    date: '2024-01-21',
    slots: ['09:00', '10:30', '13:00', '16:00', '17:30']
  },
  {
    date: '2024-01-22',
    slots: ['10:30', '13:00', '14:30', '16:00']
  }
]

// 优惠券数据
const coupons = [
  {
    id: 1,
    name: '新人专享券',
    description: '首次预约立减50元',
    amount: 50,
    minSpend: 100,
    validUntil: '2024-03-31',
    isNew: true
  },
  {
    id: 2,
    name: '生日特权券',
    description: '生日当月享受8折优惠',
    discount: 0.8,
    minSpend: 200,
    validUntil: '2024-02-29',
    isNew: false
  },
  {
    id: 3,
    name: '会员专享券',
    description: '指定项目立减100元',
    amount: 100,
    minSpend: 300,
    validUntil: '2024-06-30',
    isNew: false
  }
]

// 预约数据
const bookings = [
  {
    id: 1,
    orderNo: generateOrderNo(),
    userId: 101,
    serviceId: 1,
    serviceName: '深层清洁面部护理',
    TechnicianUserID: 1,
    technicianName: '李美美',
    bookingDate: '2024-01-15',
    bookingTime: '10:30',
    duration: 90,
    status: 'completed',
    statusText: '已完成',
    customerName: '张女士',
    customerPhone: '13800138000',
    customerNote: '皮肤敏感，请温和操作',
    totalPrice: 198,
    discount: 50,
    finalPrice: 148,
    paymentMethod: 'wechat',
    createTime: '2024-01-10 15:30:00'
  },
  {
    id: 2,
    orderNo: generateOrderNo(),
    userId: 101,
    serviceId: 2,
    serviceName: '日式美甲套餐',
    TechnicianUserID: 2,
    technicianName: '张小雅',
    bookingDate: '2024-01-20',
    bookingTime: '14:30',
    duration: 60,
    status: 'confirmed',
    statusText: '已确认',
    customerName: '张女士',
    customerPhone: '13800138000',
    customerNote: '',
    totalPrice: 88,
    discount: 0,
    finalPrice: 88,
    paymentMethod: 'alipay',
    createTime: '2024-01-15 09:20:00'
  },
  {
    id: 3,
    orderNo: generateOrderNo(),
    userId: 101,
    serviceId: 1,
    serviceName: '深层清洁面部护理',
    TechnicianUserID: 1,
    technicianName: '李美美',
    bookingDate: '2024-01-25',
    bookingTime: '16:00',
    duration: 90,
    status: 'pending',
    statusText: '待确认',
    customerName: '张女士',
    customerPhone: '13800138000',
    customerNote: '',
    totalPrice: 198,
    discount: 0,
    finalPrice: 198,
    paymentMethod: 'wechat',
    createTime: '2024-01-20 10:15:00'
  },
  {
    id: 4,
    orderNo: generateOrderNo(),
    userId: 101,
    serviceId: 3,
    serviceName: '韩式半永久眉毛',
    TechnicianUserID: 4,
    technicianName: '陈晓敏',
    bookingDate: '2024-01-12',
    bookingTime: '11:00',
    duration: 120,
    status: 'cancelled',
    statusText: '已取消',
    customerName: '张女士',
    customerPhone: '13800138000',
    customerNote: '临时有事',
    totalPrice: 688,
    discount: 100,
    finalPrice: 588,
    paymentMethod: 'wechat',
    createTime: '2024-01-08 14:30:00'
  },
  {
    id: 5,
    orderNo: generateOrderNo(),
    userId: 101,
    serviceId: 2,
    serviceName: '日式美甲套餐',
    TechnicianUserID: 2,
    technicianName: '张小雅',
    bookingDate: '2024-01-05',
    bookingTime: '15:30',
    duration: 60,
    status: 'completed',
    statusText: '已完成',
    customerName: '张女士',
    customerPhone: '13800138000',
    customerNote: '',
    totalPrice: 88,
    discount: 0,
    finalPrice: 88,
    paymentMethod: 'alipay',
    createTime: '2024-01-02 16:20:00'
  }
]

// 收藏数据
const favorites = [
  {
    id: 1,
    userId: 1,
    serviceId: 1,
    createTime: '2024-01-10T10:00:00Z'
  },
  {
    id: 2,
    userId: 1,
    serviceId: 3,
    createTime: '2024-01-12T14:30:00Z'
  },
  {
    id: 3,
    userId: 1,
    serviceId: 5,
    createTime: '2024-01-15T16:20:00Z'
  },
  {
    id: 4,
    userId: 2,
    serviceId: 2,
    createTime: '2024-01-11T09:15:00Z'
  },
  {
    id: 5,
    userId: 2,
    serviceId: 4,
    createTime: '2024-01-13T11:45:00Z'
  }
]

// 延迟函数，模拟网络请求延迟
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))

// 美容系统API
export const beautyApi = {
  // 服务相关API
  getServiceCategories: async () => {
    await delay()
    return {
      code: 200,
      message: 'success',
      data: serviceCategories
    }
  },
  
  getServices: async (categoryId = null) => {
    await delay()
    let result = [...services]
    
    if (categoryId) {
      result = result.filter(service => service.categoryId === categoryId)
    }
    
    return {
      code: 200,
      message: 'success',
      data: result
    }
  },
  
  getServiceDetail: async (id) => {
    await delay()
    const service = services.find(s => s.id === id)
    
    if (!service) {
      return {
        code: 404,
        message: '服务不存在',
        data: null
      }
    }
    
    // 获取推荐技师
    const recommendTechnicians = technicians.filter(t => 
      t.services && t.services.includes(id)
    ).slice(0, 2)
    
    // 获取服务评价
    const serviceReviews = reviews.filter(r => r.serviceId === id)
    
    return {
      code: 200,
      message: 'success',
      data: {
        ...service,
        recommendTechnicians,
        reviews: serviceReviews
      }
    }
  },
  
  // 技师相关API
  getTechnicians: async (serviceId = null) => {
    await delay()
    let result = [...technicians]
    
    if (serviceId) {
      result = result.filter(tech => 
        tech.services && tech.services.includes(serviceId)
      )
    }
    
    return {
      code: 200,
      message: 'success',
      data: result
    }
  },
  
  getTechnicianDetail: async (id) => {
    await delay()
    const technician = technicians.find(t => t.id === id)
    
    if (!technician) {
      return {
        code: 404,
        message: '技师不存在',
        data: null
      }
    }
    
    return {
      code: 200,
      message: 'success',
      data: technician
    }
  },
  
  getTechnicianTimeSlots: async (id, date) => {
    await delay()
    // 简单返回模拟数据，实际应该根据技师ID和日期查询
    return {
      code: 200,
      message: 'success',
      data: availableTimeSlots
    }
  },
  
  // 评价相关API
  getReviews: async (serviceId = null, TechnicianUserID= null, params = {}) => {
    await delay()
    let result = [...reviews]
    
    if (serviceId) {
      result = result.filter(review => review.serviceId === serviceId)
    }
    
    if (TechnicianUserID) {
      result = result.filter(review => review.TechnicianUserID=== TechnicianUserID)
    }
    
    // 分页处理
    const page = params.page || 1
    const pageSize = params.pageSize || 10
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedReviews = result.slice(startIndex, endIndex)
    
    return {
      code: 200,
      message: 'success',
      data: paginatedReviews,
      total: result.length
    }
  },

  // 提交评价
  submitReview: async (reviewData) => {
    await delay(1000)
    
    // 模拟提交评价
    const newReview = {
      id: reviews.length + 1,
      serviceId: reviewData.serviceId || 1,
      TechnicianUserID: reviewData.TechnicianUserID|| 1,
      userId: 104,
      userName: reviewData.isAnonymous ? '匿名用户' : '当前用户',
      userAvatar: 'https://picsum.photos/100/100?random=24',
      rating: reviewData.overallRating,
      content: reviewData.content,
      images: reviewData.images || [],
      createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
      tags: reviewData.tags || [],
      isAnonymous: reviewData.isAnonymous || false,
      serviceName: '深层清洁面部护理',
      technicianName: '张美美'
    }
    
    reviews.unshift(newReview)
    
    return {
      code: 200,
      message: '评价成功',
      data: newReview
    }
  },

  // 获取预约详情（用于写评价）
  getBookingDetail: async (bookingId) => {
    await delay()
    
    // 模拟预约详情数据
    const bookingDetail = {
      id: bookingId,
      serviceName: '深层清洁面部护理',
      serviceImage: 'https://picsum.photos/300/200?random=1',
      technicianName: '张美美',
      bookingDate: '2024-01-15 14:00:00',
      status: 'completed'
    }
    
    return {
      code: 200,
      message: 'success',
      data: bookingDetail
    }
  },
  
  // 优惠券相关API
  getCoupons: async () => {
    await delay()
    return {
      code: 200,
      message: 'success',
      data: coupons
    }
  },
  
  // 预约相关API
  createBooking: async (bookingData) => {
    await delay(1000) // 预约操作延迟更长
    
    const newBooking = {
      id: bookings.length + 1,
      orderNo: generateOrderNo(),
      status: 'pending',
      statusText: '待确认',
      createTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
      ...bookingData
    }
    
    bookings.push(newBooking)
    
    return {
      code: 200,
      message: 'success',
      data: newBooking
    }
  },
  
  getMyBookings: async () => {
    await delay()
    return {
      code: 200,
      message: 'success',
      data: bookings
    }
  },
  
  getBookingDetail: async (id) => {
    await delay()
    const booking = bookings.find(b => b.id === id)
    
    if (!booking) {
      return {
        code: 404,
        message: '预约不存在',
        data: null
      }
    }
    
    // 获取关联的服务和技师信息
    const service = services.find(s => s.id === booking.serviceId)
    const technician = technicians.find(t => t.id === booking.TechnicianUserID)
    
    return {
      code: 200,
      message: 'success',
      data: {
        ...booking,
        service,
        technician,
        // 添加状态历史记录
        statusHistory: [
          {
            status: 'pending',
            statusText: '预约提交',
            timestamp: booking.createTime,
            reason: '用户提交预约'
          },
          ...(booking.status !== 'pending' ? [{
            status: booking.status,
            statusText: booking.statusText,
            timestamp: new Date(new Date(booking.createTime).getTime() + 2 * 60 * 60 * 1000).toISOString(),
            reason: booking.status === 'cancelled' ? '用户取消' : '系统自动更新'
          }] : [])
        ]
      }
    }
  },
  
  getUserBookings: async () => {
    await delay(1000)
    return {
      code: 200,
      message: '获取成功',
      data: bookings
    }
  },
  
  cancelBooking: async (id) => {
    await delay(1000)
    const bookingIndex = bookings.findIndex(b => b.id === id)
    
    if (bookingIndex === -1) {
      return {
        code: 404,
        message: '预约不存在',
        data: null
      }
    }
    
    // 更新预约状态
    bookings[bookingIndex].status = 'cancelled'
    bookings[bookingIndex].statusText = '已取消'
    
    return {
      code: 200,
      message: 'success',
      data: bookings[bookingIndex]
    }
  },
  
  // 更新预约状态
  updateBookingStatus: async (id, status, reason = '') => {
    await delay(1000)
    const bookingIndex = bookings.findIndex(b => b.id === id)
    
    if (bookingIndex === -1) {
      return {
        code: 404,
        message: '预约不存在',
        data: null
      }
    }
    
    const booking = bookings[bookingIndex]
    const oldStatus = booking.status
    
    // 状态变更规则验证
    const statusTransitions = {
      'pending': ['confirmed', 'cancelled'],
      'confirmed': ['in_service', 'cancelled'],
      'in_service': ['completed'],
      'completed': [],
      'cancelled': []
    }
    
    if (!statusTransitions[oldStatus] || !statusTransitions[oldStatus].includes(status)) {
      return {
        code: 400,
        message: '无效的状态变更',
        data: null
      }
    }
    
    // 更新状态
    booking.status = status
    booking.statusText = getStatusText(status)
    booking.updateTime = new Date().toISOString()
    
    // 添加状态变更记录
    if (!booking.statusHistory) {
      booking.statusHistory = []
    }
    
    booking.statusHistory.push({
      status: status,
      statusText: getStatusText(status),
      reason: reason,
      timestamp: new Date().toISOString(),
      operator: 'system' // 可以是 'user', 'technician', 'admin'
    })
    
    // 触发通知
    await triggerStatusChangeNotification(booking, oldStatus, status)
    
    return {
      code: 200,
      message: 'success',
      data: booking
    }
  },
  
  // 收藏相关API
  addToFavorites: async (serviceId) => {
    await delay(500)
    const userId = 1 // 模拟当前用户ID
    
    // 检查是否已收藏
    const existingFavorite = favorites.find(f => f.userId === userId && f.serviceId === serviceId)
    if (existingFavorite) {
      return {
        code: 400,
        message: '已收藏该服务',
        data: null
      }
    }
    
    // 添加收藏
    const newFavorite = {
      id: favorites.length + 1,
      userId: userId,
      serviceId: serviceId,
      createTime: new Date().toISOString()
    }
    
    favorites.push(newFavorite)
    
    return {
      code: 200,
      message: '收藏成功',
      data: newFavorite
    }
  },
  
  removeFromFavorites: async (serviceId) => {
    await delay(500)
    const userId = 1 // 模拟当前用户ID
    
    const favoriteIndex = favorites.findIndex(f => f.userId === userId && f.serviceId === serviceId)
    if (favoriteIndex === -1) {
      return {
        code: 404,
        message: '收藏记录不存在',
        data: null
      }
    }
    
    favorites.splice(favoriteIndex, 1)
    
    return {
      code: 200,
      message: '取消收藏成功',
      data: null
    }
  },
  
  getFavoritesList: async (page = 1, pageSize = 10) => {
    await delay(500)
    const userId = 1 // 模拟当前用户ID
    
    // 获取用户收藏的服务ID列表
    const userFavorites = favorites.filter(f => f.userId === userId)
    const serviceIds = userFavorites.map(f => f.serviceId)
    
    // 获取收藏的服务详情
    const favoriteServices = services.filter(s => serviceIds.includes(s.id))
    
    // 添加收藏时间
    const favoriteServicesWithTime = favoriteServices.map(service => {
      const favorite = userFavorites.find(f => f.serviceId === service.id)
      return {
        ...service,
        favoriteTime: favorite.createTime
      }
    })
    
    // 按收藏时间倒序排序
    favoriteServicesWithTime.sort((a, b) => new Date(b.favoriteTime) - new Date(a.favoriteTime))
    
    // 分页
    const start = (page - 1) * pageSize
    const end = start + pageSize
    const paginatedServices = favoriteServicesWithTime.slice(start, end)
    
    return {
      code: 200,
      message: 'success',
      data: {
        list: paginatedServices,
        total: favoriteServicesWithTime.length,
        page: page,
        pageSize: pageSize,
        hasMore: end < favoriteServicesWithTime.length
      }
    }
  },
  
  checkFavoriteStatus: async (serviceId) => {
    await delay(300)
    const userId = 1 // 模拟当前用户ID
    
    const favorite = favorites.find(f => f.userId === userId && f.serviceId === serviceId)
    
    return {
      code: 200,
      message: 'success',
      data: {
        isFavorite: !!favorite,
        favoriteTime: favorite ? favorite.createTime : null
      }
    }
  },
  
  // 获取预约状态历史
  getBookingStatusHistory: async (id) => {
    await delay(500)
    const booking = bookings.find(b => b.id === id)
    
    if (!booking) {
      return {
        code: 404,
        message: '预约不存在',
        data: null
      }
    }
    
    return {
      code: 200,
      message: 'success',
      data: booking.statusHistory || []
    }
  },
  
  // 预约改期申请
  rescheduleBooking: async (id, newDate, newTime, reason = '') => {
    await delay(1200)
    const bookingIndex = bookings.findIndex(b => b.id === id)
    
    if (bookingIndex === -1) {
      return {
        code: 404,
        message: '预约不存在',
        data: null
      }
    }
    
    const booking = bookings[bookingIndex]
    
    // 只有待确认和已确认的预约可以改期
    if (!['pending', 'confirmed'].includes(booking.status)) {
      return {
        code: 400,
        message: '当前状态不支持改期',
        data: null
      }
    }
    
    // 检查新时间是否可用
    const timeCheckResult = await beautyApi.checkTimeConflict(newDate, newTime, booking.TechnicianUserID, booking.duration)
    
    if (!timeCheckResult.data.available) {
      return {
        code: 400,
        message: '新时间不可用',
        data: {
          conflict: true,
          alternatives: timeCheckResult.data.alternatives
        }
      }
    }
    
    // 保存原始预约信息
    const originalBooking = {
      date: booking.bookingDate,
      time: booking.bookingTime
    }
    
    // 更新预约时间
    booking.bookingDate = newDate
    booking.bookingTime = newTime
    booking.updateTime = new Date().toISOString()
    
    // 添加改期记录
    if (!booking.rescheduleHistory) {
      booking.rescheduleHistory = []
    }
    
    booking.rescheduleHistory.push({
      originalDate: originalBooking.date,
      originalTime: originalBooking.time,
      newDate: newDate,
      newTime: newTime,
      reason: reason,
      timestamp: new Date().toISOString()
    })
    
    // 触发改期通知
    await triggerRescheduleNotification(booking, originalBooking)
    
    return {
      code: 200,
      message: '改期成功',
      data: booking
    }
  },
  
  // 获取用户通知列表
  getUserNotifications: async (userId = 1) => {
    await delay(800)
    
    // 模拟通知数据
    const notifications = [
      {
        id: 1,
        type: 'booking_confirmed',
        title: '预约确认',
        content: '您的预约已确认，请按时到店享受服务',
        bookingId: 1,
        isRead: false,
        createTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 2,
        type: 'booking_reminder',
        title: '预约提醒',
        content: '您有一个预约将在1小时后开始，请准时到店',
        bookingId: 2,
        isRead: false,
        createTime: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 3,
        type: 'booking_completed',
        title: '服务完成',
        content: '您的美容服务已完成，期待您的评价',
        bookingId: 3,
        isRead: true,
        createTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      }
    ]
    
    return {
      code: 200,
      message: 'success',
      data: notifications
    }
  },
  
  // 标记通知为已读
  markNotificationRead: async (id) => {
    await delay(300)
    return {
      code: 200,
      message: 'success',
      data: { id, isRead: true }
    }
  },
  
  // 获取未读通知数量
  getUnreadNotificationCount: async (userId = 1) => {
    await delay(200)
    return {
      code: 200,
      message: 'success',
      data: { count: 2 }
    }
  },
  
  // 获取可用时间段
  getAvailableTimeSlots: async (date, TechnicianUserID) => {
    await delay(800)
    
    // 获取技师信息
    const technician = technicians.find(t => t.id === TechnicianUserID)
    if (!technician) {
      return {
        code: 404,
        message: '技师不存在',
        data: []
      }
    }
    
    // 解析技师工作时间
    const [startTime, endTime] = technician.workTime.split('-')
    const startHour = parseInt(startTime.split(':')[0])
    const endHour = parseInt(endTime.split(':')[0])
    
    // 生成时间段
    const timeSlots = []
    const selectedDate = new Date(date)
    const now = new Date()
    
    // 检查是否是今天
    const isToday = selectedDate.toDateString() === now.toDateString()
    const currentHour = now.getHours()
    const currentMinute = now.getMinutes()
    
    // 每30分钟一个时间段
    for (let hour = startHour; hour < endHour; hour++) {
      for (let minute of [0, 30]) {
        const timeStr = String(hour).padStart(2, '0') + ':' + String(minute).padStart(2, '0')
        
        // 如果是今天，检查时间是否已过
        let available = true
        if (isToday) {
          if (hour < currentHour || (hour === currentHour && minute <= currentMinute + 30)) {
            available = false // 至少提前30分钟预约
          }
        }
        
        // 检查是否与现有预约冲突
        if (available) {
          const conflictBooking = bookings.find(booking => 
            booking.TechnicianUserID=== TechnicianUserID&&
            booking.bookingDate === date &&
            booking.bookingTime === timeStr &&
            booking.status !== 'cancelled'
          )
          if (conflictBooking) {
            available = false
          }
        }
        
        timeSlots.push({
          time: timeStr,
          available: available,
          reason: available ? '' : (isToday && (hour < currentHour || (hour === currentHour && minute <= currentMinute + 30)) ? '时间已过' : '已被预约')
        })
      }
    }
    
    return {
      code: 200,
      message: 'success',
      data: timeSlots
    }
  },
  
  // 获取技师工作日程
  getTechnicianSchedule: async (TechnicianUserID, startDate, endDate) => {
    await delay(500)
    
    const technician = technicians.find(t => t.id === TechnicianUserID)
    if (!technician) {
      return {
        code: 404,
        message: '技师不存在',
        data: {}
      }
    }
    
    // 获取指定日期范围内的预约
    const schedule = {}
    const start = new Date(startDate)
    const end = new Date(endDate)
    
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const dateStr = d.getFullYear() + '-' + 
                     String(d.getMonth() + 1).padStart(2, '0') + '-' + 
                     String(d.getDate()).padStart(2, '0')
      
      const dayBookings = bookings.filter(booking => 
        booking.TechnicianUserID=== TechnicianUserID&&
        booking.bookingDate === dateStr &&
        booking.status !== 'cancelled'
      )
      
      schedule[dateStr] = {
        workTime: technician.workTime,
        restDay: technician.restDay,
        bookings: dayBookings,
        availableSlots: dayBookings.length < 16 // 假设每天最多16个时间段
      }
    }
    
    return {
      code: 200,
      message: 'success',
      data: schedule
    }
  },
  
  // 检查时间冲突
  checkTimeConflict: async (date, time, TechnicianUserID, duration = 60) => {
    await delay(300)
    
    const [hour, minute] = time.split(':').map(Number)
    const startTime = hour * 60 + minute
    const endTime = startTime + duration
    
    // 检查与现有预约的冲突
    const conflicts = bookings.filter(booking => {
      if (booking.TechnicianUserID!== TechnicianUserID|| 
          booking.bookingDate !== date || 
          booking.status === 'cancelled') {
        return false
      }
      
      const [bookingHour, bookingMinute] = booking.bookingTime.split(':').map(Number)
      const bookingStartTime = bookingHour * 60 + bookingMinute
      const bookingEndTime = bookingStartTime + (booking.duration || 60)
      
      // 检查时间重叠
      return !(endTime <= bookingStartTime || startTime >= bookingEndTime)
    })
    
    return {
      code: 200,
      message: 'success',
      data: {
        hasConflict: conflicts.length > 0,
        conflicts: conflicts,
        suggestedTimes: conflicts.length > 0 ? generateAlternativeTimes(date, TechnicianUserID, duration) : []
      }
    }
  },

  // 新增：获取可预约时间段
  getAvailableTimeSlots: (params) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const { serviceId, TechnicianUserID, date } = params
        
        // 模拟生成时间段
        const slots = []
        const startHour = 9
        const endHour = 18
        const slotDuration = 60 // 分钟
        
        for (let hour = startHour; hour < endHour; hour++) {
          for (let minute = 0; minute < 60; minute += slotDuration) {
            const timeStr = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
            
            // 模拟一些时间段已被预约
            const isBooked = Math.random() < 0.3 // 30%概率已被预约
            const isRestTime = (hour >= 12 && hour < 14) // 午休时间
            
            slots.push({
              startTime: timeStr,
              endTime: `${(hour + 1).toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
              available: !isBooked && !isRestTime,
              reason: isBooked ? '已预约' : (isRestTime ? '休息时间' : ''),
              price: 299 + (TechnicianUserID? 50 : 0) // 指定技师额外收费
            })
          }
        }
        
        resolve({
          code: 200,
          message: 'success',
          data: {
            date,
            slots: slots.filter(slot => slot.startTime !== slot.endTime)
          }
        })
      }, 500)
    })
  },

  // 新增：检查时间可用性
  checkTimeAvailability: (params) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const { serviceId, TechnicianUserID, date, time } = params
        
        // 模拟检查时间可用性
        const available = Math.random() > 0.1 // 90%概率可用
        
        resolve({
          code: 200,
          message: 'success',
          data: {
            available,
            reason: available ? '' : '时间段已被预约'
          }
        })
      }, 300)
    })
  },

  // 新增：检查时间冲突
  checkTimeConflict: (params) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const { serviceId, TechnicianUserID, date, time } = params
        
        // 模拟检查时间冲突
        const hasConflict = Math.random() < 0.2 // 20%概率冲突
        
        const alternatives = hasConflict ? [
          '10:00',
          '14:00',
          '16:00'
        ].filter(t => t !== time) : []
        
        resolve({
          code: 200,
          message: hasConflict ? '时间冲突' : 'success',
          data: {
            hasConflict,
            alternatives,
            reason: hasConflict ? '该时间段已被预约' : ''
          }
        })
      }, 300)
    })
  },

  // 新增：获取可用优惠券
  getAvailableCoupons: (params) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const { serviceId, amount } = params
        
        // 模拟优惠券数据
        const allCoupons = [
          {
            id: 1,
            name: '新用户专享',
            type: 'amount',
            value: 50,
            minAmount: 200,
            expireDate: '2025-02-28',
            description: '新用户首次预约立减50元'
          },
          {
            id: 2,
            name: '满减优惠',
            type: 'amount',
            value: 100,
            minAmount: 500,
            expireDate: '2025-03-31',
            description: '满500元减100元'
          },
          {
            id: 3,
            name: '9折优惠',
            type: 'percent',
            value: 10,
            minAmount: 100,
            expireDate: '2025-04-30',
            description: '全场9折优惠'
          }
        ]
        
        // 筛选可用优惠券
        const availableCoupons = allCoupons.filter(coupon => {
          return amount >= coupon.minAmount && new Date(coupon.expireDate) > new Date()
        })
        
        resolve({
          code: 200,
          message: 'success',
          data: availableCoupons
        })
      }, 400)
    })
  },

  // 改进：创建预约订单
  createBooking: (bookingData) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟创建预约
        const success = Math.random() > 0.05 // 95%成功率
        
        if (success) {
          const newBooking = {
            id: Date.now(),
            bookingNo: `BK${Date.now()}`,
            ...bookingData,
            status: 'pending',
            createTime: new Date().toISOString(),
            statusHistory: [
              {
                status: 'pending',
                statusText: '预约已提交',
                timestamp: new Date().toISOString(),
                reason: '用户提交预约申请'
              }
            ]
          }
          
          // 添加到预约列表
          const existingBookings = JSON.parse(localStorage.getItem('userBookings') || '[]')
          existingBookings.push(newBooking)
          localStorage.setItem('userBookings', JSON.stringify(existingBookings))
          
          resolve({
            success: true,
            message: '预约成功',
            data: newBooking
          })
        } else {
          // 模拟不同类型的错误
          const errorTypes = [
            { message: '时间冲突，该时间段已被预约', code: 'TIME_CONFLICT' },
            { message: '余额不足，请充值后再试', code: 'INSUFFICIENT_BALANCE' },
            { message: '优惠券已过期或不可用', code: 'COUPON_INVALID' },
            { message: '系统繁忙，请稍后重试', code: 'SYSTEM_BUSY' }
          ]
          
          const error = errorTypes[Math.floor(Math.random() * errorTypes.length)]
          reject(new Error(error.message))
        }
      }, 1000)
    })
  },

  // 新增：改期预约
  rescheduleBooking: (bookingId, newDate, newTime, reason) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟改期检查
        const success = Math.random() > 0.3 // 70%成功率
        
        if (success) {
          resolve({
            code: 200,
            message: '改期成功',
            data: {
              bookingId,
              newDate,
              newTime,
              reason
            }
          })
        } else {
          // 模拟时间冲突
          const hasConflict = Math.random() < 0.7
          
          if (hasConflict) {
            resolve({
              code: 400,
              message: '选择的时间段已被预约',
              data: {
                conflict: true,
                alternatives: ['10:00', '14:00', '16:00']
              }
            })
          } else {
            resolve({
              code: 500,
              message: '改期失败，请稍后重试'
            })
          }
        }
      }, 800)
    })
  },

  // 新增：获取用户通知
  getUserNotifications: () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const notifications = [
          {
            id: 1,
            type: 'booking_confirmed',
            title: '预约确认',
            content: '您的预约已确认，请按时到店享受服务',
            bookingId: 1,
            isRead: false,
            createTime: new Date(Date.now() - 3600000).toISOString()
          },
          {
            id: 2,
            type: 'booking_reminder',
            title: '预约提醒',
            content: '您明天14:00有一个预约，请提前15分钟到店',
            bookingId: 2,
            isRead: false,
            createTime: new Date(Date.now() - 7200000).toISOString()
          },
          {
            id: 3,
            type: 'service_completed',
            title: '服务完成',
            content: '感谢您的光临，期待您的评价',
            bookingId: 3,
            isRead: true,
            createTime: new Date(Date.now() - 86400000).toISOString()
          }
        ]
        
        resolve({
          code: 200,
          message: 'success',
          data: notifications
        })
      }, 400)
    })
  },

  // 新增：标记通知为已读
  markNotificationRead: (notificationId) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          message: '标记成功',
          data: { id: notificationId }
        })
      }, 200)
    })
  },

  // 新增：获取预约统计
  getBookingStats: () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const stats = {
          pending: 2,
          confirmed: 1,
          inService: 0,
          completed: 15,
          cancelled: 1,
          totalSpent: 4580
        }
        
        resolve({
          code: 200,
          message: 'success',
          data: stats
        })
      }, 300)
    })
  },

  // 新增：获取未读通知数量
  getUnreadNotificationCount: () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          message: 'success',
          data: { count: 3 }
        })
      }, 200)
    })
  }
}

// 生成替代时间建议
function generateAlternativeTimes(date, TechnicianUserID, duration) {
  const technician = technicians.find(t => t.id === TechnicianUserID)
  if (!technician) return []
  
  const [startTime, endTime] = technician.workTime.split('-')
  const startHour = parseInt(startTime.split(':')[0])
  const endHour = parseInt(endTime.split(':')[0])
  
  const alternatives = []
  for (let hour = startHour; hour < endHour && alternatives.length < 3; hour++) {
    for (let minute of [0, 30]) {
      const timeStr = String(hour).padStart(2, '0') + ':' + String(minute).padStart(2, '0')
      
      // 检查这个时间是否可用
      const [checkHour, checkMinute] = timeStr.split(':').map(Number)
      const checkStartTime = checkHour * 60 + checkMinute
      const checkEndTime = checkStartTime + duration
      
      const hasConflict = bookings.some(booking => {
        if (booking.TechnicianUserID!== TechnicianUserID|| 
            booking.bookingDate !== date || 
            booking.status === 'cancelled') {
          return false
        }
        
        const [bookingHour, bookingMinute] = booking.bookingTime.split(':').map(Number)
        const bookingStartTime = bookingHour * 60 + bookingMinute
        const bookingEndTime = bookingStartTime + (booking.duration || 60)
        
        return !(checkEndTime <= bookingStartTime || checkStartTime >= bookingEndTime)
      })
      
      if (!hasConflict) {
        alternatives.push(timeStr)
      }
      
      if (alternatives.length >= 3) break
    }
  }
  
  return alternatives
}

// 辅助函数：获取状态文本
function getStatusText(status) {
  const statusMap = {
    'pending': '待确认',
    'confirmed': '已确认',
    'in_service': '服务中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未知状态'
}

// 辅助函数：触发状态变更通知
async function triggerStatusChangeNotification(booking, oldStatus, newStatus) {
  console.log(`预约状态变更通知: 预约${booking.id} 从 ${oldStatus} 变更为 ${newStatus}`)
  
  // 这里可以实现真实的通知逻辑
  // 例如：发送短信、推送通知、邮件等
  
  const notificationMessages = {
    'confirmed': '您的预约已确认，请按时到店享受服务',
    'in_service': '您的服务已开始，请耐心等待',
    'completed': '您的服务已完成，期待您的评价',
    'cancelled': '您的预约已取消，如有疑问请联系客服'
  }
  
  const message = notificationMessages[newStatus]
  if (message) {
    // 模拟发送通知
    console.log(`发送通知给用户: ${message}`)
  }
}

// 辅助函数：触发改期通知
async function triggerRescheduleNotification(booking, originalBooking) {
  console.log(`预约改期通知: 预约${booking.id} 从 ${originalBooking.date} ${originalBooking.time} 改期到 ${booking.bookingDate} ${booking.bookingTime}`)
  
  // 发送改期确认通知
  const message = `您的预约已成功改期至 ${booking.bookingDate} ${booking.bookingTime}，请按时到店`
  console.log(`发送改期通知: ${message}`)
}
