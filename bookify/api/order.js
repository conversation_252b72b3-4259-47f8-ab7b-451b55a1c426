import request from './request'

// 获取订单列表
export function getOrderList(params) {
  return request.get('/order/list', { params })
}

export function getOrderDetail(id) {
  return request.get(`/order/${id}`)
}

export function getOrderStats() {
  return request.get('/order/stats')
}

// 计算订单金额
export function calculateOrder(data) {
  return request.post('/order/calculate', data)
}

// 创建订单
export function createOrder(data) {
  return request.post('/order/create', data)
}

// 支付订单
export function payOrder(data) {
  return request.post('/order/pay', data)
}

// 取消订单
export function cancelOrder(id) {
  return request.post(`/order/${id}/cancel`)
}

// 确认收货
export function confirmReceive(id) {
  return request.post(`/order/${id}/confirm`)
}

// 删除订单
export function deleteOrder(id) {
  return request.delete(`/order/${id}`)
} 