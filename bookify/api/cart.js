import request from './request'

// 添加到购物车
export function addToCart(data) {
  return request.post('/cart/add', data)
}

// 获取购物车数量
export function getCartCount() {
  return request.get('/cart/count')
}

// 获取购物车列表
export function getCartList(params) {
  return request.get('/cart/list', { params })
}

// 更新购物车商品数量
export function updateCartQuantity(data) {
  return request.put('/cart/quantity', data)
}

// 更新购物车商品选中状态
export function updateCartSelected(data) {
  const { ids, selected } = data
  return request.put('/cart/selected', {
    ids,
    selected
  })
}

// 删除购物车商品
export function deleteCart(data) {
  return request.post('/cart/delete', data)
}

// 清空购物车
export function clearCart() {
  return request.post('/cart/clear')
}

// 获取已选中的商品
export function getCartSelected() {
  return request.get('/cart/selected')
} 