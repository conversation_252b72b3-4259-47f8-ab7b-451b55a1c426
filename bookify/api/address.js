import request from './request'

// 获取默认收货地址
export function getDefaultAddress() {
  return request.get('/address/default')
}

// 获取收货地址列表
export function getAddressList() {
  return request.get('/address/list')
}

// 获取地址详情
export function getAddressDetail(id) {
  return request.get(`/address/detail/${id}`)
}

// 新增地址
export function addAddress(data) {
  return request.post('/address/add', data)
}

// 修改地址
export function updateAddress(data) {
  return request.put('/address/update', data)
}

// 删除地址
export function deleteAddress(id) {
  return request.post(`/address/delete`, { id })
}

// 设置默认地址
export function setDefaultAddress(id) {
  return request.post(`/address/default/${id}`)
} 
