import config from '../config'
import request from './request'

// 登录接口
export function login(data) {
  return request.post('/user/login', data)
}

// 获取用户信息
export function getUserInfo() {
  return request.get('/user/info')
}

// 获取用户数据统计
export function getUserStats() {
  return request.get('/user/stats')
}

// 获取订单统计
export function getOrderStats() {
  return request.get('/user/order/stats')
}

// 更新用户信息
export function updateUserInfo(data) {
  return request.post('/user/update', data)
}

// 修改密码
export function updatePassword(data) {
  return request.put('/user/password', data)
}

// 退出登录
export function logout() {
  return request.post('/user/logout')
}

// 获取收藏列表
export function getCollectList(params) {
  return request.get('/user/collect/list', { params })
}

// 获取足迹列表
export function getFootprintList(params) {
  return request.get('/user/footprint/list', { params })
}

// 删除收藏
export function deleteCollect(id) {
  return request.post('/user/collect/delete', { id })
}

// 删除足迹
export function deleteFootprint(id) {
  return request.post('/user/footprint/delete', { id })
}

// 清空足迹
export function clearFootprint() {
  return request.post('/user/footprint/clear')
}

// 上传头像
export function uploadAvatar(filePath) {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: config.apiUrl + '/user/avatar',
      filePath: filePath,
      name: 'file',
      header: {
        'Authorization': 'Bearer ' + uni.getStorageSync('token')
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data)
          if (data.code === 200) {
            resolve(data)
          } else {
            reject(new Error(data.msg || '上传失败'))
          }
        } catch (e) {
          reject(new Error('解析响应数据失败'))
        }
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
} 

// 微信手机号登录
export function wechatPhoneLogin(data) {
  return request.post('/user/login/wechat-phone', data)
} 

// 注册接口
export function register(data) {
  return request.post('/user/register', data)
}

// 发送短信验证码
export function sendSmsCode(data) {
  return request.post('/user/sms/send', data)
}

// 验证短信验证码
export function verifySmsCode(data) {
  return request.post('/user/sms/verify', data)
} 