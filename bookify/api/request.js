// 配置接口地址
import config from '@/config'
import <PERSON>rror<PERSON><PERSON><PERSON> from '@/utils/errorHandler'

const BASE_URL = config.apiUrl

// 请求封装
const request = {
	get(url, options = {}) {
		// 处理 query 参数
		if (options.params) {
			const queryString = Object.keys(options.params)
				.filter(key => options.params[key] !== null && options.params[key] !== undefined)
				.map(key => `${key}=${encodeURIComponent(options.params[key])}`)
				.join('&')
			if (queryString) {
				url = `${url}?${queryString}`
			}
		}

		return this._request({
			url,
			method: 'GET',
			...options
		})
	},

	post(url, data, options = {}) {
		return this._request({
			url,
			method: 'POST',
			data,
			...options
		})
	},

	put(url, data, options = {}) {
		return this._request({
			url,
			method: 'PUT',
			data,
			...options
		})
	},

	delete(url, options = {}) {
		return this._request({
			url,
			method: 'DELETE',
			...options
		})
	},

	_request(options = {}) {
		// 拼接完整请求地址
		const url = BASE_URL + options.url
		
		// 请求头
		const header = {
			'content-type': 'application/json',
			...options.header
		}
		
		// 获取本地存储的token
		const token = ErrorHandler.safeStorage(() => {
			return uni.getStorageSync('token')
		}, '获取token') || null
		
		console.log('Request token check:', { 
			hasToken: !!token, 
			tokenLength: token ? token.length : 0,
			tokenPrefix: token ? token.substring(0, 10) + '...' : 'null'
		})
		
		if (token) {
			header.Authorization = `Bearer ${token}`
		}
		
		// 打印请求信息
		console.log('Request:', {
			url,
			method: options.method,
			data: options.data,
			header
		})
		
		// 返回 Promise
		return new Promise((resolve, reject) => {
			uni.request({
				url,
				method: options.method || 'GET',
				data: options.data,
				header,
				success: (res) => {
					const { statusCode, data } = res
					
					// 打印响应信息
					console.log('Response:', {
						statusCode,
						data
					})
					
					// 请求成功
					if (statusCode >= 200 && statusCode < 300) {
						if(data.code === 200) {
							resolve(data)
						}// 401：未登录
						else if (data.code === 401) {
							// 清除本地存储的用户相关数据
							ErrorHandler.safeStorage(() => {
								uni.removeStorageSync('token')
								uni.removeStorageSync('userInfo')
							}, '清除用户数据')
							
							// 使用 reLaunch 重新启动到登录页
							ErrorHandler.safeNavigation(() => {
								uni.reLaunch({
									url: '/pages/login/login'
								})
							}, '跳转登录页')
							reject(new Error('请先登录'))
						} else {
							ErrorHandler.safeToast({
								title: data.message || '请求失败',
								icon: 'none'
							}, data.message || '请求失败')
							reject(new Error(data.message || '请求失败'))
						}
					} 
					
					// 其他错误
					else {
						ErrorHandler.safeToast({
							title: data.message || '请求失败',
							icon: 'none'
						}, data.message || '请求失败')
						reject(new Error(data.message || '请求失败'))
					}
				},
				fail: (err) => {
					// 打印错误信息
					console.error('Request Error:', err)
					
					ErrorHandler.safeToast({
						title: '网络错误',
						icon: 'none'
					}, '网络错误')
					reject(new Error('网络错误'))
				}
			})
		})
	}
}

export default request 