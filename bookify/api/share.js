import request from '@/utils/request'

// 获取分销配置
export function getShareConfig() {
  return request({
    url: '/share/config',
    method: 'get'
  })
}

// 获取分销员信息
export function getDistributorInfo() {
  return request({
    url: '/share/info',
    method: 'get'
  })
}

// 申请成为分销员
export function applyDistributor() {
  return request({
    url: '/share/apply',
    method: 'post'
  })
}

// 检查分销资格
export function checkQualification() {
  return request({
    url: '/share/check-qualification',
    method: 'get'
  })
}

// 获取团队统计
export function getTeamStats() {
  return request({
    url: '/share/team-stats',
    method: 'get'
  })
}

// 获取团队成员列表
export function getTeamMembers(params) {
  return request({
    url: '/share/team',
    method: 'get',
    params
  })
}

// 获取分销订单列表
export function getDistributionOrders(params) {
  return request({
    url: '/share/orders',
    method: 'get',
    params
  })
}

// 获取分销订单统计
export function getDistributionOrderStats() {
  return request({
    url: '/share/order-stats',
    method: 'get'
  })
}

// 提现申请
export function withdraw(data) {
  return request({
    url: '/share/withdraw',
    method: 'post',
    data
  })
}

// 获取提现记录
export function getWithdrawList(params) {
  return request({
    url: '/share/withdraw/list',
    method: 'get',
    params
  })
} 