import request from './request'

/**
 * 获取用户的生效弹窗列表
 * @param {number} userId - 用户ID
 * @returns {Promise}
 */
export function getActivePopups(userId) {
  return request.get('/api/popup/active', { params: { userId: userId } })
}

/**
 * 记录弹窗操作日志
 * @param {Object} data - 日志数据
 * @param {number} data.popupId - 弹窗ID
 * @param {number} data.userId - 用户ID
 * @param {string} data.action - 操作类型：show/click/close
 * @param {string} data.buttonText - 点击的按钮文字（可选）
 * @returns {Promise}
 */
export function recordPopupLog(data) {
  return request.post('/popup/log', data)
}

/**
 * 获取弹窗统计信息
 * @returns {Promise}
 */
export function getPopupStats() {
  return request.get('/popup/stats')
}

// 弹窗API
export const popupApi = {
  // 获取生效的弹窗列表
  getActivePopups(params = {}) {
    return request.get('/popup/active', { params })
  },

  // 记录弹窗操作日志
  recordLog(data) {
    return request.post('/popup/log', data)
  },

  // 获取弹窗统计信息
  getStats(params = {}) {
    return request.get('/popup/stats', { params })
  }
}

export default popupApi 