import request from './request'

// 获取配置列表
export function getConfigs(data) {
	return request.post('/config/list', data)
}

// 获取单个配置
export function getConfig(moduleKey, configKey) {
	return request.get('/config/get', {
		moduleKey,
		configKey
	})
}

// 获取商城配置
export function getShopConfigs(keys = []) {
	return request.get('/config/shop', {
		keys
	})
}

// 清除配置缓存
export function clearConfigCache(moduleKey = '') {
	return request.post('/config/cache/clear', {}, {
		moduleKey
	})
} 