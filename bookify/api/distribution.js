import request from './request'

// 获取分销配置
export function getConfig() {
  return request.get('/share/config')
}

// 获取分销员信息
export function getDistributorInfo() {
  return request.get('/share/info')
}

// 申请成为分销员
export function applyDistributor(data) {
  return request.post('/share/apply', data)
}

// 检查分销资格
export function checkQualification() {
  return request.get('/share/check-qualification')
}

// 获取分销订单列表
export function getDistributionOrders(params) {
  return request.get('/share/orders', { params })
}

// 获取分销订单统计
export function getDistributionOrderStats() {
  return request.get('/share/order-stats')
}

// 获取订单统计（新接口名称）
export function getOrderStats() {
  return request.get('/share/order-stats')
}

// 获取团队成员列表
export function getTeamMembers(params) {
  return request.get('/share/team', { params })
}

// 获取团队列表（新接口名称）
export function getTeamList(params) {
  return request.get('/share/team', { params })
}

// 获取团队统计数据
export function getTeamStats() {
  return request.get('/share/team-stats')
}

// 提交提现申请
export function submitWithdraw(data) {
  return request.post('/share/withdraw', data)
}

// 获取账户信息
export function getAccountInfo() {
  return request.get('/share/account')
}

// 获取余额信息
export function getBalanceInfo() {
  return request.get('/share/balance')
}

// 获取提现配置
export function getWithdrawConfig() {
  return request.get('/share/withdraw/config')
}

// 获取提现列表
export function getWithdrawList(params) {
  return request.get('/share/withdraw/list', { params })
}

// 获取提现统计
export function getWithdrawStats() {
  return request.get('/share/withdraw/stats')
} 