import request from './request'

// 收藏相关API

// 收藏/取消收藏
export function collectItem(data) {
  return request.post('/collect/action', data)
}

// 获取收藏状态
export function getCollectStatus(params) {
  return request.get('/collect/status', { params })
}

// 点赞相关API

// 点赞/取消点赞
export function likeItem(data) {
  return request.post('/collect/like', data)
}

// 获取点赞状态
export function getLikeStatus(params) {
  return request.get('/collect/like/status', { params })
} 