import request from './request'

// 获取商品列表
export function getGoodsList(params) {
  return request.get('/goods/list', { params })
}

// 获取商品详情
export function getGoodsDetail(params) {
  return request.get('/goods/detail', { params })
}

// 获取商品评价列表
export function getGoodsComments(params) {
  return request.get('/goods/comments', { params })
}

// 收藏/取消收藏商品
export function collectGoods(data) {
  const params = `goodsId=${data.goodsId}&type=${data.type}`
  return request.post('/goods/collect', params, {
    header: {
      'content-type': 'application/x-www-form-urlencoded'
    }
  })
}

// 点赞/取消点赞商品
export function likeGoods(data) {
  const params = `goodsId=${data.goodsId}&type=${data.type}`
  return request.post('/goods/like', params, {
    header: {
      'content-type': 'application/x-www-form-urlencoded'
    }
  })
}

// 记录商品足迹
export function addFootprint(goodsId) {
  const params = `goodsId=${goodsId}`
  return request.post('/goods/footprint', params, {
    header: {
      'content-type': 'application/x-www-form-urlencoded'
    }
  })
}

// 获取购物车数量
export function getCartCount() {
  return request.get('/cart/count')
}

// 加入购物车
export function addToCart(data) {
  return request.post('/cart/add', data)
} 