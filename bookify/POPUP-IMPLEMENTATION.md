# 弹窗功能实现总结

## 实现概述

弹窗功能已完全基于后端接口实现，前端不再包含硬编码的测试数据。所有弹窗配置都通过后端API动态获取。

## 核心特性

### 1. 时间控制
- **必须设置开始时间**: 只有设置了`start_time`的弹窗才会从接口返回
- **结束时间可选**: `end_time`可以为空，表示永不过期
- **实时过滤**: 后端会根据当前时间自动过滤已过期的弹窗

### 2. 触发机制
- **手动触发** (`manual`): 管理员手动配置的弹窗
- **自动触发** (`auto`): 根据用户类型、页面路径等条件自动触发
- **定时触发** (`schedule`): 按照设定的时间规则触发

### 3. 智能过滤
- **用户类型过滤**: 支持新用户/老用户区分
- **页面路径过滤**: 可指定特定页面显示
- **频次控制**: 支持最大显示次数限制
- **显示间隔**: 支持两次显示的时间间隔控制

## 文件结构

### 后端文件
```
server/
├── app/model/do/ShopDB/popup.go          # 数据模型
├── app/model/dto/popup_dto.go            # 数据传输对象
├── app/service/popup/popup.go            # 业务逻辑
├── app/controller/popup/popup.go         # 控制器
├── app/route/popup.go                    # 路由配置
├── migrations/20250113_create_popup_tables.sql  # 数据库迁移
└── update_popup_data.sql                 # 数据更新脚本
```

### 前端文件
```
web/
├── stores/popup.js                       # Pinia状态管理
├── api/popup.js                          # API接口
├── components/popup/BasePopup.vue        # 弹窗组件
├── components/layout/PageLayout.vue      # 布局组件(集成弹窗)
├── pages/index/index.vue                 # 首页(调用接口)
└── pages/popup-demo/index.vue            # 功能演示页面
```

## API接口

### 1. 获取生效弹窗
```
GET /popup/active?userId=1&page=/pages/index/index&userType=new
```

### 2. 记录操作日志
```
POST /popup/log
{
  "popupId": 1,
  "userId": 1,
  "action": "show",
  "buttonText": ""
}
```

### 3. 获取统计信息
```
GET /popup/stats
```

## 数据库表结构

### popup 表
- 存储弹窗配置信息
- 支持JSON字段存储复杂配置
- 包含时间控制、优先级、统计等字段

### popup_user_log 表
- 记录用户操作行为
- 支持显示、点击、关闭等操作类型
- 用于频次控制和统计分析

## 使用方式

### 首页集成
```javascript
// 页面加载时自动调用
async showWelcomePopup() {
  const popupStore = usePopupStore()
  const userId = this.userInfo.id || 0
  
  await popupStore.loadPopupsFromServer({
    userId: userId,
    page: '/pages/index/index',
    userType: userId > 0 ? 'old' : 'new'
  })
}
```

### 手动添加弹窗
```javascript
// 仅在演示页面使用，实际应用中应通过后端配置
popupStore.addPopup({
  id: 'custom',
  title: '自定义弹窗',
  content: '这是一个自定义弹窗',
  // ... 其他配置
})
```

## 配置管理

弹窗配置完全通过数据库管理，支持：
- 动态开启/关闭
- 时间范围控制
- 用户群体定向
- 显示频次限制
- 优先级排序
- 实时统计分析

## 注意事项

1. **时间设置必须**: 没有设置`start_time`的弹窗不会显示
2. **前端无测试数据**: 首页完全依赖后端接口，不包含硬编码数据
3. **演示页面独立**: `/pages/popup-demo/index.vue`用于功能测试，包含硬编码示例
4. **日志自动记录**: 所有弹窗操作都会自动记录到数据库
5. **统计实时更新**: 显示次数和点击次数实时更新 