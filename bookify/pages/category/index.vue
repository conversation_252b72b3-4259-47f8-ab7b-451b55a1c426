<template>
	<view class="category-container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="circle circle-1"></view>
			<view class="circle circle-2"></view>
			<view class="circle circle-3"></view>
		</view>
		
		<!-- 搜索栏 -->
		<view class="search-box">
			<view class="search-bar" @click="toSearch">
				<text class="iconfont icon-sousuo">🔍</text>
				<text class="placeholder">搜索非遗产品</text>
			</view>
		</view>
		
		<!-- 分类内容 -->
		<view class="category-content">
			<!-- 顶部主分类菜单 -->
			<scroll-view 
				scroll-x 
				class="main-category" 
				:scroll-into-view="'category_' + currentCategory"
				:scroll-with-animation="true"
			>
				<view class="menu-list">
					<view 
						class="menu-item"
						v-for="(item, index) in categoryList"
						:key="item.id"
						:id="'category_' + index"
						:class="{ active: currentCategory === index }"
						@click="switchCategory(index)"
					>
						<text>{{ item.name }}</text>
					</view>
				</view>
			</scroll-view>
			
			<!-- 商品区域 -->
			<scroll-view 
				scroll-y 
				class="content-scroll" 
				@scrolltolower="loadMore"
				:refresher-enabled="true"
				:refresher-triggered="refreshing"
				@refresherrefresh="onRefresh"
			>
				<!-- 二级分类导航 -->
				<scroll-view 
					scroll-x 
					class="sub-category" 
					v-if="currentCategory !== null && categoryList[currentCategory]"
				>
					<view class="sub-list">
						<view 
							class="sub-item"
							:class="{ active: currentSubCategory === -1 }"
							@click="switchSubCategory(-1)"
						>
							<text>全部</text>
						</view>
						<view 
							class="sub-item"
							v-for="(sub, index) in categoryList[currentCategory].children"
							:key="sub.id"
							:class="{ active: currentSubCategory === index }"
							@click="switchSubCategory(index)"
						>
							<text>{{ sub.name }}</text>
						</view>
					</view>
				</scroll-view>

				<view class="goods-list" v-if="goodsList.length">
					<view 
						class="goods-item"
						v-for="goods in goodsList"
						:key="goods.id"
						@click="toGoodsDetail(goods)"
					>
						<image :src="goods.image" mode="aspectFill"></image>
						<view class="info">
							<text class="name">{{ goods.name }}</text>
							<text class="desc">{{ goods.description }}</text>
							<view class="price-box">
								<text class="price">{{ goods.price }}</text>
								<text class="original-price" v-if="goods.original_price > goods.price">¥{{ goods.original_price }}</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 空状态 -->
				<view class="empty" v-else-if="!loading">
					<image src="/static/images/empty.png" mode="aspectFit"></image>
					<text>暂无商品</text>
				</view>
				
				<!-- 加载状态 -->
				<view class="loading" v-if="loading">
					<uni-load-more status="loading"></uni-load-more>
				</view>
				
				<!-- 加载完成 -->
				<uni-load-more v-if="!loading && hasMore" status="more"></uni-load-more>
				<uni-load-more v-if="!loading && !hasMore && goodsList.length" status="noMore"></uni-load-more>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import request from '@/api/request.js'

export default {
	data() {
		return {
			categoryList: [], // 分类列表
			goodsList: [], // 商品列表
			currentCategory: 0, // 当前选中的分类索引
			currentSubCategory: -1, // 当前选中的二级分类索引，-1表示全部
			page: 1, // 当前页码
			pageSize: 6, // 每页数量
			loading: false, // 加载状态
			hasMore: true, // 是否有更多数据
			refreshing: false, // 刷新状态
			total: 0, // 商品总数
		}
	},
	onLoad() {
		// 从全局状态获取选中的分类ID
		const app = getApp()
		const selectedCategoryId = app.globalData?.selectedCategoryId
		// 获取分类数据
		this.getCategoryTree(selectedCategoryId)
		// 清除全局状态
		if (app.globalData) {
			app.globalData.selectedCategoryId = null
		}
	},
	onShow() {
		// 每次显示页面时，检查是否有新的选中分类
		const app = getApp()
		const selectedCategoryId = app.globalData?.selectedCategoryId
		if (selectedCategoryId) {
			// 找到对应的索引
			const index = this.categoryList.findIndex(item => item.id === selectedCategoryId)
			if (index !== -1 && index !== this.currentCategory) {
				this.switchCategory(index)
			}
			// 清除全局状态
			app.globalData.selectedCategoryId = null
		}
	},
	methods: {
		// 获取分类树
		async getCategoryTree(selectedCategoryId) {
			try {
				const res = await request.get('/category/tree')
				this.categoryList = res.data
				
				// 如果有选中的分类ID，找到对应的索引
				if(selectedCategoryId && this.categoryList.length > 0) {
					const index = this.categoryList.findIndex(item => item.id === selectedCategoryId)
					if (index !== -1) {
						this.currentCategory = index
					}
				}
				
				// 获取商品列表
				if(this.categoryList.length > 0) {
					this.getGoodsList()
				}
			} catch(e) {
				uni.showToast({
					title: '获取分类失败',
					icon: 'none'
				})
			}
		},
		
		// 获取分类商品
		async getGoodsList(isLoadMore = false) {
			if(this.loading) return
			
			try {
				this.loading = true
				const category = this.categoryList[this.currentCategory]
				if(!category) return
				
				// 获取当前选中的二级分类
				const subCategory = this.currentSubCategory === -1 ? null : category.children?.[this.currentSubCategory]
				
				// 构建请求参数
				const params = {
					categoryId: category.id,
					page: this.page,
					pageSize: this.pageSize
				}
				
				// 只有选择了具体子分类时才添加subCategoryId参数
				if (subCategory) {
					params.subCategoryId = subCategory.id
				}
				
				const res = await request.get('/category/goods', { params })
				
				const { list, total } = res.data
				this.total = total // 保存总数
				
				if(isLoadMore) {
					this.goodsList = [...this.goodsList, ...list]
				} else {
					this.goodsList = list
				}
				
				this.hasMore = this.goodsList.length < total
			} catch(e) {
				uni.showToast({
					title: '获取商品失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
				if(this.refreshing) {
					this.refreshing = false
				}
			}
		},
		
		// 切换分类
		async switchCategory(index) {
			if(this.currentCategory === index) return
			this.currentCategory = index
			this.currentSubCategory = -1 // 重置为全部
			this.page = 1
			this.hasMore = true
			this.goodsList = []
			await this.getGoodsList()
		},
		
		// 切换二级分类
		async switchSubCategory(index) {
			if(this.currentSubCategory === index) return
			this.currentSubCategory = index
			this.page = 1
			this.hasMore = true
			this.goodsList = []
			await this.getGoodsList()
		},
		
		// 加载更多
		async loadMore() {
			// 如果正在加载、没有更多数据、或者正在刷新，则不加载
			if(this.loading || !this.hasMore || this.refreshing) return
			
			// 如果商品列表为空，也不加载
			if(this.goodsList.length === 0) return
			
			// 计算当前已加载的商品数量
			const loadedCount = this.page * this.pageSize
			
			// 如果已加载数量小于总数，才进行加载
			if(loadedCount < this.total) {
				this.page++
				await this.getGoodsList(true)
			} else {
				this.hasMore = false
			}
		},
		
		// 下拉刷新
		async onRefresh() {
			this.refreshing = true
			this.page = 1
			this.hasMore = true
			await this.getGoodsList()
		},
		
		// 跳转搜索页
		toSearch() {
			uni.navigateTo({
				url: '/pages/search/index'
			})
		},
		
		// 跳转商品详情
		toGoodsDetail(goods) {
			uni.navigateTo({
				url: `/pages/goods/detail?id=${goods.id}`
			})
		}
	}
}
</script>

<style lang="scss">
.category-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	position: relative;
	overflow: hidden;
}

// 背景装饰
.bg-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1;
	
	.circle {
		position: absolute;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.1);
		
		&.circle-1 {
			width: 300rpx;
			height: 300rpx;
			top: -150rpx;
			right: -150rpx;
			animation: float 6s ease-in-out infinite;
		}
		
		&.circle-2 {
			width: 200rpx;
			height: 200rpx;
			bottom: 200rpx;
			left: -100rpx;
			animation: float 8s ease-in-out infinite reverse;
		}
		
		&.circle-3 {
			width: 150rpx;
			height: 150rpx;
			top: 300rpx;
			left: 50rpx;
			animation: float 10s ease-in-out infinite;
		}
	}
}

@keyframes float {
	0%, 100% { transform: translateY(0px); }
	50% { transform: translateY(-20px); }
}

.search-box {
	position: relative;
	z-index: 2;
	padding: 40rpx 30rpx 20rpx;
	
	.search-bar {
		height: 80rpx;
		background: rgba(255, 255, 255, 0.9);
		backdrop-filter: blur(20px);
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
		border: 1rpx solid rgba(255, 255, 255, 0.2);
		transition: all 0.3s ease;
		
		&:active {
			transform: scale(0.98);
		}
		
		.icon-sousuo {
			margin-right: 16rpx;
			font-size: 32rpx;
			color: #667eea;
		}
		
		.placeholder {
			font-size: 28rpx;
			color: #666;
		}
	}
}

.category-content {
	position: relative;
	z-index: 2;
	height: calc(100vh - 140rpx);
	
	.main-category {
		height: 88rpx;
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(20px);
		white-space: nowrap;
		border-radius: 24rpx 24rpx 0 0;
		margin: 0 20rpx;
		box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
		border: 1rpx solid rgba(255, 255, 255, 0.2);
		
		.menu-list {
			display: inline-flex;
			padding: 0 20rpx;
			height: 100%;
			
			.menu-item {
				padding: 0 30rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 30rpx;
				color: #333;
				position: relative;
				height: 100%;
				flex-shrink: 0;
				border-radius: 20rpx;
				margin: 12rpx 8rpx;
				transition: all 0.3s ease;
				
				&:active {
					transform: scale(0.95);
				}
				
				&.active {
					color: #fff;
					background: linear-gradient(135deg, #667eea, #764ba2);
					font-weight: 600;
					box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.4);
				}
			}
		}
	}
	
	.content-scroll {
		height: calc(100vh - 228rpx);
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(20px);
		margin: 0 20rpx;
		border-radius: 0 0 24rpx 24rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
		border: 1rpx solid rgba(255, 255, 255, 0.2);
		border-top: none;
		
		.sub-category {
			height: 80rpx;
			white-space: nowrap;
			background: rgba(255, 255, 255, 0.5);
			backdrop-filter: blur(10px);
			border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
			
			.sub-list {
				display: inline-flex;
				padding: 0 20rpx;
				height: 100%;
				
				.sub-item {
					padding: 0 24rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 28rpx;
					color: #666;
					position: relative;
					height: 100%;
					min-width: 112rpx;
					border-radius: 16rpx;
					margin: 12rpx 6rpx;
					transition: all 0.3s ease;
					
					&:active {
						transform: scale(0.95);
					}
					
					&.active {
						color: #fff;
						background: linear-gradient(135deg, #667eea, #764ba2);
						font-weight: 600;
						box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
					}
				}
			}
		}
		
		.goods-list {
			padding: 30rpx;
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			
			.goods-item {
				width: 48%;
				background: rgba(255, 255, 255, 0.9);
				backdrop-filter: blur(10px);
				border-radius: 20rpx;
				overflow: hidden;
				margin-bottom: 20rpx;
				box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
				border: 1rpx solid rgba(255, 255, 255, 0.2);
				transition: all 0.3s ease;
				
				&:active {
					transform: scale(0.98);
					box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
				}
				
				image {
					width: 100%;
					height: 320rpx;
				}
				
				.info {
					padding: 20rpx;
					
					.name {
						font-size: 26rpx;
						color: #333;
						font-weight: 600;
						line-height: 36rpx;
						max-height: 72rpx;
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
						white-space: normal;
						word-break: break-all;
						margin-bottom: 8rpx;
					}
					
					.desc {
						font-size: 24rpx;
						color: #999;
						line-height: 32rpx;
						height: 32rpx;
						margin-bottom: 12rpx;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
					
					.price-box {
						display: flex;
						align-items: center;
						
						.price {
							font-size: 32rpx;
							color: #ff6b6b;
							font-weight: 700;
							
							&::before {
								content: '¥';
								font-size: 24rpx;
								margin-right: 2rpx;
							}
						}
						
						.original-price {
							font-size: 24rpx;
							color: #999;
							margin-left: 12rpx;
							text-decoration: line-through;
						}
					}
				}
			}
		}
		
		.empty {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			min-height: calc(100vh - 400rpx);
			padding: 60rpx 40rpx;
			text-align: center;
			
			image {
				width: 240rpx;
				height: 240rpx;
				margin-bottom: 30rpx;
				opacity: 0.6;
			}
			
			text {
				font-size: 28rpx;
				color: #999;
				line-height: 1.5;
			}
		}
		
		.loading {
			padding: 30rpx 0;
		}
	}
}
</style> 