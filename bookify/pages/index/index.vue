<template>
	<PageLayout :theme="currentTheme" :showNavBar="false" :content-style="{paddingTop: '0px'}">
		<!-- 主要内容区域 -->
		<view class="main-content">
			

			<!-- 轮播图区域 -->
			<view class="banner-section">
				<swiper 
					class="banner-swiper" 
					:indicator-dots="true" 
					:autoplay="true" 
					:interval="3000" 
					:duration="500"
					indicator-color="rgba(255, 255, 255, 0.5)"
					indicator-active-color="#fff"
					@change="onBannerChange"
				>
					<swiper-item v-for="(banner, index) in bannerList" :key="index" @click="onBannerClick(banner)">
						<view class="banner-item">
							<image 
								:src="banner.image || '/static/images/category/smart.png'" 
								mode="aspectFill"
								class="banner-image"
								@error="onImageError"
							></image>
							<view class="banner-overlay">
								<view class="banner-content">
									<text class="banner-title">{{banner.title || '精选好物'}}</text>
									<text class="banner-desc">{{banner.description || '1000万+品质用户共同选择'}}</text>
								</view>
							</view>
						</view>
					</swiper-item>
					<!-- 默认轮播项，当没有数据时显示 -->
				
				</swiper>
			</view>
<!-- 堂食外卖入口 -->
<view class="dining-section">
				<view class="dining-types">
					<view 
						class="dining-item" 
						v-for="item in diningTypes" 
						:key="item.id"
						@click="onDiningClick(item)"
					>
						<view class="dining-icon">
							<text class="dining-emoji">{{item.code === 'dine_in' ? '🍽️' : '🥡'}}</text>
						</view>
						<text class="dining-name">{{item.name}}</text>
						<text class="dining-desc">{{item.description}}</text>
					</view>
				</view>
			</view>
			
		
			<!-- 商品推荐 -->
			<view class="goods-recommend">
				<view class="recommend-header">
					<text class="recommend-title">为你推荐</text>
					<view class="recommend-tabs">
						<text 
							class="tab-item" 
							:class="{active: currentTab === tab.key}"
							v-for="tab in recommendTabs" 
							:key="tab.key"
							@click="switchRecommendTab(tab.key)"
						>
							{{tab.name}}
						</text>
					</view>
				</view>

				<!-- 商品网格 -->
				<view class="goods-grid">
					<view class="goods-card" v-for="item in goodsList" :key="item.id" @click="goToDetail(item)">
						<view class="goods-image">
							<image :src="item.image" mode="aspectFill"></image>
							<view class="goods-badge" v-if="item.isHot">
								<text>推荐</text>
							</view>
							<view class="like-btn" @click.stop="toggleGoodsLike(item)">
								<text class="like-icon" :class="{liked: item.isLiked}">♥</text>
								<text class="like-count">{{item.likes || 0}}</text>
							</view>
						
						</view>
						<view class="goods-info">
							<text class="goods-title">{{item.name}}</text>
							<view class="goods-meta">
								<view class="price-row">
									<text class="goods-price">{{item.price}}</text>
									<text class="original-price" v-if="item.originalPrice && item.originalPrice > item.price">{{item.originalPrice}}</text>
								</view>
								<view class="sales-row">
									<text class="goods-sales">{{item.sales || 0}}人购买</text>
								
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
		
		</view>
	</PageLayout>
</template>

<script>
import { getBannerList, getCategoryList, getActivityList, getTagList } from '@/api/home'
import { getGoodsList } from '@/api/goods'
import { getDiningTypes } from '@/api/dining'
import { getCartCount } from '@/api/cart'
import AuthUtils from '@/utils/auth'
import PageLayout from '@/components/layout/PageLayout.vue'
import { usePopupStore } from '@/stores/popup'
import themeMixin from '@/mixins/themeMixin.js'
import collectMixin from '@/mixins/collectMixin.js'
import shareMixin from '@/mixins/shareMixin.js'

export default {
	components: {
		PageLayout
	},
	
	mixins: [themeMixin, collectMixin, shareMixin],
	
	data() {
		return {
			bannerList: [],
			goodsList: [],
			diningTypes: [],
			page: 1,
			pageSize: 10,
			hotTags: [
				{ id: 0, name: '全部' }
			],
			currentTag: 0,
			hotGoodsList: [],
			cartCount: 0,
			sortType: 'default',
			currentTab: 'hot',
			currentBannerIndex: 0,
			recommendTabs: [
				{ key: 'hot', name: '热门精选' },
				{ key: 'new', name: '新品上市' },
				{ key: 'sale', name: '特价优惠' }
			],
			userInfo: {},
			// 分享配置
			shareTitle: '发现好物，一起分享',
			sharePath: '/pages/index/index',
			// 弹窗管理
			popupStore: null
		}
	},

	async onLoad() {
		// 初始化弹窗Store
		this.popupStore = usePopupStore()
		
		this.loadUserInfo()
		// 先设置默认堂食外卖数据，确保用户能看到按钮
		this.setDefaultDiningTypes()
		// 分模块加载首页数据
		this.loadBanners()
		this.loadDiningTypes()
		this.loadGoods()
		this.loadCartCount()
		
		// 显示欢迎弹窗
		await this.showWelcomePopup()
	},

	onShow() {
		this.loadCartCount()
		this.loadUserInfo()
	},

	methods: {
		// === 弹窗相关方法 ===
		
		// 显示欢迎弹窗 - 从后端加载
		async showWelcomePopup() {
			const popupStore = usePopupStore()
			
			// 获取用户ID
			const userId = this.userInfo.id || 0
			
			// 从后端加载弹窗
			await popupStore.loadPopupsFromServer({
				userId: userId,
				page: '/pages/index/index', // 当前页面路径
				userType: userId > 0 ? 'old' : 'new' // 用户类型
			})
		},
		
		// 轮播图切换事件
		onBannerChange(e) {
			this.currentBannerIndex = e.detail.current
		},

		// 轮播图点击事件
		onBannerClick(banner) {
			console.log('点击轮播图:', banner)
			console.log('banner.targetId:', banner.targetId)
			console.log('banner.url:', banner.url)
			console.log('banner.type:', banner.type)
			
			// 根据类型进行跳转
			if (banner.type === 1) {
				// 商品详情 - 使用targetId或从url中提取
				let goodsId = banner.targetId
				if (!goodsId && banner.url) {
					goodsId = this.extractIdFromUrl(banner.url)
				}
				
				// 如果还是没有goodsId，尝试使用一个默认的商品ID进行测试
				if (!goodsId) {
					console.log('没有找到商品ID，使用默认商品ID 13 进行测试')
					goodsId = 13
				}
				
				if (goodsId) {
					console.log('跳转到商品详情:', goodsId)
					uni.navigateTo({
						url: `/pages/goods/detail?id=${goodsId}`
					})
				} else {
					uni.showToast({
						title: '商品信息不完整',
						icon: 'none'
					})
				}
			} else if (banner.type === 2) {
				// 活动页面 - 使用targetId或从url中提取
				let activityId = banner.targetId
				if (!activityId && banner.url) {
					activityId = this.extractIdFromUrl(banner.url)
				}
				if (activityId) {
					console.log('跳转到活动页面:', activityId)
					// 这里可以跳转到活动详情页面，暂时显示提示
					uni.showToast({
						title: `跳转到活动${activityId}`,
						icon: 'none'
					})
				} else {
					uni.showToast({
						title: '活动信息不完整',
						icon: 'none'
					})
				}
			} else if (banner.type === 3) {
				// 外部链接
				if (banner.url) {
					console.log('跳转外部链接:', banner.url)
					uni.showToast({
						title: '跳转外部链接',
						icon: 'none'
					})
				} else {
					uni.showToast({
						title: '链接地址不完整',
						icon: 'none'
					})
				}
			} else {
				// 默认处理或type为0的情况
				console.log('默认处理轮播图点击')
				uni.showToast({
					title: banner.title || '点击了轮播图',
					icon: 'none'
				})
			}
		},

		// 从URL中提取ID
		extractIdFromUrl(url) {
			const match = url.match(/id=(\d+)/)
			return match ? match[1] : null
		},

		// 加载用户信息
		loadUserInfo() {
			const userInfo = uni.getStorageSync('userInfo')
			if (userInfo) {
				this.userInfo = userInfo
			}
		},

		// 加载轮播图数据
		async loadBanners() {
			try {
				const res = await getBannerList({ position: 1 })
				console.log('轮播图数据响应:', res)
				if (res.code === 200 && res.data && res.data.length > 0) {
					this.bannerList = res.data
					} else {
						this.setDefaultBanners()
					}
			} catch (error) {
				console.error('加载轮播图失败:', error)
				this.setDefaultBanners()
			}
		},

		// 加载用餐类型数据
		async loadDiningTypes() {
			try {
				const res = await getDiningTypes()
				console.log('用餐类型数据响应:', res)
				if (res.code === 200 && res.data && res.data.length > 0) {
					this.diningTypes = res.data
				} else {
					this.setDefaultDiningTypes()
				}
			} catch (error) {
				console.error('加载用餐类型失败:', error)
				this.setDefaultDiningTypes()
			}
		},

		// 设置默认轮播图
		setDefaultBanners() {
			this.bannerList = [
				{
					id: 1,
					title: '精选好物',
					image: '/static/images/category/smart.png',
					url: '',
					type: 0,
					description: '1000万+品质用户共同选择'
				},
				{
					id: 2,
					title: '热门推荐',
					image: '/static/images/category/mobile.png',
					url: '',
					type: 0,
					description: '发现更多优质商品'
				}
			]
		},

		// 设置默认堂食外卖类型
		setDefaultDiningTypes() {
			this.diningTypes = [
				{
					id: 1,
					name: '堂食',
					code: 'dine_in',
					icon: '/static/images/dining/dine-in.png',
					description: '店内用餐，享受舒适环境',
					sort: 1
				},
				{
					id: 2,
					name: '外卖',
					code: 'takeout',
					icon: '/static/images/dining/takeout.png',
					description: '外卖配送，方便快捷',
					sort: 2
				}
			]
			console.log('设置默认堂食外卖类型:', this.diningTypes)
		},

		// 堂食外卖点击事件
		onDiningClick(item) {
			if (item.code === 'dine_in') {
				// 堂食 - 跳转到商品列表页面，带上堂食标识
				// 由于商品列表是tabbar页面，需要使用switchTab，但switchTab不支持参数
				// 所以先存储参数到本地存储，然后跳转
				uni.setStorageSync('diningType', 'dine_in')
				uni.setStorageSync('diningTitle', item.name)
				uni.switchTab({
					url: '/pages/goods/list'
				})
			} else if (item.code === 'takeout') {
				// 外卖 - 跳转到商品列表页面，带上外卖标识
				uni.setStorageSync('diningType', 'takeout')
				uni.setStorageSync('diningTitle', item.name)
				uni.switchTab({
					url: '/pages/goods/list'
				})
			} else {
				// 其他类型
				uni.showToast({
					title: `点击了${item.name}`,
					icon: 'none'
				})
			}
		},

		// 切换喜欢状态
		async toggleGoodsLike(item) {
			const result = await this.toggleLike(item.id, 'goods', item.isLiked)
			item.isLiked = result.isLiked
			item.likes = result.count
		},

		// 切换收藏状态
		async toggleGoodsCollect(item) {
			const newStatus = await this.toggleCollect(item.id, 'goods', item.isCollected)
			item.isCollected = newStatus
		},

		// 加载购物车数量
		async loadCartCount() {
			if (AuthUtils.checkLoginStatus()) {
				try {
					const res = await getCartCount()
					this.cartCount = res.data || 0
				} catch (error) {
					console.log('获取购物车数量失败:', error)
					this.cartCount = 0
				}
			}
		},

		// 搜索点击事件
		handleSearchClick() {
			uni.navigateTo({
				url: '/pages/search/search'
			})
		},

		// 购物车点击事件
		handleCartClick() {
			if (!AuthUtils.checkLoginStatus()) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				})
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/login/login'
					})
				}, 1500)
				return
			}
			
			uni.switchTab({
				url: '/pages/cart/cart'
			})
		},

		// 加载商品数据
		async loadGoods() {
			try {
				const res = await getGoodsList({
					page: 1,
					pageSize: 10,
					sort: 'sales'  // 首页显示热销商品
				})
				console.log('商品数据响应:', res)
				if (res.code === 200 && res.data) {
					const goods = res.data.list || []
					// 添加一些模拟数据
					const processedGoods = goods.map(item => ({
						...item,
						isHot: Math.random() > 0.7,
						isLiked: item.isLiked || false, // 使用后端返回的点赞状态
						isCollected: item.isCollected || false, // 使用后端返回的收藏状态
						originalPrice: item.originalPrice || (parseFloat(item.price) * (1.2 + Math.random() * 0.5)).toFixed(2),
						rating: (4.0 + Math.random() * 1.0).toFixed(1)
					}))
					
					// 直接使用处理后的商品数据，不再调用批量状态查询
					this.goodsList = processedGoods
					
					// 商品加载完成后的处理
					console.log('商品加载完成')
				}
			} catch (error) {
				console.error('加载商品失败:', error)
			}
		},

		// 切换推荐标签时重新加载商品
		async switchRecommendTab(key) {
			this.currentTab = key
			try {
				const res = await getGoodsList({
					page: 1,
					pageSize: 10,
					sort: key === 'hot' ? 'sales' : key === 'new' ? 'time' : 'price'
				})
				if (res.code === 200 && res.data) {
					const goods = res.data.list || []
					const processedGoods = goods.map(item => ({
						...item,
						isHot: Math.random() > 0.7,
						isLiked: item.isLiked || false, // 使用后端返回的点赞状态
						isCollected: item.isCollected || false, // 使用后端返回的收藏状态
						originalPrice: item.originalPrice || (parseFloat(item.price) * (1.2 + Math.random() * 0.5)).toFixed(2),
						rating: (4.0 + Math.random() * 1.0).toFixed(1)
					}))
					
					// 直接使用处理后的商品数据，不再调用批量状态查询
					this.goodsList = processedGoods
				}
			} catch (error) {
				console.error('切换标签加载商品失败:', error)
			}
		},

		// 跳转商品详情
		goToDetail(item) {
			uni.navigateTo({
				url: `/pages/goods/detail?id=${item.id}`
			})
		},

		// 切换标签
		async switchTag(id) {
			this.currentTag = id
			await this.loadHotGoods()
		},
		
		// 加载标签
		async loadTags() {
			try {
				const res = await getTagList({
					status: 1
				})
				if (res.code === 200 && res.data) {
					this.hotTags = [
						{ id: 0, name: '全部' },
						...res.data
					]
				}
			} catch (error) {
				console.error('加载标签失败:', error)
			}
		},

		// 加载热门商品
		async loadHotGoods() {
			try {
				const res = await getGoodsList({
					page: 1,
					pageSize: 8,
					tagId: this.currentTag,
					sort: 'sales'
				})
				if (res.code === 200 && res.data) {
					this.hotGoodsList = res.data.list || []
				}
			} catch (error) {
				console.error('加载热门商品失败:', error)
			}
		},

		// 图片加载错误事件
		onImageError(e) {
			console.error('轮播图加载失败:', e)
			// 可以在这里设置默认图片或者其他处理逻辑
		}
	}
}
</script>

<style lang="scss" scoped>
.main-content {
	background: linear-gradient(135deg, #ff6b35 0%, #ff4757 50%, #ffa726 100%);
	min-height: 100vh;
	position: relative;
	z-index: 1;
	
	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: 
			radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.2) 0%, transparent 50%),
			radial-gradient(circle at 80% 20%, rgba(255, 71, 87, 0.2) 0%, transparent 50%);
		pointer-events: none;
		z-index: -1;
	}
	
	&::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.85) 100%);
		pointer-events: none;
		z-index: -1;
	}
}

// 堂食外卖入口样式
.dining-section {
	margin: 20rpx;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	border-radius: 24rpx;
	padding: 30rpx;
	box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	position: relative;
	z-index: 2;
}

.dining-types {
	display: flex;
	justify-content: space-around;
	align-items: center;
}

.dining-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	border-radius: 16rpx;
	transition: all 0.3s ease;
	min-width: 200rpx;
	
	&:active {
		background: rgba(255, 107, 53, 0.1);
		transform: scale(0.95);
	}
}

.dining-icon {
	width: 80rpx;
	height: 80rpx;
	margin-bottom: 16rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #ff6b35, #ff8e53);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.3);
	
	.dining-emoji {
		font-size: 40rpx;
		line-height: 1;
	}
	
	image {
		width: 48rpx;
		height: 48rpx;
		filter: brightness(0) invert(1);
	}
}

.dining-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
}

.dining-desc {
	font-size: 24rpx;
	color: #666;
	text-align: center;
	line-height: 1.4;
}

// 轮播图区域
.banner-section {
	position: relative;
	z-index: 2;
	margin-bottom: -40rpx;
	
	.banner-swiper {
		height: 480rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.2);
		position: relative;
		
		&::after {
			content: '';
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			height: 80rpx;
			background: linear-gradient(transparent, rgba(255, 107, 53, 0.1));
			pointer-events: none;
			z-index: 10;
		}
		
		:deep(.uni-swiper-wrapper) {
			border-radius: 0 0 32rpx 32rpx;
		}
		
		:deep(.uni-swiper-dots) {
			bottom: 30rpx;
			z-index: 20;
		}
		
		:deep(.uni-swiper-dot) {
			width: 20rpx;
			height: 20rpx;
			border-radius: 10rpx;
			margin: 0 10rpx;
			transition: all 0.3s ease;
			background: rgba(255, 255, 255, 0.5);
			border: 2rpx solid rgba(255, 255, 255, 0.8);
		}
		
		:deep(.uni-swiper-dot-active) {
			width: 40rpx;
			border-radius: 10rpx;
			background: rgba(255, 255, 255, 0.9);
		}
		
		.banner-item {
			position: relative;
			height: 100%;
			overflow: hidden;
			
			.banner-image {
				width: 100%;
				height: 100%;
				object-fit: cover;
				transition: transform 0.4s ease;
			}
			
			.banner-overlay {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(
					180deg, 
					rgba(255, 107, 53, 0.1) 0%, 
					rgba(255, 107, 53, 0.2) 30%,
					rgba(255, 107, 53, 0.4) 70%, 
					rgba(255, 107, 53, 0.7) 100%
				);
				display: flex;
				align-items: flex-end;
				padding: 50rpx 40rpx 60rpx;
				
				.banner-content {
					color: #fff;
					max-width: 80%;
					
					.banner-title {
						display: block;
						font-size: 42rpx;
						font-weight: 900;
						margin-bottom: 16rpx;
						text-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.6);
						line-height: 1.2;
						letter-spacing: 1rpx;
					}
					
					.banner-desc {
						display: block;
						font-size: 30rpx;
						font-weight: 600;
						opacity: 0.95;
						text-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.5);
						line-height: 1.4;
					}
				}
			}
		}
		
		.default-banner {
			background: linear-gradient(135deg, #ff6b35 0%, #ff4757 50%, #ffa726 100%);
			padding: 80rpx 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			overflow: hidden;
			
			&::before {
				content: '';
				position: absolute;
				top: -50%;
				left: -50%;
				width: 200%;
				height: 200%;
				background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent, rgba(255, 255, 255, 0.05), transparent);
				animation: rotate 30s linear infinite;
				pointer-events: none;
				z-index: -1;
			}
			
			.default-banner-content {
				text-align: center;
				position: relative;
				z-index: 3;
				color: #fff;
				
				.default-title {
					display: block;
					font-size: 48rpx;
					margin-bottom: 24rpx;
					font-weight: 900;
					text-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
					letter-spacing: 2rpx;
				}
				
				.default-desc {
					display: block;
					font-size: 32rpx;
					font-weight: 600;
					margin-bottom: 50rpx;
					opacity: 0.9;
					text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
				}
				
				.default-stats {
					.stat-number {
						display: block;
						font-size: 88rpx;
						font-weight: 900;
						line-height: 1;
						margin-bottom: 20rpx;
						text-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.4);
					}
					
					.stat-label {
						display: block;
						font-size: 28rpx;
						font-weight: 600;
						opacity: 0.9;
					}
				}
			}
		}
	}
}

// 商品推荐 - 橙色系设计
.goods-recommend {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
	backdrop-filter: blur(20rpx);
	margin: 0 20rpx 30rpx;
	border-radius: 28rpx;
	padding: 50rpx 40rpx;
	border: 1rpx solid rgba(255, 107, 53, 0.2);
	box-shadow: 
		0 12rpx 40rpx rgba(255, 107, 53, 0.15),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
	position: relative;
	z-index: 2;
	
	.recommend-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 50rpx;
		padding-bottom: 30rpx;
		border-bottom: 2rpx solid;
		border-image: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.3), rgba(255, 167, 38, 0.3), transparent) 1;
		
		.recommend-title {
			font-size: 40rpx;
			font-weight: 900;
			background: linear-gradient(135deg, #ff6b35, #ff4757, #ffa726);
			background-clip: text;
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			position: relative;
			
			&::after {
				content: '';
				position: absolute;
				bottom: -12rpx;
				left: 0;
				width: 60rpx;
				height: 4rpx;
				background: linear-gradient(90deg, #ff6b35, #ffa726);
				border-radius: 2rpx;
			}
		}
		
		.recommend-tabs {
			display: flex;
			background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.7));
			border-radius: 30rpx;
			padding: 8rpx;
			border: 1rpx solid rgba(255, 107, 53, 0.3);
			box-shadow: inset 0 2rpx 4rpx rgba(255, 107, 53, 0.1);
			
			.tab-item {
				padding: 16rpx 24rpx;
				font-size: 26rpx;
				color: #666;
				border-radius: 22rpx;
				transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
				font-weight: 600;
				
				&.active {
					background: linear-gradient(135deg, #ff6b35, #ff4757);
					color: #fff;
					font-weight: 800;
					box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.4);
					transform: translateY(-2rpx);
				}
			}
		}
	}
	
	.goods-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20rpx;
		
		.goods-card {
			background: #fff;
			border-radius: 16rpx;
			overflow: hidden;
			box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.1);
			transition: all 0.3s ease;
			position: relative;
			border: 1rpx solid rgba(255, 107, 53, 0.1);
		
			&:active {
				transform: translateY(-4rpx);
				box-shadow: 0 8rpx 20rpx rgba(255, 107, 53, 0.15);
			}
			
			.goods-image {
				position: relative;
				height: 280rpx;
				overflow: hidden;
				z-index: 2;
		
				image {
					width: 100%;
					height: 100%;
					object-fit: cover;
					transition: transform 0.3s ease;
				}
				
				.goods-badge {
					position: absolute;
					top: 16rpx;
					left: 16rpx;
					background: linear-gradient(135deg, #ff6b35, #ff4757);
					color: #fff;
					font-size: 20rpx;
					font-weight: 700;
					padding: 8rpx 16rpx;
					border-radius: 12rpx;
					box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
					z-index: 3;
					
					text {
						line-height: 1;
					}
				}
				
				.like-btn {
					position: absolute;
					top: 16rpx;
					right: 16rpx;
					background: rgba(255, 255, 255, 0.9);
					backdrop-filter: blur(10rpx);
					border-radius: 20rpx;
					padding: 8rpx 12rpx;
					display: flex;
					align-items: center;
					box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.1);
					z-index: 3;
					
					.like-icon {
						font-size: 24rpx;
						color: #ccc;
						margin-right: 6rpx;
						transition: all 0.3s ease;
						
						&.liked {
							color: #ff6b35;
							transform: scale(1.2);
						}
					}
					
					.like-count {
						font-size: 20rpx;
						color: #666;
						font-weight: 600;
					}
				}
				
				.collect-btn {
					position: absolute;
					top: 66rpx;
					right: 16rpx;
					background: rgba(255, 255, 255, 0.9);
					backdrop-filter: blur(10rpx);
					border-radius: 50%;
					width: 60rpx;
					height: 60rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.1);
					z-index: 3;
					transition: all 0.3s ease;
					
					&:active {
						transform: scale(0.9);
					}
					
					.collect-icon {
						font-size: 28rpx;
						color: #ccc;
						transition: all 0.3s ease;
						
						&.collected {
							color: #ffa726;
							text-shadow: 0 0 10rpx rgba(255, 167, 38, 0.5);
							transform: scale(1.2);
						}
					}
				}
			}
			
			.goods-info {
				padding: 24rpx 20rpx;
				background: #fff;
				position: relative;
				z-index: 2;
				
				.goods-title {
					display: block;
					font-size: 28rpx;
					font-weight: 600;
					color: #333;
					margin-bottom: 16rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					line-height: 1.4;
				}
				
				.goods-meta {
					display: flex;
					flex-direction: column;
					gap: 12rpx;
					
					.price-row {
						display: flex;
						align-items: baseline;
						justify-content: space-between;
						
						.goods-price {
							font-size: 32rpx;
							font-weight: 900;
							color: #ff6b35;
							
							&::before {
								content: '¥';
								font-size: 24rpx;
								font-weight: 700;
							}
						}
						
						.original-price {
							font-size: 24rpx;
							color: #999;
							text-decoration: line-through;
							
							&::before {
								content: '¥';
								font-size: 20rpx;
							}
						}
					}
					
					.sales-row {
						display: flex;
						align-items: center;
						justify-content: space-between;
						
						.goods-sales {
							font-size: 22rpx;
							color: #999;
							font-weight: 500;
						}
						
						.goods-rating {
							display: flex;
							align-items: center;
							gap: 8rpx;
							
							.rating-stars {
								font-size: 20rpx;
								color: #ffa726;
							}
							
							.rating-text {
								font-size: 20rpx;
								color: #999;
							}
						}
					}
				}
			}
		}
	}
}

// 会员推广卡片
.vip-promotion {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
	backdrop-filter: blur(20rpx);
	margin: 0 20rpx 30rpx;
	border-radius: 28rpx;
	padding: 50rpx 40rpx;
	border: 1rpx solid rgba(255, 107, 53, 0.2);
	box-shadow: 
		0 12rpx 40rpx rgba(255, 107, 53, 0.15),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
	position: relative;
	z-index: 2;
	
	.vip-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 50rpx;
		
		.vip-text {
			flex: 1;
			
			.vip-title {
				display: block;
				font-size: 40rpx;
				font-weight: 900;
				color: #333;
				margin-bottom: 16rpx;
			}
			
			.vip-desc {
				display: block;
				font-size: 28rpx;
				color: #666;
				font-weight: 600;
			}
		}
		
		.vip-card {
			background: linear-gradient(135deg, #ff6b35, #ff4757);
			color: #fff;
			padding: 12rpx 24rpx;
			border-radius: 20rpx;
			font-size: 32rpx;
			font-weight: 700;
		}
	}
	
	.vip-btn {
		background: linear-gradient(135deg, #ff6b35, #ff4757);
		color: #fff;
		padding: 16rpx 24rpx;
		border-radius: 20rpx;
		font-size: 28rpx;
		font-weight: 700;
		border: none;
		cursor: pointer;
	}
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}
</style>
