<template>
  <view class="payment-page">
    <!-- 统一导航栏 -->
    <beauty-navbar 
      :title="pageTitle" 
      :show-back="true"
    />
    
    <!-- 订单信息 -->
    <view class="order-info-section" style="margin-top: 88px;">
      <view class="order-header">
        <text class="order-title">{{ orderInfo.title }}</text>
        <text class="order-subtitle" v-if="orderInfo.subtitle">{{ orderInfo.subtitle }}</text>
      </view>
      
      <!-- 动态内容区域 -->
      <view class="order-details">
        <!-- 美容预约信息 -->
        <template v-if="paymentType === 'booking'">
          <view class="detail-item">
            <text class="label">服务项目</text>
            <text class="value">{{ orderInfo.service_name }}</text>
          </view>
          <view class="detail-item" v-if="orderInfo.technician_name">
            <text class="label">技师</text>
            <text class="value">{{ orderInfo.technician_name }}</text>
          </view>
          <view class="detail-item">
            <text class="label">预约时间</text>
            <text class="value">{{ formatBookingTime(orderInfo.booking_date, orderInfo.start_time) }}</text>
          </view>
        </template>
        
        <!-- 套餐卡信息 -->
        <template v-else-if="paymentType === 'card'">
          <view class="detail-item">
            <text class="label">套餐卡</text>
            <text class="value">{{ orderInfo.card_name }}</text>
          </view>
          <view class="detail-item">
            <text class="label">购买数量</text>
            <text class="value">{{ orderInfo.quantity }}张</text>
          </view>
        </template>
        
        <!-- 商品订单信息 -->
        <template v-else-if="paymentType === 'order'">
          <view class="detail-item">
            <text class="label">商品</text>
            <text class="value">{{ orderInfo.goods_name }}</text>
          </view>
          <view class="detail-item">
            <text class="label">数量</text>
            <text class="value">{{ orderInfo.quantity }}件</text>
          </view>
        </template>
      </view>
    </view>

    <!-- 价格信息 -->
    <view class="price-section">
      <view class="price-item">
        <text class="price-label">支付金额</text>
        <text class="price-value">¥{{ orderInfo.final_price || orderInfo.amount }}</text>
      </view>
    </view>

    <!-- 支付方式选择 -->
    <view class="payment-methods-section">
      <view class="section-title">支付方式</view>
      <view class="payment-methods">
        <view 
          class="payment-method" 
          :class="{ active: selectedPayType === 1 }"
          @click="selectPayType(1)"
        >
          <view class="method-info">
            <image src="/static/images/wechat-pay.png" class="method-icon" />
            <text class="method-name">微信支付</text>
          </view>
          <text class="check-icon" v-if="selectedPayType === 1">✓</text>
        </view>
        
        <view 
          class="payment-method" 
          :class="{ active: selectedPayType === 2 }"
          @click="selectPayType(2)"
        >
          <view class="method-info">
            <image src="/static/images/alipay.png" class="method-icon" />
            <text class="method-name">支付宝支付</text>
          </view>
          <text class="check-icon" v-if="selectedPayType === 2">✓</text>
        </view>
        
        <view 
          class="payment-method" 
          :class="{ active: selectedPayType === 3 }"
          @click="selectPayType(3)"
        >
          <view class="method-info">
            <image src="/static/images/balance.png" class="method-icon" />
            <text class="method-name">余额支付</text>
            <text class="balance-info" v-if="userInfo.balance !== undefined">
              (余额：¥{{ userInfo.balance }})
            </text>
          </view>
          <text class="check-icon" v-if="selectedPayType === 3">✓</text>
        </view>
      </view>
    </view>

    <!-- 支付按钮 -->
    <view class="payment-footer">
      <button 
        class="pay-btn"
        :class="{ disabled: !canPay, loading: paying }"
        @click="pay"
        :disabled="!canPay"
      >
        {{ getPayButtonText() }}
      </button>
    </view>
  </view>
</template>

<script>
import BeautyNavbar from '@/components/beauty-navbar/beauty-navbar.vue'
import { getUserInfo } from '@/api/user'
import { payOrder, getOrderDetail } from '@/api/order'
import { payBeautyBooking, getBookingDetail } from '@/api/beauty/index.js'
import { payCard } from '@/api/beauty/card.js'

export default {
  name: 'UnifiedPayment',
  components: {
    BeautyNavbar
  },
  
  data() {
    return {
      // 支付类型：booking(预约), card(套餐卡), order(商品订单)
      paymentType: '',
      orderId: '',
      orderInfo: {},
      userInfo: {},
      selectedPayType: 1, // 1:微信 2:支付宝 3:余额
      paying: false,
      
      // 页面配置
      pageConfig: {
        booking: {
          title: '预约支付',
          api: {
            detail: getBookingDetail,
            pay: payBeautyBooking
          },
          successUrl: '/pages/beauty/user/booking-history'
        },
        card: {
          title: '套餐卡支付',
          api: {
            detail: null, // 套餐卡支付不需要单独获取详情
            pay: payCard
          },
          successUrl: '/pages/beauty/card/my'
        },
        order: {
          title: '订单支付',
          api: {
            detail: null, // 使用现有订单详情API
            pay: payOrder
          },
          successUrl: '/pages/order/list'
        }
      }
    }
  },

  computed: {
    pageTitle() {
      return this.pageConfig[this.paymentType]?.title || '支付'
    },
    
    canPay() {
      if (this.paying) return false
      if (this.orderInfo.payment_status === 'paid') return false
      return true
    }
  },

  onLoad(options) {
    // 解析参数
    this.paymentType = options.type || 'order'
    this.orderId = options.orderId || options.bookingId || options.cardId

    if (options.payType) {
      this.selectedPayType = parseInt(options.payType)
    }

    // 如果是套餐卡支付，从参数中获取订单信息
    if (this.paymentType === 'card' && options.cardInfo) {
      try {
        this.orderInfo = JSON.parse(decodeURIComponent(options.cardInfo))
      } catch (e) {
        console.error('解析套餐卡信息失败:', e)
      }
    }

    this.loadOrderInfo()
    this.loadUserInfo()
  },

  methods: {
    // 加载订单信息
    async loadOrderInfo() {
      try {
        const config = this.pageConfig[this.paymentType]
        if (!config) throw new Error('不支持的支付类型')

        // 如果是套餐卡且已有订单信息，跳过加载
        if (this.paymentType === 'card' && this.orderInfo.card_name) {
          return
        }

        let response
        if (this.paymentType === 'booking') {
          response = await config.api.detail(parseInt(this.orderId))
        } else if (this.paymentType === 'order') {
          response = await getOrderDetail(this.orderId)
        }
        // 套餐卡支付不需要额外加载详情

        if (response && response.code === 200) {
          this.orderInfo = response.data
        }
      } catch (error) {
        console.error('加载订单信息失败:', error)
        uni.showToast({
          title: '加载订单信息失败',
          icon: 'none'
        })
      }
    },

    // 加载用户信息
    async loadUserInfo() {
      try {
        const response = await getUserInfo()
        if (response.code === 200) {
          this.userInfo = response.data
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },

    // 选择支付方式
    selectPayType(type) {
      // 余额支付时检查余额
      if (type === 3 && this.userInfo.balance < (this.orderInfo.final_price || this.orderInfo.amount)) {
        uni.showModal({
          title: '余额不足',
          content: `当前余额：¥${this.userInfo.balance}\n支付金额：¥${this.orderInfo.final_price || this.orderInfo.amount}`,
          showCancel: false
        })
        return
      }
      this.selectedPayType = type
    },

    // 发起支付
    async pay() {
      if (!this.canPay) return
      
      this.paying = true
      try {
        const config = this.pageConfig[this.paymentType]
        const payParams = this.buildPayParams()
        
        const response = await config.api.pay(payParams)
        
        if (response.code === 200) {
          await this.handlePaymentResponse(response.data)
        } else {
          throw new Error(response.message || '支付失败')
        }
      } catch (error) {
        console.error('支付失败:', error)
        uni.showToast({
          title: error.message || '支付失败',
          icon: 'none'
        })
      } finally {
        this.paying = false
      }
    },

    // 构建支付参数
    buildPayParams() {
      const baseParams = {
        pay_type: this.selectedPayType
      }
      
      if (this.paymentType === 'booking') {
        return { ...baseParams, booking_id: parseInt(this.orderId) }
      } else if (this.paymentType === 'card') {
        return { ...baseParams, user_card_id: this.orderId }
      } else {
        return { ...baseParams, orderId: parseInt(this.orderId) }
      }
    },

    // 处理支付响应
    async handlePaymentResponse(payData) {
      if (this.selectedPayType === 1) {
        // 微信支付
        await this.handleWechatPayment(payData)
      } else if (this.selectedPayType === 2) {
        // 支付宝支付
        await this.handleAlipayPayment(payData)
      } else if (this.selectedPayType === 3) {
        // 余额支付
        this.paySuccess()
      }
    },

    // 微信支付处理
    async handleWechatPayment(payData) {
      const wechatData = payData.data || payData
      
      uni.requestPayment({
        provider: 'wxpay',
        appId: wechatData.appId,
        timeStamp: wechatData.timeStamp,
        nonceStr: wechatData.nonceStr,
        package: wechatData.package,
        signType: wechatData.signType,
        paySign: wechatData.paySign,
        success: () => this.paySuccess(),
        fail: (err) => {
          console.error('微信支付失败:', err)
          uni.showToast({ title: '支付失败', icon: 'none' })
        }
      })
    },

    // 支付宝支付处理
    async handleAlipayPayment(payData) {
      uni.requestPayment({
        provider: 'alipay',
        orderInfo: payData.orderInfo,
        success: () => this.paySuccess(),
        fail: (err) => {
          console.error('支付宝支付失败:', err)
          uni.showToast({ title: '支付失败', icon: 'none' })
        }
      })
    },

    // 支付成功
    paySuccess() {
      uni.showToast({
        title: '支付成功',
        icon: 'success'
      })
      
      setTimeout(() => {
        const successUrl = this.pageConfig[this.paymentType].successUrl
        uni.redirectTo({ url: successUrl })
      }, 1500)
    },

    // 获取支付按钮文本
    getPayButtonText() {
      if (this.paying) return '支付中...'
      if (this.orderInfo.payment_status === 'paid') return '已支付'
      return `立即支付 ¥${this.orderInfo.final_price || this.orderInfo.amount}`
    },

    // 格式化预约时间
    formatBookingTime(date, time) {
      if (!date || !time) return ''
      const dateObj = new Date(date)
      const month = dateObj.getMonth() + 1
      const day = dateObj.getDate()
      return `${month}月${day}日 ${time}`
    }
  }
}
</script>

<style lang="scss" scoped>
.payment-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.order-info-section {
  background: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 20px;
}

.order-header {
  .order-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    display: block;
    margin-bottom: 5px;
  }
  
  .order-subtitle {
    font-size: 14px;
    color: #666;
    display: block;
    margin-bottom: 15px;
  }
}

.order-details {
  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .label {
      font-size: 14px;
      color: #666;
    }
    
    .value {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }
  }
}

.price-section {
  background: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 20px;
  
  .price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .price-label {
      font-size: 16px;
      color: #333;
    }
    
    .price-value {
      font-size: 20px;
      font-weight: 600;
      color: #ff6b6b;
    }
  }
}

.payment-methods-section {
  background: #fff;
  margin: 15px;
  border-radius: 12px;
  padding: 20px;
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
  }
  
  .payment-method {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    &.active {
      .method-name {
        color: #FFB6C1;
      }
      .check-icon {
        color: #FFB6C1;
      }
    }
    
    .method-info {
      display: flex;
      align-items: center;
      
      .method-icon {
        width: 24px;
        height: 24px;
        margin-right: 12px;
      }
      
      .method-name {
        font-size: 16px;
        color: #333;
        margin-right: 8px;
      }
      
      .balance-info {
        font-size: 12px;
        color: #666;
      }
    }
    
    .check-icon {
      font-size: 18px;
      font-weight: 600;
    }
  }
}

.payment-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 15px;
  border-top: 1px solid #f0f0f0;
  
  .pay-btn {
    width: 100%;
    height: 50px;
    background: linear-gradient(135deg, #FFB6C1, #FF91A4);
    color: #fff;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    
    &.disabled {
      background: #ccc;
      color: #999;
    }
    
    &.loading {
      background: #ddd;
    }
  }
}
</style>
