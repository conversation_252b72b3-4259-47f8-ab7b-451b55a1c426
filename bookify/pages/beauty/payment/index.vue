<template>
  <view class="beauty-payment-page">
    <!-- 统一导航栏 -->
    <beauty-navbar 
      title="服务支付" 
      :show-back="true"
    ></beauty-navbar>

    <!-- 支付内容 -->
    <view class="payment-content" style="margin-top: 88px;">
      <!-- 服务信息 -->
      <view class="service-info-card">
        <view class="card-header">
          <text class="card-title">服务信息</text>
        </view>
        <view class="service-details">
          <view class="service-item">
            <text class="label">服务项目</text>
            <text class="value">{{ bookingInfo.service_name }}</text>
          </view>
          <view class="service-item">
            <text class="label">技师</text>
            <text class="value">{{ bookingInfo.technician_name || '无需技师' }}</text>
          </view>
          <view class="service-item">
            <text class="label">预约时间</text>
            <text class="value">{{ formatBookingTime(bookingInfo.booking_date, bookingInfo.start_time) }}</text>
          </view>
          <view class="service-item">
            <text class="label">服务时长</text>
            <text class="value">{{ bookingInfo.duration }}分钟</text>
          </view>
        </view>
      </view>

      <!-- 支付金额 -->
      <view class="amount-card">
        <view class="amount-header">
          <text class="amount-label">支付金额</text>
          <text class="amount-value">¥{{ bookingInfo.final_price }}</text>
        </view>
        <view class="amount-breakdown">
          <view class="breakdown-item">
            <text class="item-label">服务费用</text>
            <text class="item-value">¥{{ bookingInfo.service_price }}</text>
          </view>
          <view v-if="bookingInfo.technician_fee > 0" class="breakdown-item">
            <text class="item-label">技师费用</text>
            <text class="item-value">¥{{ bookingInfo.technician_fee }}</text>
          </view>
          <view v-if="bookingInfo.discount_amount > 0" class="breakdown-item discount">
            <text class="item-label">优惠金额</text>
            <text class="item-value">-¥{{ bookingInfo.discount_amount }}</text>
          </view>
        </view>
      </view>

      <!-- 支付方式 -->
      <view class="payment-methods-card">
        <view class="card-header">
          <text class="card-title">支付方式</text>
        </view>
        <view class="payment-methods">
          <view class="payment-method active">
            <view class="method-info">
              <text class="method-name">微信支付</text>
            </view>
            <text class="check-icon">✓</text>
          </view>
        </view>
      </view>

      <!-- 支付按钮 -->
      <view class="payment-button">
        <button 
          class="pay-btn"
          :class="{ disabled: !canPay, loading: paying }"
          @click="pay"
          :disabled="!canPay"
        >
          {{ getPayButtonText() }}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { getBookingDetail, payBeautyBooking } from '@/api/beauty/index.js'
import { getUserInfo } from '@/api/user'
import BeautyNavbar from '@/components/beauty-navbar/beauty-navbar.vue'

export default {
  name: 'BeautyPayment',
  components: {
    BeautyNavbar
  },
  data() {
    return {
      bookingId: '',
      bookingInfo: {
        service_name: '',
        technician_name: '',
        booking_date: '',
        start_time: '',
        duration: 0,
        service_price: 0,
        technician_fee: 0,
        discount_amount: 0,
        final_price: 0
      },
      userInfo: {},
      selectedPayment: 'wechat',
      paying: false
    }
  },

  computed: {
    canPay() {
      if (this.paying) return false
      if (this.bookingInfo.payment_status === 'paid') return false
      return true
    }
  },

  onLoad(options) {
    if (options.bookingId) {
      this.bookingId = options.bookingId
      this.loadBookingInfo()
      this.loadUserInfo()
    }
  },

  onShow() {
    // 页面显示时检查支付状态
    if (this.bookingId) {
      this.checkPaymentStatus()
    }
  },

  methods: {
    // 检查支付状态
    async checkPaymentStatus() {
      try {
        const response = await getBookingDetail(parseInt(this.bookingId))
        if (response.code === 200 && response.data.payment_status === 'paid') {
          // 如果已经支付，直接跳转到成功页面
          this.paySuccess()
        }
      } catch (error) {
        console.error('检查支付状态失败:', error)
      }
    },

    // 加载预约信息
    async loadBookingInfo() {
      try {
        const response = await getBookingDetail(parseInt(this.bookingId))
        if (response.code === 200) {
          this.bookingInfo = response.data
          // 如果已经支付，禁用支付按钮
          if (response.data.payment_status === 'paid') {
            this.paying = true // 禁用支付按钮
          }
        } else {
          throw new Error(response.message || '获取预约信息失败')
        }
      } catch (error) {
        console.error('加载预约信息失败:', error)
        uni.showToast({
          title: '加载预约信息失败',
          icon: 'none'
        })
      }
    },

    // 加载用户信息
    async loadUserInfo() {
      try {
        const response = await getUserInfo()
        if (response.code === 200) {
          this.userInfo = response.data
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },



    // 发起支付
    async pay() {
      if (!this.canPay) return

      this.paying = true
      try {
        const response = await payBeautyBooking({
          booking_id: parseInt(this.bookingId),
          pay_type: 1 // 微信支付
        })

        console.log('支付接口响应:', response)

        if (response.code === 200) {
          // 微信支付
          this.handleWechatPayment(response.data)
        } else if (response.code === 400 && response.message === '预约已支付') {
          // 预约已经支付，直接跳转到成功页面
          this.paySuccess()
        } else {
          throw new Error(response.message || '支付失败')
        }
      } catch (error) {
        console.error('支付失败:', error)
        uni.showToast({
          title: error.message || '支付失败',
          icon: 'none'
        })
      } finally {
        this.paying = false
      }
    },

    // 获取支付按钮文本
    getPayButtonText() {
      if (this.paying) {
        return '支付中...'
      }
      if (this.bookingInfo.payment_status === 'paid') {
        return '已支付'
      }
      return `立即支付 ¥${this.bookingInfo.final_price}`
    },

    // 处理微信支付
    handleWechatPayment(payData) {
      console.log('支付数据:', payData)
      
      // 后端返回的数据结构是 {success: true, data: {...}, message: "支付成功"}
      // 实际的支付参数在 payData.data 中
      const wechatPayData = payData.data || payData
      
      console.log('微信支付数据:', wechatPayData)
      
      // 检查支付数据是否包含必要的参数
      if (!wechatPayData.appId || !wechatPayData.timeStamp || !wechatPayData.nonceStr || !wechatPayData.package || !wechatPayData.signType || !wechatPayData.paySign) {
        console.error('微信支付参数不完整:', wechatPayData)
        uni.showToast({
          title: '支付参数错误',
          icon: 'none'
        })
        return
      }
      
      console.log('调用微信支付，参数:', {
        provider: 'wxpay',
        appId: wechatPayData.appId,
        timeStamp: wechatPayData.timeStamp,
        nonceStr: wechatPayData.nonceStr,
        package: wechatPayData.package,
        signType: wechatPayData.signType,
        paySign: wechatPayData.paySign
      })
      
      uni.requestPayment({
        provider: 'wxpay',
        appId: wechatPayData.appId,
        timeStamp: wechatPayData.timeStamp,
        nonceStr: wechatPayData.nonceStr,
        package: wechatPayData.package,
        signType: wechatPayData.signType,
        paySign: wechatPayData.paySign,
        success: () => {
          console.log('微信支付成功')
          this.paySuccess()
        },
        fail: (err) => {
          console.error('微信支付失败:', err)
          uni.showToast({
            title: '支付失败',
            icon: 'none'
          })
        }
      })
    },

    // 支付成功
    paySuccess() {
      uni.showToast({
        title: '支付成功',
        icon: 'success'
      })
      
      setTimeout(() => {
        // 返回到预约详情页面，刷新数据
        const pages = getCurrentPages()
        if (pages.length > 1) {
          // 返回到上一页（预约详情页）
          uni.navigateBack()
        } else {
          // 如果没有上一页，跳转到预约历史
          uni.redirectTo({
            url: '/pages/beauty/user/booking-history'
          })
        }
      }, 1500)
    },

    // 格式化预约时间
    formatBookingTime(date, time) {
      if (!date || !time) return ''
      const dateObj = new Date(date)
      const month = dateObj.getMonth() + 1
      const day = dateObj.getDate()
      return `${month}月${day}日 ${time}`
    },


  }
}
</script>

<style lang="scss" scoped>
.beauty-payment-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}



.payment-content {
  padding: 15px;
}

.service-info-card,
.amount-card,
.payment-methods-card {
  background: #fff;
  border-radius: 12px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.service-details {
  padding: 15px;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  font-size: 14px;
  color: #666;
}

.value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.amount-header {
  padding: 20px 15px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.amount-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.amount-value {
  font-size: 32px;
  font-weight: 600;
  color: #FFB6C1;
}

.amount-breakdown {
  padding: 15px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &.discount .item-value {
    color: #ff4d4f;
  }
}

.item-label {
  font-size: 14px;
  color: #666;
}

.item-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.payment-methods {
  padding: 15px;
}

.payment-method {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 10px;
  border: 2px solid transparent;
  cursor: pointer;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &.active {
    border-color: #FFB6C1;
    background: #fff5f6;
  }
}

.method-info {
  display: flex;
  align-items: center;
}

.method-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.check-icon {
  color: #FFB6C1;
  font-size: 18px;
  font-weight: bold;
}

.payment-button {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px;
  background: #fff;
  border-top: 1px solid #e0e0e0;
}

.pay-btn {
  width: 100%;
  height: 50px;
  background: linear-gradient(135deg, #FF6B81 0%, #FF8E9E 100%);
  color: #fff;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 129, 0.3);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
  }
  
  &.disabled {
    background: #ccc;
    box-shadow: none;
  }
  
  &.loading {
    background: #ccc;
    box-shadow: none;
  }
}
</style> 