<template>
  <view class="card-record-page">
    <beauty-navbar title="使用记录" :show-back="true" />
    <view class="record-list">
      <view class="record-item" v-for="item in recordList" :key="item.id">
        <view class="record-info">
          <text class="record-service">服务：{{ item.service_name }}</text>
          <text class="record-time">时间：{{ item.usage_time }}</text>
          <text class="record-technician">技师：{{ item.technician_name }}</text>
        </view>
        <view class="record-amount">
          <text v-if="item.used_times">消耗次数：{{ item.used_times }}</text>
          <text v-if="item.used_amount">消耗金额：￥{{ item.used_amount }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CardRecord',
  data() {
    return {
      recordList: [
        {
          id: 1,
          service_name: '面部护理',
          usage_time: '2024-06-15 14:00',
          technician_name: '李美美',
          used_times: 1,
          used_amount: 0
        },
        {
          id: 2,
          service_name: '美甲',
          usage_time: '2024-06-20 10:30',
          technician_name: '王丽丽',
          used_times: 0,
          used_amount: 88
        }
      ]
    }
  },
  onLoad(options) {
    // TODO: 根据options.id请求使用记录接口
    // this.fetchRecordList(options.id)
  }
}
</script>

<style scoped>
.card-record-page {
  background: #f8f9fa;
  min-height: 100vh;
}
.record-list {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.record-item {
  background: #fff;
  border-radius: 10px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}
.record-info {
  margin-bottom: 8px;
}
.record-service {
  font-size: 16px;
  font-weight: bold;
}
.record-time {
  color: #888;
  font-size: 13px;
  margin-left: 12px;
}
.record-technician {
  color: #888;
  font-size: 13px;
  margin-left: 12px;
}
.record-amount {
  color: #e43d33;
  font-size: 15px;
}
</style> 