<template>
  <view class="card-page">
    <!-- 顶部导航栏 -->
    <beauty-navbar 
      title="套餐卡商城" 
      :showBack="true" 
      :showSearch="true" 
      @search="onSearch" 
      ref="navbar"
    />
    
    <!-- 页面内容 -->
    <view class="page-content" :style="{ paddingTop: navbarHeight + 'px' }">
      <!-- 搜索栏 -->
      <view class="search-section" v-if="showSearchBar">
        <view class="search-box">
          <text class="search-icon">🔍</text>
          <input 
            class="search-input" 
            placeholder="搜索套餐卡" 
            v-model="searchKeyword"
            @input="onSearchInput"
          />
        </view>
      </view>
      
      <!-- 筛选标签 -->
      <view class="filter-section">
        <view class="filter-tabs">
          <view 
            class="filter-tab" 
            :class="{ active: activeFilter === 'all' }"
            @click="setFilter('all')"
          >
            全部
          </view>
          <view 
            class="filter-tab" 
            :class="{ active: activeFilter === 'hot' }"
            @click="setFilter('hot')"
          >
            热门
          </view>
          <view 
            class="filter-tab" 
            :class="{ active: activeFilter === 'new' }"
            @click="setFilter('new')"
          >
            新品
          </view>
        </view>
      </view>
      
      <!-- 套餐卡列表 -->
      <view class="card-list">
        <view 
          class="card-item" 
          v-for="card in filteredCardList" 
          :key="card.id" 
          @click="goDetail(card.id)"
        >
          <view class="card-image-section">
            <image class="card-img" :src="getCardImage(card)" mode="aspectFill" />
            <view v-if="card.is_hot" class="hot-badge">热门</view>
            <view v-if="card.is_new" class="new-badge">新品</view>
          </view>
          <view class="card-info">
            <text class="card-name">{{ card.name }}</text>
            <text class="card-subtitle">{{ card.subtitle }}</text>
            <view class="card-price-section">
              <text class="card-price">￥{{ card.price }}</text>
              <text v-if="card.original_price > card.price" class="card-original-price">￥{{ card.original_price }}</text>
            </view>
            <view class="card-meta">
              <text class="card-sales">已售{{ card.sales_count || 0 }}</text>
              <text class="card-type">{{ getCardTypeText(card.card_type) }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-if="filteredCardList.length === 0" class="empty-state">
        <text class="empty-text">暂无套餐卡</text>
      </view>
    </view>
  </view>
</template>

<script>
import { listCard } from '@/api/beauty/card.js'
import BeautyNavbar from '@/components/beauty-navbar/beauty-navbar.vue'

export default {
  name: 'CardMall',
  components: {
    BeautyNavbar
  },
  data() {
    return {
      cardList: [],
      searchKeyword: '',
      activeFilter: 'all',
      showSearchBar: false,
      navbarHeight: 64 // 默认高度：状态栏20px + 导航栏44px
    }
  },
  computed: {
    filteredCardList() {
      let filtered = this.cardList
      
      // 搜索过滤
      if (this.searchKeyword) {
        filtered = filtered.filter(card => 
          card.name.includes(this.searchKeyword) || 
          card.subtitle.includes(this.searchKeyword)
        )
      }
      
      // 标签过滤
      switch (this.activeFilter) {
        case 'hot':
          filtered = filtered.filter(card => card.is_hot === 1)
          break
        case 'new':
          filtered = filtered.filter(card => card.is_new === 1)
          break
        default:
          break
      }
      
      return filtered
    }
  },
  onLoad() {
    this.fetchCardList()
    this.calculateNavbarHeight()
  },
  methods: {
    async fetchCardList() {
      try {
        const res = await listCard()
        if (res.code === 200 && Array.isArray(res.data)) {
          this.cardList = res.data
        } else {
          uni.showToast({ title: res.message || '获取套餐卡失败', icon: 'none' })
        }
      } catch (e) {
        uni.showToast({ title: '网络错误', icon: 'none' })
      }
    },
    goDetail(id) {
      uni.navigateTo({
        url: `/pages/beauty/card/detail?id=${id}`
      })
    },
    onSearch() {
      this.showSearchBar = !this.showSearchBar
      if (!this.showSearchBar) {
        this.searchKeyword = ''
      }
    },
    onSearchInput() {
      // 搜索输入处理
    },
    setFilter(filter) {
      this.activeFilter = filter
    },
    getCardImage(card) {
      if (card.images) {
        const images = typeof card.images === 'string' ? JSON.parse(card.images) : card.images
        return images[0] || '/static/beauty/banner1.jpg'
      }
      return '/static/beauty/banner1.jpg'
    },
    getCardTypeText(type) {
      const typeMap = {
        1: '次数卡',
        2: '金额卡',
        3: '混合卡'
      }
      return typeMap[type] || '套餐卡'
    },
    calculateNavbarHeight() {
      // 获取系统信息计算navbar高度
      try {
        const systemInfo = uni.getSystemInfoSync()
        const statusBarHeight = systemInfo.statusBarHeight || 20
        const navbarContentHeight = 44 // 导航栏内容高度
        this.navbarHeight = statusBarHeight + navbarContentHeight
      } catch (error) {
        console.warn('获取系统信息失败:', error)
        this.navbarHeight = 64 // 默认高度
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.card-page {
  min-height: 100vh;
  background: #f8f9fa;
}

.page-content {
  /* 动态padding-top，由JavaScript计算 */
}

.search-section {
  padding: 15px;
  background: #fff;
  
  .search-box {
    display: flex;
    align-items: center;
    background: #f5f5f5;
    border-radius: 20px;
    padding: 8px 15px;
    
    .search-icon {
      margin-right: 8px;
      font-size: 16px;
      color: #999;
    }
    
    .search-input {
      flex: 1;
      font-size: 14px;
      color: #333;
    }
  }
}

.filter-section {
  padding: 15px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  
  .filter-tabs {
    display: flex;
    gap: 20px;
    
    .filter-tab {
      padding: 6px 12px;
      border-radius: 15px;
      font-size: 14px;
      color: #666;
      background: #f5f5f5;
      cursor: pointer;
      transition: all 0.3s;
      
      &.active {
        background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 100%);
        color: #fff;
      }
    }
  }
}

.card-list {
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.card-item {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  cursor: pointer;
  transition: transform 0.2s;
  
  &:active {
    transform: scale(0.98);
  }
}

.card-image-section {
  position: relative;
  height: 120px;
  
  .card-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .hot-badge, .new-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    color: #fff;
    font-weight: bold;
  }
  
  .hot-badge {
    background: #ff4757;
  }
  
  .new-badge {
    background: #2ed573;
  }
}

.card-info {
  padding: 15px;
}

.card-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.card-subtitle {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
  display: block;
  line-height: 1.4;
}

.card-price-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.card-price {
  font-size: 18px;
  font-weight: bold;
  color: #ff4757;
}

.card-original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-sales {
  font-size: 11px;
  color: #999;
}

.card-type {
  font-size: 11px;
  color: #666;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 8px;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
  
  .empty-text {
    font-size: 16px;
    color: #999;
  }
}
</style> 