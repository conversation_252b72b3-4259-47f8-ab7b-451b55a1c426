<template>
  <view class="confirm-page">
    <!-- 顶部导航栏 -->
    <beauty-navbar 
      title="确认购买" 
      :showBack="true" 
      :showSearch="false"
      ref="navbar"
    />
    
    <!-- 页面内容 -->
    <view class="confirm-content" :style="{ paddingTop: navbarHeight + 'px' }">
      <!-- 套餐卡信息 -->
      <view class="card-info-section">
        <view class="card-image">
          <image :src="getCardImage(card)" mode="aspectFill" />
        </view>
        <view class="card-details">
          <text class="card-name">{{ card.name }}</text>
          <text class="card-subtitle">{{ card.subtitle }}</text>
          <view class="card-price-row">
            <text class="card-price">￥{{ card.price }}</text>
            <text class="card-original-price" v-if="card.original_price > card.price">￥{{ card.original_price }}</text>
          </view>
        </view>
      </view>
      
      <!-- 购买数量 -->
      <view class="quantity-section">
        <text class="section-title">购买数量</text>
        <view class="quantity-control">
          <view class="quantity-btn" @click="decreaseQuantity" :class="{ disabled: quantity <= 1 }">-</view>
          <text class="quantity-text">{{ quantity }}</text>
          <view class="quantity-btn" @click="increaseQuantity">+</view>
        </view>
      </view>
      
      <!-- 使用说明 -->
      <view class="usage-section">
        <text class="section-title">使用说明</text>
        <view class="usage-list">
          <view class="usage-item">
            <text class="usage-icon">📅</text>
            <text class="usage-text">有效期：购买后{{ card.valid_days }}天</text>
          </view>
          <view class="usage-item">
            <text class="usage-icon">🎯</text>
            <text class="usage-text">{{ getCardTypeText(card.card_type) }}</text>
          </view>
          <view class="usage-item">
            <text class="usage-icon">💳</text>
            <text class="usage-text">可在门店出示使用</text>
          </view>
        </view>
      </view>
      
      <!-- 价格明细 -->
      <view class="price-section">
        <text class="section-title">价格明细</text>
        <view class="price-list">
          <view class="price-item">
            <text class="price-label">套餐卡单价</text>
            <text class="price-value">￥{{ card.price }}</text>
          </view>
          <view class="price-item">
            <text class="price-label">购买数量</text>
            <text class="price-value">{{ quantity }}张</text>
          </view>
          <view class="price-item total">
            <text class="price-label">总计</text>
            <text class="price-value">￥{{ totalPrice }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部确认按钮 -->
    <view class="confirm-footer">
      <view class="price-summary">
        <text class="total-label">总计：</text>
        <text class="total-price">￥{{ totalPrice }}</text>
      </view>
      <button class="confirm-btn" @click="confirmPurchase" :disabled="loading">
        {{ loading ? '处理中...' : '确认购买' }}
      </button>
    </view>
  </view>
</template>

<script>
import { getCardDetail, purchaseCard, payCard } from '@/api/beauty/card.js'
import BeautyNavbar from '@/components/beauty-navbar/beauty-navbar.vue'

export default {
  name: 'CardConfirm',
  components: {
    BeautyNavbar
  },
  data() {
    return {
      cardId: null,
      card: {},
      quantity: 1,
      loading: false,
      navbarHeight: 64,
      defaultImage: '/static/beauty/banner1.jpg'
    }
  },
  computed: {
    totalPrice() {
      return (this.card.price || 0) * this.quantity
    }
  },
  onLoad(options) {
    this.cardId = options.id
    if (this.cardId) {
      this.fetchCardDetail()
    } else {
      uni.showToast({ title: '参数错误', icon: 'none' })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
    this.calculateNavbarHeight()
  },
  methods: {
    async fetchCardDetail() {
      this.loading = true
      try {
        const res = await getCardDetail({ id: this.cardId })
        if (res.code === 200 && res.data) {
          this.card = res.data
        } else {
          uni.showToast({ title: res.message || '获取详情失败', icon: 'none' })
        }
      } catch (error) {
        console.error('获取套餐卡详情失败:', error)
        uni.showToast({ title: '网络错误', icon: 'none' })
      } finally {
        this.loading = false
      }
    },
    getCardImage(card) {
      if (card.images) {
        try {
          const images = typeof card.images === 'string' ? JSON.parse(card.images) : card.images
          return images[0] || this.defaultImage
        } catch (error) {
          return this.defaultImage
        }
      }
      return this.defaultImage
    },
    getCardTypeText(type) {
      const typeMap = {
        1: '次数卡：按次数使用',
        2: '金额卡：按金额使用',
        3: '混合卡：次数和金额混合使用'
      }
      return typeMap[type] || '套餐卡'
    },
    decreaseQuantity() {
      if (this.quantity > 1) {
        this.quantity--
      }
    },
    increaseQuantity() {
      this.quantity++
    },
    async confirmPurchase() {
      if (this.loading) return
      
      // 检查登录状态
      const token = uni.getStorageSync('token')
      if (!token) {
        uni.showToast({ title: '请先登录', icon: 'none' })
        setTimeout(() => {
          uni.navigateTo({ url: '/pages/login/login' })
        }, 1500)
        return
      }
      
      // 显示确认对话框
      uni.showModal({
        title: '确认购买',
        content: `确定购买 ${this.quantity} 张"${this.card.name}"吗？\n总计：￥${this.totalPrice}`,
        success: (res) => {
          if (res.confirm) {
            this.processPurchase()
          }
        }
      })
    },
    async processPurchase() {
      this.loading = true
      try {
        // 先创建购买记录
        const purchaseRes = await purchaseCard({
          card_id: this.cardId,
          quantity: this.quantity
        })

        if (purchaseRes.code === 200) {
          // 购买记录创建成功，跳转到统一支付页面
          const cardInfo = {
            card_name: this.card.name,
            quantity: this.quantity,
            final_price: this.totalPrice,
            user_card_id: purchaseRes.data.user_card_id
          }

          uni.navigateTo({
            url: `/pages/common/payment?type=card&cardId=${purchaseRes.data.user_card_id}&cardInfo=${encodeURIComponent(JSON.stringify(cardInfo))}`
          })
        } else {
          uni.showToast({ title: purchaseRes.message || '购买失败', icon: 'none' })
        }

      } catch (error) {
        console.error('购买失败:', error)
        uni.showToast({ title: error.message || '购买失败，请重试', icon: 'none' })
      } finally {
        this.loading = false
      }
    },
    

    calculateNavbarHeight() {
      try {
        const systemInfo = uni.getSystemInfoSync()
        const statusBarHeight = systemInfo.statusBarHeight || 20
        const navbarContentHeight = 44
        this.navbarHeight = statusBarHeight + navbarContentHeight
      } catch (error) {
        console.warn('获取系统信息失败:', error)
        this.navbarHeight = 64
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.confirm-page {
  background: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 120px;
}

.confirm-content {
  /* 动态padding-top */
}

.card-info-section {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  margin: 16px;
  display: flex;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.card-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
  
  image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.card-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.card-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.card-subtitle {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.card-price-row {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.card-price {
  font-size: 18px;
  font-weight: bold;
  color: #ff4757;
}

.card-original-price {
  font-size: 12px;
  color: #bbb;
  text-decoration: line-through;
}

.quantity-section, .usage-section, .price-section {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  margin: 0 16px 16px 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  display: block;
}

.quantity-control {
  display: flex;
  align-items: center;
  gap: 20px;
}

.quantity-btn {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 100%);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  
  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.quantity-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  min-width: 40px;
  text-align: center;
}

.usage-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.usage-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.usage-icon {
  font-size: 16px;
}

.usage-text {
  font-size: 14px;
  color: #666;
}

.price-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  &.total {
    border-top: 1px solid #f0f0f0;
    padding-top: 12px;
    margin-top: 8px;
  }
}

.price-label {
  font-size: 14px;
  color: #666;
}

.price-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  
  .total & {
    font-size: 18px;
    font-weight: bold;
    color: #ff4757;
  }
}

.confirm-footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  box-shadow: 0 -2px 12px rgba(0,0,0,0.08);
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.price-summary {
  flex: 1;
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.total-label {
  font-size: 14px;
  color: #666;
}

.total-price {
  font-size: 20px;
  font-weight: bold;
  color: #ff4757;
}

.confirm-btn {
  background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 100%);
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  border-radius: 20px;
  padding: 12px 24px;
  border: none;
  
  &:disabled {
    opacity: 0.6;
  }
}
</style> 