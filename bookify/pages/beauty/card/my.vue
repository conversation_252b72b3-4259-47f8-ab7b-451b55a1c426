<template>
  <view class="my-card-page">
    <beauty-navbar title="我的套餐卡" :show-back="true" />
    <view class="my-card-list">
      <view class="my-card-item" v-for="card in myCardList" :key="card.id" @click="goToRecord(card.id)">
        <image class="my-card-img" :src="card.images && card.images[0]" mode="aspectFill" />
        <view class="my-card-info">
          <text class="my-card-name">{{ card.card_name }}</text>
          <text class="my-card-status">{{ getStatusText(card.status) }}</text>
          <text class="my-card-valid">有效期：{{ card.start_time }} ~ {{ card.end_time }}</text>
          <text class="my-card-amount" v-if="card.card_type === 1">剩余次数：{{ card.remaining_times }}</text>
          <text class="my-card-amount" v-else>剩余金额：￥{{ card.remaining_amount }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'MyCard',
  data() {
    return {
      myCardList: [
        {
          id: 1,
          card_name: '美容体验卡',
          card_type: 1,
          status: 1,
          start_time: '2024-06-01',
          end_time: '2024-09-01',
          remaining_times: 2,
          remaining_amount: 0,
          images: ['/static/beauty/banner1.jpg']
        },
        {
          id: 2,
          card_name: '护理金卡',
          card_type: 2,
          status: 1,
          start_time: '2024-06-10',
          end_time: '2024-12-10',
          remaining_times: 0,
          remaining_amount: 300,
          images: ['/static/beauty/banner2.jpg']
        }
      ]
    }
  },
  methods: {
    getStatusText(status) {
      switch(status) {
        case 0: return '已过期';
        case 1: return '正常';
        case 2: return '已用完';
        case 3: return '已冻结';
        default: return '未知';
      }
    },
    goToRecord(id) {
      uni.navigateTo({
        url: `/pages/beauty/card/record?id=${id}`
      })
    }
  }
}
</script>

<style scoped>
.my-card-page {
  background: #f8f9fa;
  min-height: 100vh;
}
.my-card-list {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.my-card-item {
  display: flex;
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  cursor: pointer;
}
.my-card-img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  margin-right: 16px;
}
.my-card-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 4px;
}
.my-card-name {
  font-size: 18px;
  font-weight: bold;
}
.my-card-status {
  color: #e43d33;
  font-size: 15px;
}
.my-card-valid {
  color: #888;
  font-size: 13px;
}
.my-card-amount {
  color: #333;
  font-size: 14px;
}
</style> 