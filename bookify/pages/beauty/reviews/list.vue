<template>
  <view class="reviews-list">
    <!-- 导航栏 -->
    <beauty-navbar 
      :title="navTitle" 
      :show-back="true"
    ></beauty-navbar>
    
    <!-- 页面内容 -->
    <view class="content" style="margin-top: 88px;">
      <!-- 评价统计 -->
      <view class="stats-section">
        <view class="overall-rating">
          <text class="rating-score">{{ overallRating }}</text>
          <view class="rating-stars">
            <text class="star" v-for="i in 5" :key="i" :class="{ active: i <= Math.floor(overallRating) }">★</text>
          </view>
          <text class="rating-count">{{ totalReviews }}条评价</text>
        </view>
        
        <view class="rating-breakdown">
          <view class="rating-item" v-for="(item, index) in ratingBreakdown" :key="index">
            <text class="rating-label">{{ 5 - index }}星</text>
            <view class="rating-bar">
              <view class="rating-fill" :style="{ width: item.percentage + '%' }"></view>
            </view>
            <text class="rating-count">{{ item.count }}</text>
          </view>
        </view>
      </view>
      
      <!-- 筛选栏 -->
      <view class="filter-section">
        <scroll-view scroll-x="true" class="filter-scroll">
          <view class="filter-tabs">
            <view 
              class="filter-tab" 
              :class="{ active: currentFilter === 'all' }" 
              @click="changeFilter('all')"
            >
              <text>全部({{ totalReviews }})</text>
            </view>
            <view 
              class="filter-tab" 
              :class="{ active: currentFilter === 'withImages' }" 
              @click="changeFilter('withImages')"
            >
              <text>有图({{ withImagesCount }})</text>
            </view>
            <view 
              class="filter-tab" 
              :class="{ active: currentFilter === '5' }" 
              @click="changeFilter('5')"
            >
              <text>好评({{ ratingCounts[5] || 0 }})</text>
            </view>
            <view 
              class="filter-tab" 
              :class="{ active: currentFilter === '3' }" 
              @click="changeFilter('3')"
            >
              <text>中评({{ ratingCounts[3] || 0 }})</text>
            </view>
            <view 
              class="filter-tab" 
              :class="{ active: currentFilter === '1' }" 
              @click="changeFilter('1')"
            >
              <text>差评({{ ratingCounts[1] || 0 }})</text>
            </view>
          </view>
        </scroll-view>
        
        <view class="sort-section">
          <view class="sort-item" @click="showSortModal">
            <text>{{ sortOptions.find(item => item.value === currentSort).label }}</text>
            <text class="sort-arrow">▼</text>
          </view>
        </view>
      </view>
      
      <!-- 评价列表 -->
      <view class="reviews-container">
        <view class="review-item" v-for="review in filteredReviews" :key="review.id">
          <view class="review-header">
            <image :src="review.userAvatar" class="user-avatar" mode="aspectFill"></image>
            <view class="review-info">
              <text class="user-name">{{ review.userName }}</text>
              <view class="review-rating">
                <text class="star" v-for="i in 5" :key="i" :class="{ active: i <= review.rating }">★</text>
              </view>
              <text class="review-date">{{ formatDate(review.createTime) }}</text>
            </view>
          </view>
          
          <view class="review-content">
            <text class="review-text">{{ review.content }}</text>
          </view>
          
          <!-- 评价图片 -->
          <view class="review-images" v-if="review.images && review.images.length > 0">
            <image 
              v-for="(img, imgIndex) in review.images" 
              :key="imgIndex"
              :src="img" 
              class="review-image"
              mode="aspectFill"
              @click="previewImage(review.images, imgIndex)"
            ></image>
          </view>
          
          <!-- 服务信息 -->
          <view class="service-info" v-if="review.serviceName">
            <text class="service-name">{{ review.serviceName }}</text>
            <text class="technician-name" v-if="review.technicianName">技师：{{ review.technicianName }}</text>
          </view>
          
          <!-- 商家回复 -->
          <view class="merchant-reply" v-if="review.replyContent">
            <view class="reply-header">
              <text class="reply-label">商家回复</text>
              <text class="reply-date">{{ formatDate(review.replyTime) }}</text>
            </view>
            <text class="reply-content">{{ review.replyContent }}</text>
          </view>
        </view>
        
        <!-- 加载更多 -->
        <view class="load-more" v-if="hasMore && !loading">
          <text @click="loadMore">加载更多</text>
        </view>
        
        <!-- 加载中 -->
        <view class="loading" v-if="loading">
          <text>加载中...</text>
        </view>
        
        <!-- 没有更多 -->
        <view class="no-more" v-if="!hasMore && filteredReviews.length > 0">
          <text>没有更多评价了</text>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-if="!loading && filteredReviews.length === 0">
          <view class="empty-icon">📝</view>
          <view class="empty-text">暂无评价</view>
        </view>
      </view>
    </view>
    
    <!-- 排序模态框 -->
    <view class="sort-modal" v-if="showSortModalFlag" @click="hideSortModal">
      <view class="sort-content" @click.stop>
        <view class="sort-title">排序方式</view>
        <view 
          class="sort-option" 
          v-for="option in sortOptions" 
          :key="option.value"
          :class="{ active: currentSort === option.value }"
          @click="changeSort(option.value)"
        >
          <text>{{ option.label }}</text>
          <text class="check-icon" v-if="currentSort === option.value">✓</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { beautyApi } from '@/api/beauty/mock.js'

export default {
  name: 'ReviewsList',
  
  data() {
    return {
      // 页面参数
      serviceId: null,
      TechnicianUserID: null,
      
      // 评价数据
      reviews: [],
      filteredReviews: [],
      totalReviews: 0,
      overallRating: 0,
      ratingBreakdown: [],
      ratingCounts: {},
      withImagesCount: 0,
      
      // 筛选和排序
      currentFilter: 'all',
      currentSort: 'time_desc',
      showSortModalFlag: false,
      
      // 分页
      page: 1,
      pageSize: 10,
      hasMore: true,
      loading: false,
      
      // 配置
      sortOptions: [
        { label: '按时间倒序', value: 'time_desc' },
        { label: '按时间正序', value: 'time_asc' },
        { label: '按评分从高到低', value: 'rating_desc' },
        { label: '按评分从低到高', value: 'rating_asc' }
      ]
    }
  },
  
  computed: {
    navTitle() {
      if (this.serviceId) {
        return '服务评价'
      } else if (this.TechnicianUserID) {
        return '技师评价'
      }
      return '用户评价'
    }
  },
  
  onLoad(options) {
    if (options.serviceId) {
      this.serviceId = parseInt(options.serviceId)
    }
    if (options.TechnicianUserID) {
      this.TechnicianUserID= parseInt(options.TechnicianUserID)
    }
    
    this.loadReviews()
  },
  
  methods: {
    async loadReviews() {
      if (this.loading) return
      
      this.loading = true
      
      try {
        const params = {
          page: this.page,
          pageSize: this.pageSize
        }
        
        if (this.serviceId) {
          params.serviceId = this.serviceId
        }
        if (this.TechnicianUserID) {
          params.TechnicianUserID= this.TechnicianUserID
        }
        
        const response = await beautyApi.getReviews(this.serviceId, this.TechnicianUserID, params)
        
        if (response.code === 200) {
          const newReviews = response.data || []
          
          if (this.page === 1) {
            this.reviews = newReviews
          } else {
            this.reviews = [...this.reviews, ...newReviews]
          }
          
          this.hasMore = newReviews.length === this.pageSize
          this.calculateStatistics()
          this.filterReviews()
        }
      } catch (error) {
        console.error('加载评价失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    calculateStatistics() {
      if (this.reviews.length === 0) {
        this.totalReviews = 0
        this.overallRating = 0
        this.ratingBreakdown = []
        this.ratingCounts = {}
        this.withImagesCount = 0
        return
      }
      
      this.totalReviews = this.reviews.length
      
      // 计算总评分
      const totalRating = this.reviews.reduce((sum, review) => sum + review.rating, 0)
      this.overallRating = (totalRating / this.reviews.length).toFixed(1)
      
      // 计算各评分数量
      this.ratingCounts = {}
      this.reviews.forEach(review => {
        this.ratingCounts[review.rating] = (this.ratingCounts[review.rating] || 0) + 1
      })
      
      // 计算评分分布
      this.ratingBreakdown = []
      for (let i = 5; i >= 1; i--) {
        const count = this.ratingCounts[i] || 0
        const percentage = this.totalReviews > 0 ? (count / this.totalReviews) * 100 : 0
        this.ratingBreakdown.push({
          rating: i,
          count: count,
          percentage: percentage
        })
      }
      
      // 计算有图评价数量
      this.withImagesCount = this.reviews.filter(review => 
        review.images && review.images.length > 0
      ).length
    },
    
    filterReviews() {
      let filtered = [...this.reviews]
      
      // 按筛选条件过滤
      if (this.currentFilter === 'withImages') {
        filtered = filtered.filter(review => review.images && review.images.length > 0)
      } else if (this.currentFilter === '5') {
        filtered = filtered.filter(review => review.rating === 5)
      } else if (this.currentFilter === '3') {
        filtered = filtered.filter(review => review.rating === 3 || review.rating === 4)
      } else if (this.currentFilter === '1') {
        filtered = filtered.filter(review => review.rating === 1 || review.rating === 2)
      }
      
      // 排序
      filtered.sort((a, b) => {
        switch (this.currentSort) {
          case 'time_desc':
            return new Date(b.createTime) - new Date(a.createTime)
          case 'time_asc':
            return new Date(a.createTime) - new Date(b.createTime)
          case 'rating_desc':
            return b.rating - a.rating
          case 'rating_asc':
            return a.rating - b.rating
          default:
            return 0
        }
      })
      
      this.filteredReviews = filtered
    },
    
    changeFilter(filter) {
      this.currentFilter = filter
      this.filterReviews()
    },
    
    changeSort(sort) {
      this.currentSort = sort
      this.filterReviews()
      this.hideSortModal()
    },
    
    showSortModal() {
      this.showSortModalFlag = true
    },
    
    hideSortModal() {
      this.showSortModalFlag = false
    },
    
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.page++
        this.loadReviews()
      }
    },
    
    previewImage(images, current) {
      uni.previewImage({
        urls: images,
        current: images[current]
      })
    },
    
    formatDate(dateString) {
      const date = new Date(dateString)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前'
      } else if (diff < 86400000) { // 1天内
        return Math.floor(diff / 3600000) + '小时前'
      } else if (diff < 2592000000) { // 30天内
        return Math.floor(diff / 86400000) + '天前'
      } else {
        return date.getFullYear() + '-' + 
               String(date.getMonth() + 1).padStart(2, '0') + '-' + 
               String(date.getDate()).padStart(2, '0')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.reviews-list {
  background: #f5f5f5;
  min-height: 100vh;
}

.content {
  padding-bottom: 20px;
}

/* 评价统计 */
.stats-section {
  background: #fff;
  padding: 20px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  
  .overall-rating {
    flex: 1;
    text-align: center;
    
    .rating-score {
      font-size: 36px;
      font-weight: bold;
      color: #ff6b9d;
      display: block;
      margin-bottom: 5px;
    }
    
    .rating-stars {
      margin-bottom: 5px;
      
      .star {
        font-size: 16px;
        color: #ddd;
        margin-right: 2px;
        
        &.active {
          color: #FFD700;
        }
      }
    }
    
    .rating-count {
      font-size: 14px;
      color: #999;
    }
  }
  
  .rating-breakdown {
    flex: 2;
    padding-left: 20px;
    
    .rating-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .rating-label {
        width: 30px;
        font-size: 12px;
        color: #666;
      }
      
      .rating-bar {
        flex: 1;
        height: 8px;
        background: #f0f0f0;
        border-radius: 4px;
        margin: 0 10px;
        position: relative;
        
        .rating-fill {
          height: 100%;
          background: #ff6b9d;
          border-radius: 4px;
          transition: width 0.3s ease;
        }
      }
      
      .rating-count {
        width: 30px;
        font-size: 12px;
        color: #666;
        text-align: right;
      }
    }
  }
}

/* 筛选栏 */
.filter-section {
  background: #fff;
  padding: 15px 0;
  margin-bottom: 10px;
  
  .filter-scroll {
    white-space: nowrap;
    
    .filter-tabs {
      display: flex;
      padding: 0 15px;
      
      .filter-tab {
        flex-shrink: 0;
        padding: 8px 16px;
        margin-right: 10px;
        background: #f5f5f5;
        border-radius: 20px;
        font-size: 14px;
        color: #666;
        
        &.active {
          background: #ff6b9d;
          color: #fff;
        }
      }
    }
  }
  
  .sort-section {
    padding: 10px 15px 0;
    
    .sort-item {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      font-size: 14px;
      color: #666;
      
      .sort-arrow {
        margin-left: 5px;
        font-size: 12px;
      }
    }
  }
}

/* 评价列表 */
.reviews-container {
  .review-item {
    background: #fff;
    padding: 15px;
    margin-bottom: 10px;
    
    .review-header {
      display: flex;
      margin-bottom: 10px;
      
      .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 20px;
        margin-right: 10px;
      }
      
      .review-info {
        flex: 1;
        
        .user-name {
          font-size: 14px;
          font-weight: bold;
          color: #333;
          display: block;
          margin-bottom: 5px;
        }
        
        .review-rating {
          margin-bottom: 5px;
          
          .star {
            font-size: 12px;
            color: #ddd;
            margin-right: 2px;
            
            &.active {
              color: #FFD700;
            }
          }
        }
        
        .review-date {
          font-size: 12px;
          color: #999;
        }
      }
    }
    
    .review-content {
      margin-bottom: 10px;
      
      .review-text {
        font-size: 14px;
        color: #333;
        line-height: 1.6;
      }
    }
    
    .review-images {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 10px;
      
      .review-image {
        width: 80px;
        height: 80px;
        border-radius: 4px;
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
    
    .service-info {
      padding: 10px;
      background: #f8f9fa;
      border-radius: 4px;
      margin-bottom: 10px;
      
      .service-name {
        font-size: 14px;
        color: #333;
        display: block;
        margin-bottom: 5px;
      }
      
      .technician-name {
        font-size: 12px;
        color: #666;
      }
    }
    
    .merchant-reply {
      padding: 10px;
      background: #f0f8ff;
      border-radius: 4px;
      border-left: 3px solid #ff6b9d;
      
      .reply-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
        
        .reply-label {
          font-size: 12px;
          color: #ff6b9d;
          font-weight: bold;
        }
        
        .reply-date {
          font-size: 12px;
          color: #999;
        }
      }
      
      .reply-content {
        font-size: 14px;
        color: #333;
        line-height: 1.6;
      }
    }
  }
}

/* 加载状态 */
.load-more, .loading, .no-more {
  text-align: center;
  padding: 20px;
  font-size: 14px;
  color: #999;
}

.load-more {
  color: #ff6b9d;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
  
  .empty-icon {
    font-size: 60px;
    margin-bottom: 20px;
  }
  
  .empty-text {
    font-size: 16px;
    color: #999;
  }
}

/* 排序模态框 */
.sort-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
  
  .sort-content {
    background: #fff;
    border-radius: 10px 10px 0 0;
    padding: 20px;
    width: 100%;
    
    .sort-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      text-align: center;
      margin-bottom: 20px;
    }
    
    .sort-option {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 0;
      font-size: 14px;
      color: #333;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      &.active {
        color: #ff6b9d;
        
        .check-icon {
          color: #ff6b9d;
        }
      }
      
      .check-icon {
        font-size: 16px;
        color: transparent;
      }
    }
  }
}
</style> 