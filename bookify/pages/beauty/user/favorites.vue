<template>
	<view class="favorites-page">
		<beauty-navbar title="我的收藏" :show-back="true"></beauty-navbar>
		
		<view class="content" style="margin-top: 88px;">
			<!-- 统计信息 -->
			<view class="stats-section" v-if="favoritesList.length > 0">
				<view class="stat-item">
					<view class="stat-number">{{ favoritesList.length }}</view>
					<view class="stat-label">收藏服务</view>
				</view>
				<view class="stat-item">
					<view class="stat-number">{{ totalValue }}</view>
					<view class="stat-label">总价值</view>
				</view>
			</view>
			
			<!-- 收藏列表 -->
			<view class="favorites-list" v-if="favoritesList.length > 0">
				<view 
					class="favorite-item" 
					v-for="service in favoritesList" 
					:key="service.id"
					@click="goToDetail(service.id)"
				>
					<image 
						:src="getServiceImage(service)" 
						mode="aspectFill" 
						class="service-image"
					></image>
					<view class="service-info">
						<text class="service-name">{{ service.name }}</text>
						<text class="service-desc">{{ service.description }}</text>
						<view class="service-meta">
							<text class="service-price">¥{{ service.price }}</text>
							<text class="service-original-price" v-if="service.originalPrice">¥{{ service.originalPrice }}</text>
							<text class="service-rating">{{ service.rating }}分</text>
						</view>
						<view class="service-tags" v-if="service.tags && service.tags.length > 0">
							<text 
								class="service-tag" 
								v-for="(tag, index) in service.tags.slice(0, 3)" 
								:key="index"
							>{{ tag }}</text>
						</view>
						<view class="favorite-time">收藏于 {{ formatTime(service.favoriteTime) }}</view>
					</view>
					<view class="service-actions">
						<view class="action-btn remove-btn" @click.stop="removeFromFavorites(service)">
							<text class="action-icon">🗑️</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 加载更多 -->
			<view class="loading-more" v-if="isLoading">
				<text class="loading-text">加载中...</text>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-if="favoritesList.length === 0 && !isLoading">
				<view class="empty-icon">💝</view>
				<view class="empty-text">暂无收藏</view>
				<view class="empty-desc">快去收藏您喜欢的服务吧</view>
				<view class="empty-btn" @click="goToServices">
					<text>浏览服务</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { beautyApi } from '@/api/beauty/mock.js'

export default {
	data() {
		return {
			favoritesList: [],
			isLoading: false,
			page: 1,
			pageSize: 10,
			hasMore: true
		}
	},
	
	computed: {
		totalValue() {
			return this.favoritesList.reduce((sum, service) => sum + (service.price || 0), 0).toFixed(0)
		}
	},
	
	onLoad() {
		this.loadFavorites()
	},
	
	onShow() {
		// 页面显示时刷新收藏列表
		this.refreshFavorites()
	},
	
	methods: {
		// 获取服务图片
		getServiceImage(service) {
			if (service.images && service.images.length > 0) {
				return service.images[0]
			}
			return '/static/beauty/default-service.jpg'
		},
		
		// 加载收藏列表
		async loadFavorites() {
			if (this.isLoading) return
			
			this.isLoading = true
			
			try {
				const response = await beautyApi.getFavoritesList(this.page, this.pageSize)
				
				if (response.code === 200) {
					const { list, hasMore } = response.data
					
					if (this.page === 1) {
						this.favoritesList = list || []
					} else {
						this.favoritesList = [...this.favoritesList, ...(list || [])]
					}
					
					this.hasMore = hasMore
					
					if (list && list.length > 0) {
						this.page++
					}
				} else {
					uni.showToast({
						title: response.message || '加载失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加载收藏列表失败:', error)
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				})
			} finally {
				this.isLoading = false
			}
		},
		
		// 刷新收藏列表
		async refreshFavorites() {
			this.page = 1
			this.hasMore = true
			this.favoritesList = []
			await this.loadFavorites()
		},
		
		// 从收藏中移除
		async removeFromFavorites(service) {
			uni.showModal({
				title: '确认删除',
				content: `确定要取消收藏"${service.name}"吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							const response = await beautyApi.removeFromFavorites(service.id)
							
							if (response.code === 200) {
								// 从列表中移除
								this.favoritesList = this.favoritesList.filter(item => item.id !== service.id)
								
								uni.showToast({
									title: '取消收藏成功',
									icon: 'success'
								})
								
								// 更新本地存储
								let favorites = uni.getStorageSync('service_favorites') || []
								favorites = favorites.filter(id => id !== service.id)
								uni.setStorageSync('service_favorites', favorites)
							} else {
								uni.showToast({
									title: response.message || '操作失败',
									icon: 'none'
								})
							}
						} catch (error) {
							console.error('取消收藏失败:', error)
							uni.showToast({
								title: '网络错误，请重试',
								icon: 'none'
							})
						}
					}
				}
			})
		},
		
		// 跳转到服务详情
		goToDetail(serviceId) {
			uni.navigateTo({
				url: `/pages/beauty/service/detail?id=${serviceId}`
			})
		},
		
		// 跳转到服务列表
		goToServices() {
			uni.switchTab({
				url: '/pages/beauty/service/list'
			})
		},
		
		// 格式化时间
		formatTime(timeString) {
			if (!timeString) return '未知时间'
			
			try {
				const time = new Date(timeString)
				const now = new Date()
				const diff = now - time
				
				const minutes = Math.floor(diff / (1000 * 60))
				const hours = Math.floor(diff / (1000 * 60 * 60))
				const days = Math.floor(diff / (1000 * 60 * 60 * 24))
				
				if (minutes < 60) {
					return `${minutes}分钟前`
				} else if (hours < 24) {
					return `${hours}小时前`
				} else if (days < 7) {
					return `${days}天前`
				} else {
					return time.toLocaleDateString()
				}
			} catch (error) {
				return '未知时间'
			}
		}
	}
}
</script>

<style scoped>
.favorites-page {
	min-height: 100vh;
	background-color: #f5f5f5;
}

.content {
	padding: 15px;
}

/* 统计信息 */
.stats-section {
	display: flex;
	background: #fff;
	padding: 20px;
	margin-bottom: 15px;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-item {
	flex: 1;
	text-align: center;
}

.stat-number {
	font-size: 24px;
	font-weight: bold;
	color: #ff6b9d;
	margin-bottom: 5px;
}

.stat-label {
	font-size: 14px;
	color: #666;
}

/* 收藏列表 */
.favorites-list .favorite-item {
	display: flex;
	background: #fff;
	margin-bottom: 15px;
	border-radius: 8px;
	padding: 15px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	position: relative;
}

.favorites-list .service-image {
	width: 120px;
	height: 120px;
	border-radius: 8px;
	margin-right: 15px;
	flex-shrink: 0;
}

.favorites-list .service-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.favorites-list .service-name {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 5px;
}

.favorites-list .service-desc {
	font-size: 14px;
	color: #666;
	margin-bottom: 10px;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.favorites-list .service-meta {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

.favorites-list .service-price {
	font-size: 18px;
	font-weight: bold;
	color: #ff6b9d;
	margin-right: 10px;
}

.favorites-list .service-original-price {
	font-size: 12px;
	color: #999;
	text-decoration: line-through;
	margin-right: 10px;
}

.favorites-list .service-rating {
	font-size: 12px;
	color: #999;
}

.favorites-list .service-tags {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 10px;
}

.favorites-list .service-tag {
	background: #fff5f8;
	color: #ff6b9d;
	font-size: 12px;
	padding: 2px 6px;
	border-radius: 4px;
	margin-right: 6px;
	margin-bottom: 5px;
}

.favorites-list .favorite-time {
	font-size: 12px;
	color: #999;
}

.favorites-list .service-actions {
	position: absolute;
	top: 15px;
	right: 15px;
}

.favorites-list .action-btn {
	width: 36px;
	height: 36px;
	border-radius: 18px;
	background: rgba(255, 255, 255, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.favorites-list .action-icon {
	font-size: 16px;
}

.favorites-list .remove-btn {
	background: rgba(255, 107, 157, 0.1);
}

/* 加载更多 */
.loading-more {
	text-align: center;
	padding: 15px 0;
}

.loading-text {
	font-size: 14px;
	color: #999;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 80px 20px;
	text-align: center;
}

.empty-icon {
	font-size: 60px;
	margin-bottom: 20px;
}

.empty-text {
	font-size: 18px;
	color: #333;
	margin-bottom: 10px;
}

.empty-desc {
	font-size: 14px;
	color: #666;
	margin-bottom: 30px;
}

.empty-btn {
	padding: 12px 24px;
	background: #ff6b9d;
	color: #fff;
	border-radius: 20px;
	font-size: 14px;
}
</style>
