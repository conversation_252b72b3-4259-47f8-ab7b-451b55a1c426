<template>
  <view class="profile-page">
    <beauty-navbar title="个人资料" :show-back="true"></beauty-navbar>
    <view class="content" style="margin-top: 88px;">
      <view class="form-section">
        <view class="form-item avatar-item" @click="chooseAvatar">
          <view class="label">头像</view>
          <view class="avatar-box">
            <image class="avatar" :src="userInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
            <text class="iconfont icon-right"></text>
          </view>
        </view>
        <view class="form-item">
          <view class="label">昵称</view>
          <input class="input" v-model="userInfo.nickname" placeholder="请输入昵称" />
        </view>
        <view class="form-item">
          <view class="label">性别</view>
          <view class="gender-group">
            <text 
              class="gender-item" 
              :class="{active: userInfo.gender === 1}"
              @click="userInfo.gender = 1"
            >男</text>
            <text 
              class="gender-item" 
              :class="{active: userInfo.gender === 2}"
              @click="userInfo.gender = 2"
            >女</text>
          </view>
        </view>
        <view class="form-item">
          <view class="label">手机号</view>
          <input class="input" v-model="userInfo.phone" placeholder="请输入手机号" maxlength="11" />
        </view>
        <view class="form-item">
          <view class="label">生日</view>
          <picker 
            mode="date" 
            :value="userInfo.birthday" 
            :end="endDate"
            @change="onBirthdayChange"
          >
            <view class="picker">
              <text>{{userInfo.birthday || '请选择生日'}}</text>
              <text class="iconfont icon-right"></text>
            </view>
          </picker>
        </view>
        <view class="form-item">
          <view class="label">邮箱</view>
          <input class="input" v-model="userInfo.email" placeholder="请输入邮箱" />
        </view>
      </view>
      <view class="save-btn" @click="saveProfile">保存</view>
    </view>
  </view>
</template>

<script>
import { getUserInfo, updateUserInfo, uploadAvatar } from '@/api/user'
import AuthUtils from '@/utils/auth'
import config from '@/config'

export default {
  data() {
    return {
      userInfo: {
        avatar: '',
        nickname: '',
        gender: 0,
        phone: '',
        birthday: '',
        email: ''
      },
      endDate: new Date().toISOString().split('T')[0] // 生日选择器的结束日期（今天）
    }
  },
  onLoad() {
    this.loadUserInfo()
  },
  methods: {
    // 加载用户信息
    async loadUserInfo() {
      try {
        // 检查登录状态
        if (!AuthUtils.checkLoginStatus()) {
          uni.showToast({
            title: '请先登录',
            icon: 'none'
          })
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/login/login'
            })
          }, 1500)
          return
        }

        const { code, data } = await getUserInfo()
        if (code === 200) {
          // 处理头像URL
          if (data.avatar) {
            data.avatar = config.apiHost + data.avatar
          }
          this.userInfo = {
            ...this.userInfo,
            ...data
          }
        } else {
          uni.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          })
        }
      } catch (e) {
        console.error('获取用户信息失败:', e)
        uni.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        })
      }
    },
    
    // 选择头像
    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          try {
            // 显示上传中
            uni.showLoading({
              title: '上传中...',
              mask: true
            })
            
            // 上传头像
            const { code, data, message } = await uploadAvatar(res.tempFilePaths[0])
            
            // 隐藏loading
            uni.hideLoading()
            if (code === 200) {
              // 更新头像
              this.userInfo.avatar = config.apiHost + data.avatar
              uni.showToast({
                title: '上传成功',
                icon: 'success'
              })
            } else {
              uni.showToast({
                title: message || '上传失败',
                icon: 'none'
              })
            }
          } catch (e) {
            uni.hideLoading()
            uni.showToast({
              title: e.message || '上传失败',
              icon: 'none'
            })
          }
        }
      })
    },
    
    // 生日改变
    onBirthdayChange(e) {
      this.userInfo.birthday = e.detail.value
    },
    
    // 保存资料
    async saveProfile() {
      // 表单验证
      if (!this.userInfo.nickname) {
        uni.showToast({
          title: '请输入昵称',
          icon: 'none'
        })
        return
      }
      
      if (this.userInfo.phone && !/^1\d{10}$/.test(this.userInfo.phone)) {
        uni.showToast({
          title: '手机号格式不正确',
          icon: 'none'
        })
        return
      }
      
      if (this.userInfo.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.userInfo.email)) {
        uni.showToast({
          title: '邮箱格式不正确',
          icon: 'none'
        })
        return
      }
      
      try {
        const { code, message } = await updateUserInfo(this.userInfo)
        if (code === 200) {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
          // 延迟返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          uni.showToast({
            title: message || '保存失败',
            icon: 'none'
          })
        }
      } catch (e) {
        console.error('保存用户信息失败:', e)
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style scoped>
.profile-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.content {
  padding: 0 15px 20px;
}
.form-section {
  background: #fff;
  border-radius: 10px;
  margin-bottom: 15px;
  overflow: hidden;
}
.form-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}
.form-item:last-child {
  border-bottom: none;
}
.label {
  width: 80px;
  color: #333;
  font-size: 14px;
  flex-shrink: 0;
}
.input {
  flex: 1;
  font-size: 14px;
  color: #333;
  text-align: right;
}
.picker {
  flex: 1;
  font-size: 14px;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.icon-right {
  color: #999;
  font-size: 12px;
}
.avatar-item .avatar-box {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}
.gender-group {
  flex: 1;
  display: flex;
  align-items: center;
}
.gender-item {
  margin-right: 20px;
  font-size: 14px;
  color: #666;
  padding: 5px 15px;
  border-radius: 15px;
  background: #f5f5f5;
}
.gender-item.active {
  color: #fff;
  background: #FFB6C1;
}
.save-btn {
  background: #FFB6C1;
  color: #fff;
  text-align: center;
  padding: 15px;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
}
</style>
