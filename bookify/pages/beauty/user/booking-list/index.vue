<template>
  <view class="booking-list">
    <view v-if="loading">加载中...</view>
    <view v-else-if="bookings.length === 0">暂无预约订单</view>
    <view v-else>
      <view v-for="item in bookings" :key="item.id" class="booking-item">
        <view class="service-name">{{ item.service_name }}</view>
        <view class="booking-time">{{ item.booking_date }} {{ item.start_time }} - {{ item.end_time }}</view>
        <view class="booking-status">状态：{{ item.booking_status }}</view>
        <view class="booking-price">实付：￥{{ item.final_price }}</view>
      </view>
    </view>
  </view>
</template>

<script>
import request from '@/api/request'
export default {
  data() {
    return {
      bookings: [],
      loading: true
    }
  },
  onLoad() {
    this.fetchBookings()
  },
  methods: {
    async fetchBookings() {
      this.loading = true
      try {
        const res = await request.get('/beauty/booking/my-list')
        this.bookings = res.data?.list || []
      } catch (e) {
        uni.showToast({ title: '获取预约失败', icon: 'none' })
      }
      this.loading = false
    }
  }
}
</script>

<style scoped>
.booking-list { padding: 20rpx; }
.booking-item { margin-bottom: 30rpx; padding: 20rpx; border-radius: 12rpx; background: #fff; box-shadow: 0 2rpx 8rpx #f0f0f0; }
.service-name { font-weight: bold; font-size: 32rpx; margin-bottom: 10rpx; }
.booking-time, .booking-status, .booking-price { font-size: 26rpx; color: #888; margin-bottom: 6rpx; }
</style> 