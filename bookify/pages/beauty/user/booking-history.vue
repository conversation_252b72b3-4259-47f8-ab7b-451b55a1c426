<template>
  <view class="booking-history">
    <!-- 统一导航栏 -->
    <beauty-navbar 
      title="我的预约" 
      :show-back="false"
    ></beauty-navbar>
    
    <!-- 统计信息 -->
    <view class="stats-section" style="margin-top: 88px;">
      <view class="stat-item">
        <view class="stat-number">{{ bookingStats.total }}</view>
        <view class="stat-label">总预约</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ bookingStats.pending }}</view>
        <view class="stat-label">待确认</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ bookingStats.confirmed }}</view>
        <view class="stat-label">已确认</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{ bookingStats.completed }}</view>
        <view class="stat-label">已完成</view>
      </view>
    </view>
    
    <!-- 筛选标签 -->
    <view class="filter-tabs">
      <view 
        class="filter-tab"
        :class="{ active: currentFilter === 'all' }"
        @click="switchFilter('all')"
      >
        全部
      </view>
      <view 
        class="filter-tab"
        :class="{ active: currentFilter === 'pending' }"
        @click="switchFilter('pending')"
      >
        待确认
      </view>
      <view 
        class="filter-tab"
        :class="{ active: currentFilter === 'confirmed' }"
        @click="switchFilter('confirmed')"
      >
        已确认
      </view>
      <view 
        class="filter-tab"
        :class="{ active: currentFilter === 'completed' }"
        @click="switchFilter('completed')"
      >
        已完成
      </view>
      <view 
        class="filter-tab"
        :class="{ active: currentFilter === 'cancelled' }"
        @click="switchFilter('cancelled')"
      >
        已取消
      </view>
    </view>
    
    <!-- 预约记录列表 -->
    <view class="content">
      <view v-if="loading && page === 1" class="loading-state">
        <text>加载中...</text>
      </view>
      
      <view v-else-if="bookings.length === 0 && !loading" class="empty-state">
        <view class="empty-icon">📅</view>
        <view class="empty-text">暂无预约记录</view>
        <view class="empty-desc">快去预约您喜欢的服务吧</view>
        <view class="empty-btn" @click="goToServices">
          <text>浏览服务</text>
        </view>
      </view>
      
      <view v-else class="booking-list">
        <view 
          class="booking-item" 
          v-for="booking in bookings" 
          :key="booking.id"
          @click="goToBookingDetail(booking)"
        >
          <!-- 预约状态和时间 -->
          <view class="booking-header">
            <view class="status-badge" :class="getStatusClass(booking.booking_status)">
              {{ getStatusText(booking.booking_status) }}
            </view>
            <view class="booking-time">
              {{ booking.booking_date }} {{ booking.start_time }}
            </view>
          </view>
          
          <!-- 服务信息 -->
          <view class="service-info">
            <view class="service-name">{{ booking.service_name }}</view>
            <view class="service-meta">
              <text class="technician">{{ booking.technician_name || '无需技师' }}</text>
              <text class="duration">{{ booking.duration }}分钟</text>
              <text v-if="booking.technician_fee > 0" class="technician-fee">
                技师费：¥{{ booking.technician_fee }}
              </text>
            </view>
          </view>
          
          <!-- 价格和支付信息 -->
          <view class="price-info">
            <view class="price-section">
              <text class="price">¥{{ booking.final_price }}</text>
            </view>
            <view class="payment-section">
              <view class="payment-status" :class="getPaymentStatusClass(booking.payment_status)">
                {{ getPaymentStatusText(booking.payment_status) }}
              </view>
            </view>
          </view>
          
          <!-- 操作按钮 -->
          <view class="action-buttons">
            <!-- 待确认状态 -->
            <view 
              v-if="booking.booking_status === 'pending' && booking.can_cancel"
              class="action-btn secondary"
              @click.stop="cancelBooking(booking)"
            >
              取消预约
            </view>
            <view 
              v-if="booking.booking_status === 'pending' && booking.can_pay"
              class="action-btn primary"
              @click.stop="goToPay(booking)"
            >
              去支付
            </view>
            
            <!-- 已确认状态 -->
            <view 
              v-if="booking.booking_status === 'confirmed' && booking.can_reschedule"
              class="action-btn secondary"
              @click.stop="rescheduleBooking(booking)"
            >
              改期
            </view>
            <view 
              v-if="booking.booking_status === 'confirmed' && booking.can_cancel"
              class="action-btn secondary"
              @click.stop="cancelBooking(booking)"
            >
              取消预约
            </view>
            
            <!-- 已完成状态 -->
            <view 
              v-if="booking.booking_status === 'completed' && booking.payment_status === 'unpaid'"
              class="action-btn primary"
              @click.stop="goToPay(booking)"
            >
              去支付
            </view>
            <view 
              v-if="booking.booking_status === 'completed' && booking.payment_status === 'paid'"
              class="action-btn primary"
              @click.stop="rebookService(booking)"
            >
              再次预约
            </view>
            <view 
              v-if="booking.booking_status === 'completed' && booking.payment_status === 'paid' && booking.can_review"
              class="action-btn secondary"
              @click.stop="reviewService(booking)"
            >
              评价服务
            </view>
            
            <!-- 已取消状态 -->
            <view 
              v-if="booking.booking_status === 'cancelled'"
              class="action-btn primary"
              @click.stop="rebookService(booking)"
            >
              重新预约
            </view>
          </view>
        </view>
        
        <!-- 加载更多 -->
        <view v-if="hasMore && !loading" class="load-more" @click="loadMore">
          <text>加载更多</text>
        </view>
        
        <!-- 加载中 -->
        <view v-if="loading && page > 1" class="loading-more">
          <text>加载中...</text>
        </view>
        
        <!-- 没有更多 -->
        <view v-if="!hasMore && bookings.length > 0" class="no-more">
          <text>没有更多了</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getMyBookingList, cancelBooking } from '@/api/beauty/index.js'

export default {
  name: 'BookingHistory',
  data() {
    return {
      loading: false,
      bookings: [],
      currentFilter: 'all',
      bookingStats: {
        total: 0,
        pending: 0,
        confirmed: 0,
        completed: 0,
        cancelled: 0,
        totalSpent: 0
      },
      page: 1, // 当前页码
      pageSize: 10, // 每页加载数量
      hasMore: true, // 是否还有更多数据
    }
  },
  
  computed: {
    // 移除不再使用的computed属性，因为现在直接使用后端筛选
  },
  
  onLoad() {
    this.loadBookings()
  },
  
  onPullDownRefresh() {
    this.page = 1 // 下拉刷新时重置页码
    this.loadBookings().then(() => {
      uni.stopPullDownRefresh()
    })
  },
  
  methods: {
    // 加载预约记录
    async loadBookings() {
      this.loading = true
      try {
        const params = {
          page: this.page,
          page_size: this.pageSize,
          status: this.currentFilter === 'all' ? '' : this.currentFilter // 全部状态传空字符串
        }
        
        console.log('开始加载预约记录，参数:', params)
        console.log('API调用参数详情:', {
          page: params.page,
          page_size: params.page_size,
          status: params.status,
          pageType: typeof params.page,
          pageSizeType: typeof params.page_size
        })
        
        const response = await getMyBookingList(params)
        
        console.log('预约记录API响应:', response)
        
        if (this.page === 1) {
          this.bookings = response.data.list || []
        } else {
          this.bookings = [...this.bookings, ...(response.data.list || [])]
        }
        
        this.calculateStats()
        this.hasMore = (response.data.total || 0) > this.page * this.pageSize
        
        console.log('预约记录加载完成:', {
          total: this.bookings.length,
          hasMore: this.hasMore,
          stats: this.bookingStats
        })
      } catch (error) {
        console.error('加载预约记录失败:', error)
        console.error('错误详情:', {
          message: error.message,
          response: error.response,
          status: error.response?.status,
          data: error.response?.data
        })
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 计算统计信息
    calculateStats() {
      const stats = {
        total: this.bookings.length,
        pending: 0,
        confirmed: 0,
        completed: 0,
        cancelled: 0,
        totalSpent: 0
      }
      
      this.bookings.forEach(booking => {
        if (booking.booking_status === 'pending') {
          stats.pending++
        } else if (booking.booking_status === 'confirmed') {
          stats.confirmed++
        } else if (booking.service_status === 'completed') {
          stats.completed++
          stats.totalSpent += booking.final_price
        } else if (booking.booking_status === 'cancelled') {
          stats.cancelled++
        }
      })
      
      this.bookingStats = stats
    },
    
    // 切换筛选条件
    switchFilter(filter) {
      this.currentFilter = filter
      this.page = 1 // 切换筛选条件时重置页码
      this.loadBookings()
    },
    
    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        'pending': 'pending',
        'confirmed': 'confirmed',
        'completed': 'completed',
        'cancelled': 'cancelled'
      }
      return classMap[status] || 'default'
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'pending': '待确认',
        'confirmed': '已确认',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return textMap[status] || status
    },
    
    // 获取支付状态文本
    getPaymentStatusText(paymentStatus) {
      const textMap = {
        'unpaid': '未支付',
        'paid': '已支付',
        'refunded': '已退款',
        'partial_refunded': '部分退款'
      }
      return textMap[paymentStatus] || '未知状态'
    },
    
    // 获取支付状态样式类
    getPaymentStatusClass(paymentStatus) {
      const classMap = {
        'unpaid': 'payment-unpaid',
        'paid': 'payment-paid',
        'refunded': 'payment-refunded',
        'partial_refunded': 'payment-partial-refunded'
      }
      return classMap[paymentStatus] || ''
    },
    
    // 跳转到预约详情
    goToBookingDetail(booking) {
      uni.navigateTo({
        url: `/pages/beauty/user/booking-detail?id=${booking.id}`
      })
    },
    
    // 再次预约
    rebookService(booking) {
      uni.navigateTo({
        url: `/pages/beauty/service/detail?id=${booking.service_id}`
      })
    },
    
    // 评价服务
    reviewService(booking) {
      uni.navigateTo({
        url: `/pages/beauty/user/write-review?bookingId=${booking.id}`
      })
    },
    
    // 跳转到服务列表
    goToServices() {
      uni.switchTab({
        url: '/pages/beauty/service/list'
      })
    },
    
    // 取消预约
    async cancelBooking(booking) {
      // 显示取消原因选择
      uni.showActionSheet({
        itemList: ['时间冲突', '临时有事', '选择其他服务', '其他原因'],
        success: async (res) => {
          const reasons = ['时间冲突', '临时有事', '选择其他服务', '其他原因']
          const cancelReason = reasons[res.tapIndex]
          
          // 如果是其他原因，让用户输入
          if (cancelReason === '其他原因') {
            uni.showModal({
              title: '请输入取消原因',
              editable: true,
              placeholderText: '请输入取消原因',
              success: async (modalRes) => {
                if (modalRes.confirm) {
                  await this.performCancelBooking(booking, modalRes.content || '其他原因')
                }
              }
            })
          } else {
            await this.performCancelBooking(booking, cancelReason)
          }
        }
      })
    },
    
    // 执行取消预约
    async performCancelBooking(booking, cancelReason) {
      try {
        await cancelBooking({ 
          id: booking.id,
          cancel_reason: cancelReason
        })
        uni.showToast({
          title: '取消成功',
          icon: 'success'
        })
        this.loadBookings()
      } catch (error) {
        console.error('取消预约失败:', error)
       
      }
    },
    
    // 改期预约
    rescheduleBooking(booking) {
      uni.navigateTo({
        url: `/pages/beauty/user/booking-detail?id=${booking.id}`
      })
    },

    // 去支付
    goToPay(booking) {
      uni.navigateTo({
        url: `/pages/beauty/payment/index?bookingId=${booking.id}`
      })
    },

    // 加载更多
    async loadMore() {
      if (!this.hasMore || this.loading) return
      this.page++
      await this.loadBookings()
    }
  }
}
</script>

<style scoped>
.booking-history {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 统计信息 */
.stats-section {
  display: flex;
  background: #fff;
  padding: 20px 0;
  margin-bottom: 10px;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.stat-number {
  font-size: 20px;
  font-weight: 600;
  color: #FFB6C1;
}

.stat-label {
  font-size: 12px;
  color: #999;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 10px;
  overflow-x: auto;
  white-space: nowrap;
}

.filter-tab {
  flex: 1;
  min-width: 80px;
  text-align: center;
  padding: 15px 8px;
  font-size: 13px;
  color: #666;
  position: relative;
}

.filter-tab.active {
  color: #FFB6C1;
  font-weight: 600;
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 2px;
  background: #FFB6C1;
}

/* 内容区域 */
.content {
  padding: 0 15px;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 50px 0;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 60px;
  margin-bottom: 20px;
}

.empty-text {
  font-size: 18px;
  color: #333;
  margin-bottom: 10px;
}

.empty-desc {
  font-size: 14px;
  color: #999;
  margin-bottom: 30px;
}

.empty-btn {
  padding: 12px 30px;
  background: #FFB6C1;
  border-radius: 25px;
  color: #fff;
  font-size: 14px;
}

/* 预约列表 */
.booking-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.booking-item {
  background: #fff;
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 预约头部 */
.booking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  min-width: 60px;
  text-align: center;
}

.status-badge.pending {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-badge.confirmed {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-badge.completed {
  background: #f9f0ff;
  color: #722ed1;
  border: 1px solid #d3adf7;
}

.status-badge.cancelled {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.booking-time {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* 服务信息 */
.service-info {
  margin-bottom: 15px;
}

.service-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.service-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  font-size: 14px;
  color: #666;
  align-items: center;
}

.service-meta .technician {
  color: #333;
  font-weight: 500;
}

.service-meta .duration {
  color: #666;
}

.service-meta .technician-fee {
  font-size: 12px;
  color: #fa8c16;
  font-weight: 500;
  background: #fff7e6;
  padding: 2px 6px;
  border-radius: 4px;
}

/* 价格信息 */
.price-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
}

.price-section {
  display: flex;
  align-items: center;
}

.price {
  font-size: 20px;
  font-weight: 600;
  color: #FF6B81;
}

.payment-section {
  display: flex;
  align-items: center;
}

/* 支付状态样式 */
.payment-status {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 12px;
  min-width: 50px;
  text-align: center;
}

.payment-unpaid {
  color: #ff6b6b;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
}

.payment-paid {
  color: #51cf66;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

.payment-refunded {
  color: #ffd43b;
  background-color: #fffbe6;
  border: 1px solid #ffeaa7;
}

.payment-partial-refunded {
  color: #ffa500;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  text-align: center;
  cursor: pointer;
}

.action-btn.primary {
  background: #FFB6C1;
  color: #fff;
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #666;
}

/* 加载更多 */
.load-more,
.loading-more,
.no-more {
  text-align: center;
  padding: 15px 0;
  color: #999;
  font-size: 14px;
}

.load-more {
  background: #fff;
  border-top: 1px solid #e0e0e0;
}

.loading-more {
  background: #fff;
  border-top: 1px solid #e0e0e0;
}

.no-more {
  background: #fff;
  border-top: 1px solid #e0e0e0;
}
</style> 