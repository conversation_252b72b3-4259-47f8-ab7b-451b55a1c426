<template>
  <view class="notification-page">
    <!-- 统一导航栏 -->
    <beauty-navbar 
      title="消息通知" 
      :show-back="true"
    ></beauty-navbar>
    
    <!-- 页面头部 -->
    <view class="page-header" style="margin-top: 88px;">
      <text class="header-title">消息通知</text>
      <text class="header-desc">预约相关的通知信息</text>
    </view>
    
    <!-- 通知列表 -->
    <view class="notification-list">
      <!-- 加载状态 -->
      <view class="loading-state" v-if="loading">
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 通知项 -->
      <view 
        class="notification-item" 
        :class="{ unread: !notification.isRead }"
        v-for="notification in notifications" 
        :key="notification.id"
        @click="handleNotificationClick(notification)"
      >
        <view class="notification-icon">
          <text class="icon-text" :style="{ color: getNotificationColor(notification.type) }">
            {{ getNotificationIcon(notification.type) }}
          </text>
        </view>
        
        <view class="notification-content">
          <view class="notification-header">
            <text class="notification-title">{{ notification.title }}</text>
            <text class="notification-time">{{ formatTime(notification.createTime) }}</text>
          </view>
          <text class="notification-message">{{ notification.content }}</text>
        </view>
        
        <view class="notification-status" v-if="!notification.isRead">
          <view class="unread-dot"></view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && notifications.length === 0">
        <text class="empty-icon">🔔</text>
        <text class="empty-text">暂无通知消息</text>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons" v-if="notifications.length > 0">
      <button class="action-btn" @click="markAllAsRead">
        全部标记为已读
      </button>
    </view>
  </view>
</template>

<script>
import { beautyApi } from '@/api/beauty/mock.js'

export default {
  name: 'NotificationPage',
  
  data() {
    return {
      loading: false,
      notifications: []
    }
  },
  
  onLoad() {
    this.loadNotifications()
  },
  
  onPullDownRefresh() {
    this.loadNotifications().then(() => {
      uni.stopPullDownRefresh()
    })
  },
  
  methods: {
    async loadNotifications() {
      this.loading = true
      try {
        const result = await beautyApi.getUserNotifications()
        this.notifications = result.data
      } catch (error) {
        console.error('加载通知失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    async handleNotificationClick(notification) {
      // 标记为已读
      if (!notification.isRead) {
        try {
          await beautyApi.markNotificationRead(notification.id)
          notification.isRead = true
        } catch (error) {
          console.error('标记已读失败:', error)
        }
      }
      
      // 根据通知类型跳转到相应页面
      if (notification.bookingId) {
        uni.navigateTo({
          url: `/pages/beauty/user/booking-detail?id=${notification.bookingId}`
        })
      }
    },
    
    async markAllAsRead() {
      try {
        const unreadNotifications = this.notifications.filter(n => !n.isRead)
        
        for (const notification of unreadNotifications) {
          await beautyApi.markNotificationRead(notification.id)
          notification.isRead = true
        }
        
        uni.showToast({
          title: '已全部标记为已读',
          icon: 'success'
        })
      } catch (error) {
        console.error('标记失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    },
    
    getNotificationIcon(type) {
      const iconMap = {
        'booking_confirmed': '✅',
        'booking_reminder': '⏰',
        'booking_completed': '🎉',
        'booking_cancelled': '❌',
        'booking_rescheduled': '📅',
        'system': '🔔'
      }
      return iconMap[type] || '📢'
    },
    
    getNotificationColor(type) {
      const colorMap = {
        'booking_confirmed': '#52c41a',
        'booking_reminder': '#fa8c16',
        'booking_completed': '#722ed1',
        'booking_cancelled': '#ff4d4f',
        'booking_rescheduled': '#1890ff',
        'system': '#666'
      }
      return colorMap[type] || '#666'
    },
    
    formatTime(timeString) {
      const time = new Date(timeString)
      const now = new Date()
      const diff = now - time
      
      const minute = 60 * 1000
      const hour = 60 * minute
      const day = 24 * hour
      
      if (diff < minute) {
        return '刚刚'
      } else if (diff < hour) {
        return `${Math.floor(diff / minute)}分钟前`
      } else if (diff < day) {
        return `${Math.floor(diff / hour)}小时前`
      } else if (diff < 7 * day) {
        return `${Math.floor(diff / day)}天前`
      } else {
        return time.toLocaleDateString()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.notification-page {
  background: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 100rpx;
}

.page-header {
  background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 100%);
  padding: 60rpx 30rpx 40rpx;
  color: white;
  text-align: center;
  
  .header-title {
    font-size: 36rpx;
    font-weight: 600;
    display: block;
    margin-bottom: 8rpx;
  }
  
  .header-desc {
    font-size: 26rpx;
    opacity: 0.9;
  }
}

.notification-list {
  padding: 20rpx;
}

.loading-state {
  text-align: center;
  padding: 80rpx 0;
  
  .loading-text {
    font-size: 28rpx;
    color: #999;
  }
}

.notification-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  
  &.unread {
    border-left: 6rpx solid #FFB6C1;
    background: #fefefe;
  }
  
  &:active {
    transform: scale(0.98);
    background: #f5f5f5;
  }
  
  .notification-icon {
    margin-right: 20rpx;
    
    .icon-text {
      font-size: 32rpx;
    }
  }
  
  .notification-content {
    flex: 1;
    
    .notification-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8rpx;
      
      .notification-title {
        font-size: 30rpx;
        font-weight: 600;
        color: #333;
      }
      
      .notification-time {
        font-size: 24rpx;
        color: #999;
      }
    }
    
    .notification-message {
      font-size: 26rpx;
      color: #666;
      line-height: 1.5;
    }
  }
  
  .notification-status {
    margin-left: 20rpx;
    display: flex;
    align-items: center;
    
    .unread-dot {
      width: 12rpx;
      height: 12rpx;
      border-radius: 50%;
      background: #FFB6C1;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 100rpx 0;
  
  .empty-icon {
    font-size: 80rpx;
    display: block;
    margin-bottom: 20rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  
  .action-btn {
    width: 100%;
    height: 80rpx;
    background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 100%);
    color: white;
    border: none;
    border-radius: 40rpx;
    font-size: 30rpx;
    font-weight: 600;
    
    &:active {
      opacity: 0.8;
    }
  }
}
</style>
