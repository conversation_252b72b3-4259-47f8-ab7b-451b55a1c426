<template>
  <view class="reviews-page">
    <beauty-navbar title="我的评价" :show-back="true"></beauty-navbar>
    
    <view class="content" style="margin-top: 88px;">
      <!-- 评价统计 -->
      <view class="stats-section" v-if="myReviews.length > 0">
        <view class="stat-item">
          <text class="stat-number">{{ myReviews.length }}</text>
          <text class="stat-label">评价数量</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ averageRating }}</text>
          <text class="stat-label">平均评分</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ withImagesCount }}</text>
          <text class="stat-label">有图评价</text>
        </view>
      </view>
      
      <!-- 评价列表 -->
      <view class="reviews-list" v-if="myReviews.length > 0">
        <view class="review-item" v-for="review in myReviews" :key="review.id">
          <view class="review-header">
            <view class="service-info">
              <text class="service-name">{{ review.serviceName }}</text>
              <text class="technician-name">技师：{{ review.technicianName }}</text>
            </view>
            <view class="review-rating">
              <text class="star" v-for="i in 5" :key="i" :class="{ active: i <= review.rating }">★</text>
            </view>
          </view>
          
          <view class="review-content">
            <text class="review-text">{{ review.content }}</text>
          </view>
          
          <!-- 评价图片 -->
          <view class="review-images" v-if="review.images && review.images.length > 0">
            <image 
              v-for="(img, imgIndex) in review.images" 
              :key="imgIndex"
              :src="img" 
              class="review-image"
              mode="aspectFill"
              @click="previewImage(review.images, imgIndex)"
            ></image>
          </view>
          
          <!-- 评价标签 -->
          <view class="review-tags" v-if="review.tags && review.tags.length > 0">
            <view class="tag-item" v-for="tag in review.tags" :key="tag">
              <text>{{ tag }}</text>
            </view>
          </view>
          
          <view class="review-footer">
            <text class="review-date">{{ formatDate(review.createTime) }}</text>
            <text class="anonymous-tag" v-if="review.isAnonymous">匿名</text>
          </view>
          
          <!-- 商家回复 -->
          <view class="merchant-reply" v-if="review.replyContent">
            <view class="reply-header">
              <text class="reply-label">商家回复</text>
              <text class="reply-date">{{ formatDate(review.replyTime) }}</text>
            </view>
            <text class="reply-content">{{ review.replyContent }}</text>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="myReviews.length === 0 && !loading">
        <view class="empty-icon">📝</view>
        <view class="empty-text">暂无评价</view>
        <view class="empty-desc">完成服务后可以进行评价</view>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-state" v-if="loading">
        <text>加载中...</text>
      </view>
    </view>
  </view>
</template>

<script>
import { beautyApi } from '@/api/beauty/mock.js'

export default {
  name: 'MyReviews',
  
  data() {
    return {
      myReviews: [],
      loading: false
    }
  },
  
  computed: {
    averageRating() {
      if (this.myReviews.length === 0) return 0
      const total = this.myReviews.reduce((sum, review) => sum + review.rating, 0)
      return (total / this.myReviews.length).toFixed(1)
    },
    
    withImagesCount() {
      return this.myReviews.filter(review => review.images && review.images.length > 0).length
    }
  },
  
  onLoad() {
    this.loadMyReviews()
  },
  
  methods: {
    async loadMyReviews() {
      this.loading = true
      
      try {
        // 这里应该调用获取当前用户评价的API
        // 暂时使用模拟数据
        const response = await beautyApi.getReviews()
        
        if (response.code === 200) {
          // 模拟筛选当前用户的评价
          this.myReviews = response.data.filter(review => review.userId === 104)
        }
      } catch (error) {
        console.error('加载评价失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    previewImage(images, current) {
      uni.previewImage({
        urls: images,
        current: images[current]
      })
    },
    
    formatDate(dateString) {
      const date = new Date(dateString)
      return date.getFullYear() + '-' + 
             String(date.getMonth() + 1).padStart(2, '0') + '-' + 
             String(date.getDate()).padStart(2, '0')
    }
  }
}
</script>

<style lang="scss" scoped>
.reviews-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 15px;
  padding-bottom: 30px;
}

/* 评价统计 */
.stats-section {
  background: #fff;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-around;
  
  .stat-item {
    text-align: center;
    
    .stat-number {
      font-size: 24px;
      font-weight: bold;
      color: #ff6b9d;
      display: block;
      margin-bottom: 5px;
    }
    
    .stat-label {
      font-size: 14px;
      color: #666;
    }
  }
}

/* 评价列表 */
.reviews-list {
  .review-item {
    background: #fff;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 15px;
    
    .review-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 10px;
      
      .service-info {
        flex: 1;
        
        .service-name {
          font-size: 16px;
          font-weight: bold;
          color: #333;
          display: block;
          margin-bottom: 5px;
        }
        
        .technician-name {
          font-size: 14px;
          color: #666;
        }
      }
      
      .review-rating {
        .star {
          font-size: 16px;
          color: #ddd;
          margin-left: 2px;
          
          &.active {
            color: #FFD700;
          }
        }
      }
    }
    
    .review-content {
      margin-bottom: 10px;
      
      .review-text {
        font-size: 14px;
        color: #333;
        line-height: 1.6;
      }
    }
    
    .review-images {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 10px;
      
      .review-image {
        width: 80px;
        height: 80px;
        border-radius: 4px;
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
    
    .review-tags {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 10px;
      
      .tag-item {
        padding: 4px 8px;
        background: #f0f8ff;
        border-radius: 12px;
        margin-right: 8px;
        margin-bottom: 5px;
        
        text {
          font-size: 12px;
          color: #ff6b9d;
        }
      }
    }
    
    .review-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      
      .review-date {
        font-size: 12px;
        color: #999;
      }
      
      .anonymous-tag {
        font-size: 12px;
        color: #ff6b9d;
        background: #fff0f5;
        padding: 2px 6px;
        border-radius: 8px;
      }
    }
    
    .merchant-reply {
      padding: 10px;
      background: #f0f8ff;
      border-radius: 4px;
      border-left: 3px solid #ff6b9d;
      
      .reply-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
        
        .reply-label {
          font-size: 12px;
          color: #ff6b9d;
          font-weight: bold;
        }
        
        .reply-date {
          font-size: 12px;
          color: #999;
        }
      }
      
      .reply-content {
        font-size: 14px;
        color: #333;
        line-height: 1.6;
      }
    }
  }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80px 20px;
  text-align: center;
  
  .empty-icon {
    font-size: 60px;
    margin-bottom: 20px;
  }
  
  .empty-text {
    font-size: 18px;
    color: #333;
    margin-bottom: 10px;
  }
  
  .empty-desc {
    font-size: 14px;
    color: #666;
  }
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 40px;
  font-size: 14px;
  color: #999;
}
</style>
