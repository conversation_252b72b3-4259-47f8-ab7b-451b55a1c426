<template>
  <view class="user-center">
    <!-- 统一导航栏 -->
    <beauty-navbar 
      title="个人中心" 
      :show-back="false"
    ></beauty-navbar>
    
    <!-- 用户信息卡片 -->
    <view class="user-card" style="margin-top: 88px;">
      <view class="user-bg">
        <view class="user-info">
          <image class="user-avatar" :src="userInfo.avatar ? userInfo.avatar : '/static/images/default-avatar.png'" mode="aspectFill"></image>
          <view class="user-details">
            <view class="user-name">{{ userInfo.nickname || '未设置昵称' }}</view>
            <view class="user-level">{{ userInfo.level || '普通会员' }}</view>
            <view class="user-phone">{{ formatPhone(userInfo.phone) }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 预约统计 -->
    <view class="booking-stats">
      <view class="stat-item" @click="goToBookingList('pending')">
        <view class="stat-number">{{ bookingStats.pending }}</view>
        <view class="stat-label">待确认</view>
      </view>
      <view class="stat-item" @click="goToBookingList('confirmed')">
        <view class="stat-number">{{ bookingStats.confirmed }}</view>
        <view class="stat-label">已确认</view>
      </view>
      <view class="stat-item" @click="goToBookingList('in_service')">
        <view class="stat-number">{{ bookingStats.inService }}</view>
        <view class="stat-label">服务中</view>
      </view>
      <view class="stat-item" @click="goToBookingList('completed')">
        <view class="stat-number">{{ bookingStats.completed }}</view>
        <view class="stat-label">已完成</view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions">
      <view class="action-item" @click="goToServiceList">
        <view class="action-icon">
          <text class="iconfont icon-service"></text>
        </view>
        <view class="action-text">浏览服务</view>
      </view>
      <view class="action-item" @click="goToTechnicianList">
        <view class="action-icon">
          <text class="iconfont icon-technician"></text>
        </view>
        <view class="action-text">选择技师</view>
      </view>
      <view class="action-item" @click="goToBooking">
        <view class="action-icon">
          <text class="iconfont icon-booking"></text>
        </view>
        <view class="action-text">立即预约</view>
      </view>
      <view class="action-item" @click="goToNotification">
        <view class="action-icon">
          <text class="iconfont icon-notification"></text>
          <view v-if="unreadCount > 0" class="notification-badge">{{ unreadCount }}</view>
        </view>
        <view class="action-text">消息通知</view>
      </view>
    </view>

    <!-- 技师工作台入口 -->
    <view class="menu-section" v-if="userInfo.isTechnician">
      <view class="menu-item technician-entry" @click="goToTechnicianDashboard">
        <view class="menu-icon technician-icon">
          <text>💆‍♀️</text>
        </view>
        <view class="menu-content">
          <view class="menu-title">技师工作台</view>
          <view class="menu-desc">管理预约和服务</view>
        </view>
        <view class="menu-right">
          <view class="technician-badge">技师</view>
          <view class="menu-arrow">
            <text class="iconfont icon-arrow-right"></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-item" @click="goToProfile">
        <view class="menu-icon">
          <text class="iconfont icon-profile"></text>
        </view>
        <view class="menu-content">
          <view class="menu-title">个人资料</view>
          <view class="menu-desc">编辑个人信息</view>
        </view>
        <view class="menu-arrow">
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
      
      <view class="menu-item" @click="goToBookingHistory">
        <view class="menu-icon">
          <text class="iconfont icon-booking"></text>
        </view>
        <view class="menu-content">
          <view class="menu-title">我的预约</view>
          <view class="menu-desc">查看预约记录</view>
        </view>
        <view class="menu-arrow">
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
      
      <view class="menu-item" @click="goToFavorites">
        <view class="menu-icon">
          <text class="iconfont icon-favorite"></text>
        </view>
        <view class="menu-content">
          <view class="menu-title">我的收藏</view>
          <view class="menu-desc">收藏的服务和技师</view>
        </view>
        <view class="menu-arrow">
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
      
      <view class="menu-item" @click="goToReviews">
        <view class="menu-icon">
          <text class="iconfont icon-review"></text>
        </view>
        <view class="menu-content">
          <view class="menu-title">我的评价</view>
          <view class="menu-desc">查看和管理评价</view>
        </view>
        <view class="menu-arrow">
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>

      <view class="menu-item" @click="goToOrderList">
        <view class="menu-icon">
          <text class="iconfont icon-history"></text>
        </view>
        <view class="menu-content">
          <view class="menu-title">我的订单</view>
          <view class="menu-desc">商城订单与套餐卡</view>
        </view>
        <view class="menu-arrow">
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
    </view>

    <!-- 服务支持 -->
    <view class="support-section">
      <view class="menu-item" @click="contactService">
        <view class="menu-icon">
          <text class="iconfont icon-service-support"></text>
        </view>
        <view class="menu-content">
          <view class="menu-title">客服支持</view>
          <view class="menu-desc">在线客服咨询</view>
        </view>
        <view class="menu-arrow">
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
      
      <view class="menu-item" @click="goToHelp">
        <view class="menu-icon">
          <text class="iconfont icon-help"></text>
        </view>
        <view class="menu-content">
          <view class="menu-title">帮助中心</view>
          <view class="menu-desc">常见问题解答</view>
        </view>
        <view class="menu-arrow">
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
      
      <view class="menu-item" @click="goToAbout">
        <view class="menu-icon">
          <text class="iconfont icon-info"></text>
        </view>
        <view class="menu-content">
          <view class="menu-title">关于我们</view>
          <view class="menu-desc">了解美丽时光</view>
        </view>
        <view class="menu-arrow">
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section">
      <button class="logout-btn" @click="logout">退出登录</button>
    </view>
  </view>
</template>

<script>
import { getUserInfo, logout as userLogout } from '@/api/user'
import AuthUtils from '@/utils/auth'
import { getBookingStats, getUnreadNotificationCount } from '@/api/beauty'
import config from '@/config'

export default {
  data() {
    return {
      userInfo: {
        nickname: '',
        level: '普通会员',
        phone: '',
        avatar: '',
        isTechnician: false // 新增：是否是技师
      },
      bookingStats: {
        pending: 0,
        confirmed: 0,
        inService: 0,
        completed: 0
      },
      unreadCount: 0,
      isTechnician: false,    // 是否是技师
      technicianInfo: null    // 技师信息
    }
  },
  
  onLoad() {
    this.loadUserInfo()
    this.loadBookingStats()
    this.loadUnreadCount()
    this.checkTechnicianStatus()
  },
  
  onShow() {
    // 页面显示时刷新数据
    this.loadUserInfo()
    this.loadBookingStats()
    this.loadUnreadCount()
  },
  
  methods: {
    // 格式化手机号显示
    formatPhone(phone) {
      if (!phone) return '未绑定手机号'
      if (phone.length === 11) {
        return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
      }
      return phone
    },
    
    async loadUserInfo() {
      try {
        // 检查登录状态
        if (!AuthUtils.checkLoginStatus()) {
          uni.showToast({
            title: '请先登录',
            icon: 'none'
          })
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/login/login'
            })
          }, 1500)
          return
        }

        const { code, data } = await getUserInfo()
        if (code === 200) {
          // 处理头像URL
        
          this.userInfo = {
            nickname: data.nickname || '未设置昵称',
            level: data.level || '普通会员',
            phone: data.phone || '',
            avatar: data.avatar || '',
            isTechnician: data.isTechnician || false // 新增：设置技师状态
          }
        } else {
          console.error('获取用户信息失败')
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },
    
    async loadBookingStats() {
      try {
        const { code, data } = await getBookingStats()
        if (code === 200) {
          this.bookingStats = data
        } else {
          console.error('获取预约统计失败')
        }
      } catch (error) {
        console.error('加载预约统计失败:', error)
      }
    },
    
    async loadUnreadCount() {
      try {
        const { code, data } = await getUnreadNotificationCount()
        if (code === 200) {
          this.unreadCount = data.count || 0
        } else {
          console.error('获取未读消息数失败')
        }
      } catch (error) {
        console.error('加载未读消息数失败:', error)
      }
    },
    
    async checkTechnicianStatus() {
      try {
        // TODO: 调用技师状态检查API
        // const { code, data } = await checkTechnicianStatus()
        // this.isTechnician = code === 200 && data.isTechnician
        // this.technicianInfo = data.technicianInfo
      } catch (error) {
        console.error('检查技师状态失败:', error)
      }
    },
    
    goToServiceList() {
      uni.switchTab({
        url: '/pages/beauty/service/list'
      })
    },
    
    goToTechnicianList() {
      uni.navigateTo({
        url: '/pages/beauty/technician/list'
      })
    },
    
    goToBooking() {
      // 跳转到服务列表页面，让用户先选择服务
      uni.switchTab({
        url: '/pages/beauty/service/list'
      })
    },
    
    goToNotification() {
      uni.navigateTo({
        url: '/pages/beauty/user/notification'
      })
    },
    
    goToBookingList(status) {
      // 由于booking-history是tabbar页面，使用switchTab跳转
      uni.switchTab({
        url: '/pages/beauty/user/booking-history'
      })
    },
    
    goToProfile() {
      uni.navigateTo({
        url: '/pages/beauty/user/profile'
      })
    },
    
    goToBookingHistory() {
      uni.switchTab({
        url: '/pages/beauty/user/booking-history'
      })
    },
    
    goToFavorites() {
      uni.navigateTo({
        url: '/pages/beauty/user/favorites'
      })
    },
    
    goToReviews() {
      uni.navigateTo({
        url: '/pages/beauty/user/reviews'
      })
    },

    goToOrderList() {
      uni.navigateTo({ url: '/pages/order/list' })
    },
    
    goToTechnicianDashboard() {
      uni.navigateTo({ url: '/pages/beauty/technician/dashboard/index' })
    },
    
    contactService() {
      uni.showModal({
        title: '客服支持',
        content: '请拨打客服电话：400-123-4567\n或添加微信：beauty_service',
        showCancel: false
      })
    },
    
    goToHelp() {
      uni.navigateTo({
        url: '/pages/beauty/help/index'
      })
    },
    
    goToAbout() {
      uni.navigateTo({
        url: '/pages/beauty/about/index'
      })
    },
    
    async logout() {
      uni.showModal({
        title: '退出登录',
        content: '确定要退出登录吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              // 调用后端退出登录接口
              await userLogout()
            } catch (error) {
              console.error('退出登录失败:', error)
            } finally {
              // 清除本地登录信息
              AuthUtils.clearLoginInfo()
              
              // 跳转到登录页面
              uni.reLaunch({
                url: '/pages/login/login'
              })
            }
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.user-center {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 用户信息卡片 */
.user-card {
  margin-bottom: 10px;
}

.user-bg {
  background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 100%);
  padding: 20px 15px;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  margin-right: 15px;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 20px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 5px;
}

.user-level {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 5px;
}

.user-phone {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

/* 预约统计 */
.booking-stats {
  display: flex;
  background: #fff;
  padding: 20px 0;
  margin-bottom: 10px;
}

.stat-item {
  flex: 1;
  text-align: center;
  cursor: pointer;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #FFB6C1;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  background: #fff;
  padding: 20px 0;
  margin-bottom: 10px;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.action-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 100%);
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  position: relative;
}

.action-icon .iconfont {
  font-size: 24px;
  color: #fff;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4757;
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

.action-text {
  font-size: 12px;
  color: #333;
}

/* 功能菜单 */
.menu-section {
  background: #fff;
  margin-bottom: 10px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 40px;
  height: 40px;
  background: #f8f9fa;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.menu-icon .iconfont {
  font-size: 20px;
  color: #FFB6C1;
}

.menu-content {
  flex: 1;
}

.menu-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 2px;
}

.menu-desc {
  font-size: 12px;
  color: #999;
}

.menu-arrow {
  width: 20px;
  text-align: center;
}

.menu-arrow .iconfont {
  font-size: 14px;
  color: #ccc;
}

/* 技师工作台入口 */
.technician-entry {
  background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 100%);
  color: #fff;
  position: relative;
  overflow: hidden;
}

.technician-entry::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.technician-entry .menu-title {
  color: #fff;
  font-weight: 600;
}

.technician-entry .menu-desc {
  color: rgba(255, 255, 255, 0.9);
}

.technician-icon {
  background: rgba(255, 255, 255, 0.2) !important;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.technician-icon text {
  font-size: 24px;
}

.menu-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.technician-badge {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.technician-entry .menu-arrow .iconfont {
  color: rgba(255, 255, 255, 0.8);
}

/* 服务支持 */
.support-section {
  background: #fff;
  margin-bottom: 10px;
}

/* 退出登录 */
.logout-section {
  padding: 20px 15px;
}

.logout-btn {
  width: 100%;
  height: 50px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 25px;
  font-size: 16px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logout-btn:hover {
  background: #f8f9fa;
}

/* 图标字体 */
.iconfont {
  font-family: "iconfont";
}

.icon-service::before { content: "🛍️"; }
.icon-technician::before { content: "👩‍💼"; }
.icon-booking::before { content: "📅"; }
.icon-notification::before { content: "🔔"; }
.icon-profile::before { content: "👤"; }
.icon-history::before { content: "📋"; }
.icon-favorite::before { content: "❤️"; }
.icon-review::before { content: "⭐"; }
.icon-service-support::before { content: "🎧"; }
.icon-help::before { content: "❓"; }
.icon-info::before { content: "ℹ️"; }
.icon-arrow-right::before { content: "▶️"; }
</style>