<template>
  <view class="write-review">
    <!-- 导航栏 -->
    <beauty-navbar 
      title="写评价" 
      :show-back="true"
    ></beauty-navbar>
    
    <!-- 页面内容 -->
    <view class="content" style="margin-top: 88px;">
      <!-- 预约信息 -->
      <view class="booking-info" v-if="bookingInfo">
        <view class="service-info">
          <image :src="bookingInfo.serviceImage" class="service-image" mode="aspectFill"></image>
          <view class="service-detail">
            <text class="service-name">{{ bookingInfo.serviceName }}</text>
            <text class="technician-name">技师：{{ bookingInfo.technicianName }}</text>
            <text class="booking-date">{{ formatDate(bookingInfo.bookingDate) }}</text>
          </view>
        </view>
      </view>
      
      <!-- 评价表单 -->
      <view class="review-form">
        <!-- 服务评分 -->
        <view class="form-section">
          <view class="section-title">服务评分</view>
          <view class="rating-container">
            <view class="rating-item">
              <text class="rating-label">服务质量</text>
              <view class="rating-stars">
                <text 
                  class="star" 
                  v-for="i in 5" 
                  :key="'service-' + i"
                  :class="{ active: i <= form.serviceRating }"
                  @click="setServiceRating(i)"
                >★</text>
              </view>
            </view>
            
            <view class="rating-item">
              <text class="rating-label">技师专业</text>
              <view class="rating-stars">
                <text 
                  class="star" 
                  v-for="i in 5" 
                  :key="'technician-' + i"
                  :class="{ active: i <= form.technicianRating }"
                  @click="setTechnicianRating(i)"
                >★</text>
              </view>
            </view>
            
            <view class="rating-item">
              <text class="rating-label">环境卫生</text>
              <view class="rating-stars">
                <text 
                  class="star" 
                  v-for="i in 5" 
                  :key="'environment-' + i"
                  :class="{ active: i <= form.environmentRating }"
                  @click="setEnvironmentRating(i)"
                >★</text>
              </view>
            </view>
          </view>
          
          <view class="overall-rating">
            <text class="overall-label">综合评分</text>
            <text class="overall-score">{{ overallRating }}</text>
            <view class="overall-stars">
              <text 
                class="star" 
                v-for="i in 5" 
                :key="'overall-' + i"
                :class="{ active: i <= Math.floor(overallRating) }"
              >★</text>
            </view>
          </view>
        </view>
        
        <!-- 评价内容 -->
        <view class="form-section">
          <view class="section-title">评价内容</view>
          <textarea 
            class="review-textarea"
            v-model="form.content"
            placeholder="请分享您的服务体验，您的评价对其他用户很有帮助"
            maxlength="500"
            :auto-height="true"
          ></textarea>
          <view class="word-count">{{ form.content.length }}/500</view>
        </view>
        
        <!-- 上传图片 -->
        <view class="form-section">
          <view class="section-title">上传图片</view>
          <view class="upload-container">
            <view class="upload-list">
              <view 
                class="upload-item" 
                v-for="(image, index) in form.images" 
                :key="index"
              >
                <image :src="image" mode="aspectFill" @click="previewImage(index)"></image>
                <view class="delete-btn" @click="deleteImage(index)">
                  <text class="delete-icon">×</text>
                </view>
              </view>
              
              <view 
                class="upload-btn" 
                v-if="form.images.length < 9"
                @click="chooseImage"
              >
                <text class="upload-icon">+</text>
                <text class="upload-text">添加图片</text>
              </view>
            </view>
            <view class="upload-tip">最多上传9张图片</view>
          </view>
        </view>
        
        <!-- 标签选择 -->
        <view class="form-section">
          <view class="section-title">服务标签</view>
          <view class="tags-container">
            <view 
              class="tag-item" 
              v-for="tag in availableTags" 
              :key="tag"
              :class="{ active: form.tags.includes(tag) }"
              @click="toggleTag(tag)"
            >
              <text>{{ tag }}</text>
            </view>
          </view>
        </view>
        
        <!-- 匿名评价 -->
        <view class="form-section">
          <view class="anonymous-option">
            <checkbox 
              :checked="form.isAnonymous" 
              @change="toggleAnonymous"
              color="#ff6b9d"
            ></checkbox>
            <text class="anonymous-text">匿名评价</text>
          </view>
        </view>
      </view>
      
      <!-- 提交按钮 -->
      <view class="submit-section">
        <button 
          class="submit-btn" 
          :class="{ disabled: !canSubmit }"
          @click="submitReview"
          :disabled="!canSubmit"
        >
          提交评价
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { beautyApi } from '@/api/beauty/mock.js'

export default {
  name: 'WriteReview',
  
  data() {
    return {
      bookingId: null,
      bookingInfo: null,
      
      form: {
        serviceRating: 5,
        technicianRating: 5,
        environmentRating: 5,
        content: '',
        images: [],
        tags: [],
        isAnonymous: false
      },
      
      availableTags: [
        '服务专业', '技师耐心', '环境舒适', '效果显著', 
        '价格合理', '预约方便', '服务周到', '值得推荐'
      ],
      
      submitting: false
    }
  },
  
  computed: {
    overallRating() {
      const total = this.form.serviceRating + this.form.technicianRating + this.form.environmentRating
      return (total / 3).toFixed(1)
    },
    
    canSubmit() {
      return this.form.content.trim().length > 0 && !this.submitting
    }
  },
  
  onLoad(options) {
    if (options.bookingId) {
      this.bookingId = parseInt(options.bookingId)
      this.loadBookingInfo()
    } else {
      uni.showToast({
        title: '预约信息不存在',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  },
  
  methods: {
    async loadBookingInfo() {
      try {
        const response = await beautyApi.getBookingDetail(this.bookingId)
        if (response.code === 200) {
          this.bookingInfo = response.data
        } else {
          throw new Error(response.message)
        }
      } catch (error) {
        console.error('加载预约信息失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    },
    
    setServiceRating(rating) {
      this.form.serviceRating = rating
    },
    
    setTechnicianRating(rating) {
      this.form.technicianRating = rating
    },
    
    setEnvironmentRating(rating) {
      this.form.environmentRating = rating
    },
    
    chooseImage() {
      const remainingCount = 9 - this.form.images.length
      
      uni.chooseImage({
        count: remainingCount,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.form.images = [...this.form.images, ...res.tempFilePaths]
        }
      })
    },
    
    previewImage(index) {
      uni.previewImage({
        urls: this.form.images,
        current: this.form.images[index]
      })
    },
    
    deleteImage(index) {
      this.form.images.splice(index, 1)
    },
    
    toggleTag(tag) {
      const index = this.form.tags.indexOf(tag)
      if (index > -1) {
        this.form.tags.splice(index, 1)
      } else {
        this.form.tags.push(tag)
      }
    },
    
    toggleAnonymous(e) {
      this.form.isAnonymous = e.detail.value
    },
    
    async submitReview() {
      if (!this.canSubmit) return
      
      this.submitting = true
      
      try {
        const reviewData = {
          bookingId: this.bookingId,
          serviceRating: this.form.serviceRating,
          technicianRating: this.form.technicianRating,
          environmentRating: this.form.environmentRating,
          overallRating: parseFloat(this.overallRating),
          content: this.form.content.trim(),
          images: this.form.images,
          tags: this.form.tags,
          isAnonymous: this.form.isAnonymous
        }
        
        const response = await beautyApi.submitReview(reviewData)
        
        if (response.code === 200) {
          uni.showToast({
            title: '评价成功',
            icon: 'success'
          })
          
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          throw new Error(response.message)
        }
      } catch (error) {
        console.error('提交评价失败:', error)
        uni.showToast({
          title: error.message || '提交失败',
          icon: 'none'
        })
      } finally {
        this.submitting = false
      }
    },
    
    formatDate(dateString) {
      const date = new Date(dateString)
      return date.getFullYear() + '年' + 
             String(date.getMonth() + 1).padStart(2, '0') + '月' + 
             String(date.getDate()).padStart(2, '0') + '日'
    }
  }
}
</script>

<style lang="scss" scoped>
.write-review {
  background: #f5f5f5;
  min-height: 100vh;
}

.content {
  padding-bottom: 100px;
}

/* 预约信息 */
.booking-info {
  background: #fff;
  padding: 15px;
  margin-bottom: 10px;
  
  .service-info {
    display: flex;
    align-items: center;
    
    .service-image {
      width: 80px;
      height: 80px;
      border-radius: 8px;
      margin-right: 15px;
    }
    
    .service-detail {
      flex: 1;
      
      .service-name {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 5px;
      }
      
      .technician-name {
        font-size: 14px;
        color: #666;
        display: block;
        margin-bottom: 5px;
      }
      
      .booking-date {
        font-size: 12px;
        color: #999;
      }
    }
  }
}

/* 表单区域 */
.review-form {
  .form-section {
    background: #fff;
    padding: 20px 15px;
    margin-bottom: 10px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 15px;
    }
  }
}

/* 评分区域 */
.rating-container {
  .rating-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    
    .rating-label {
      font-size: 14px;
      color: #333;
    }
    
    .rating-stars {
      .star {
        font-size: 20px;
        color: #ddd;
        margin-left: 5px;
        
        &.active {
          color: #FFD700;
        }
      }
    }
  }
}

.overall-rating {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .overall-label {
    font-size: 14px;
    color: #333;
    font-weight: bold;
  }
  
  .overall-score {
    font-size: 18px;
    font-weight: bold;
    color: #ff6b9d;
    margin-right: 10px;
  }
  
  .overall-stars {
    .star {
      font-size: 16px;
      color: #ddd;
      margin-left: 2px;
      
      &.active {
        color: #FFD700;
      }
    }
  }
}

/* 评价内容 */
.review-textarea {
  width: 100%;
  min-height: 100px;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  background: #fff;
  resize: none;
}

.word-count {
  text-align: right;
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

/* 上传图片 */
.upload-container {
  .upload-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    
    .upload-item {
      position: relative;
      width: 80px;
      height: 80px;
      
      image {
        width: 100%;
        height: 100%;
        border-radius: 8px;
      }
      
      .delete-btn {
        position: absolute;
        top: -5px;
        right: -5px;
        width: 20px;
        height: 20px;
        background: #ff4d4f;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .delete-icon {
          color: #fff;
          font-size: 14px;
          line-height: 1;
        }
      }
    }
    
    .upload-btn {
      width: 80px;
      height: 80px;
      border: 1px dashed #d9d9d9;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #999;
      
      .upload-icon {
        font-size: 24px;
        margin-bottom: 5px;
      }
      
      .upload-text {
        font-size: 12px;
      }
    }
  }
  
  .upload-tip {
    font-size: 12px;
    color: #999;
    margin-top: 10px;
  }
}

/* 标签选择 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  
  .tag-item {
    padding: 8px 16px;
    background: #f5f5f5;
    border-radius: 20px;
    font-size: 14px;
    color: #666;
    
    &.active {
      background: #ff6b9d;
      color: #fff;
    }
  }
}

/* 匿名选项 */
.anonymous-option {
  display: flex;
  align-items: center;
  
  .anonymous-text {
    margin-left: 10px;
    font-size: 14px;
    color: #333;
  }
}

/* 提交按钮 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 15px;
  border-top: 1px solid #e0e0e0;
  
  .submit-btn {
    width: 100%;
    height: 45px;
    background: #ff6b9d;
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    
    &.disabled {
      background: #ccc;
      color: #999;
    }
  }
}
</style> 