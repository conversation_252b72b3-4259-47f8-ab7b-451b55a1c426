<template>
	<view class="beauty-home">
		<!-- 统一导航栏 -->
		<beauty-navbar 
			title="美丽时光" 
			:show-back="false"
			:show-search="true"
			:show-notification="true"
			@search="showSearch"
			@notification="showNotifications"
		></beauty-navbar>

		<!-- 加载指示器 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-spinner"></view>
			<text class="loading-text">加载中...</text>
		</view>

		<!-- 主要内容 -->
		<view v-else class="main-content">
			<!-- 轮播图 -->
			<view class="banner-section" style="margin-top: 88px;">
				<swiper 
					class="banner-swiper" 
					:indicator-dots="true" 
					:autoplay="true" 
					:interval="3000" 
					:duration="500"
					indicator-color="rgba(255, 255, 255, 0.5)"
					indicator-active-color="#fff"
				>
					<swiper-item v-for="(banner, index) in bannerList" :key="index">
						<view class="banner-item">
							<image 
								:src="banner.image" 
								mode="aspectFill"
								class="banner-image"
								@error="handleImageError"
							></image>
							<view class="banner-overlay">
								<view class="banner-content">
									<text class="banner-title">{{banner.title}}</text>
									<text class="banner-desc">{{banner.description}}</text>
								</view>
							</view>
						</view>
					</swiper-item>
				</swiper>
			</view>

			<!-- 服务分类 -->
			<view class="service-category">
				<view class="section-header">
					<text class="section-title">热门服务</text>
					<text class="section-more" @click="showMore">更多 ></text>
				</view>
				<view class="category-grid">
					<view 
						class="category-item" 
						v-for="category in serviceCategories" 
						:key="category.id"
						@click="onCategoryClick(category)"
					>
						<view class="category-icon">
							<text class="category-emoji">{{category.icon}}</text>
						</view>
						<text class="category-name">{{category.name}}</text>
					</view>
				</view>
			</view>

			<!-- 套餐卡商城入口 -->
			<view class="card-mall-entry" @click="goToCardMall">
				<image class="card-mall-img" src="/static/beauty/banner1.jpg" mode="aspectFill" />
				<view class="card-mall-info">
					<text class="card-mall-title">套餐卡商城</text>
					<text class="card-mall-desc">超值套餐卡，限时特惠</text>
				</view>
				<view class="card-mall-arrow">&gt;</view>
			</view>

			<!-- 推荐服务 -->
			<view class="recommended-services">
				<view class="section-header">
					<text class="section-title">精选服务</text>
					<text class="section-more" @click="showMore">查看全部 ></text>
				</view>
				<view class="services-list">
					<view 
						class="service-card" 
						v-for="service in recommendedServices" 
						:key="service.id"
						@click="onServiceClick(service)"
					>
						<view class="service-image">
							<image :src="service.image" mode="aspectFill"></image>
							<view class="service-badge" v-if="service.isHot">
								<text>热门</text>
							</view>
						</view>
						<view class="service-info">
							<text class="service-name">{{service.name}}</text>
							<text class="service-desc">{{service.description}}</text>
							<view class="service-meta">
								<text class="service-price">￥{{service.price}}</text>
								<text class="service-duration">{{service.duration}}分钟</text>
							</view>
							<view class="service-rating">
								<text class="rating-stars">⭐⭐⭐⭐⭐</text>
								<text class="rating-text">{{service.rating}} ({{service.reviews}}人评价)</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 优秀技师 -->
			<view class="excellent-technicians">
				<view class="section-header">
					<text class="section-title">明星技师</text>
					<text class="section-more" @click="showMoreTechnicians">查看更多 ></text>
				</view>
				<scroll-view class="technicians-scroll" scroll-x="true">
					<view class="technicians-list">
						<view 
							class="technician-card" 
							v-for="technician in excellentTechnicians" 
							:key="technician.id"
							@click="onTechnicianClick(technician)"
						>
							<view class="technician-avatar">
								<image :src="technician.avatar" mode="aspectFill"></image>
								<view class="technician-badge" v-if="technician.isTop">
									<text>金牌</text>
								</view>
							</view>
							<view class="technician-info">
								<text class="technician-name">{{technician.name}}</text>
								<text class="technician-title">{{technician.title}}</text>
								<view class="technician-stats">
									<text class="stats-item">⭐{{technician.rating}}</text>
									<text class="stats-item">{{technician.experience}}年经验</text>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>

			<!-- 侧边栏预约按钮 -->
			<view class="side-booking">
				<view class="booking-btn" @click="onQuickBooking">
					<text class="booking-text">立即预约</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getHomeData } from '@/api/beauty/index.js'
import ErrorHandler from '@/utils/errorHandler'
import AuthUtils from '@/utils/auth'

export default {
	data() {
		return {
			// 加载状态
			loading: false,
			
			// 轮播图数据
			bannerList: [
				{
					id: 1,
					title: '专业美容护理',
					description: '焕发肌肤光彩，重现年轻活力',
					image: 'https://picsum.photos/400/200?random=81'
				},
				{
					id: 2,
					title: '精致美甲艺术',
					description: '指尖上的艺术，展现独特魅力',
					image: 'https://picsum.photos/400/200?random=82'
				},
				{
					id: 3,
					title: '专业美发造型',
					description: '打造完美造型，彰显个人风格',
					image: 'https://picsum.photos/400/200?random=83'
				}
			],
			
			// 服务分类
			serviceCategories: [
				{ id: 1, name: '面部护理', icon: '🧴', code: 'facial' },
				{ id: 2, name: '美甲服务', icon: '💅', code: 'nail' },
				{ id: 3, name: '美发造型', icon: '💇', code: 'hair' },
				{ id: 4, name: '美睫服务', icon: '👁️', code: 'eyelash' },
				{ id: 5, name: '身体护理', icon: '💆', code: 'body' },
				{ id: 6, name: '美容仪器', icon: '🔬', code: 'device' },
				{ id: 7, name: '纹绣服务', icon: '🎨', code: 'tattoo' },
				{ id: 8, name: '更多服务', icon: '➕', code: 'more' }
			],
			
			// 推荐服务
			recommendedServices: [
				{
					id: 1,
					name: '深层清洁面部护理',
					description: '专业深层清洁，去除黑头粉刺，收缩毛孔',
					price: 198,
					duration: 90,
					rating: 4.8,
					reviews: 256,
					image: '/static/beauty/service1.jpg',
					isHot: true
				},
				{
					id: 2,
					name: '日式美甲套餐',
					description: '精致日式美甲，持久不脱落，多种款式可选',
					price: 88,
					duration: 60,
					rating: 4.9,
					reviews: 189,
					image: '/static/beauty/service2.jpg',
					isHot: false
				},
				{
					id: 3,
					name: '韩式半永久眉毛',
					description: '自然韩式眉型，专业纹绣师操作',
					price: 688,
					duration: 120,
					rating: 4.7,
					reviews: 98,
					image: '/static/beauty/service3.jpg',
					isHot: true
				}
			],
			
			// 优秀技师
			excellentTechnicians: [
				{
					id: 1,
					name: '李美美',
					title: '首席美容师',
					rating: 4.9,
					experience: 8,
					avatar: '/static/beauty/technician1.jpg',
					isTop: true
				},
				{
					id: 2,
					name: '张小雅',
					title: '高级美甲师',
					rating: 4.8,
					experience: 5,
					avatar: '/static/beauty/technician2.jpg',
					isTop: false
				},
				{
					id: 3,
					name: '王丽丽',
					title: '资深美发师',
					rating: 4.9,
					experience: 10,
					avatar: '/static/beauty/technician3.jpg',
					isTop: true
				},
				{
					id: 4,
					name: '陈晓敏',
					title: '纹绣师',
					rating: 4.7,
					experience: 6,
					avatar: '/static/beauty/technician4.jpg',
					isTop: false
				}
			]
		}
	},
	
	onLoad() {
		console.log('美容预约首页加载完成')
		this.loadHomeData()
	},
	
	methods: {
		// 加载首页数据
		async loadHomeData() {
			this.loading = true
			try {
				// 使用新的API接口
				const response = await getHomeData()
				
				if (response.code === 200) {
					const data = response.data
					
					// 更新轮播图
					if (data.banners) {
						this.bannerList = data.banners.map(banner => ({
							id: banner.id,
							title: banner.title,
							description: banner.title, // 使用title作为描述
							image: banner.image
						}))
					}
					
					// 更新服务分类
					if (data.categories) {
						this.serviceCategories = data.categories.map(category => ({
							id: category.id,
							name: category.name,
							icon: this.getCategoryIcon(category.name), // 转换为emoji
							code: category.name.toLowerCase()
						}))
					}
					
					// 更新推荐服务（使用热门服务）
					if (data.hot_services) {
						this.recommendedServices = data.hot_services.map(service => ({
							id: service.id,
							name: service.name,
							description: service.subtitle || service.description,
							price: service.price,
							duration: service.duration,
							rating: service.rating_avg || 0,
							reviews: service.rating_count || 0,
							image: service.images && service.images[0] || '/static/beauty/default-service.jpg',
							isHot: service.is_hot
						}))
					}
					
					// 更新明星技师
					if (data.featured_technicians) {
						console.log('首页明星技师数据:', data.featured_technicians)
						this.excellentTechnicians = data.featured_technicians.map(technician => ({
							id: technician.id,
							user_id: technician.user_id, // 添加user_id字段
							name: technician.name,
							avatar: technician.avatar || '/static/beauty/default-avatar.jpg',
							title: technician.level,
							rating: technician.rating_avg || 0,
							experience: technician.experience || 0,
							isTop: technician.is_featured
						}))
						console.log('处理后的明星技师数据:', this.excellentTechnicians)
					}
					
				} else {
					console.error('获取首页数据失败:', response)
					ErrorHandler.handleBusinessError(response, '加载首页数据')
				}
			} catch (error) {
				console.error('加载首页数据失败:', error)
				ErrorHandler.handleNetworkError(error, '加载首页数据')
			} finally {
				this.loading = false
			}
		},
		
		// 根据分类名称获取对应的emoji图标
		getCategoryIcon(categoryName) {
			const iconMap = {
				'面部护理': '🧴',
				'身体护理': '💆',
				'美甲美睫': '💅',
				'美发造型': '💇',
				'美容仪器': '🔬',
				'纹绣服务': '🎨'
			}
			return iconMap[categoryName] || '✨'
		},
		
		// 搜索功能
		showSearch() {
			ErrorHandler.safeToast({
				title: '搜索功能开发中',
				icon: 'none'
			}, '搜索功能开发中')
		},
		
		// 通知功能
		showNotifications() {
			ErrorHandler.simpleNavigateDirect('/pages/beauty/user/notification')
		},
		
		// 更多功能
		showMore() {
			uni.switchTab({
				url: '/pages/beauty/service/list'
			})
		},
		
		// 查看更多技师
		showMoreTechnicians() {
			ErrorHandler.simpleNavigateDirect('/pages/beauty/technician/list')
		},
		
		// 分类点击事件
		onCategoryClick(category) {
			console.log('点击分类:', category)
			// 由于switchTab不支持参数传递，使用本地存储
			ErrorHandler.safeStorage(() => {
				uni.setStorageSync('selectedCategoryId', category.id)
				uni.setStorageSync('selectedCategoryName', category.name)
			}, '存储分类信息')
			uni.switchTab({
				url: '/pages/beauty/service/list'
			})
		},
		
		// 服务点击事件
		onServiceClick(service) {
			console.log('点击服务:', service)
			// 直接使用原生方法测试，路由守卫会自动检查登录状态
			uni.navigateTo({
				url: `/pages/beauty/service/detail?id=${service.id}`
			})
		},
		
		// 技师点击事件
		onTechnicianClick(technician) {
			console.log('点击技师:', technician)
			console.log('技师id:', technician.id)
			console.log('技师user_id:', technician.user_id)
			
			// 检查技师数据是否完整
			if (!technician || !technician.user_id) {
				console.error('技师数据不完整:', technician)
				uni.showToast({
					title: '技师信息不完整',
					icon: 'none'
				})
				return
			}
			
			// 使用user_id而不是id来获取技师详情
			const technicianUserId = technician.user_id
			console.log('使用的技师ID:', technicianUserId)
			ErrorHandler.simpleNavigateDirect(`/pages/beauty/technician/detail?id=${technicianUserId}`)
		},
		
		// 快速预约
		onQuickBooking() {
			console.log('快速预约')
			// 使用switchTab跳转到服务页面（tabbar页面）
			uni.switchTab({
				url: '/pages/beauty/service/list'
			})
		},
		
		// 技师入口
		goToTechnicianEntry() {
			ErrorHandler.simpleNavigateDirect('/pages/beauty/technician-entry')
		},

		// 套餐卡商城入口
		goToCardMall() {
			uni.navigateTo({
				url: '/pages/beauty/card/index'
			})
		},

		// 图片加载错误处理
		handleImageError(e) {
			console.log('图片加载失败:', e)
			// 可以在这里设置默认图片
			e.target.src = '/static/beauty/default-image.jpg'
		}
	}
}
</script>

<style lang="scss" scoped>
.beauty-home {
	background-color: #f8f9fa;
	min-height: 100vh;
}

/* 页面内容区域 */
.beauty-home {
	padding-top: 0;
}

/* 加载指示器 */
.loading-container {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(255, 255, 255, 0.9);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	z-index: 999;
	
	.loading-spinner {
		border: 4px solid #f3f3f3;
		border-top: 4px solid #ff6b9d;
		border-radius: 50%;
		width: 40px;
		height: 40px;
		animation: spin 1s linear infinite;
		margin-bottom: 10px;
	}
	
	.loading-text {
		font-size: 16px;
		color: #666;
	}
}

/* 轮播图 */
.banner-section {
	margin: 0 15px 20px;
	border-radius: 12px;
	overflow: hidden;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	
	.banner-swiper {
		height: 180px;
		
		.banner-item {
			position: relative;
			height: 100%;
			
			.banner-image {
				width: 100%;
				height: 100%;
			}
			
			.banner-overlay {
				position: absolute;
				bottom: 0;
				left: 0;
				right: 0;
				background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
				padding: 20px;
				
				.banner-content {
					.banner-title {
						display: block;
						font-size: 18px;
						font-weight: bold;
						color: white;
						margin-bottom: 5px;
					}
					
					.banner-desc {
						font-size: 14px;
						color: rgba(255, 255, 255, 0.9);
					}
				}
			}
		}
	}
}

/* 公共样式 */
.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 20px 15px;
	
	.section-title {
		font-size: 18px;
		font-weight: bold;
		color: #333;
	}
	
	.section-more {
		font-size: 14px;
		color: #ff6b9d;
	}
}

/* 服务分类 */
.service-category {
	background: white;
	margin: 0 15px 20px;
	border-radius: 12px;
	padding: 20px 0;
	
	.category-grid {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 20px;
		padding: 0 20px;
		
		.category-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			
			.category-icon {
				width: 50px;
				height: 50px;
				background: linear-gradient(135deg, #ff6b9d, #ff8e9b);
				border-radius: 25px;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 8px;
				
				.category-emoji {
					font-size: 24px;
				}
			}
			
			.category-name {
				font-size: 12px;
				color: #666;
				text-align: center;
			}
		}
	}
}

/* 推荐服务 */
.recommended-services {
	background: white;
	margin: 0 15px 20px;
	border-radius: 12px;
	padding: 20px 0;
	
	.services-list {
		.service-card {
			display: flex;
			padding: 15px 20px;
			border-bottom: 1px solid #f0f0f0;
			
			&:last-child {
				border-bottom: none;
			}
			
			.service-image {
				position: relative;
				width: 80px;
				height: 80px;
				border-radius: 8px;
				overflow: hidden;
				margin-right: 15px;
				
				image {
					width: 100%;
					height: 100%;
				}
				
				.service-badge {
					position: absolute;
					top: 5px;
					right: 5px;
					background: #ff6b9d;
					color: white;
					font-size: 10px;
					padding: 2px 6px;
					border-radius: 10px;
				}
			}
			
			.service-info {
				flex: 1;
				
				.service-name {
					display: block;
					font-size: 16px;
					font-weight: bold;
					color: #333;
					margin-bottom: 5px;
				}
				
				.service-desc {
					display: block;
					font-size: 12px;
					color: #999;
					margin-bottom: 8px;
					line-height: 1.4;
				}
				
				.service-meta {
					display: flex;
					align-items: center;
					gap: 15px;
					margin-bottom: 5px;
					
					.service-price {
						font-size: 18px;
						font-weight: bold;
						color: #ff6b9d;
					}
					
					.service-duration {
						font-size: 12px;
						color: #666;
					}
				}
				
				.service-rating {
					display: flex;
					align-items: center;
					gap: 5px;
					
					.rating-stars {
						font-size: 12px;
					}
					
					.rating-text {
						font-size: 12px;
						color: #666;
					}
				}
			}
		}
	}
}

/* 优秀技师 */
.excellent-technicians {
	background: white;
	margin: 0 15px 20px;
	border-radius: 12px;
	padding: 20px 0;
	
	.technicians-scroll {
		white-space: nowrap;
		
		.technicians-list {
			display: flex;
			gap: 15px;
			padding: 0 20px;
			
			.technician-card {
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 100px;
				
				.technician-avatar {
					position: relative;
					width: 60px;
					height: 60px;
					border-radius: 30px;
					overflow: hidden;
					margin-bottom: 8px;
					
					image {
						width: 100%;
						height: 100%;
					}
					
					.technician-badge {
						position: absolute;
						top: -5px;
						right: -5px;
						background: #ffd700;
						color: #333;
						font-size: 8px;
						padding: 2px 4px;
						border-radius: 8px;
					}
				}
				
				.technician-info {
					text-align: center;
					
					.technician-name {
						display: block;
						font-size: 14px;
						font-weight: bold;
						color: #333;
						margin-bottom: 2px;
					}
					
					.technician-title {
						display: block;
						font-size: 12px;
						color: #666;
						margin-bottom: 5px;
					}
					
					.technician-stats {
						display: flex;
						flex-direction: column;
						gap: 2px;
						
						.stats-item {
							font-size: 10px;
							color: #999;
						}
					}
				}
			}
		}
	}
}

/* 侧边栏预约按钮 */
.side-booking {
	position: fixed;
	right: 20px;
	top: 50%;
	transform: translateY(-50%);
	z-index: 100;
	
	.booking-btn {
		background: linear-gradient(135deg, #ff6b9d, #ff8e9b);
		color: white;
		border-radius: 25px;
		padding: 12px 8px;
		box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3);
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		writing-mode: vertical-lr;
		text-orientation: mixed;
		min-height: 80px;
		width: 40px;
		
		.booking-text {
			font-size: 14px;
			font-weight: bold;
			color: white;
			letter-spacing: 2px;
		}
		
		&:hover {
			transform: scale(1.05);
			transition: transform 0.2s ease;
		}
	}
}

/* 技师入口按钮 */
.technician-entry-btn {
	position: fixed;
	bottom: 20px;
	left: 20px;
	right: 20px;
	z-index: 100;
	
	.entry-btn {
		background: linear-gradient(135deg, #667eea, #764ba2);
		color: white;
		border: none;
		border-radius: 22px;
		height: 45px;
		font-size: 15px;
		font-weight: 500;
		box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
		
		.entry-text {
			color: white;
		}
	}
}

/* 套餐卡商城入口 */
.card-mall-entry {
	display: flex;
	align-items: center;
	background: #fff0f6;
	border-radius: 12px;
	margin: 20px 15px;
	padding: 12px 16px;
	box-shadow: 0 2px 8px rgba(255, 105, 180, 0.06);
	cursor: pointer;
}
.card-mall-img {
	width: 60px;
	height: 60px;
	border-radius: 8px;
	object-fit: cover;
	margin-right: 16px;
}
.card-mall-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
}
.card-mall-title {
	font-size: 18px;
	font-weight: bold;
	color: #e43d33;
}
.card-mall-desc {
	color: #888;
	font-size: 14px;
	margin-top: 2px;
}
.card-mall-arrow {
	font-size: 22px;
	color: #e43d33;
	margin-left: 8px;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
</style>