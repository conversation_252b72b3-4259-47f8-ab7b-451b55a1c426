<template>
  <view class="technician-list">
    <!-- 统一导航栏 -->
    <beauty-navbar 
      title="技师列表" 
      :show-back="true"
    ></beauty-navbar>
    
    <!-- 筛选栏 -->
    <view class="filter-bar" style="margin-top: 88px;">
      <view class="filter-item" @click="showServiceModal" v-if="!serviceId">
        <text>{{ selectedService ? selectedService.name : '选择服务' }}</text>
        <text class="icon-text">▼</text>
      </view>
      <view class="filter-item" @click="showSortModal">
        <text>排序</text>
        <text class="icon-text">▼</text>
      </view>
    </view>
    
    <!-- 技师列表 -->
    <view class="technician-content">
      <view class="technician-grid">
        <technician-card 
          v-for="technician in technicianList" 
          :key="technician.id"
          :technician="technician"
          @click="goToTechnicianDetail"
          @collect="handleCollect"
        />
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="technicianList.length === 0 && !loading">
        <view class="empty-content">
          <text class="empty-icon">📋</text>
          <text class="empty-text">暂无技师</text>
          <text class="empty-desc">暂时没有找到相关技师</text>
        </view>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-state" v-if="loading">
        <uni-icons type="spinner-cycle" size="24" color="#FFB6C1"></uni-icons>
        <text>加载中...</text>
      </view>
    </view>
    
    <!-- 服务选择弹窗 -->
    <uni-popup ref="servicePopup" type="bottom" background-color="#fff">
      <view class="service-modal">
        <view class="modal-header">
          <text class="modal-title">选择服务</text>
          <text class="close-icon" @click="closeServiceModal">✕</text>
        </view>
        <view class="service-options">
          <view 
            class="service-option" 
            :class="{ active: selectedServiceId === null }"
            @click="selectService(null)"
          >
            <text>全部技师</text>
            <text class="check-icon" v-if="selectedServiceId === null">✓</text>
          </view>
          <view 
            class="service-option" 
            :class="{ active: selectedServiceId === service.id }"
            v-for="service in serviceList" 
            :key="service.id"
            @click="selectService(service)"
          >
            <text>{{ service.name }}</text>
            <text class="check-icon" v-if="selectedServiceId === service.id">✓</text>
          </view>
        </view>
      </view>
    </uni-popup>
    
    <!-- 排序弹窗 -->
    <uni-popup ref="sortPopup" type="bottom" background-color="#fff">
      <view class="sort-modal">
        <view class="modal-header">
          <text class="modal-title">排序方式</text>
          <text class="close-icon" @click="closeSortModal">✕</text>
        </view>
        <view class="sort-options">
          <view 
            class="sort-option" 
            :class="{ active: currentSort === option.value }"
            v-for="option in sortOptions" 
            :key="option.value"
            @click="selectSort(option.value)"
          >
            <text>{{ option.label }}</text>
            <text class="check-icon" v-if="currentSort === option.value">✓</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { getTechnicianList, getServiceList } from '@/api/beauty/index.js'
import { collectItem } from '@/api/collect.js'
import TechnicianCard from '@/components/beauty/technician-card.vue'

export default {
  name: 'TechnicianList',
  components: {
    TechnicianCard
  },
  
  data() {
    return {
      loading: false,
      technicianList: [],
      serviceList: [],
      serviceId: null,
      selectedServiceId: null,
      selectedService: null,
      currentSort: 'default',
      sortOptions: [
        { label: '默认排序', value: 'default' },
        { label: '评分从高到低', value: 'rating_desc' },
        { label: '经验从高到低', value: 'experience_desc' },
        { label: '评价数量', value: 'review_count' },
        { label: '指定费用从低到高', value: 'price_asc' },
        { label: '指定费用从高到低', value: 'price_desc' }
      ]
    }
  },
  
  onLoad(options) {
    if (options.serviceId) {
      this.serviceId = parseInt(options.serviceId)
      this.selectedServiceId = this.serviceId
    }
    this.initData()
  },
  
  onPullDownRefresh() {
    this.initData().then(() => {
      uni.stopPullDownRefresh()
    })
  },
  
  methods: {
    async initData() {
      this.loading = true
      try {
        const [techniciansRes, servicesRes] = await Promise.all([
          getTechnicianList({
            page: 1,
            page_size: 50,
            service_id: this.selectedServiceId || null,
            sort: this.currentSort
          }),
          getServiceList({
            page: 1,
            page_size: 100
          })
        ])
        
        console.log('技师列表API响应:', techniciansRes.data)
        console.log('技师列表数据:', techniciansRes.data.list)
        this.technicianList = this.sortTechnicians(techniciansRes.data.list || [])
        this.serviceList = servicesRes.data.list || []
        
        // 设置选中的服务
        if (this.selectedServiceId) {
          this.selectedService = this.serviceList.find(s => s.id === this.selectedServiceId)
        }
        
      } catch (error) {
        console.error('加载数据失败:', error)
        uni.showToast({
          title: '加载数据失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    sortTechnicians(technicians) {
      let sortedTechnicians = [...technicians]
      
      switch (this.currentSort) {
        case 'rating_desc':
          sortedTechnicians.sort((a, b) => (b.rating_avg || 0) - (a.rating_avg || 0))
          break
        case 'experience_desc':
          sortedTechnicians.sort((a, b) => (b.experience || 0) - (a.experience || 0))
          break
        case 'review_count':
          sortedTechnicians.sort((a, b) => (b.rating_count || 0) - (a.rating_count || 0))
          break
        case 'price_asc':
          sortedTechnicians.sort((a, b) => (a.extra_fee || 0) - (b.extra_fee || 0))
          break
        case 'price_desc':
          sortedTechnicians.sort((a, b) => (b.extra_fee || 0) - (a.extra_fee || 0))
          break
        default:
          // 默认排序保持原顺序
          break
      }
      
      return sortedTechnicians
    },
    
    showServiceModal() {
      this.$refs.servicePopup.open()
    },
    
    closeServiceModal() {
      this.$refs.servicePopup.close()
    },
    
    showSortModal() {
      this.$refs.sortPopup.open()
    },
    
    closeSortModal() {
      this.$refs.sortPopup.close()
    },
    
    selectService(service) {
      this.selectedService = service
      this.selectedServiceId = service ? service.id : null
      this.closeServiceModal()
      this.loadTechnicians()
    },
    
    selectSort(sortValue) {
      this.currentSort = sortValue
      this.closeSortModal()
      this.technicianList = this.sortTechnicians(this.technicianList)
    },
    
    async loadTechnicians() {
      this.loading = true
      try {
        const techniciansRes = await getTechnicianList({
          page: 1,
          page_size: 50,
          service_id: this.selectedServiceId || null,
          sort: this.currentSort
        })
        this.technicianList = this.sortTechnicians(techniciansRes.data.list || [])
      } catch (error) {
        console.error('加载技师失败:', error)
        uni.showToast({
          title: '加载技师失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    goToTechnicianDetail(technician) {
      console.log('技师卡片点击，技师数据:', technician)
      console.log('技师id:', technician.id)
      console.log('技师user_id:', technician.user_id)
      
      // 检查技师数据是否完整
      if (!technician || !technician.user_id) {
        console.error('技师数据不完整:', technician)
        uni.showToast({
          title: '技师信息不完整',
          icon: 'none'
        })
        return
      }
      
      const url = this.serviceId 
        ? `/pages/beauty/technician/detail?id=${technician.user_id}&serviceId=${this.serviceId}`
        : `/pages/beauty/technician/detail?id=${technician.user_id}`
      
      console.log('跳转到技师详情:', url)
      
      uni.navigateTo({
        url: url
      })
    },
    
    async handleCollect(technician) {
      try {
        const response = await collectItem({
          item_id: technician.id,
          item_type: 'technician',
          action: 'toggle'
        })
        
        if (response.code === 200) {
          uni.showToast({
            title: response.data.is_collected ? '收藏成功' : '取消收藏成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: response.message || '操作失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('收藏操作失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.technician-list {
  background: #f8f9fa;
  min-height: 100vh;
  
  .filter-bar {
    background: #fff;
    padding: 20rpx 30rpx;
    display: flex;
    justify-content: space-around;
    border-bottom: 1rpx solid #f0f0f0;
    
    .filter-item {
      display: flex;
      align-items: center;
      padding: 10rpx 20rpx;
      
      text {
        font-size: 28rpx;
        color: #333;
        margin-right: 10rpx;
      }
      
      .icon-text {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
  
  .technician-content {
    padding: 20rpx;
    
    .technician-grid {
      display: flex;
      flex-direction: column;
      gap: 20rpx;
    }
    
    .empty-state {
      padding: 100rpx 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      
      .empty-content {
        display: flex;
        flex-direction: column;
      align-items: center;
        
        .empty-icon {
          font-size: 80rpx;
          margin-bottom: 20rpx;
        }
        
        .empty-text {
          font-size: 32rpx;
          color: #333;
          margin-bottom: 10rpx;
        }
        
        .empty-desc {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
    
    .loading-state {
      padding: 40rpx 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      
      text {
        margin-top: 20rpx;
        font-size: 24rpx;
        color: #999;
      }
    }
  }
  
  .service-modal, .sort-modal {
    background: #fff;
    border-radius: 20rpx 20rpx 0 0;
    overflow: hidden;
    
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1rpx solid #f0f0f0;
      
      .modal-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
      
      .close-icon {
        font-size: 32rpx;
        color: #999;
        padding: 10rpx;
      }
    }
    
    .service-options, .sort-options {
      max-height: 600rpx;
      overflow-y: auto;
      
      .service-option, .sort-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx;
        border-bottom: 1rpx solid #f0f0f0;
        
        text {
          font-size: 28rpx;
          color: #333;
        }
        
        .check-icon {
          color: #FFB6C1;
        }
        
        &.active {
          background-color: rgba(255, 182, 193, 0.1);
          
          text {
            color: #FFB6C1;
            font-weight: bold;
          }
        }
      }
    }
  }
}
</style>
