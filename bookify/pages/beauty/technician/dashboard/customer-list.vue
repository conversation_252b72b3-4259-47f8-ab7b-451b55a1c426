<template>
	<view class="customer-list">
		<!-- 统一导航栏 -->
		<beauty-navbar 
			title="客户管理" 
			:show-back="true"
			:show-search="true"
			@search="toggleSearch"
		></beauty-navbar>
		
		<!-- 搜索框 -->
		<view class="search-section" v-if="showSearch" style="margin-top: 88px;">
			<view class="search-box">
				<text class="iconfont icon-search"></text>
				<input placeholder="搜索客户姓名或手机号" v-model="searchText" @input="handleSearch" />
			</view>
		</view>
		
		<!-- 统计卡片 -->
		<view class="stats-section" :style="!showSearch ? 'margin-top: 88px;' : ''">
			<view class="stats-row">
				<view class="stat-item">
					<view class="stat-value">{{ totalCustomers }}</view>
					<view class="stat-label">总客户数</view>
				</view>
				<view class="stat-item">
					<view class="stat-value">{{ activeCustomers }}</view>
					<view class="stat-label">活跃客户</view>
				</view>
				<view class="stat-item">
					<view class="stat-value">{{ newCustomers }}</view>
					<view class="stat-label">本月新增</view>
				</view>
			</view>
		</view>
		
		<!-- 客户列表 -->
		<view class="customer-section">
			<view class="section-title">客户列表</view>
			
			<view class="customer-list-container">
				<view class="customer-item" v-for="customer in filteredCustomers" :key="customer.id" @click="goToCustomerDetail(customer)">
					<view class="customer-avatar">
						<image :src="customer.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
					</view>
					<view class="customer-info">
						<view class="customer-name">{{ customer.name }}</view>
						<view class="customer-phone">{{ customer.phone }}</view>
						<view class="customer-tags">
							<text class="tag" :class="customer.level">{{ customer.levelText }}</text>
							<text class="tag service-count">{{ customer.serviceCount }}次服务</text>
						</view>
					</view>
					<view class="customer-stats">
						<view class="total-amount">¥{{ customer.totalAmount }}</view>
						<view class="last-visit">{{ customer.lastVisit }}</view>
					</view>
					<view class="arrow-icon">
						<text class="iconfont icon-arrow-right"></text>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-if="filteredCustomers.length === 0">
				<image src="/static/images/empty-customer.png" mode="aspectFit"></image>
				<text>暂无客户数据</text>
			</view>
		</view>
		
		<!-- 添加客户按钮 -->
		<view class="add-customer-btn" @click="showAddCustomer">
			<text class="iconfont icon-plus"></text>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'CustomerList',
		data() {
			return {
				showSearch: false,
				searchText: '',
				totalCustomers: 156,
				activeCustomers: 89,
				newCustomers: 12,
				customers: [
					{
						id: 1,
						name: '张小姐',
						phone: '138****8888',
						avatar: '/static/images/avatar1.png',
						level: 'vip',
						levelText: 'VIP',
						serviceCount: 15,
						totalAmount: 4280,
						lastVisit: '2024-01-15'
					},
					{
						id: 2,
						name: '李女士',
						phone: '139****9999',
						avatar: '/static/images/avatar2.png',
						level: 'gold',
						levelText: '金牌',
						serviceCount: 8,
						totalAmount: 2150,
						lastVisit: '2024-01-10'
					},
					{
						id: 3,
						name: '王小姐',
						phone: '136****6666',
						avatar: '/static/images/avatar3.png',
						level: 'silver',
						levelText: '银牌',
						serviceCount: 5,
						totalAmount: 1280,
						lastVisit: '2024-01-08'
					},
					{
						id: 4,
						name: '赵女士',
						phone: '137****7777',
						avatar: '/static/images/avatar4.png',
						level: 'normal',
						levelText: '普通',
						serviceCount: 2,
						totalAmount: 580,
						lastVisit: '2024-01-05'
					}
				]
			}
		},
		computed: {
			filteredCustomers() {
				if (!this.searchText) {
					return this.customers
				}
				return this.customers.filter(customer => 
					customer.name.includes(this.searchText) || 
					customer.phone.includes(this.searchText)
				)
			}
		},
		methods: {
			toggleSearch() {
				this.showSearch = !this.showSearch
			},
			handleSearch() {
				// 搜索逻辑
			},
			goToCustomerDetail(customer) {
				uni.navigateTo({
					url: `/pages/beauty/technician/customer-detail?id=${customer.id}`
				})
			},
			showAddCustomer() {
				uni.showToast({
					title: '添加客户功能开发中',
					icon: 'none'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.customer-list {
		background: #f5f5f5;
		min-height: 100vh;
	}
	
	.custom-navbar {
		background: linear-gradient(135deg, #FFB6C1 0%, #FF69B4 100%);
		padding-top: var(--status-bar-height);
		
		.navbar-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 88rpx;
			padding: 0 40rpx;
			
			.back-btn, .right-btn {
				width: 60rpx;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
				background: rgba(255, 255, 255, 0.2);
				
				.iconfont {
					font-size: 32rpx;
					color: white;
				}
			}
			
			.title {
				font-size: 36rpx;
				font-weight: bold;
				color: white;
			}
		}
	}
	
	.search-section {
		padding: 20rpx 40rpx;
		background: white;
		
		.search-box {
			display: flex;
			align-items: center;
			background: #f5f5f5;
			border-radius: 50rpx;
			padding: 20rpx 30rpx;
			
			.iconfont {
				font-size: 28rpx;
				color: #999;
				margin-right: 20rpx;
			}
			
			input {
				flex: 1;
				font-size: 28rpx;
				color: #333;
			}
		}
	}
	
	.stats-section {
		background: white;
		padding: 40rpx;
		margin-bottom: 20rpx;
		
		.stats-row {
			display: flex;
			justify-content: space-between;
			
			.stat-item {
				text-align: center;
				
				.stat-value {
					font-size: 40rpx;
					font-weight: bold;
					color: #FF69B4;
					margin-bottom: 10rpx;
				}
				
				.stat-label {
					font-size: 24rpx;
					color: #666;
				}
			}
		}
	}
	
	.customer-section {
		background: white;
		padding: 40rpx;
		
		.section-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 30rpx;
		}
		
		.customer-list-container {
			.customer-item {
				display: flex;
				align-items: center;
				padding: 30rpx 0;
				border-bottom: 1rpx solid #f0f0f0;
				
				&:last-child {
					border-bottom: none;
				}
				
				.customer-avatar {
					width: 80rpx;
					height: 80rpx;
					border-radius: 50%;
					overflow: hidden;
					margin-right: 20rpx;
					
					image {
						width: 100%;
						height: 100%;
					}
				}
				
				.customer-info {
					flex: 1;
					
					.customer-name {
						font-size: 30rpx;
						font-weight: bold;
						color: #333;
						margin-bottom: 10rpx;
					}
					
					.customer-phone {
						font-size: 24rpx;
						color: #666;
						margin-bottom: 10rpx;
					}
					
					.customer-tags {
						display: flex;
						gap: 10rpx;
						
						.tag {
							padding: 4rpx 12rpx;
							border-radius: 20rpx;
							font-size: 20rpx;
							
							&.vip {
								background: #FFD700;
								color: #fff;
							}
							
							&.gold {
								background: #FFA500;
								color: #fff;
							}
							
							&.silver {
								background: #C0C0C0;
								color: #fff;
							}
							
							&.normal {
								background: #f0f0f0;
								color: #666;
							}
							
							&.service-count {
								background: #E8F4FD;
								color: #1890FF;
							}
						}
					}
				}
				
				.customer-stats {
					text-align: right;
					margin-right: 20rpx;
					
					.total-amount {
						font-size: 28rpx;
						font-weight: bold;
						color: #FF69B4;
						margin-bottom: 10rpx;
					}
					
					.last-visit {
						font-size: 24rpx;
						color: #999;
					}
				}
				
				.arrow-icon {
					.iconfont {
						font-size: 24rpx;
						color: #ccc;
					}
				}
			}
		}
		
		.empty-state {
			text-align: center;
			padding: 100rpx 0;
			
			image {
				width: 200rpx;
				height: 200rpx;
				margin-bottom: 30rpx;
			}
			
			text {
				font-size: 28rpx;
				color: #999;
			}
		}
	}
	
	.add-customer-btn {
		position: fixed;
		bottom: 40rpx;
		right: 40rpx;
		width: 100rpx;
		height: 100rpx;
		background: linear-gradient(135deg, #FFB6C1 0%, #FF69B4 100%);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 20rpx rgba(255, 105, 180, 0.3);
		
		.iconfont {
			font-size: 40rpx;
			color: white;
		}
	}
</style>
