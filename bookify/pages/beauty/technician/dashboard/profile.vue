<template>
	<view class="profile">
		<!-- 统一导航栏 -->
		<beauty-navbar 
			title="个人设置" 
			:show-back="true"
		>
			<template #right>
				<view class="nav-icon" @click="showEditMode">
					<text>✏️</text>
				</view>
			</template>
		</beauty-navbar>
		
		<!-- 个人信息卡片 -->
		<view class="profile-card" style="margin-top: 88px;">
			<view class="avatar-section">
				<view class="avatar-container">
					<image :src="userInfo.avatar" mode="aspectFill" @click="changeAvatar"></image>
					<view class="avatar-edit">
						<text class="iconfont icon-camera"></text>
					</view>
				</view>
				<view class="user-info">
					<view class="user-name">{{ userInfo.name || '技师' }}</view>
					<view class="user-level">{{ userInfo.level || '技师' }}</view>
					<view class="user-rating">
						<view class="rating-stars">
							<text class="star" v-for="i in 5" :key="i" :class="{ active: i <= Math.round(userInfo.rating) }">★</text>
						</view>
						<view class="rating-info">
							<text class="rating-score">{{ userInfo.rating || 0 }}分</text>
							<text class="rating-label">客户评分</text>
						</view>
					</view>
				</view>
			</view>
			
			<view class="stats-section">
				<view class="stat-item">
					<view class="stat-value">{{ userInfo.totalServices || 0 }}</view>
					<view class="stat-label">总服务次数</view>
				</view>
				<view class="stat-item">
					<view class="stat-value">¥{{ userInfo.totalEarnings || 0 }}</view>
					<view class="stat-label">总收益</view>
				</view>
				<view class="stat-item">
					<view class="stat-value satisfaction-value">{{ userInfo.satisfaction || 0 }}%</view>
					<view class="stat-label">客户满意度</view>
				</view>
			</view>
		</view>
		
		<!-- 工作信息 -->
		<view class="info-section">
			<view class="section-title">工作信息</view>
			<view class="info-list">
				<view class="info-item" @click="editInfo('specialty')">
					<view class="info-label">专业技能</view>
					<view class="info-value">{{ formatSpecialty(userInfo.specialty) }}</view>
					<text class="iconfont icon-arrow-right"></text>
				</view>
				<view class="info-item" @click="editInfo('experience')">
					<view class="info-label">工作经验</view>
					<view class="info-value">{{ userInfo.experience || 0 }}年</view>
					<text class="iconfont icon-arrow-right"></text>
				</view>
			</view>
		</view>
		
		<!-- 工作设置 -->
		<view class="work-section">
			<view class="section-title">工作设置</view>
			<view class="work-list">
				<view class="work-item">
					<view class="work-label">工作状态</view>
					<view class="work-value">
						<switch :checked="workSettings.isOnline" @change="toggleWorkStatus" color="#FF69B4" />
					</view>
				</view>
				<view class="work-item" @click="setWorkTime">
					<view class="work-label">工作时间</view>
					<view class="work-value">{{ formatWorkTime(workSettings.workTime) }}</view>
					<text class="iconfont icon-arrow-right"></text>
				</view>
			</view>
		</view>
		

		

		

	</view>
</template>

<script>
import { getTechnicianProfile, updateTechnicianProfile, getTechnicianWorkSettings, updateTechnicianWorkSettings } from '@/api/beauty/index'
import { uploadAvatar } from '@/api/user'

export default {
	name: 'Profile',
	data() {
		return {
			loading: false,
			userInfo: {
				name: '',
				level: '',
				rating: 0,
				avatar: '/static/images/default-avatar.png',
				specialty: '',
				experience: 0,
				totalServices: 0,
				totalEarnings: 0,
				satisfaction: 0
			},
			workSettings: {
				isOnline: true,
				workTime: '09:00-18:00'
			}
		}
	},
	onLoad() {
		this.loadProfileData()
	},
	methods: {
		// 加载个人信息数据
		async loadProfileData() {
			this.loading = true
			try {
				await Promise.all([
					this.loadTechnicianProfile(),
					this.loadWorkSettings()
				])
			} catch (error) {
				console.error('加载数据失败:', error)
				uni.showToast({
					title: '加载数据失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},

		// 加载技师个人信息
		async loadTechnicianProfile() {
			try {
				const response = await getTechnicianProfile()
				if (response.code === 200) {
					this.userInfo = {
						name: response.data.name || '',
						level: response.data.level || '',
						rating: response.data.rating_avg || 0,
						avatar: response.data.avatar || '/static/images/default-avatar.png',
						specialty: response.data.specialties || '',
						experience: response.data.experience || 0,
						totalServices: response.data.total_services || 0,
						totalEarnings: response.data.total_earnings || 0,
						satisfaction: response.data.satisfaction || 0
					}
				}
			} catch (error) {
				console.error('加载技师信息失败:', error)
			}
		},

		// 加载工作设置
		async loadWorkSettings() {
			try {
				const response = await getTechnicianWorkSettings()
				if (response.code === 200) {
					this.workSettings = {
						isOnline: response.data.is_online || false,
						workTime: response.data.work_hours || '09:00-18:00'
					}
				}
			} catch (error) {
				console.error('加载工作设置失败:', error)
			}
		},



		goBack() {
			uni.navigateBack()
		},
		showEditMode() {
			uni.showToast({
				title: '编辑模式开发中',
				icon: 'none'
			})
		},
		async changeAvatar() {
			try {
				const res = await uni.chooseImage({
					count: 1,
					sizeType: ['compressed']
				})
				
				// 这里应该先上传图片到服务器，然后获取URL
				// 暂时使用本地路径
				const avatarURL = res.tempFilePaths[0]
				
				// 调用用户头像上传API
				const response = await uploadAvatar(avatarURL)
				
				if (response.code === 200) {
					this.userInfo.avatar = avatarURL
					uni.showToast({
						title: '头像更新成功',
						icon: 'success'
					})
				}
			} catch (error) {
				console.error('上传头像失败:', error)
				uni.showToast({
					title: '上传头像失败',
					icon: 'none'
				})
			}
		},
		editInfo(type) {
			// 只允许编辑工作相关信息
			if (type === 'phone' || type === 'email') {
				uni.showToast({
					title: '基本信息请在用户中心修改',
					icon: 'none'
				})
				return
			}
			uni.navigateTo({
				url: `/pages/beauty/technician/dashboard/edit-info?type=${type}`
			})
		},
		async toggleWorkStatus(e) {
			try {
				const isOnline = e.detail.value
				this.workSettings.isOnline = isOnline
				
				// 更新技师状态（在线/离线）
				// 这里需要调用技师状态更新API，暂时使用工作设置API
				await updateTechnicianWorkSettings({
					work_hours: this.workSettings.workTime
				})
				
				uni.showToast({
					title: isOnline ? '已上线' : '已下线',
					icon: 'success'
				})
			} catch (error) {
				console.error('更新工作状态失败:', error)
				// 恢复原状态
				this.workSettings.isOnline = !e.detail.value
				uni.showToast({
					title: '更新失败',
					icon: 'none'
				})
			}
		},
		setWorkTime() {
			uni.navigateTo({
				url: '/pages/beauty/technician/dashboard/work-time'
			})
		},


		
		// 格式化专业技能显示
		formatSpecialty(specialty) {
			if (!specialty) return '未设置'
			
			try {
				// 如果是JSON字符串，解析为数组
				if (typeof specialty === 'string' && specialty.startsWith('[')) {
					const specialties = JSON.parse(specialty)
					if (Array.isArray(specialties)) {
						return specialties.join('、')
					}
				}
				// 如果已经是数组
				if (Array.isArray(specialty)) {
					return specialty.join('、')
				}
				// 如果是字符串，直接返回
				return specialty
			} catch (error) {
				console.error('解析专业技能失败:', error)
				return specialty || '未设置'
			}
		},
		
		// 格式化工作时间显示
		formatWorkTime(workTime) {
			if (!workTime) return '未设置'
			
			try {
				// 如果是JSON字符串，解析为对象
				if (typeof workTime === 'string' && workTime.startsWith('{')) {
					const workSchedule = JSON.parse(workTime)
					if (typeof workSchedule === 'object') {
						// 提取工作时间信息
						const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
						const workDays = days.filter(day => workSchedule[day] && workSchedule[day] !== 'off')
						
						if (workDays.length > 0) {
							const firstDay = workDays[0]
							const schedule = workSchedule[firstDay]
							if (schedule && schedule.start && schedule.end) {
								return `${schedule.start}-${schedule.end}`
							}
						}
					}
				}
				// 如果已经是对象
				if (typeof workTime === 'object') {
					const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
					const workDays = days.filter(day => workTime[day] && workTime[day] !== 'off')
					
					if (workDays.length > 0) {
						const firstDay = workDays[0]
						const schedule = workTime[firstDay]
						if (schedule && schedule.start && schedule.end) {
							return `${schedule.start}-${schedule.end}`
						}
					}
				}
				// 如果是简单字符串，直接返回
				return workTime
			} catch (error) {
				console.error('解析工作时间失败:', error)
				return workTime || '未设置'
			}
		},
		

		

		}
	}
</script>

<style lang="scss" scoped>
	.profile {
		background: #f5f5f5;
		min-height: 100vh;
	}
	
	.custom-navbar {
		background: linear-gradient(135deg, #FFB6C1 0%, #FF69B4 100%);
		padding-top: var(--status-bar-height);
		
		.navbar-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 88rpx;
			padding: 0 40rpx;
			
			.back-btn, .right-btn {
				width: 60rpx;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
				background: rgba(255, 255, 255, 0.2);
				
				.iconfont {
					font-size: 32rpx;
					color: white;
				}
			}
			
			.title {
				font-size: 36rpx;
				font-weight: bold;
				color: white;
			}
		}
	}
	
	.profile-card {
		background: white;
		padding: 40rpx;
		margin-bottom: 20rpx;
		
		.avatar-section {
			display: flex;
			align-items: center;
			margin-bottom: 40rpx;
			
			.avatar-container {
				position: relative;
				width: 120rpx;
				height: 120rpx;
				margin-right: 30rpx;
				
				image {
					width: 100%;
					height: 100%;
					border-radius: 50%;
				}
				
				.avatar-edit {
					position: absolute;
					bottom: 0;
					right: 0;
					width: 40rpx;
					height: 40rpx;
					background: #FF69B4;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					border: 4rpx solid white;
					
					.iconfont {
						font-size: 20rpx;
						color: white;
					}
				}
			}
			
			.user-info {
				flex: 1;
				
				.user-name {
					font-size: 36rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 10rpx;
				}
				
				.user-level {
					font-size: 28rpx;
					color: #FF69B4;
					margin-bottom: 10rpx;
				}
				
				.user-rating {
					display: flex;
					align-items: center;
					margin-top: 10rpx;
					
					.rating-stars {
						display: flex;
						align-items: center;
						margin-right: 15rpx;
						
						.star {
							font-size: 32rpx;
							color: #ddd;
							margin-right: 4rpx;
							line-height: 1;
						}
						
						.star.active {
							color: #FFD700;
						}
					}
					
					.rating-info {
						display: flex;
						flex-direction: column;
						align-items: flex-start;
						
						.rating-score {
							font-size: 28rpx;
							font-weight: bold;
							color: #FF69B4;
							line-height: 1;
						}
						
						.rating-label {
							font-size: 20rpx;
							color: #999;
							margin-top: 2rpx;
						}
					}
				}
			}
		}
		
		.stats-section {
			display: flex;
			justify-content: space-between;
			
			.stat-item {
				text-align: center;
				
				.stat-value {
					font-size: 36rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 10rpx;
				}
				
				.satisfaction-value {
					color: #FF69B4;
				}
				
				.stat-label {
					font-size: 24rpx;
					color: #666;
				}
			}
		}
	}
	
	.info-section, .work-section {
		background: white;
		padding: 40rpx;
		margin-bottom: 20rpx;
		
		.section-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 30rpx;
		}
		
		.info-list, .work-list {
			.info-item, .work-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 30rpx 0;
				border-bottom: 1rpx solid #f0f0f0;
				
				.info-label, .work-label {
					font-size: 30rpx;
					color: #333;
					
					.iconfont {
						font-size: 28rpx;
						color: #FF69B4;
						margin-right: 20rpx;
					}
				}
				
				.info-value, .work-value {
					font-size: 28rpx;
					color: #666;
				}
				
				.iconfont {
					font-size: 24rpx;
					color: #ccc;
				}
			}
			
			.info-item:last-child, .work-item:last-child {
				border-bottom: none;
			}
		}
	}
	

</style>
