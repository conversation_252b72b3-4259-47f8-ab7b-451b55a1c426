<template>
	<view class="work-time">
		<!-- 通用导航栏 -->
		<beauty-navbar 
			title="工作时间设置" 
			:show-back="true"
		>
		</beauty-navbar>
		
		<!-- 工作时间设置 -->
		<view class="work-time-content" style="margin-top: 88px;">
			<view class="section-title">设置每周工作时间</view>
			
			<view class="work-days">
				<view 
					v-for="(day, index) in workDays" 
					:key="day.key"
					class="day-item"
				>
					<view class="day-header">
						<view class="day-name">{{ day.name }}</view>
						<switch 
							:checked="day.enabled" 
							@change="toggleDay(index)"
							color="#FF69B4"
						/>
					</view>
					
					<view class="time-settings" v-if="day.enabled">
						<view class="time-item">
							<text class="time-label">开始时间</text>
							<picker 
								mode="time" 
								:value="day.startTime" 
								@change="onStartTimeChange(index, $event)"
							>
								<view class="time-picker">
									<text>{{ day.startTime }}</text>
									<text class="picker-icon">🕐</text>
								</view>
							</picker>
						</view>
						
						<view class="time-item">
							<text class="time-label">结束时间</text>
							<picker 
								mode="time" 
								:value="day.endTime" 
								@change="onEndTimeChange(index, $event)"
							>
								<view class="time-picker">
									<text>{{ day.endTime }}</text>
									<text class="picker-icon">🕐</text>
								</view>
							</picker>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 快捷设置 -->
			<view class="quick-settings">
				<view class="section-title">快捷设置</view>
				<view class="quick-buttons">
					<view class="quick-btn" @click="setStandardTime">
						<text>标准工作时间</text>
						<text class="quick-desc">09:00-18:00</text>
					</view>
					<view class="quick-btn" @click="setFlexibleTime">
						<text>灵活工作时间</text>
						<text class="quick-desc">10:00-19:00</text>
					</view>
					<view class="quick-btn" @click="setWeekendTime">
						<text>周末工作时间</text>
						<text class="quick-desc">10:00-17:00</text>
					</view>
				</view>
			</view>
			
			<!-- 保存按钮 -->
			<view class="save-section">
				<button class="save-btn" @click="saveWorkTime">
					保存设置
				</button>
			</view>
		</view>
	</view>
</template>

<script>
import { getTechnicianWorkSettings, updateTechnicianWorkSettings } from '@/api/beauty/index'

export default {
	name: 'WorkTime',
	data() {
		return {
			workDays: [
				{ key: 'monday', name: '周一', enabled: true, startTime: '09:00', endTime: '18:00' },
				{ key: 'tuesday', name: '周二', enabled: true, startTime: '09:00', endTime: '18:00' },
				{ key: 'wednesday', name: '周三', enabled: true, startTime: '09:00', endTime: '18:00' },
				{ key: 'thursday', name: '周四', enabled: true, startTime: '09:00', endTime: '18:00' },
				{ key: 'friday', name: '周五', enabled: true, startTime: '09:00', endTime: '18:00' },
				{ key: 'saturday', name: '周六', enabled: true, startTime: '10:00', endTime: '17:00' },
				{ key: 'sunday', name: '周日', enabled: false, startTime: '10:00', endTime: '17:00' }
			]
		}
	},
	
	onLoad() {
		this.loadWorkSettings()
	},
	
	methods: {
		async loadWorkSettings() {
			try {
				const response = await getTechnicianWorkSettings()
				if (response.code === 200 && response.data.work_hours) {
					const workHours = response.data.work_hours
					
					// 解析工作时间设置
					if (typeof workHours === 'string') {
						try {
							const parsed = JSON.parse(workHours)
							this.updateWorkDaysFromData(parsed)
						} catch (error) {
							console.error('解析工作时间失败:', error)
						}
					} else if (typeof workHours === 'object') {
						this.updateWorkDaysFromData(workHours)
					}
				}
			} catch (error) {
				console.error('加载工作时间设置失败:', error)
			}
		},
		
		updateWorkDaysFromData(data) {
			this.workDays.forEach(day => {
				if (data[day.key]) {
					if (data[day.key] === 'off') {
						day.enabled = false
					} else if (data[day.key].start && data[day.key].end) {
						day.enabled = true
						day.startTime = data[day.key].start
						day.endTime = data[day.key].end
					}
				}
			})
		},
		
		toggleDay(index) {
			this.workDays[index].enabled = !this.workDays[index].enabled
		},
		
		onStartTimeChange(index, event) {
			this.workDays[index].startTime = event.detail.value
		},
		
		onEndTimeChange(index, event) {
			this.workDays[index].endTime = event.detail.value
		},
		
		setStandardTime() {
			this.workDays.forEach((day, index) => {
				if (index < 5) { // 周一到周五
					day.enabled = true
					day.startTime = '09:00'
					day.endTime = '18:00'
				} else { // 周末
					day.enabled = false
				}
			})
		},
		
		setFlexibleTime() {
			this.workDays.forEach((day, index) => {
				if (index < 5) { // 周一到周五
					day.enabled = true
					day.startTime = '10:00'
					day.endTime = '19:00'
				} else { // 周末
					day.enabled = true
					day.startTime = '10:00'
					day.endTime = '17:00'
				}
			})
		},
		
		setWeekendTime() {
			this.workDays.forEach((day, index) => {
				if (index < 5) { // 周一到周五
					day.enabled = false
				} else { // 周末
					day.enabled = true
					day.startTime = '10:00'
					day.endTime = '17:00'
				}
			})
		},
		

		
		async saveWorkTime() {
			try {
				// 构建工作时间数据
				const workHours = {}
				this.workDays.forEach(day => {
					if (day.enabled) {
						workHours[day.key] = {
							start: day.startTime,
							end: day.endTime
						}
					} else {
						workHours[day.key] = 'off'
					}
				})
				
				// 调用API保存
				const response = await updateTechnicianWorkSettings({
					work_hours: JSON.stringify(workHours)
				})
				
				if (response.code === 200) {
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					})
					
					// 返回上一页
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} else {
					uni.showToast({
						title: response.message || '保存失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('保存工作时间失败:', error)
				uni.showToast({
					title: '保存失败',
					icon: 'none'
				})
			}
		}
	}
}
</script>

<style lang="scss" scoped>
	.work-time {
		background: #f5f5f5;
		min-height: 100vh;
	}
	
	.work-time-content {
		padding: 40rpx;
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 30rpx;
	}
	
	.work-days {
		background: white;
		border-radius: 20rpx;
		padding: 20rpx;
		margin-bottom: 40rpx;
		
		.day-item {
			border-bottom: 1rpx solid #f0f0f0;
			padding: 30rpx 0;
			
			&:last-child {
				border-bottom: none;
			}
			
			.day-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;
				
				.day-name {
					font-size: 30rpx;
					font-weight: 600;
					color: #333;
				}
			}
			
			.time-settings {
				.time-item {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 20rpx;
					
					.time-label {
						font-size: 28rpx;
						color: #666;
					}
					
					.time-picker {
						display: flex;
						align-items: center;
						padding: 15rpx 20rpx;
						background: #f8f9fa;
						border-radius: 10rpx;
						min-width: 120rpx;
						justify-content: space-between;
						
						.picker-icon {
							font-size: 24rpx;
						}
					}
				}
			}
		}
	}
	
	.quick-settings {
		.quick-buttons {
			display: flex;
			flex-direction: column;
			gap: 20rpx;
			
			.quick-btn {
				background: white;
				padding: 30rpx;
				border-radius: 15rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				
				text {
					font-size: 28rpx;
					color: #333;
					margin-bottom: 10rpx;
				}
				
				.quick-desc {
					font-size: 24rpx;
					color: #666;
					margin-bottom: 0;
				}
			}
		}
	}
	
	.save-section {
		margin-top: 60rpx;
		padding: 0 20rpx;
		
		.save-btn {
			width: 100%;
			height: 88rpx;
			background: linear-gradient(135deg, #FFB6C1 0%, #FF69B4 100%);
			color: white;
			border: none;
			border-radius: 44rpx;
			font-size: 32rpx;
			font-weight: bold;
			display: flex;
			align-items: center;
			justify-content: center;
			
			&:active {
				opacity: 0.8;
			}
		}
	}
</style> 