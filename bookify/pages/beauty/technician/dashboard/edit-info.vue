<template>
	<view class="edit-info">
		<!-- 通用导航栏 -->
		<beauty-navbar 
			:title="pageTitle" 
			:show-back="true"
		>
		</beauty-navbar>
		
		<!-- 编辑表单 -->
		<view class="edit-content" style="margin-top: 88px;">
			<!-- 手机号码编辑 -->
			<view v-if="editType === 'phone'" class="edit-form">
				<view class="form-item">
					<view class="form-label">手机号码</view>
					<input 
						v-model="formData.phone" 
						type="number"
						placeholder="请输入手机号码"
						maxlength="11"
						class="form-input"
					/>
				</view>
				<view class="form-tip">手机号码用于接收预约通知和重要消息</view>
			</view>
			
			<!-- 邮箱编辑 -->
			<view v-if="editType === 'email'" class="edit-form">
				<view class="form-item">
					<view class="form-label">邮箱地址</view>
					<input 
						v-model="formData.email" 
						type="text"
						placeholder="请输入邮箱地址"
						class="form-input"
					/>
				</view>
				<view class="form-tip">邮箱用于接收系统通知和账单信息</view>
			</view>
			
			<!-- 专业技能编辑 -->
			<view v-if="editType === 'specialty'" class="edit-form">
				<view class="form-item">
					<view class="form-label">专业技能</view>
					<view class="specialty-tags">
						<view 
							v-for="skill in availableSkills" 
							:key="skill"
							class="skill-tag"
							:class="{ selected: selectedSkills.includes(skill) }"
							@click="toggleSkill(skill)"
						>
							<text>{{ skill }}</text>
						</view>
					</view>
				</view>
				<view class="form-item">
					<view class="form-label">自定义技能</view>
					<view class="custom-skill">
						<input 
							v-model="newSkill" 
							placeholder="输入其他技能，按回车添加"
							@confirm="addCustomSkill"
							class="form-input"
						/>
						<button class="add-skill-btn" @click="addCustomSkill">添加</button>
					</view>
				</view>
				<view class="form-tip">选择您擅长的专业技能，提高客户匹配度</view>
			</view>
			
			<!-- 工作经验编辑 -->
			<view v-if="editType === 'experience'" class="edit-form">
				<view class="form-item">
					<view class="form-label">工作经验</view>
					<picker 
						mode="selector" 
						:range="experienceOptions" 
						:value="experienceIndex"
						@change="onExperienceChange"
					>
						<view class="picker-input">
							<text>{{ experienceOptions[experienceIndex] }}</text>
							<text class="picker-icon">▼</text>
						</view>
					</picker>
				</view>
				<view class="form-item">
					<view class="form-label">详细描述</view>
					<textarea 
						v-model="formData.experienceDesc" 
						placeholder="请详细描述您的工作经验、培训经历等"
						class="form-textarea"
						maxlength="500"
					></textarea>
					<view class="char-count">{{ formData.experienceDesc.length }}/500</view>
				</view>
				<view class="form-tip">工作经验越丰富，客户信任度越高</view>
			</view>
		</view>
		
		<!-- 底部保存按钮 -->
		<view class="bottom-save-btn" @click="saveInfo">
			<text>保存设置</text>
		</view>
	</view>
</template>

<script>
import { getTechnicianProfile, updateTechnicianProfile } from '@/api/beauty/index'

export default {
	name: 'EditInfo',
	data() {
		return {
			editType: '',
			formData: {
				specialty: '',
				experience: 0,
				experienceDesc: ''
			},
			selectedSkills: [],
			newSkill: '',
			experienceIndex: 0,
			experienceOptions: [
				'1年以下', '1-2年', '3-5年', '6-10年', '10年以上'
			],
			availableSkills: [
				'面部护理', '深层清洁', '抗衰老护理', '美白护理', '补水保湿',
				'美甲服务', '美睫服务', '美发造型', '身体护理', '按摩推拿',
				'纹绣服务', '美容仪器', '皮肤管理', 'SPA护理', '足疗服务'
			]
		}
	},
	
	computed: {
		pageTitle() {
			const titleMap = {
				'specialty': '编辑专业技能',
				'experience': '编辑工作经验'
			}
			return titleMap[this.editType] || '编辑信息'
		}
	},
	
	onLoad(options) {
		this.editType = options.type || ''
		this.loadCurrentInfo()
	},
	
	methods: {
		async loadCurrentInfo() {
			try {
				const response = await getTechnicianProfile()
				if (response.code === 200) {
					const data = response.data
					
					// 加载当前信息
					this.formData.experience = data.experience || 0
					this.formData.experienceDesc = data.experience_desc || ''
					
					// 处理专业技能
					if (data.specialties) {
						try {
							if (typeof data.specialties === 'string') {
								this.selectedSkills = JSON.parse(data.specialties)
							} else if (Array.isArray(data.specialties)) {
								this.selectedSkills = data.specialties
							}
						} catch (error) {
							console.error('解析专业技能失败:', error)
						}
					}
					
					// 设置工作经验选项
					this.setExperienceIndex(data.experience || 0)
				}
			} catch (error) {
				console.error('加载当前信息失败:', error)
			}
		},
		
		setExperienceIndex(experience) {
			if (experience <= 1) this.experienceIndex = 0
			else if (experience <= 2) this.experienceIndex = 1
			else if (experience <= 5) this.experienceIndex = 2
			else if (experience <= 10) this.experienceIndex = 3
			else this.experienceIndex = 4
		},
		
		toggleSkill(skill) {
			const index = this.selectedSkills.indexOf(skill)
			if (index > -1) {
				this.selectedSkills.splice(index, 1)
			} else {
				this.selectedSkills.push(skill)
			}
		},
		
		addCustomSkill() {
			if (!this.newSkill.trim()) {
				uni.showToast({
					title: '请输入技能名称',
					icon: 'none'
				})
				return
			}
			
			const skill = this.newSkill.trim()
			if (this.selectedSkills.includes(skill)) {
				uni.showToast({
					title: '该技能已添加',
					icon: 'none'
				})
				return
			}
			
			this.selectedSkills.push(skill)
			this.newSkill = ''
			
			uni.showToast({
				title: '添加成功',
				icon: 'success'
			})
		},
		
		onExperienceChange(event) {
			this.experienceIndex = event.detail.value
			const experienceMap = [1, 2, 5, 10, 15]
			this.formData.experience = experienceMap[this.experienceIndex]
		},
		

		
		async saveInfo() {
			try {
				console.log('开始保存信息，编辑类型:', this.editType)
				let updateData = {}
				
				switch (this.editType) {
					case 'specialty':
						if (this.selectedSkills.length === 0) {
							uni.showToast({
								title: '请至少选择一个专业技能',
								icon: 'none'
							})
							return
						}
						updateData.specialties = JSON.stringify(this.selectedSkills)
						break
						
					case 'experience':
						updateData.experience = this.formData.experience
						updateData.experience_desc = this.formData.experienceDesc
						break
				}
				
				console.log('准备发送的数据:', updateData)
				const response = await updateTechnicianProfile(updateData)
				console.log('API响应:', response)
				
				if (response.code === 200) {
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					})
					
					// 返回上一页
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} else {
					uni.showToast({
						title: response.message || '保存失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('保存信息失败:', error)
				uni.showToast({
					title: '保存失败: ' + error.message,
					icon: 'none'
				})
			}
		}
	}
}
</script>

<style lang="scss" scoped>
	.edit-info {
		background: #f5f5f5;
		min-height: 100vh;
	}
	
	.edit-content {
		padding: 40rpx;
	}
	
	.edit-form {
		background: white;
		border-radius: 20rpx;
		padding: 40rpx;
	}
	
	.form-item {
		margin-bottom: 40rpx;
		
		.form-label {
			font-size: 28rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 20rpx;
			padding-left: 10rpx;
		}
		
		.form-input {
			width: 100%;
			padding: 0 30rpx;
			border: 2rpx solid #e0e0e0;
			border-radius: 10rpx;
			font-size: 28rpx;
			box-sizing: border-box;
			background: white;
			color: #333;
			height: 80rpx;
			line-height: 80rpx;
			text-align: left;
		}
		
		.form-textarea {
			width: 100%;
			padding: 25rpx 30rpx;
			border: 2rpx solid #e0e0e0;
			border-radius: 10rpx;
			font-size: 28rpx;
			min-height: 200rpx;
			box-sizing: border-box;
			background: white;
			color: #333;
			line-height: 1.6;
		}
		
		.picker-input {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 30rpx;
			border: 2rpx solid #e0e0e0;
			border-radius: 10rpx;
			font-size: 28rpx;
			background: white;
			color: #333;
			height: 80rpx;
			
			.picker-icon {
				color: #999;
				font-size: 24rpx;
			}
		}
		
		.char-count {
			text-align: right;
			font-size: 24rpx;
			color: #999;
			margin-top: 10rpx;
		}
	}
	
	.specialty-tags {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		margin-bottom: 30rpx;
		
		.skill-tag {
			padding: 15rpx 25rpx;
			border: 2rpx solid #e0e0e0;
			border-radius: 25rpx;
			font-size: 24rpx;
			color: #666;
			
			&.selected {
				background: #FF69B4;
				color: white;
				border-color: #FF69B4;
			}
		}
	}
	
	.custom-skill {
		display: flex;
		gap: 20rpx;
		
		.form-input {
			flex: 1;
		}
		
		.add-skill-btn {
			padding: 25rpx 30rpx;
			background: #FF69B4;
			color: white;
			border: none;
			border-radius: 10rpx;
			font-size: 28rpx;
		}
	}
	
	.form-tip {
		font-size: 24rpx;
		color: #999;
		line-height: 1.5;
		padding: 20rpx 30rpx;
		background: #f8f9fa;
		border-radius: 10rpx;
		margin-top: 20rpx;
	}
	
	.bottom-save-btn {
		position: fixed;
		bottom: 40rpx;
		left: 40rpx;
		right: 40rpx;
		height: 100rpx;
		background: linear-gradient(135deg, #FFB6C1 0%, #FF69B4 100%);
		border-radius: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 8rpx 20rpx rgba(255, 105, 180, 0.3);
		
		text {
			color: white;
			font-size: 32rpx;
			font-weight: 600;
		}
	}
</style> 