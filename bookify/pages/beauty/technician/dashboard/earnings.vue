<template>
	<view class="earnings">
		<!-- 统一导航栏 -->
		<beauty-navbar 
			title="收益统计" 
			:show-back="true"
		>
			<template #right>
				<view class="nav-icon" @click="showDatePicker">
					<text>📅</text>
				</view>
			</template>
		</beauty-navbar>
		
		<!-- 时间选择器 -->
		<view class="date-tabs" style="margin-top: 88px;">
			<view class="tab-item" 
				  v-for="(tab, index) in dateTabs" 
				  :key="index"
				  :class="{ active: activeTab === index }"
				  @click="switchTab(index)">
				{{ tab }}
			</view>
		</view>
		
		<!-- 收益概览 -->
		<view class="earnings-overview">
			<view class="overview-card">
				<view class="card-header">
					<text class="card-title">本月收益</text>
					<text class="card-period">{{ currentMonth }}</text>
				</view>
				<view class="total-earnings">¥{{ totalEarnings }}</view>
				<view class="earnings-change">
					<text class="change-text" :class="{ positive: earningsChange > 0, negative: earningsChange < 0 }">
						{{ earningsChange > 0 ? '+' : '' }}{{ earningsChange }}%
					</text>
					<text class="change-label">较上月</text>
				</view>
			</view>
		</view>
		
		<!-- 收益明细 -->
		<view class="earnings-details">
			<view class="details-row">
				<view class="detail-item">
					<view class="detail-value">¥{{ serviceEarnings }}</view>
					<view class="detail-label">服务收入</view>
				</view>
				<view class="detail-item">
					<view class="detail-value">¥{{ bonusEarnings }}</view>
					<view class="detail-label">奖金收入</view>
				</view>
				<view class="detail-item">
					<view class="detail-value">¥{{ tipEarnings }}</view>
					<view class="detail-label">小费收入</view>
				</view>
			</view>
		</view>
		
		<!-- 收益趋势图 -->
		<view class="chart-section">
			<view class="section-title">收益趋势</view>
			<view class="chart-container">
				<!-- 这里可以集成图表组件 -->
				<view class="chart-placeholder">
					<text>收益趋势图</text>
					<text class="chart-desc">显示最近7天的收益变化</text>
				</view>
			</view>
		</view>
		
		<!-- 服务统计 -->
		<view class="service-stats">
			<view class="section-title">服务统计</view>
			<view class="stats-list">
				<view class="stats-item" v-for="service in serviceStats" :key="service.id">
					<view class="service-info">
						<view class="service-name">{{ service.name }}</view>
						<view class="service-count">{{ service.count }}次</view>
					</view>
					<view class="service-earnings">¥{{ service.earnings }}</view>
				</view>
			</view>
		</view>
		
		<!-- 收益记录 -->
		<view class="earnings-records">
			<view class="section-title">收益记录</view>
			<view class="records-list">
				<view class="record-item" v-for="record in earningsRecords" :key="record.id">
					<view class="record-info">
						<view class="record-title">{{ record.title }}</view>
						<view class="record-time">{{ record.time }}</view>
					</view>
					<view class="record-amount" :class="{ positive: record.amount > 0, negative: record.amount < 0 }">
						{{ record.amount > 0 ? '+' : '' }}¥{{ Math.abs(record.amount) }}
					</view>
				</view>
			</view>
		</view>
		
		<!-- 提现按钮 -->
		<view class="withdraw-section">
			<view class="available-amount">
				<text class="amount-label">可提现金额</text>
				<text class="amount-value">¥{{ availableAmount }}</text>
			</view>
			<view class="withdraw-btn" @click="showWithdraw">
				<text>立即提现</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'Earnings',
		data() {
			return {
				activeTab: 0,
				dateTabs: ['今日', '本周', '本月', '本年'],
				currentMonth: '2024年1月',
				totalEarnings: 8520,
				earningsChange: 12.5,
				serviceEarnings: 6800,
				bonusEarnings: 1200,
				tipEarnings: 520,
				availableAmount: 7850,
				serviceStats: [
					{
						id: 1,
						name: '深层清洁面部护理',
						count: 15,
						earnings: 4320
					},
					{
						id: 2,
						name: '美白补水面膜',
						count: 12,
						earnings: 2880
					},
					{
						id: 3,
						name: '眼部护理',
						count: 8,
						earnings: 1600
					},
					{
						id: 4,
						name: '身体按摩',
						count: 6,
						earnings: 1800
					}
				],
				earningsRecords: [
					{
						id: 1,
						title: '深层清洁面部护理',
						time: '2024-01-15 14:30',
						amount: 288
					},
					{
						id: 2,
						title: '美白补水面膜',
						time: '2024-01-15 10:00',
						amount: 240
					},
					{
						id: 3,
						title: '提现',
						time: '2024-01-14 16:20',
						amount: -2000
					},
					{
						id: 4,
						title: '眼部护理',
						time: '2024-01-14 11:15',
						amount: 200
					},
					{
						id: 5,
						title: '奖金',
						time: '2024-01-13 18:00',
						amount: 500
					}
				]
			}
		},
		methods: {
			goBack() {
				uni.navigateBack()
			},
			switchTab(index) {
				this.activeTab = index
				this.loadEarningsData()
			},
			showDatePicker() {
				uni.showToast({
					title: '日期选择器开发中',
					icon: 'none'
				})
			},
			loadEarningsData() {
				// 根据选择的时间段加载数据
				console.log('加载收益数据', this.dateTabs[this.activeTab])
			},
			showWithdraw() {
				uni.showModal({
					title: '提现申请',
					content: `确认提现 ¥${this.availableAmount} 到绑定银行卡？`,
					success: (res) => {
						if (res.confirm) {
							uni.showToast({
								title: '提现申请已提交',
								icon: 'success'
							})
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.earnings {
		background: #f5f5f5;
		min-height: 100vh;
	}
	
	.custom-navbar {
		background: linear-gradient(135deg, #FFB6C1 0%, #FF69B4 100%);
		padding-top: var(--status-bar-height);
		
		.navbar-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 88rpx;
			padding: 0 40rpx;
			
			.back-btn, .right-btn {
				width: 60rpx;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
				background: rgba(255, 255, 255, 0.2);
				
				.iconfont {
					font-size: 32rpx;
					color: white;
				}
			}
			
			.title {
				font-size: 36rpx;
				font-weight: bold;
				color: white;
			}
		}
	}
	
	.date-tabs {
		display: flex;
		background: white;
		padding: 20rpx 40rpx;
		
		.tab-item {
			flex: 1;
			text-align: center;
			padding: 20rpx;
			font-size: 28rpx;
			color: #666;
			border-radius: 20rpx;
			transition: all 0.3s ease;
			
			&.active {
				background: #FF69B4;
				color: white;
			}
		}
	}
	
	.earnings-overview {
		padding: 40rpx;
		
		.overview-card {
			background: linear-gradient(135deg, #FFB6C1 0%, #FF69B4 100%);
			border-radius: 24rpx;
			padding: 40rpx;
			color: white;
			
			.card-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;
				
				.card-title {
					font-size: 28rpx;
				}
				
				.card-period {
					font-size: 24rpx;
					opacity: 0.8;
				}
			}
			
			.total-earnings {
				font-size: 60rpx;
				font-weight: bold;
				margin-bottom: 20rpx;
			}
			
			.earnings-change {
				display: flex;
				align-items: center;
				gap: 10rpx;
				
				.change-text {
					font-size: 24rpx;
					font-weight: bold;
					
					&.positive {
						color: #52C41A;
					}
					
					&.negative {
						color: #FF4D4F;
					}
				}
				
				.change-label {
					font-size: 24rpx;
					opacity: 0.8;
				}
			}
		}
	}
	
	.earnings-details {
		background: white;
		padding: 40rpx;
		margin-bottom: 20rpx;
		
		.details-row {
			display: flex;
			justify-content: space-between;
			
			.detail-item {
				text-align: center;
				
				.detail-value {
					font-size: 36rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 10rpx;
				}
				
				.detail-label {
					font-size: 24rpx;
					color: #666;
				}
			}
		}
	}
	
	.chart-section, .service-stats, .earnings-records {
		background: white;
		padding: 40rpx;
		margin-bottom: 20rpx;
		
		.section-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 30rpx;
		}
		
		.chart-container {
			.chart-placeholder {
				height: 300rpx;
				background: #f8f9fa;
				border-radius: 16rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				border: 2rpx dashed #ddd;
				
				text:first-child {
					font-size: 28rpx;
					color: #666;
					margin-bottom: 10rpx;
				}
				
				.chart-desc {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
		
		.stats-list, .records-list {
			.stats-item, .record-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 30rpx 0;
				border-bottom: 1rpx solid #f0f0f0;
				
				&:last-child {
					border-bottom: none;
				}
				
				.service-info, .record-info {
					.service-name, .record-title {
						font-size: 30rpx;
						color: #333;
						margin-bottom: 10rpx;
					}
					
					.service-count, .record-time {
						font-size: 24rpx;
						color: #666;
					}
				}
				
				.service-earnings, .record-amount {
					font-size: 30rpx;
					font-weight: bold;
					color: #333;
					
					&.positive {
						color: #52C41A;
					}
					
					&.negative {
						color: #FF4D4F;
					}
				}
			}
		}
	}
	
	.withdraw-section {
		background: white;
		padding: 40rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		
		.available-amount {
			.amount-label {
				font-size: 28rpx;
				color: #666;
				margin-bottom: 10rpx;
			}
			
			.amount-value {
				font-size: 36rpx;
				font-weight: bold;
				color: #FF69B4;
			}
		}
		
		.withdraw-btn {
			background: linear-gradient(135deg, #FFB6C1 0%, #FF69B4 100%);
			color: white;
			padding: 20rpx 40rpx;
			border-radius: 50rpx;
			font-size: 28rpx;
			font-weight: bold;
		}
	}
</style>
