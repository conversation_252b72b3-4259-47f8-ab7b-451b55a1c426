<template>
  <view class="technician-dashboard">
    <!-- 统一导航栏 -->
    <beauty-navbar 
      title="工作台" 
      :show-back="true"
    >
    </beauty-navbar>

    <!-- 技师信息 -->
    <view class="technician-info" style="margin-top: 88px;">
      <image class="technician-avatar" :src="technicianInfo.avatar" mode="aspectFill"></image>
      <view class="technician-details">
        <view class="technician-name">{{ technicianInfo.name }}</view>
        <view class="technician-level">{{ technicianInfo.level }}</view>
        <view class="technician-rating">
          <text class="rating-text">{{ technicianInfo.rating }}</text>
          <text class="rating-stars">★★★★★</text>
        </view>
      </view>
      <view class="technician-actions">
        <view class="status-indicator" :class="'status-' + workStatus" @click="toggleWorkStatus">
          <text class="status-dot"></text>
          <text class="status-text">{{ getWorkStatusText() }}</text>
        </view>
        <view class="settings-btn" @click="goToSettings">
          <text class="settings-icon">✏️</text>
        </view>
      </view>
    </view>

    <!-- 日期概览 -->
    <view class="today-overview">
      <view class="overview-header">
        <view class="overview-title-section">
          <text class="overview-title">{{ selectedDate === getCurrentDateString() ? '今日概览' : '日期概览' }}</text>
          <text class="overview-date">{{ formatSelectedDate() }}</text>
        </view>
        <view class="date-picker-section">
          <picker mode="date" :value="selectedDate" @change="onDateChange">
            <view class="date-picker">
              <text class="date-picker-text">{{ formatSelectedDate() }}</text>
              <text class="date-picker-icon">📅</text>
            </view>
          </picker>
        </view>
      </view>
      <view class="overview-stats">
        <view class="stat-item">
          <view class="stat-number">{{ todayStats.bookingCount }}</view>
          <view class="stat-label">{{ selectedDate === getCurrentDateString() ? '今日预约' : '预约数' }}</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">¥{{ todayStats.expectedIncome }}</view>
          <view class="stat-label">预计收入</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ todayStats.completedCount }}</view>
          <view class="stat-label">已完成</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{ todayStats.satisfaction }}%</view>
          <view class="stat-label">满意度</view>
        </view>
      </view>
    </view>



    <!-- 当前服务 -->
    <view class="current-service" v-if="currentBooking">
      <view class="service-header">
        <text class="service-title">当前服务</text>
        <view class="service-status" :class="'status-' + currentBooking.booking_status">
          {{ getBookingStatusText(currentBooking.booking_status) }}
        </view>
      </view>
      <view class="service-content">
        <view class="service-info">
          <view class="service-name">{{ currentBooking.service_name }}</view>
          <view class="service-time">{{ currentBooking.start_time }} - {{ getEndTime(currentBooking.start_time, currentBooking.duration) }}</view>
        </view>
        <view class="customer-info">
          <view class="customer-name">{{ currentBooking.contact_name }}</view>
          <view class="customer-phone">{{ currentBooking.contact_phone }}</view>
        </view>
      </view>
      <view class="service-actions">
        <button 
          v-if="currentBooking.booking_status === 'confirmed'" 
          class="action-btn start-btn" 
          @click="startCurrentService"
        >
          开始服务
        </button>
        <button 
          v-if="currentBooking.booking_status === 'in_service'" 
          class="action-btn complete-btn" 
          @click="completeCurrentService"
        >
          完成服务
        </button>
      </view>
    </view>

    <!-- 预约列表 -->
    <view class="booking-container">
      <!-- 预约列表Tab -->
      <view class="booking-tabs">
        <view 
          v-for="tab in statusTabs" 
          :key="tab.value"
          class="filter-tab"
          :class="{ active: currentStatus === tab.value }"
          @click="switchStatus(tab.value)"
        >
          {{ tab.label }}
        </view>
      </view>
      <!-- 预约列表内容 -->
      <view class="booking-list">
      <view 
        v-for="booking in bookings" 
        :key="booking.id"
        class="booking-item"
      >
        <view class="booking-header">
          <view class="booking-time">
            <text class="time-text">{{ booking.start_time }}</text>
            <text class="duration-text">{{ booking.duration }}分钟</text>
          </view>
          <view class="booking-status" :class="'status-' + booking.booking_status">
            {{ getBookingStatusText(booking.booking_status) }}
          </view>
        </view>
        <view class="booking-content">
          <view class="service-info">
            <text class="service-name">{{ booking.service_name }}</text>
            <text class="service-price">¥{{ booking.final_price }}</text>
            <view class="payment-status" :class="getPaymentStatusClass(booking.payment_status)">
              {{ getPaymentStatusText(booking.payment_status) }}
            </view>
          </view>
          <view class="customer-info">
            <text class="customer-name">{{ booking.contact_name }}</text>
            <text class="customer-phone">{{ booking.contact_phone }}</text>
          </view>
        </view>
        <!-- 操作按钮区 -->
        <view class="booking-actions">
          <button v-if="booking.booking_status === 'pending'" class="action-btn confirm-btn" @click="confirmBooking(booking)">确认预约</button>
          <button v-if="booking.booking_status === 'confirmed'" class="action-btn start-btn" @click="startServiceForBooking(booking)">开始服务</button>
          <button v-if="booking.booking_status === 'in_service'" class="action-btn complete-btn" @click="completeServiceForBooking(booking)">完成服务</button>
        </view>
      </view>
      <view v-if="bookings.length === 0 && !loading" class="empty-state">
        <text class="empty-icon">📅</text>
        <text class="empty-text">暂无预约</text>
      </view>
      </view>
    </view>

    <!-- 消息通知 -->
    <view class="notifications" v-if="notifications.length > 0">
      <view class="notifications-header">
        <text class="notifications-title">消息通知</text>
        <text class="view-all" @click="viewAllNotifications">查看全部</text>
      </view>
      <view class="notifications-list">
        <view 
          v-for="notification in notifications.slice(0, 3)" 
          :key="notification.id"
          class="notification-item"
          @click="viewNotification(notification)"
        >
          <view class="notification-icon">
            <text>{{ getNotificationIcon(notification.type) }}</text>
          </view>
          <view class="notification-content">
            <view class="notification-title">{{ notification.title }}</view>
            <view class="notification-desc">{{ notification.content }}</view>
            <view class="notification-time">{{ formatTime(notification.createdAt) }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getTechnicianBookingList, getTechnicianTodayBookings, getTechnicianTodayStats, getTechnicianDateBookings, getTechnicianDateStats, getNotifications, getTechnicianDetail, updateBookingStatus, getTechnicianProfile } from '@/api/beauty/index'

export default {
  data() {
    return {
      workStatus: 'online', // online, offline, busy, break
      technicianInfo: {
        name: '技师',
        level: '技师',
        rating: '0.0',
        avatar: 'https://picsum.photos/100/100?random=1'
      },
      selectedDate: this.getCurrentDateString(), // 当前选中的日期
      todayStats: {
        bookingCount: 0,
        expectedIncome: 0,
        completedCount: 0,
        satisfaction: 0
      },
      currentBooking: null,
      bookings: [],
      page: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      finished: false,
      statusTabs: [
        { label: '全部', value: 'all' },
        { label: '待确认', value: 'pending' },
        { label: '已确认', value: 'confirmed' },
        { label: '服务中', value: 'in_service' },
        { label: '已完成', value: 'completed' }
      ],
      currentStatus: 'all',
      notifications: []
    }
  },
  
  onLoad() {
    this.initData()
  },
  
  onShow() {
    this.refreshData()
  },
  
  methods: {
    async initData() {
      await this.loadTechnicianInfo()
      await this.loadTodayStats()
      await this.loadNotifications()
      await this.loadBookings(true)
    },
    
    async refreshData() {
      await this.loadTodayStats()
      await this.loadBookings(true)
      this.checkCurrentBooking()
    },
    
    async loadTechnicianInfo() {
      try {
        // 使用技师个人设置API获取正确的头像和评分
        const response = await getTechnicianProfile()
        if (response && response.code === 200 && response.data) {
          this.technicianInfo = {
            name: response.data.name || '技师',
            level: response.data.level || '技师',
            rating: response.data.rating_avg || '0.0',
            avatar: response.data.avatar || '/static/images/default-avatar.png'
          }
          return
        }
      } catch (error) {
        console.error('技师信息获取失败:', error)
      }
      
              // 使用默认数据
        this.technicianInfo = {
          name: '技师',
          level: '技师',
          rating: '0.0',
          avatar: 'https://dlsj.oss-cn-beijing.aliyuncs.com/images/2025/06/04/89ad83cf-6c25-4105-9e78-2cf693993144.jpeg'
        }
    },
    
    async loadTodayStats() {
      try {
        const response = await getTechnicianDateStats(this.selectedDate)
        this.todayStats = (response && response.data) ? response.data : {
          bookingCount: 0,
          expectedIncome: 0,
          completedCount: 0,
          satisfaction: 0
        }
      } catch (error) {
        this.todayStats = {
          bookingCount: 0,
          expectedIncome: 0,
          completedCount: 0,
          satisfaction: 0
        }
        console.error('加载日期统计失败:', error)
      }
    },
    
    async loadNotifications() {
      try {
        const params = {
          page: 1,
          pageSize: 5
        }
        const res = await getNotifications(params)
        if (res && res.data) {
          this.notifications = Array.isArray(res.data.list) ? res.data.list : []
        } else {
          this.notifications = []
        }
      } catch (error) {
        this.notifications = []
        console.error('加载通知失败:', error)
      }
    },
    
    async loadBookings(reset = false) {
      if (reset) {
        this.page = 1
        this.bookings = []
        this.finished = false
      }
      this.loading = true
      
      try {
        // 使用日期预约接口
        const res = await getTechnicianDateBookings(this.selectedDate)
        if (res.code === 200) {
          const list = Array.isArray(res.data) ? res.data : []
          
          // 根据当前筛选状态过滤预约
          let filteredList = list
          if (this.currentStatus !== 'all') {
            filteredList = list.filter(booking => booking.booking_status === this.currentStatus)
          }
          
          this.bookings = reset ? filteredList : this.bookings.concat(filteredList)
          this.total = filteredList.length
          this.finished = true // 日期预约不需要分页
        }
      } catch (error) {
        console.error('加载日期预约失败:', error)
      }
      this.loading = false
    },
    
    switchStatus(status) {
      this.currentStatus = status
      this.loadBookings(true)
    },
    

    
    checkCurrentBooking() {
      // 确保 bookings 数组存在
      if (!Array.isArray(this.bookings)) {
        this.currentBooking = null
        return
      }
      
      this.currentBooking = this.bookings.find(booking => 
        booking.booking_status === 'in_service' || 
        (booking.booking_status === 'confirmed' && this.isCurrentTime(booking.start_time))
      )
    },
    
    toggleWorkStatus() {
      const statusMap = {
        'online': 'offline',
        'offline': 'online',
        'busy': 'online',
        'break': 'online'
      }
      this.workStatus = statusMap[this.workStatus]
      this.updateWorkStatus()
    },
    
    async updateWorkStatus() {
      try {
        await this.setWorkStatus(this.workStatus)
        uni.showToast({
          title: `已切换为${this.getWorkStatusText()}`,
          icon: 'success'
        })
      } catch (error) {
        console.error('更新工作状态失败:', error)
      }
    },
    
    getWorkStatusText() {
      const statusMap = {
        'online': '在线',
        'offline': '离线',
        'busy': '忙碌',
        'break': '休息'
      }
      return statusMap[this.workStatus]
    },
    
    goToSettings() {
      uni.navigateTo({
        url: '/pages/beauty/technician/dashboard/profile'
      })
    },
    
    getBookingStatusText(status) {
      const statusMap = {
        'pending': '待确认',
        'confirmed': '已确认',
        'in_service': '服务中',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[status]
    },
    
    // 获取支付状态文本
    getPaymentStatusText(paymentStatus) {
      const textMap = {
        'unpaid': '未支付',
        'paid': '已支付',
        'refunded': '已退款',
        'partial_refunded': '部分退款'
      }
      return textMap[paymentStatus] || '未知状态'
    },
    
    // 获取支付状态样式类
    getPaymentStatusClass(paymentStatus) {
      const classMap = {
        'unpaid': 'payment-unpaid',
        'paid': 'payment-paid',
        'refunded': 'payment-refunded',
        'partial_refunded': 'payment-partial-refunded'
      }
      return classMap[paymentStatus] || ''
    },
    
    getCurrentDate() {
      const date = new Date()
      const month = date.getMonth() + 1
      const day = date.getDate()
      const weekDay = ['日', '一', '二', '三', '四', '五', '六'][date.getDay()]
      return `${month}月${day}日 周${weekDay}`
    },
    
    getCurrentDateString() {
      const date = new Date()
      return date.toISOString().split('T')[0] // 返回 YYYY-MM-DD 格式
    },
    
    formatSelectedDate() {
      const date = new Date(this.selectedDate)
      const month = date.getMonth() + 1
      const day = date.getDate()
      const weekDay = ['日', '一', '二', '三', '四', '五', '六'][date.getDay()]
      return `${month}月${day}日 周${weekDay}`
    },
    
    onDateChange(e) {
      this.selectedDate = e.detail.value
      this.refreshData()
    },
    
    getEndTime(startTime, duration) {
      const [hours, minutes] = startTime.split(':').map(Number)
      const endMinutes = hours * 60 + minutes + duration
      const endHours = Math.floor(endMinutes / 60)
      const endMins = endMinutes % 60
      return `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`
    },
    
    isCurrentTime(bookingTime) {
      const now = new Date()
      const currentHour = now.getHours()
      const [bookingHour] = bookingTime.split(':').map(Number)
      return Math.abs(currentHour - bookingHour) <= 1
    },
    

    
    async startCurrentService() {
      try {
        const response = await updateBookingStatus({
          id: this.currentBooking.id,
          status: 'in_service'
        })
        if (response && response.code === 200) {
        this.currentBooking.booking_status = 'in_service'
        this.workStatus = 'busy'
          uni.showToast({ title: '服务已开始', icon: 'success' })
        } else {
          uni.showToast({ title: response?.message || '开始服务失败', icon: 'none' })
        }
      } catch (error) {
        console.error('开始服务失败:', error)
        uni.showToast({ title: '开始服务失败', icon: 'none' })
      }
    },
    
    async completeCurrentService() {
      try {
        const response = await updateBookingStatus({
          id: this.currentBooking.id,
          status: 'completed'
        })
        if (response && response.code === 200) {
        this.currentBooking.booking_status = 'completed'
        this.workStatus = 'online'
        this.currentBooking = null
          uni.showToast({ title: '服务已完成', icon: 'success' })
        this.refreshData()
        } else {
          uni.showToast({ title: response?.message || '完成服务失败', icon: 'none' })
        }
      } catch (error) {
        console.error('完成服务失败:', error)
        uni.showToast({ title: '完成服务失败', icon: 'none' })
      }
    },
    
    
    
    viewBookingDetail(booking) {
      uni.navigateTo({
        url: `/pages/beauty/technician/dashboard/booking-detail?id=${booking.id}`
      })
    },
    
    viewAllNotifications() {
      uni.navigateTo({
        url: '/pages/beauty/technician/dashboard/notifications'
      })
    },
    
    viewNotification(notification) {
      uni.navigateTo({
        url: `/pages/beauty/technician/dashboard/notification-detail?id=${notification.id}`
      })
    },
    
    getNotificationIcon(type) {
      const iconMap = {
        'booking': '📅',
        'system': '🔔',
        'customer': '👤',
        'payment': '💰'
      }
      return iconMap[type] || '📢'
    },
    
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      return `${Math.floor(diff / 86400000)}天前`
    },
    
    // 模拟API方法
    async setWorkStatus(status) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ success: true })
        }, 300)
      })
    },
    
    // 删除模拟API方法
    // async updateBookingStatus(bookingId, status) {
    //   return new Promise((resolve) => {
    //     setTimeout(() => {
    //       resolve({ success: true })
    //     }, 300)
    //   })
    // }

    // 新增：预约操作事件
    async startServiceForBooking(booking) {
      try {
        const response = await updateBookingStatus({
          id: booking.id,
          status: 'in_service'
        })
        if (response && response.code === 200) {
          booking.booking_status = 'in_service'
          this.workStatus = 'busy'
          uni.showToast({ title: '服务已开始', icon: 'success' })
        } else {
          uni.showToast({ title: response?.message || '开始服务失败', icon: 'none' })
        }
      } catch (error) {
        console.error('开始服务失败:', error)
        uni.showToast({ title: '开始服务失败', icon: 'none' })
      }
    },
    async completeServiceForBooking(booking) {
      try {
        const response = await updateBookingStatus({
          id: booking.id,
          status: 'completed'
        })
        if (response && response.code === 200) {
          booking.booking_status = 'completed'
          this.workStatus = 'online'
          
          // 显示服务完成和支付提示
          uni.showModal({
            title: '服务完成',
            content: '服务已完成，客户将收到支付通知。请提醒客户及时完成支付。',
            showCancel: false,
            confirmText: '确定',
            success: () => {
              uni.showToast({ title: '服务已完成', icon: 'success' })
            }
          })
          
          this.refreshData()
        } else {
          uni.showToast({ title: response?.message || '完成服务失败', icon: 'none' })
        }
      } catch (error) {
        console.error('完成服务失败:', error)
        uni.showToast({ title: '完成服务失败', icon: 'none' })
      }
    },

    async confirmBooking(booking) {
      try {
        const response = await updateBookingStatus({
          id: booking.id,
          status: 'confirmed'
        })
        if (response && response.code === 200) {
          booking.booking_status = 'confirmed'
          uni.showToast({ title: '预约已确认', icon: 'success' })
          this.refreshData()
        } else {
          uni.showToast({ title: response?.message || '确认预约失败', icon: 'none' })
        }
      } catch (error) {
        console.error('确认预约失败:', error)
        uni.showToast({ title: '确认预约失败', icon: 'none' })
      }
    },
  }
}
</script>

<style scoped>
.technician-dashboard {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 自定义导航栏 */
.custom-navbar {
  background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 100%);
  padding-top: var(--status-bar-height);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 15px;
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-indicator {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  cursor: pointer;
}

.settings-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
}

.settings-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.settings-icon {
  font-size: 16px;
  color: #fff;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-online .status-dot {
  background: #52C41A;
}

.status-offline .status-dot {
  background: #8C8C8C;
}

.status-busy .status-dot {
  background: #FA8C16;
}

.status-break .status-dot {
  background: #1890FF;
}

.status-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

/* 技师信息 */
.technician-info {
  display: flex;
  align-items: center;
  padding: 20px 15px;
  background: white;
  margin: 0;
}

.technician-details {
  flex: 1;
}

.technician-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: auto;
}

.status-indicator {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 16px;
  background: #f5f5f5;
  cursor: pointer;
  transition: all 0.2s ease;
}

.status-indicator:hover {
  background: #e8e8e8;
}

.status-indicator:active {
  background: #ddd;
}

.settings-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: #f5f5f5;
  cursor: pointer;
  transition: all 0.2s ease;
}

.settings-btn:hover {
  background: #e8e8e8;
}

.settings-btn:active {
  background: #ddd;
}

.settings-icon {
  font-size: 16px;
  color: #666;
}

.technician-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 15px;
}

.technician-name {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.technician-level {
  font-size: 14px;
  color: #FF69B4;
  margin-bottom: 10px;
}

.technician-rating {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.rating-text {
  font-size: 14px;
  color: #FFB6C1;
  font-weight: 600;
  margin-right: 4px;
}

.rating-stars {
  font-size: 12px;
  color: #FFB6C1;
}

/* 今日概览 */
.today-overview {
  background: #fff;
  margin: 0 0 15px 0;
  padding: 15px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.overview-title-section {
  display: flex;
  flex-direction: column;
}

.overview-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.overview-date {
  font-size: 14px;
  color: #666;
}

.date-picker-section {
  display: flex;
  align-items: center;
}

.date-picker {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.date-picker:hover {
  background: #e8e8e8;
}

.date-picker-text {
  font-size: 14px;
  color: #333;
  margin-right: 8px;
}

.date-picker-icon {
  font-size: 16px;
}

.overview-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-number {
  font-size: 20px;
  font-weight: 600;
  color: #FFB6C1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}



/* 当前服务 */
.current-service {
  background: #fff;
  margin: 0 0 20px 0;
  padding: 15px;
  border-left: 4px solid #FFB6C1;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.service-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.service-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.service-status.status-confirmed {
  background: #d4edda;
  color: #155724;
}

.service-status.status-in_service {
  background: #cce5ff;
  color: #004085;
}

.service-content {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.service-name {
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
}

.service-time {
  font-size: 14px;
  color: #666;
}

.customer-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.customer-phone {
  font-size: 12px;
  color: #666;
}

.service-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  flex: 1;
  height: 36px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

.start-btn {
  background: #52C41A;
  color: #fff;
}

.complete-btn {
  background: #1890FF;
  color: #fff;
}

/* 预约列表 */
.booking-container {
  background: #fff;
  margin: 0 0 20px 0;
}

.booking-tabs {
  display: flex;
  justify-content: space-around;
  background: #fff;
  margin: 0;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.filter-tab {
  flex: 1;
  text-align: center;
  padding: 8px 15px;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
  cursor: pointer;
}

.filter-tab.active {
  color: #FFB6C1;
  border-bottom-color: #FFB6C1;
  font-weight: 600;
}

.booking-list {
  background: #fff;
  margin: 0;
  padding: 20px;
}

.booking-item {
  padding: 20px;
  margin-bottom: 15px;
  background: #f8f9fa;
  border: 1px solid #eee;
  transition: all 0.3s ease;
}

.booking-item:hover {
  background: #f0f0f0;
}

.booking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.booking-time {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.duration-text {
  font-size: 12px;
  color: #666;
}

.booking-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.booking-status.status-pending {
  background: #fffbe6;
  color: #e6a700;
}

.booking-status.status-confirmed {
  background: #d4edda;
  color: #155724;
}

.booking-status.status-in_service {
  background: #cce5ff;
  color: #004085;
}

.booking-status.status-completed {
  background: #d1ecf1;
  color: #0c5460;
}

.booking-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-info {
  flex: 1;
  margin-right: 15px;
}

.service-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.service-price {
  font-size: 14px;
  color: #FFB6C1;
  font-weight: 600;
}

/* 支付状态样式 */
.payment-status {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 8px;
  margin-top: 4px;
  display: inline-block;
}

.payment-unpaid {
  color: #ff6b6b;
  background-color: #fff2f0;
}

.payment-paid {
  color: #51cf66;
  background-color: #f6ffed;
}

.payment-refunded {
  color: #ffd43b;
  background-color: #fffbe6;
}

.payment-partial-refunded {
  color: #ffa500;
  background-color: #fff7e6;
}

.customer-info {
  flex: 1;
}

.customer-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.customer-phone {
  font-size: 12px;
  color: #666;
}

/* 操作按钮区 */
.booking-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}
.action-btn {
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  opacity: 0.8;
}
.start-btn {
  background: linear-gradient(135deg, #FF6B81 0%, #FF8E9E 100%);
  color: #fff;
}
.complete-btn {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  color: #fff;
}

.confirm-btn {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: #fff;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
  display: block;
}

.empty-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.empty-desc {
  font-size: 14px;
  color: #999;
  display: block;
}

/* 消息通知 */
.notifications {
  background: #fff;
  margin: 0 0 15px 0;
  padding: 15px;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.notifications-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.view-all {
  font-size: 14px;
  color: #FFB6C1;
  cursor: pointer;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 8px;
  background: #f8f9fa;
  cursor: pointer;
}

.notification-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 16px;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.notification-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.notification-time {
  font-size: 12px;
  color: #999;
}
</style>