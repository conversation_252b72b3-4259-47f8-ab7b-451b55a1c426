<template>
  <view class="technician-detail">
    <!-- 统一导航栏 -->
    <beauty-navbar 
      title="技师详情" 
      :show-back="true"
    ></beauty-navbar>
    
    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading" style="margin-top: 88px;">
      <view class="loading-content">
        <text class="loading-text">加载中...</text>
      </view>
    </view>
    
    <!-- 技师信息 -->
    <view class="technician-info" v-if="!loading && technicianInfo.id" style="margin-top: 88px;">
      <!-- 基本信息 -->
      <view class="basic-info">
        <image 
          :src="technicianInfo.avatar || '/static/beauty/default-avatar.jpg'" 
          class="avatar" 
          mode="aspectFill"
          @error="handleAvatarError"
        ></image>
        <view class="info-content">
          <view class="name-level">
            <text class="name">{{ technicianInfo.name }}</text>
            <text class="level">{{ technicianInfo.level }}</text>
            <view class="featured-badge" v-if="technicianInfo.is_featured">
              <text>推荐</text>
            </view>
          </view>
          <view class="rating-info">
            <view class="stars">
              <text class="star" v-for="i in 5" :key="i">
                {{ i <= Math.floor(technicianInfo.rating_avg || 0) ? '★' : '☆' }}
              </text>
            </view>
            <text class="rating-score">{{ technicianInfo.rating_avg || 0 }}</text>
            <text class="review-count">({{ technicianInfo.rating_count || 0 }}条评价)</text>
          </view>
          <view class="meta-info">
            <text class="experience">{{ technicianInfo.experience || 0 }}年经验</text>
            <text class="price">¥{{ technicianInfo.extra_fee || 0 }}/次</text>
          </view>
          <view class="stats-info">
            <text class="stat-item">总预约 {{ technicianInfo.total_bookings || 0 }}次</text>
            <text class="stat-item">完成 {{ technicianInfo.completed_bookings || 0 }}次</text>
          </view>
        </view>
        <view class="action-buttons">
          <view class="collect-btn" @click="handleCollect">
            <text class="collect-icon">{{ isCollected ? '♥' : '♡' }}</text>
            <text class="collect-text">{{ isCollected ? '已收藏' : '收藏' }}</text>
          </view>
        </view>
      </view>
      
      <!-- 工作时间 -->
      <view class="work-schedule" v-if="technicianInfo.work_hours">
        <view class="schedule-header">
          <text class="schedule-title">工作时间</text>
          <text class="view-available-time" @click="showAvailableTime">查看可用时间</text>
        </view>
        <view class="schedule-content">
          <text class="work-time">{{ formatWorkHours(technicianInfo.work_hours) }}</text>
        </view>
      </view>
      
      <!-- 专业技能 -->
      <view class="skills-section" v-if="technicianInfo.specialties && technicianInfo.specialties.length > 0">
        <view class="section-header">
          <text class="section-title">专业技能</text>
        </view>
        <view class="skills-content">
          <text class="skill-tag" v-for="skill in technicianInfo.specialties" :key="skill">
            {{ skill }}
          </text>
        </view>
      </view>
      
      <!-- 个人简介 -->
      <view class="introduction-section">
        <view class="section-header">
          <text class="section-title">个人简介</text>
        </view>
        <view class="introduction-content">
          <text class="introduction-text">{{ technicianInfo.introduction }}</text>
        </view>
      </view>
      
      <!-- 作品展示 -->
      <view class="gallery-section" v-if="technicianInfo.gallery && technicianInfo.gallery.length > 0">
        <view class="section-header">
          <text class="section-title">作品展示</text>
        </view>
        <view class="gallery-grid">
          <image 
            v-for="(img, index) in technicianInfo.gallery" 
            :key="index"
            :src="img" 
            class="gallery-item"
            mode="aspectFill"
            @click="previewImage(technicianInfo.gallery, index)"
          ></image>
        </view>
      </view>
      
      <!-- 可预约服务 -->
      <view class="services-section">
        <view class="section-header">
          <text class="section-title">可预约服务</text>
        </view>
        <view class="services-content">
          <view class="service-item" v-for="service in technicianServices" :key="service.id">
            <text class="service-name">{{ service.name }}</text>
            <text class="service-price">¥{{ service.price }}</text>
          </view>
        </view>
      </view>
      
      <!-- 用户评价 -->
      <view class="reviews-section">
        <view class="section-header">
          <text class="section-title">用户评价</text>
          <text class="view-all" @click="viewAllReviews">查看全部</text>
        </view>
        <view class="reviews-content">
          <view class="review-item" v-for="review in technicianReviews.slice(0, 3)" :key="review.id">
            <view class="review-header">
              <image :src="review.user_avatar" class="user-avatar" mode="aspectFill"></image>
              <view class="review-info">
                <text class="user-name">{{ review.user_nickname }}</text>
                <view class="review-rating">
                  <text class="star" v-for="i in 5" :key="i">
                    {{ i <= review.rating ? '★' : '☆' }}
                  </text>
                </view>
              </view>
              <text class="review-date">{{ formatDate(review.review_time) }}</text>
            </view>
            <view class="review-content">
              <text class="review-text">{{ review.review }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部预约栏 -->
    <view class="booking-bar" v-if="!loading && technicianInfo.id">
      <view class="booking-info">
        <text class="booking-price">¥{{ technicianInfo.extra_fee || 0 }}/次</text>
        <text class="booking-desc">专业技师服务</text>
      </view>
      <view class="booking-actions">
        <button class="consult-btn" @click="consult">咨询</button>
        <button class="booking-btn" @click="goToBooking">立即预约</button>
      </view>
    </view>
  </view>
</template>

<script>
import { getTechnicianDetail, getServiceList, getTechnicianReviews, getTechnicianAvailableTime } from '@/api/beauty/index.js'
import { collectItem } from '@/api/collect.js'

export default {
  name: 'TechnicianDetail',
  
  data() {
    return {
      TechnicianUserID: null,
      serviceId: null,
      technicianInfo: {},
      technicianServices: [],
      technicianReviews: [],
      loading: true,
      isCollected: false
    }
  },
  
  onLoad(options) {
    console.log('技师详情页面参数:', options)
    if (options.id) {
      this.TechnicianUserID = parseInt(options.id)
      console.log('解析后的技师UserID:', this.TechnicianUserID)
      if (options.serviceId) {
        this.serviceId = parseInt(options.serviceId)
        console.log('解析后的服务ID:', this.serviceId)
      }
      this.loadTechnicianDetail()
    } else {
      console.error('技师ID不存在')
      uni.showToast({
        title: '技师ID不存在',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  },
  
  methods: {
    async loadTechnicianDetail() {
      this.loading = true
      console.log('开始加载技师详情，技师ID:', this.TechnicianUserID)
      
      try {
        const [technicianRes, servicesRes, reviewsRes] = await Promise.all([
          getTechnicianDetail(this.TechnicianUserID),
          getServiceList({ page: 1, page_size: 100 }),
          getTechnicianReviews({ technician_user_id: this.TechnicianUserID, page: 1, page_size: 10 })
        ])
        
        console.log('技师详情API响应:', technicianRes)
        
        if (technicianRes.code === 200) {
          console.log('技师详情加载成功:', technicianRes.data)
          console.log('技师头像:', technicianRes.data.avatar)
          this.technicianInfo = technicianRes.data
          
          // 获取技师可提供的服务
          const allServices = servicesRes.data.list || []
          this.technicianServices = allServices.filter(service => 
            this.technicianInfo.service_ids && this.technicianInfo.service_ids.includes(service.id)
          )
          
          // 获取技师的评价
          this.technicianReviews = reviewsRes.data?.list || []
          
        } else {
          console.error('技师详情加载失败:', technicianRes)
          uni.showToast({
            title: technicianRes.message || '加载失败',
            icon: 'none'
          })
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        }
      } catch (error) {
        console.error('加载技师详情失败:', error)
        console.error('错误详情:', {
          message: error.message,
          stack: error.stack,
          technicianId: this.TechnicianUserID
        })
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } finally {
        this.loading = false
      }
    },
    
    formatDate(dateString) {
      const date = new Date(dateString)
      return date.getFullYear() + '-' + 
             String(date.getMonth() + 1).padStart(2, '0') + '-' + 
             String(date.getDate()).padStart(2, '0')
    },
    
    formatWorkHours(workHoursStr) {
      try {
        const workHours = JSON.parse(workHoursStr)
        const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
        const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        
        let result = []
        days.forEach((day, index) => {
          if (workHours[day] && workHours[day] !== 'off') {
            result.push(`${dayNames[index]} ${workHours[day].start}-${workHours[day].end}`)
          }
        })
        
        return result.join('\n') || '暂无工作时间信息'
      } catch (error) {
        return '暂无工作时间信息'
      }
    },
    
    async handleCollect() {
      try {
        const response = await collectItem({
          item_id: this.technicianInfo.id,
          item_type: 'technician',
          action: 'toggle'
        })
        
        if (response.code === 200) {
          this.isCollected = response.data.is_collected
          uni.showToast({
            title: this.isCollected ? '收藏成功' : '取消收藏成功',
            icon: 'success'
          })
        } else {
          uni.showToast({
            title: response.message || '操作失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('收藏操作失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    },
    
    previewImage(images, current) {
      uni.previewImage({
        urls: images,
        current: images[current]
      })
    },
    
    viewAllReviews() {
      uni.navigateTo({
        url: `/pages/beauty/reviews/list?TechnicianUserID=${this.TechnicianUserID}`
      })
    },
    
    consult() {
      uni.showToast({
        title: '咨询功能开发中',
        icon: 'none'
      })
    },
    
    async showAvailableTime() {
      try {
        const today = new Date()
        const dateStr = today.getFullYear() + '-' + 
                       String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                       String(today.getDate()).padStart(2, '0')
        
        const params = {
          technician_id: this.technicianInfo.id,
          date: dateStr
        }
        
        // 只有当serviceId有效时才添加service_id参数
        if (this.serviceId && this.serviceId !== null && this.serviceId !== undefined) {
          params.service_id = this.serviceId
        }
        
        const response = await getTechnicianAvailableTime(params)
        
        if (response.code === 200) {
          const timeSlots = response.data.time_slots || []
          const availableSlots = timeSlots.filter(slot => slot.available)
          
          if (availableSlots.length > 0) {
            const timeText = availableSlots.map(slot => 
              `${slot.start_time}-${slot.end_time}`
            ).join('、')
            
            uni.showModal({
              title: '今日可用时间',
              content: `可预约时间段：\n${timeText}`,
              showCancel: true,
              cancelText: '取消',
              confirmText: '立即预约',
              success: (res) => {
                if (res.confirm) {
                  this.goToBooking()
                }
              }
            })
          } else {
            uni.showModal({
              title: '暂无可用时间',
              content: '今日该技师暂无可用时间段，请选择其他日期或技师。',
              showCancel: false
            })
          }
        } else {
          uni.showToast({
            title: '获取时间失败',
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('获取可用时间失败:', error)
        uni.showToast({
          title: '获取时间失败',
          icon: 'none'
        })
      }
    },
    
    goToBooking() {
      // 如果有预选服务，直接跳转到预约时间选择页面
      if (this.serviceId) {
        uni.navigateTo({
          url: `/pages/beauty/booking/time?serviceId=${this.serviceId}&TechnicianUserID=${this.TechnicianUserID}`
        })
      } else {
        // 如果没有预选服务，使用本地存储传递技师ID，然后跳转到服务列表页面
        console.log('使用本地存储传递技师ID:', this.TechnicianUserID)
        uni.setStorageSync('preSelectedTechnicianId', this.TechnicianUserID)
        uni.switchTab({
          url: '/pages/beauty/service/list'
        })
      }
    },
    
    handleAvatarError(e) {
      console.log('头像加载失败:', e)
      // 设置默认头像
      this.technicianInfo.avatar = '/static/beauty/default-avatar.jpg'
    }
  }
}
</script>

<style lang="scss" scoped>
.technician-detail {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 100px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
  
  .loading-text {
    font-size: 14px;
    color: #999;
  }
}

.technician-info {
  .basic-info {
    background: #fff;
    padding: 30rpx;
    margin-bottom: 20rpx;
    display: flex;
    align-items: flex-start;
    position: relative;
    
    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      margin-right: 30rpx;
      border: 3rpx solid #FFB6C1;
    }
    
    .info-content {
      flex: 1;
      
      .name-level {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;
        
        .name {
          font-size: 36rpx;
          font-weight: bold;
          color: #333;
          margin-right: 20rpx;
        }
        
        .level {
          font-size: 24rpx;
          color: #666;
          background: #f0f0f0;
          padding: 6rpx 12rpx;
          border-radius: 6rpx;
          margin-right: 10rpx;
        }
        
        .featured-badge {
          background: linear-gradient(135deg, #FFB6C1 0%, #FF69B4 100%);
          color: white;
          font-size: 20rpx;
          padding: 4rpx 8rpx;
          border-radius: 10rpx;
          
          text {
            color: white;
          }
        }
      }
      
      .rating-info {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;
        
        .stars {
          margin-right: 10rpx;
          
          .star {
            color: #FFB6C1;
            font-size: 28rpx;
          }
        }
        
        .rating-score {
          font-size: 28rpx;
          font-weight: bold;
          color: #333;
          margin-right: 10rpx;
        }
        
        .review-count {
          font-size: 24rpx;
          color: #666;
        }
      }
      
      .meta-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10rpx;
        
        .experience {
          font-size: 26rpx;
          color: #666;
        }
        
        .price {
          font-size: 28rpx;
          font-weight: bold;
          color: #FF6B81;
        }
      }
      
      .stats-info {
        display: flex;
        gap: 20rpx;
        
        .stat-item {
          font-size: 24rpx;
          color: #666;
          background: #f8f9fa;
          padding: 4rpx 8rpx;
          border-radius: 4rpx;
        }
      }
    }
    
    .action-buttons {
      position: absolute;
      top: 30rpx;
      right: 30rpx;
      
      .collect-btn {
        background: rgba(255, 255, 255, 0.9);
        color: #FF69B4;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 22rpx;
        display: flex;
        align-items: center;
        gap: 4rpx;
        border: 1rpx solid #FFB6C1;
        
        .collect-icon {
          font-size: 20rpx;
          color: #FF69B4;
        }
        
        .collect-text {
          font-size: 20rpx;
        }
      }
    }
  }
  
  .work-schedule {
    background: #fff;
    padding: 30rpx;
    margin-bottom: 20rpx;
    
    .schedule-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      
      .schedule-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
      
      .view-available-time {
        font-size: 26rpx;
        color: #FFB6C1;
        border: 1rpx solid #FFB6C1;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
      }
    }
    
    .schedule-content {
      .work-time {
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
      }
    }
  }
  
  .skills-section, .introduction-section, .gallery-section, .services-section, .reviews-section {
    background: #fff;
    padding: 30rpx;
    margin-bottom: 20rpx;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      
      .section-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
      
      .view-all {
        font-size: 26rpx;
        color: #FFB6C1;
      }
    }
    
    .skills-content {
      display: flex;
      flex-wrap: wrap;
      gap: 10rpx;
      
      .skill-tag {
        background: #FFF5F8;
        color: #FFB6C1;
        font-size: 24rpx;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        border: 1rpx solid #FFB6C1;
      }
    }
    
    .introduction-content {
      .introduction-text {
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
      }
    }
    
    .gallery-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 10rpx;
      
      .gallery-item {
        width: 100%;
        height: 200rpx;
        border-radius: 8rpx;
      }
    }
    
    .services-content {
      .service-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .service-name {
          font-size: 28rpx;
          color: #333;
        }
        
        .service-price {
          font-size: 28rpx;
          font-weight: bold;
          color: #FF6B81;
        }
      }
    }
    
    .reviews-content {
      .review-item {
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .review-header {
          display: flex;
          align-items: center;
          margin-bottom: 10rpx;
          
          .user-avatar {
            width: 60rpx;
            height: 60rpx;
            border-radius: 30rpx;
            margin-right: 15rpx;
          }
          
          .review-info {
            flex: 1;
            
            .user-name {
              font-size: 26rpx;
              color: #333;
              margin-bottom: 5rpx;
            }
            
            .review-rating {
              .star {
                color: #FFB6C1;
                font-size: 24rpx;
              }
            }
          }
          
          .review-date {
            font-size: 24rpx;
            color: #999;
          }
        }
        
        .review-content {
          .review-text {
            font-size: 26rpx;
            color: #666;
            line-height: 1.5;
          }
        }
      }
    }
  }
}

.booking-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .booking-info {
    .booking-price {
      font-size: 32rpx;
      font-weight: bold;
      color: #FF6B81;
      display: block;
    }
    
    .booking-desc {
      font-size: 24rpx;
      color: #999;
    }
  }
  
  .booking-actions {
    display: flex;
    gap: 20rpx;
    
    .consult-btn, .booking-btn {
      padding: 20rpx 40rpx;
      border-radius: 40rpx;
      font-size: 28rpx;
      border: none;
    }
    
    .consult-btn {
      background: #f0f0f0;
      color: #666;
    }
    
    .booking-btn {
      background: linear-gradient(135deg, #FFB6C1 0%, #FF69B4 100%);
      color: white;
    }
  }
}
</style> 