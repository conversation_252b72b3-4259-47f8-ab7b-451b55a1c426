<template>
	<view class="service-list-container">
		<!-- 统一导航栏 -->
		<beauty-navbar 
			title="服务列表" 
			:show-back="showBackButton"
			:show-search="true"
			@search="showSearch"
		></beauty-navbar>
		
		<!-- 搜索框 -->
		<view class="search-box" v-if="showSearchInput" style="margin-top: 88px;">
			<input 
				class="search-input" 
				type="text" 
				v-model="searchKeyword" 
				placeholder="搜索服务名称或关键词" 
				confirm-type="search"
				@confirm="handleSearch"
			/>
			<text class="search-btn" @click="handleSearch">搜索</text>
		</view>
		
		<!-- 分类筛选 -->
		<scroll-view scroll-x="true" class="category-scroll" :style="!showSearchInput ? 'margin-top: 88px;' : ''">
			<view class="category-list">
				<view 
					class="category-item" 
					v-for="(category, index) in categories" 
					:key="index"
					:class="{'active': currentCategory === category.id}"
					@click="selectCategory(category.id)"
				>
					{{category.name}}
				</view>
			</view>
		</scroll-view>
		
		<!-- 排序筛选 -->
		<view class="filter-bar">
			<view 
				class="filter-item" 
				v-for="(filter, index) in filters" 
				:key="index"
				:class="{'active': currentFilter === filter.type}"
				@click="selectFilter(filter.type)"
			>
				{{filter.name}}
				<text class="filter-icon" v-if="filter.sortable">
					{{ getSortIcon(filter.type) }}
				</text>
			</view>
		</view>
		
		<!-- 服务列表 -->
		<scroll-view 
			scroll-y="true" 
			class="service-scroll"
			@scrolltolower="loadMore"
			:refresher-enabled="true"
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresh"
		>
			<view class="service-list">
				<view 
					class="service-item" 
					v-for="service in serviceList" 
					:key="service.id"
					@click="goToDetail(service.id)"
				>
					<image 
						:src="getServiceImage(service)" 
						mode="aspectFill" 
						class="service-image"
						@error="handleImageError"
					></image>
					<view class="service-info">
						<text class="service-name">{{service.name}}</text>
						<text class="service-subtitle" v-if="service.subtitle">{{service.subtitle}}</text>
						<text class="service-desc">{{service.description}}</text>
						<view class="service-meta">
							<view class="service-price-box">
								<text class="service-price">¥{{service.price}}</text>
								<text class="service-original-price" v-if="service.original_price && service.original_price > service.price">¥{{service.original_price}}</text>
							</view>
							<view class="service-stats">
								<text class="service-sold">已售{{service.booking_count}}+</text>
								<text class="service-rating">{{service.rating_avg}}分</text>
							</view>
						</view>
						<view class="service-tags">
							<text class="service-tag" v-for="(tag, tagIndex) in service.tags.slice(0, 3)" :key="tagIndex">{{tag}}</text>
						</view>
						<view class="service-badges">
							<text class="service-badge hot" v-if="service.is_hot">热门</text>
							<text class="service-badge new" v-if="service.is_new">新品</text>
						</view>
					</view>
					<view class="service-actions">
						<view 
							class="favorite-btn" 
							:class="{'favorited': service.isFavorite}"
							@click.stop="toggleFavorite(service)"
						>
							<text class="favorite-icon">{{service.isFavorite ? '❤️' : '♡'}}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 加载更多 -->
			<view class="loading-more" v-if="isLoading">
				<text class="loading-text">加载中...</text>
			</view>
			
			<!-- 无数据提示 -->
			<view class="empty-tip" v-if="serviceList.length === 0 && !isLoading">
				<image src="/static/beauty/empty.png" mode="aspectFit" class="empty-image"></image>
				<text class="empty-text">暂无相关服务</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import { getServiceList, getServiceCategories } from '@/api/beauty/index.js'
import { collectItem } from '@/api/collect.js'

export default {
	data() {
		return {
			searchKeyword: '',
			showSearchInput: false,
			showBackButton: false, // 动态控制返回按钮显示
			currentCategory: 0, // 0表示全部
			currentFilter: 'default',
			sortDirection: 'desc',
			page: 1,
			pageSize: 10,
			isLoading: false,
			isRefreshing: false,
			hasMore: true,
			TechnicianUserID: null, // 预选技师ID
			
			// 分类数据
			categories: [
				{ id: 0, name: '全部' }
			],
			
			// 筛选选项
			filters: [
				{ type: 'default', name: '综合排序', sortable: false },
				{ type: 'booking_desc', name: '销量', sortable: true },
				{ type: 'price', name: '价格', sortable: true },
				{ type: 'rating_desc', name: '评分', sortable: true }
			],
			
			// 服务列表数据
			serviceList: []
		}
	},
	
	onLoad(options) {
		console.log('服务列表页面参数:', options)
		console.log('所有参数键:', Object.keys(options))
		
		// 检查页面栈，决定是否显示返回按钮
		const pages = getCurrentPages()
		this.showBackButton = pages.length > 1
		
		// 处理从其他页面传来的参数
		if (options.categoryId) {
			this.currentCategory = parseInt(options.categoryId)
		}
		
		if (options.keyword) {
			this.searchKeyword = options.keyword
			this.showSearchInput = true
		}
		
		// 处理预选技师ID（优先从URL参数，其次从本地存储）
		if (options.TechnicianUserID) {
			this.TechnicianUserID = parseInt(options.TechnicianUserID)
			console.log('从URL参数获取预选技师ID:', this.TechnicianUserID)
		} else {
			// 从本地存储获取预选技师ID
			const storedTechnicianId = uni.getStorageSync('preSelectedTechnicianId')
			if (storedTechnicianId) {
				this.TechnicianUserID = parseInt(storedTechnicianId)
				console.log('从本地存储获取预选技师ID:', this.TechnicianUserID)
				// 清除本地存储
				uni.removeStorageSync('preSelectedTechnicianId')
			} else {
				console.log('没有预选技师ID参数')
				// 确保清理可能存在的缓存
				uni.removeStorageSync('preSelectedTechnicianId')
			}
		}
		
		// 加载初始数据
		this.loadCategories()
		this.loadServiceList()
	},
	
	onShow() {
		// 检查页面栈，决定是否显示返回按钮
		const pages = getCurrentPages()
		this.showBackButton = pages.length > 1
		
		// 检查本地存储的分类参数（用于从首页tabbar跳转）
		const categoryId = uni.getStorageSync('selectedCategoryId')
		if (categoryId) {
			this.currentCategory = parseInt(categoryId)
			// 清除本地存储
			uni.removeStorageSync('selectedCategoryId')
			uni.removeStorageSync('selectedCategoryName')
			// 重新加载数据
			this.resetAndLoad()
		}
	},
	
	methods: {
		// 显示搜索框
		showSearch() {
			this.showSearchInput = !this.showSearchInput
		},
		
		// 加载分类数据
		async loadCategories() {
			try {
				const response = await getServiceCategories()
				if (response.code === 200) {
					this.categories = [
						{ id: 0, name: '全部' },
						...response.data
					]
				}
			} catch (error) {
				console.error('加载分类失败:', error)
			}
		},
		
		// 处理搜索
		handleSearch() {
			this.resetAndLoad()
		},
		
		// 选择分类
		selectCategory(categoryId) {
			if (this.currentCategory === categoryId) return
			
			this.currentCategory = categoryId
			this.resetAndLoad()
		},
		
		// 选择筛选条件
		selectFilter(filterType) {
			if (this.currentFilter === filterType) {
				// 切换排序方向
				this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc'
			} else {
				this.currentFilter = filterType
				this.sortDirection = 'desc'
			}
			
			this.resetAndLoad()
		},
		
		// 重置并加载数据
		resetAndLoad() {
			this.page = 1
			this.serviceList = []
			this.hasMore = true
			this.loadServiceList()
		},
		
		// 加载服务列表
		async loadServiceList() {
			if (this.isLoading || !this.hasMore) return
			
			this.isLoading = true
			
			try {
				// 构建请求参数
				const params = {
					page: this.page,
					page_size: this.pageSize
				}
				
				// 添加分类筛选
				if (this.currentCategory !== 0) {
					params.category_id = this.currentCategory
				}
				
				// 添加搜索关键词
				if (this.searchKeyword.trim()) {
					params.keyword = this.searchKeyword.trim()
				}
				
				// 添加排序参数
				if (this.currentFilter !== 'default') {
					if (this.currentFilter === 'price') {
						params.sort = this.sortDirection === 'asc' ? 'price_asc' : 'price_desc'
					} else {
						params.sort = this.currentFilter
					}
				}
				
				const response = await getServiceList(params)
				if (response.code === 200) {
					const newServices = response.data.list || []
					
					// 为每个服务设置收藏状态
					newServices.forEach(service => {
						service.isFavorite = service.is_collected || false
					})
					
					if (this.page === 1) {
						this.serviceList = newServices
					} else {
						this.serviceList = [...this.serviceList, ...newServices]
					}
					
					// 检查是否还有更多数据
					this.hasMore = newServices.length === this.pageSize
					
					if (newServices.length > 0) {
						this.page++
					}
				}
				
			} catch (error) {
				console.error('加载服务列表失败:', error)
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			} finally {
				this.isLoading = false
				
				// 如果是下拉刷新，结束刷新状态
				if (this.isRefreshing) {
					this.isRefreshing = false
				}
			}
		},
		
		// 加载更多
		loadMore() {
			if (this.hasMore && !this.isLoading) {
				this.loadServiceList()
			}
		},
		
		// 下拉刷新
		onRefresh() {
			this.isRefreshing = true
			this.page = 1
			this.hasMore = true
			this.serviceList = []
			this.loadServiceList()
		},
		
		// 跳转到详情页
		goToDetail(serviceId) {
			console.log('goToDetail被调用，serviceId:', serviceId)
			console.log('当前TechnicianUserID:', this.TechnicianUserID)
			
			let url = `/pages/beauty/service/detail?id=${serviceId}`
			// 如果有预选技师，传递技师ID
			if (this.TechnicianUserID) {
				url += `&TechnicianUserID=${this.TechnicianUserID}`
				console.log('添加预选技师参数到URL')
			} else {
				console.log('没有预选技师参数')
			}
			
			console.log('最终跳转URL:', url)
			uni.navigateTo({
				url: url
			})
		},
		

		
		// 切换收藏状态
		async toggleFavorite(service) {
			try {
				const currentStatus = service.isFavorite
				
				const response = await collectItem({
					targetId: service.id,
					targetType: 'beauty_service',
					action: currentStatus ? 0 : 1
				})
				
				if (response.code === 200) {
					service.isFavorite = !currentStatus
					uni.showToast({
						title: currentStatus ? '取消收藏成功' : '收藏成功',
						icon: 'success'
					})
				} else {
					// 处理特殊错误情况
					if (response.message && response.message.includes('已经收藏过了')) {
						// 如果后端说已经收藏过了，更新本地状态为已收藏
						service.isFavorite = true
						uni.showToast({
							title: '已收藏',
							icon: 'success'
						})
					} else if (response.message && response.message.includes('未收藏')) {
						// 如果后端说未收藏，更新本地状态为未收藏
						service.isFavorite = false
						uni.showToast({
							title: '未收藏',
							icon: 'success'
						})
					} else {
						uni.showToast({
							title: response.message || '操作失败',
							icon: 'none'
						})
					}
				}
			} catch (error) {
				console.error('收藏操作失败:', error)
				// 处理错误信息中的特殊情况
				if (error.message && error.message.includes('已经收藏过了')) {
					service.isFavorite = true
					uni.showToast({
						title: '已收藏',
						icon: 'success'
					})
				} else {
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					})
				}
			}
		},
		
		// 获取服务图片
		getServiceImage(service) {
			if (service.images && service.images.length > 0) {
				return service.images[0]
			}
			return '/static/beauty/default-service.jpg'
		},
		
		// 处理图片错误
		handleImageError(e) {
			console.error('图片加载失败:', e)
		},
		
		// 获取排序图标
		getSortIcon(filterType) {
			if (this.currentFilter === filterType && filterType !== 'default') {
				return this.sortDirection === 'asc' ? '↑' : '↓'
			}
			return ''
		}
	}
}
</script>

<style lang="scss">
.service-list-container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 页面容器 */

/* 搜索框 */
.search-box {
	padding: 10px 15px;
	display: flex;
	align-items: center;
	background: white;
	
	.search-input {
		flex: 1;
		height: 36px;
		background: #f5f5f5;
		border-radius: 18px;
		padding: 0 15px;
		font-size: 14px;
	}
	
	.search-btn {
		margin-left: 10px;
		font-size: 14px;
		color: #ff6b9d;
		font-weight: bold;
	}
}

/* 分类筛选 */
.category-scroll {
	background: white;
	white-space: nowrap;
	
	.category-list {
		display: flex;
		padding: 15px 10px;
		
		.category-item {
			padding: 6px 15px;
			margin: 0 5px;
			font-size: 14px;
			color: #666;
			background: #f5f5f5;
			border-radius: 16px;
			
			&.active {
				background: #fff5f8;
				color: #ff6b9d;
				font-weight: bold;
			}
		}
	}
}

/* 排序筛选 */
.filter-bar {
	display: flex;
	background: white;
	border-bottom: 1px solid #eee;
	
	.filter-item {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 44px;
		font-size: 14px;
		color: #666;
		
		&.active {
			color: #ff6b9d;
			font-weight: bold;
		}
		
		.filter-icon {
			margin-left: 3px;
			font-size: 12px;
		}
	}
}

/* 服务列表 */
.service-scroll {
	height: calc(100vh - 196px); /* 减去导航栏、分类栏和筛选栏的高度 */
	
	.service-list {
		padding: 10px;
		
		.service-item {
			display: flex;
			background: white;
			margin-bottom: 10px;
			border-radius: 8px;
			padding: 15px;
			box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
			position: relative;
			
			.service-image {
				width: 120px;
				height: 120px;
				border-radius: 8px;
				margin-right: 15px;
				flex-shrink: 0;
			}
			
			.service-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				
				.service-name {
					font-size: 16px;
					font-weight: bold;
					color: #333;
					margin-bottom: 5px;
				}
				
				.service-subtitle {
					font-size: 12px;
					color: #999;
					margin-bottom: 5px;
				}
				
				.service-desc {
					font-size: 14px;
					color: #666;
					margin-bottom: 10px;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					overflow: hidden;
					text-overflow: ellipsis;
				}
				
				.service-meta {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 10px;
					
					.service-price-box {
						.service-price {
							font-size: 18px;
							font-weight: bold;
							color: #ff6b9d;
							margin-right: 5px;
						}
						
						.service-original-price {
							font-size: 12px;
							color: #999;
							text-decoration: line-through;
						}
					}
					
					.service-stats {
						display: flex;
						font-size: 12px;
						color: #999;
						
						.service-sold {
							margin-right: 10px;
						}
					}
				}
				
				.service-tags {
					display: flex;
					flex-wrap: wrap;
					
					.service-tag {
						background: #fff5f8;
						color: #ff6b9d;
						font-size: 12px;
						padding: 2px 6px;
						border-radius: 4px;
						margin-right: 6px;
						margin-bottom: 5px;
					}
				}
				
				.service-badges {
					display: flex;
					flex-wrap: wrap;
					margin-top: 5px;
					
					.service-badge {
						font-size: 10px;
						color: white;
						padding: 2px 6px;
						border-radius: 4px;
						margin-right: 5px;
						margin-bottom: 5px;
						
						&.hot {
							background-color: #ff6b9d;
						}
						
						&.new {
							background-color: #4CAF50;
						}
					}
				}
			}
			
			.service-actions {
				position: absolute;
				top: 15px;
				right: 15px;
				display: flex;
				flex-direction: column;
				align-items: center;
				
				.favorite-btn {
					width: 36px;
					height: 36px;
					border-radius: 18px;
					background: rgba(255, 255, 255, 0.9);
					display: flex;
					align-items: center;
					justify-content: center;
					box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
					
					.favorite-icon {
						font-size: 18px;
						color: #ccc;
					}
					
					&.favorited .favorite-icon {
						color: #ff6b9d;
					}
				}
			}
		}
	}
}

/* 加载更多 */
.loading-more {
	text-align: center;
	padding: 15px 0;
	
	.loading-text {
		font-size: 14px;
		color: #999;
	}
}

/* 无数据提示 */
.empty-tip {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 50px 0;
	
	.empty-image {
		width: 100px;
		height: 100px;
		margin-bottom: 15px;
	}
	
	.empty-text {
		font-size: 14px;
		color: #999;
	}
}
</style>
