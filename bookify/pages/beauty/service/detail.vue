<template>
	<view class="service-detail">
		<!-- 统一导航栏 -->
		<beauty-navbar 
			title="服务详情" 
			:show-back="true"
		>
			<template #right>
				<view class="nav-actions" v-if="!isLoading">
					<text class="nav-icon" @click="toggleFavorite">{{isFavorite ? '❤️' : '♡'}}</text>
					<text class="nav-icon" @click="showShare">⋮</text>
				</view>
			</template>
		</beauty-navbar>
		
		<!-- 加载状态 -->
		<view class="loading-container" v-if="isLoading" style="margin-top: 88px;">
			<view class="loading-content">
				<text class="loading-text">加载中...</text>
			</view>
		</view>
		
		<!-- 服务图片轮播 -->
		<view class="service-images" v-if="!isLoading" style="margin-top: 88px;">
			<swiper 
				class="swiper" 
				:indicator-dots="serviceDetail.images.length > 1" 
				:autoplay="false"
				indicator-color="rgba(255, 255, 255, 0.5)"
				indicator-active-color="#fff"
			>
				<swiper-item v-for="(image, index) in serviceDetail.images" :key="index">
					<image :src="image" mode="aspectFill" class="service-image" @error="handleImageError"></image>
				</swiper-item>
			</swiper>
		</view>
		
		<!-- 服务基本信息 -->
		<view class="service-info-card" v-if="!isLoading">
			<view class="service-header">
				<view class="service-name">{{serviceDetail.name}}</view>
				<view class="service-subtitle" v-if="serviceDetail.subtitle">{{serviceDetail.subtitle}}</view>
			</view>
			
			<view class="service-price-row">
				<text class="service-price">¥{{serviceDetail.price}}</text>
				<text class="service-original-price" v-if="serviceDetail.original_price && serviceDetail.original_price > serviceDetail.price">¥{{serviceDetail.original_price}}</text>
				<text class="service-duration">{{serviceDetail.duration}}分钟</text>
			</view>
			
			<view class="service-stats">
				<text class="service-sold">已售 {{serviceDetail.booking_count}}+</text>
				<text class="service-rating">{{serviceDetail.rating_avg}}分 ({{serviceDetail.rating_count}}评价)</text>
			</view>
			
			<view class="service-tags" v-if="serviceDetail.tags && serviceDetail.tags.length > 0">
				<view class="service-tag" v-for="(tag, index) in serviceDetail.tags" :key="index">{{tag}}</view>
			</view>
			
			<view class="service-badges">
				<text class="service-badge hot" v-if="serviceDetail.is_hot">热门</text>
				<text class="service-badge new" v-if="serviceDetail.is_new">新品</text>
			</view>
		</view>
		
		<!-- 服务详细介绍 -->
		<view class="service-detail-card" v-if="!isLoading">
			<view class="card-title">服务介绍</view>
			<view class="service-description">{{serviceDetail.description}}</view>
		</view>
		
		<!-- 适用人群 -->
		<view class="service-suitable-card" v-if="!isLoading && (serviceDetail.age_min || serviceDetail.age_max || serviceDetail.gender_limit !== undefined)">
			<view class="card-title">适用人群</view>
			<view class="suitable-info">
				<view class="suitable-item" v-if="serviceDetail.age_min && serviceDetail.age_max">
					<text class="suitable-label">年龄要求：</text>
					<text class="suitable-value">{{serviceDetail.age_min}} - {{serviceDetail.age_max}}岁</text>
				</view>
				<view class="suitable-item" v-if="serviceDetail.gender_limit !== undefined">
					<text class="suitable-label">性别要求：</text>
					<text class="suitable-value">{{getGenderText(serviceDetail.gender_limit)}}</text>
				</view>
			</view>
		</view>
		
		<!-- 注意事项 -->
		<view class="service-notice-card" v-if="!isLoading && (serviceDetail.contraindications || serviceDetail.preparation_notes)">
			<view class="card-title">注意事项</view>
			
			<view class="notice-section" v-if="serviceDetail.contraindications">
				<view class="notice-header">
					<text class="notice-icon">⚠️</text>
					<text class="notice-title">禁忌症</text>
				</view>
				<text class="notice-content">{{serviceDetail.contraindications}}</text>
			</view>
			
			<view class="notice-section" v-if="serviceDetail.preparation_notes">
				<view class="notice-header">
					<text class="notice-icon">📝</text>
					<text class="notice-title">准备事项</text>
				</view>
				<text class="notice-content">{{serviceDetail.preparation_notes}}</text>
			</view>
		</view>
		
		<!-- 预约规则 -->
		<view class="service-booking-rules-card" v-if="!isLoading">
			<view class="card-title">预约规则</view>
			<view class="rules-list">
				<view class="rule-item">
					<text class="rule-icon">📅</text>
					<text class="rule-text">最多可提前{{serviceDetail.max_advance_days}}天预约</text>
				</view>
				<view class="rule-item">
					<text class="rule-icon">⏰</text>
					<text class="rule-text">最少需提前{{serviceDetail.min_advance_hours}}小时预约</text>
				</view>
				<view class="rule-item">
					<text class="rule-icon">🔄</text>
					<text class="rule-text">可在服务开始前{{serviceDetail.allow_cancel_hours}}小时取消</text>
				</view>
				<view class="rule-item" v-if="serviceDetail.need_technician">
					<text class="rule-icon">👩‍⚕️</text>
					<text class="rule-text">需要指定技师服务</text>
				</view>
			</view>
		</view>
		
		<!-- 推荐技师 -->
		<view class="technician-card" v-if="!isLoading && serviceDetail.available_technicians && serviceDetail.available_technicians.length > 0">
			<view class="card-title">推荐技师</view>
			<scroll-view scroll-x="true" class="technician-scroll">
				<view class="technician-list">
					<view 
						class="technician-item" 
						v-for="tech in serviceDetail.available_technicians" 
						:key="tech.id"
						@click="selectTechnician(tech)"
						:class="{'technician-selected': selectedTechnician && selectedTechnician.id === tech.id}"
					>
						<image :src="tech.avatar || '/static/beauty/default-avatar.png'" mode="aspectFill" class="technician-avatar"></image>
						<view class="technician-info">
							<text class="technician-name">{{tech.name}}</text>
							<text class="technician-level">{{tech.level}}</text>
							<view class="technician-rating">
								<text class="rating-text">{{tech.rating_avg}}分</text>
								<text class="experience-text">{{tech.experience}}年经验</text>
							</view>
							<text class="technician-fee" v-if="tech.extra_fee > 0">+¥{{tech.extra_fee}}</text>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
		
		<!-- 用户评价 -->
		<view class="reviews-card" v-if="!isLoading && serviceDetail.rating_count > 0">
			<view class="card-header">
				<view class="card-title">用户评价</view>
				<view class="view-all" @click="viewAllReviews">查看全部</view>
			</view>
			
			<view class="review-summary">
				<view class="rating-overview">
					<text class="rating-score">{{serviceDetail.rating_avg}}</text>
					<view class="rating-stars">
						<text class="star-filled" v-for="i in Math.floor(serviceDetail.rating_avg)" :key="'filled-' + i">⭐</text>
						<text class="star-empty" v-for="i in (5 - Math.floor(serviceDetail.rating_avg))" :key="'empty-' + i">☆</text>
					</view>
					<text class="rating-count">{{serviceDetail.rating_count}}条评价</text>
				</view>
			</view>
			
			<!-- 这里可以添加评价列表，暂时显示占位内容 -->
			<view class="review-placeholder">
				<text class="placeholder-text">暂无评价内容</text>
			</view>
		</view>
		
		<!-- 底部预约栏 -->
		<view class="booking-bar" v-if="!isLoading">
			<view class="booking-info">
				<text class="booking-price">¥{{serviceDetail.price}}</text>
				<text class="booking-duration">{{serviceDetail.duration}}分钟</text>
			</view>
			<view class="booking-actions">
				<button class="consult-btn" @click="consult">咨询</button>
				<button class="booking-btn" @click="goToBooking">立即预约</button>
			</view>
		</view>
	</view>
</template>

<script>
import { getServiceDetail, collectService } from '@/api/beauty/index.js'

export default {
	data() {
		return {
			serviceId: null,
			TechnicianUserID: null, // 预选技师ID
			isFavorite: false,
			selectedTechnician: null,
			isLoading: true,
			serviceDetail: {
				id: null,
				name: '',
				subtitle: '',
				price: 0,
				original_price: 0,
				duration: 0,
				booking_count: 0,
				rating_avg: 0,
				rating_count: 0,
				images: [],
				tags: [],
				description: '',
				contraindications: '',
				preparation_notes: '',
				need_technician: false,
				max_advance_days: 30,
				min_advance_hours: 2,
				allow_cancel_hours: 24,
				gender_limit: 0,
				age_min: 0,
				age_max: 100,
				is_hot: false,
				is_new: false,
				available_technicians: []
			}
		}
	},
	
	onLoad(options) {
		console.log('服务详情页面参数:', options)
		console.log('所有参数键:', Object.keys(options))
		
		if (options.id) {
			this.serviceId = parseInt(options.id)
			console.log('解析后的服务ID:', this.serviceId)
			
			// 处理预选技师ID
			if (options.TechnicianUserID) {
				this.TechnicianUserID = parseInt(options.TechnicianUserID)
				console.log('预选技师ID:', this.TechnicianUserID)
			} else {
				console.log('没有预选技师ID')
			}
			
			this.loadServiceDetail()
		} else {
			uni.showToast({
				title: '服务ID不存在',
				icon: 'none'
			})
			setTimeout(() => {
				uni.navigateBack()
			}, 1500)
		}
	},
	
	methods: {
		// 加载服务详情
		async loadServiceDetail() {
			this.isLoading = true
			
			try {
				const response = await getServiceDetail(this.serviceId)
				
				if (response.code === 200) {
					this.serviceDetail = {
						...this.serviceDetail,
						...response.data
					}
					
					// 检查收藏状态
					this.checkFavoriteStatus()
					
				} else {
					uni.showToast({
						title: response.message || '加载失败',
						icon: 'none'
					})
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				}
			} catch (error) {
				console.error('加载服务详情失败:', error)
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				})
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
			} finally {
				this.isLoading = false
			}
		},
		
		// 检查收藏状态
		async checkFavoriteStatus() {
			try {
				// 暂时使用本地存储检查收藏状态
				const favorites = uni.getStorageSync('service_favorites') || []
				this.isFavorite = favorites.includes(this.serviceId)
			} catch (error) {
				console.error('检查收藏状态失败:', error)
				this.isFavorite = false
			}
		},
		
		// 收藏/取消收藏
		async toggleFavorite() {
			try {
				const currentStatus = this.isFavorite
				
				const response = await collectService({
					targetId: this.serviceId,
					targetType: 'beauty_service',
					action: currentStatus ? 0 : 1
				})
				
				if (response.code === 200) {
					this.isFavorite = !currentStatus
					uni.showToast({
						title: currentStatus ? '取消收藏成功' : '收藏成功',
						icon: 'success'
					})
					
					// 更新本地存储
					let favorites = uni.getStorageSync('service_favorites') || []
					if (currentStatus) {
						favorites = favorites.filter(id => id !== this.serviceId)
					} else {
						if (!favorites.includes(this.serviceId)) {
							favorites.push(this.serviceId)
						}
					}
					uni.setStorageSync('service_favorites', favorites)
				} else {
					uni.showToast({
						title: response.message || '操作失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('收藏操作失败:', error)
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				})
			}
		},
		
		// 分享
		showShare() {
			uni.showActionSheet({
				itemList: ['分享给朋友', '分享到朋友圈', '复制链接'],
				success: (res) => {
					uni.showToast({
						title: '分享功能开发中',
						icon: 'none'
					})
				}
			})
		},
		
		// 选择技师
		selectTechnician(technician) {
			this.selectedTechnician = technician
			uni.showToast({
				title: `已选择${technician.name}`,
				icon: 'none'
			})
		},
		
		// 查看所有评价
		viewAllReviews() {
			uni.navigateTo({
				url: `/pages/beauty/reviews/list?serviceId=${this.serviceDetail.id}`
			})
		},
		
		// 处理图片错误
		handleImageError(e) {
			console.error('图片加载失败:', e)
		},
		
		// 获取性别文本
		getGenderText(genderLimit) {
			switch (genderLimit) {
				case 1:
					return '仅限男性'
				case 2:
					return '仅限女性'
				default:
					return '不限'
			}
		},
		
		// 咨询
		consult() {
			uni.showToast({
				title: '咨询功能开发中',
				icon: 'none'
			})
		},
		
		// 前往预约页面
		goToBooking() {
			console.log('goToBooking被调用')
			console.log('当前TechnicianUserID:', this.TechnicianUserID)
			console.log('服务是否需要技师:', this.serviceDetail.need_technician)
			
			// 构建基础URL
			let baseUrl = ''
			
			// 如果需要技师，跳转到技师选择页面
			if (this.serviceDetail.need_technician) {
				baseUrl = `/pages/beauty/booking/technician?serviceId=${this.serviceDetail.id}`
			} else {
				// 直接跳转到时间选择页面
				baseUrl = `/pages/beauty/booking/time?serviceId=${this.serviceDetail.id}`
			}
			
			// 如果有预选技师，添加技师ID参数
			if (this.TechnicianUserID) {
				baseUrl += `&TechnicianUserID=${this.TechnicianUserID}`
				console.log('添加预选技师参数')
			} else {
				console.log('没有预选技师参数')
				// 清理可能存在的预选技师缓存
				uni.removeStorageSync('preSelectedTechnicianId')
				console.log('清理预选技师缓存')
			}
			
			console.log('最终跳转URL:', baseUrl)
			uni.navigateTo({
				url: baseUrl
			})
		}
	}
}
</script>

<style lang="scss">
.service-detail {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 100px; /* 为底部预约栏留出空间 */
}

/* 导航栏自定义样式 */
.nav-actions {
	display: flex;
	align-items: center;
	gap: 10px;
	
	.nav-icon {
		width: 32px;
		height: 32px;
		display: flex;
		align-items: center;
		justify-content: center;
		background: rgba(255, 255, 255, 0.2);
		border-radius: 16px;
		font-size: 16px;
		color: #fff;
	}
}

/* 服务图片轮播 */
.service-images {
	height: 300px;
	
	.swiper {
		height: 100%;
		
		.service-image {
			width: 100%;
			height: 100%;
		}
	}
}

/* 卡片通用样式 */
.service-info-card, .service-detail-card, .service-suitable-card, .service-notice-card, .service-booking-rules-card, .technician-card, .reviews-card {
	background-color: #fff;
	margin: 15px;
	border-radius: 10px;
	padding: 15px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 卡片标题通用样式 */
.card-title {
	font-size: 18px;
	font-weight: bold;
	color: #333;
	margin-bottom: 15px;
	position: relative;
	padding-left: 10px;
	
	&:before {
		content: '';
		position: absolute;
		left: 0;
		top: 2px;
		bottom: 2px;
		width: 4px;
		background: linear-gradient(135deg, #ff6b9d, #ff8e9b);
		border-radius: 2px;
	}
}

/* 服务基本信息 */
.service-info-card {
	margin-top: -20px;
	border-top-left-radius: 20px;
	border-top-right-radius: 20px;
	
	.service-header {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
		
		.service-name {
			font-size: 22px;
			font-weight: bold;
			color: #333;
			margin-right: 10px;
		}
		
		.service-subtitle {
			font-size: 14px;
			color: #666;
			background: #f0f0f0;
			padding: 4px 10px;
			border-radius: 8px;
		}
	}
	
	.service-price-row {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
		
		.service-price {
			font-size: 24px;
			font-weight: bold;
			color: #ff6b9d;
			margin-right: 10px;
		}
		
		.service-original-price {
			font-size: 14px;
			color: #999;
			text-decoration: line-through;
			margin-right: 10px;
		}
		
		.service-duration {
			font-size: 14px;
			color: #999;
		}
	}
	
	.service-stats {
		display: flex;
		justify-content: space-between;
		margin-bottom: 10px;
		
		.service-sold, .service-rating {
			font-size: 14px;
			color: #999;
		}
	}
	
	.service-tags {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 10px;
		
		.service-tag {
			background-color: #fff5f8;
			color: #ff6b9d;
			font-size: 12px;
			padding: 4px 8px;
			border-radius: 4px;
			margin-right: 8px;
			margin-bottom: 8px;
		}
	}
	
	.service-badges {
		display: flex;
		gap: 8px;
		
		.service-badge {
			font-size: 12px;
			padding: 4px 8px;
			border-radius: 6px;
			
			&.hot {
				background-color: #fffbe6;
				color: #ff9800;
				border: 1px solid #ffe0b2;
			}
			
			&.new {
				background-color: #e8f5e9;
				color: #4caf50;
				border: 1px solid #c8e6c9;
			}
		}
	}
}

/* 服务详细介绍 */
.service-description {
	font-size: 14px;
	color: #666;
	line-height: 1.6;
	margin-bottom: 15px;
}

/* 服务亮点 */
.service-highlights-card {
	.highlights-grid {
		display: flex;
		flex-wrap: wrap;
		gap: 10px;
		
		.highlight-item {
			display: flex;
			align-items: center;
			background: #fff5f8;
			padding: 8px 12px;
			border-radius: 20px;
			border: 1px solid #ffe0e6;
			
			.highlight-icon {
				margin-right: 6px;
				font-size: 14px;
			}
			
			.highlight-text {
				font-size: 14px;
				color: #ff6b9d;
			}
		}
	}
}

/* 服务流程 */
.service-process-card {
	.detailed-process-steps {
		.detailed-process-step {
			margin-bottom: 20px;
			padding: 15px;
			background: #fafafa;
			border-radius: 8px;
			border-left: 4px solid #ff6b9d;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.step-header {
				display: flex;
				align-items: center;
				margin-bottom: 10px;
				
				.step-number {
					width: 30px;
					height: 30px;
					background: linear-gradient(135deg, #ff6b9d, #ff8e9b);
					color: white;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 14px;
					font-weight: bold;
					margin-right: 12px;
					flex-shrink: 0;
				}
				
				.step-info {
					flex: 1;
					
					.step-title {
						font-size: 16px;
						font-weight: bold;
						color: #333;
						display: block;
						margin-bottom: 4px;
					}
					
					.step-duration {
						font-size: 12px;
						color: #999;
						background: #e8e8e8;
						padding: 2px 6px;
						border-radius: 10px;
					}
				}
			}
			
			.step-description {
				font-size: 14px;
				color: #666;
				line-height: 1.5;
				margin-bottom: 8px;
			}
			
			.step-tools {
				font-size: 12px;
				color: #999;
				
				.tools-label {
					color: #666;
					margin-right: 4px;
				}
				
				.tools-list {
					color: #ff6b9d;
				}
			}
		}
	}
}

/* 适用人群 */
.service-suitable-card {
	.suitable-info {
		.suitable-item {
			display: flex;
			align-items: center;
			margin-bottom: 10px;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.suitable-label {
				font-size: 14px;
				font-weight: bold;
				color: #333;
				margin-right: 8px;
				flex-shrink: 0;
			}
			
			.suitable-value {
				font-size: 14px;
				color: #666;
			}
		}
	}
}

/* 注意事项 */
.service-notice-card {
	.notice-section {
		margin-bottom: 20px;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.notice-header {
			display: flex;
			align-items: center;
			margin-bottom: 10px;
			
			.notice-icon {
				margin-right: 8px;
				font-size: 16px;
			}
			
			.notice-title {
				font-size: 16px;
				font-weight: bold;
				color: #333;
			}
		}
		
		.notice-content {
			font-size: 14px;
			color: #666;
			line-height: 1.5;
		}
	}
}

/* 预约规则 */
.service-booking-rules-card {
	.rules-list {
		.rule-item {
			display: flex;
			align-items: center;
			margin-bottom: 10px;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.rule-icon {
				margin-right: 8px;
				font-size: 16px;
			}
			
			.rule-text {
				font-size: 14px;
				color: #666;
			}
		}
	}
}

/* 推荐技师 */
.technician-scroll {
	white-space: nowrap;
	
	.technician-list {
		display: flex;
		
		.technician-item {
			width: 120px;
			margin-right: 15px;
			padding: 10px;
			border-radius: 8px;
			border: 1px solid #eee;
			display: flex;
			flex-direction: column;
			align-items: center;
			
			&.technician-selected {
				border-color: #ff6b9d;
				background-color: #fff5f8;
			}
			
			.technician-avatar {
				width: 60px;
				height: 60px;
				border-radius: 30px;
				margin-bottom: 8px;
			}
			
			.technician-info {
				width: 100%;
				text-align: center;
				
				.technician-name {
					font-size: 14px;
					font-weight: bold;
					color: #333;
					display: block;
					margin-bottom: 2px;
				}
				
				.technician-level {
					font-size: 12px;
					color: #666;
					display: block;
					margin-bottom: 5px;
				}
				
				.technician-rating {
					font-size: 12px;
					color: #999;
					display: flex;
					flex-direction: column;
					
					.rating-text {
						margin-bottom: 2px;
					}
				}
				
				.technician-fee {
					font-size: 12px;
					color: #ff6b9d;
					margin-top: 5px;
				}
			}
		}
	}
}

/* 用户评价 */
.reviews-card {
	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15px;
		
		.view-all {
			font-size: 14px;
			color: #ff6b9d;
		}
	}
	
	.review-summary {
		display: flex;
		align-items: center;
		margin-bottom: 15px;
		
		.rating-overview {
			display: flex;
			align-items: center;
			
			.rating-score {
				font-size: 24px;
				font-weight: bold;
				color: #ff6b9d;
				margin-right: 5px;
			}
			
			.rating-stars {
				display: flex;
				margin-right: 5px;
				
				.star-filled {
					font-size: 14px;
					color: #ff6b9d;
				}
				
				.star-empty {
					font-size: 14px;
					color: #999;
				}
			}
			
			.rating-count {
				font-size: 12px;
				color: #999;
			}
		}
	}
	
	.review-placeholder {
		text-align: center;
		padding: 20px 0;
		color: #999;
		font-size: 14px;
	}
}

/* 加载状态 */
.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 50vh;
	
	.loading-content {
		text-align: center;
		
		.loading-text {
			font-size: 14px;
			color: #999;
		}
	}
}

/* 底部预约栏 */
.booking-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: white;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 10px 15px;
	box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
	
	.booking-info {
		.booking-price {
			font-size: 20px;
			font-weight: bold;
			color: #ff6b9d;
			display: block;
		}
		
		.booking-duration {
			font-size: 12px;
			color: #999;
		}
	}
	
	.booking-actions {
		display: flex;
		
		.consult-btn, .booking-btn {
			height: 40px;
			border-radius: 20px;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 14px;
			font-weight: bold;
			margin-left: 10px;
		}
		
		.consult-btn {
			background-color: #fff5f8;
			color: #ff6b9d;
			border: 1px solid #ff6b9d;
			width: 80px;
		}
		
		.booking-btn {
			background: linear-gradient(135deg, #ff6b9d, #ff8e9b);
			color: white;
			width: 120px;
		}
	}
}

/* 服务保障样式 */
.service-guarantee-card {
	.guarantee-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 15px;
		
		.guarantee-item {
			background: #fafafa;
			padding: 15px;
			border-radius: 8px;
			text-align: center;
			
			.guarantee-icon {
				font-size: 24px;
				margin-bottom: 8px;
				display: block;
			}
			
			.guarantee-label {
				font-size: 14px;
				font-weight: bold;
				color: #333;
				margin-bottom: 6px;
				display: block;
			}
			
			.guarantee-text {
				font-size: 12px;
				color: #666;
				line-height: 1.4;
			}
		}
	}
}

/* 护理建议样式 */
.service-care-card {
	.care-advice-list {
		.care-advice-item {
			display: flex;
			align-items: flex-start;
			margin-bottom: 12px;
			padding: 10px;
			background: #f8f9fa;
			border-radius: 8px;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.advice-icon {
				margin-right: 10px;
				font-size: 16px;
				flex-shrink: 0;
			}
			
			.advice-text {
				font-size: 14px;
				color: #666;
				line-height: 1.5;
			}
		}
	}
}
</style>

