<template>
	<view class="technician-entry">
		<!-- 统一导航栏 -->
		<beauty-navbar 
			title="技师工作台" 
			:show-back="true"
		></beauty-navbar>
		
		<!-- 今日数据统计 -->
		<view class="stats-section" style="margin-top: 88px;">
			<view class="stats-card">
				<view class="stats-title">今日数据</view>
				<view class="stats-row">
					<view class="stat-item">
						<view class="stat-value">8</view>
						<view class="stat-label">预约数</view>
					</view>
					<view class="stat-item">
						<view class="stat-value">¥1280</view>
						<view class="stat-label">预计收入</view>
					</view>
					<view class="stat-item">
						<view class="stat-value">5</view>
						<view class="stat-label">已完成</view>
					</view>
					<view class="stat-item">
						<view class="stat-value">98%</view>
						<view class="stat-label">满意度</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 功能入口 -->
		<view class="menu-section">
			<view class="menu-grid">
				<view class="menu-item" @click="goToPage('/pages/beauty/technician/dashboard/index')">
					<view class="menu-icon">
						<text class="iconfont icon-dashboard"></text>
					</view>
					<view class="menu-title">工作台</view>
					<view class="menu-desc">今日概览</view>
				</view>
				
				<view class="menu-item" @click="goToPage('/pages/beauty/technician/dashboard/customer-list')">
					<view class="menu-icon">
						<text class="iconfont icon-user"></text>
					</view>
					<view class="menu-title">客户管理</view>
					<view class="menu-desc">客户档案</view>
				</view>
				
				<view class="menu-item" @click="goToPage('/pages/beauty/technician/dashboard/earnings')">
					<view class="menu-icon">
						<text class="iconfont icon-money"></text>
					</view>
					<view class="menu-title">收益统计</view>
					<view class="menu-desc">收入分析</view>
				</view>
				
				<view class="menu-item" @click="goToPage('/pages/beauty/technician/dashboard/profile')">
					<view class="menu-icon">
						<text class="iconfont icon-setting"></text>
					</view>
					<view class="menu-title">个人设置</view>
					<view class="menu-desc">基本信息</view>
				</view>
			</view>
		</view>
		
		<!-- 返回美容首页 -->
		<view class="back-home-section">
			<view class="back-home-btn" @click="goToBeautyHome">
				<text class="iconfont icon-home"></text>
				<text>返回美容首页</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'TechnicianEntry',
		data() {
			return {
				
			}
		},
		methods: {
			goBack() {
				uni.navigateBack()
			},
			goToPage(url) {
				uni.navigateTo({
					url: url
				})
			},
			goToBeautyHome() {
				uni.navigateTo({
					url: '/pages/beauty/index/index'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.technician-entry {
		background: #f5f5f5;
		min-height: 100vh;
	}
	
	.header {
		position: relative;
		height: 200rpx;
		
		.header-bg {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 100%;
			background: linear-gradient(135deg, #FFB6C1 0%, #FF69B4 100%);
		}
		
		.header-content {
			position: relative;
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 100%;
			padding: 0 40rpx;
			padding-top: var(--status-bar-height);
			
			.back-btn {
				width: 60rpx;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
				background: rgba(255, 255, 255, 0.2);
				
				.iconfont {
					font-size: 32rpx;
					color: white;
				}
			}
			
			.title {
				font-size: 36rpx;
				font-weight: bold;
				color: white;
			}
			
			.placeholder {
				width: 60rpx;
			}
		}
	}
	
	.stats-section {
		padding: 40rpx;
		margin-top: -60rpx;
		
		.stats-card {
			background: white;
			border-radius: 24rpx;
			padding: 40rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
			
			.stats-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 30rpx;
			}
			
			.stats-row {
				display: flex;
				justify-content: space-between;
				
				.stat-item {
					text-align: center;
					
					.stat-value {
						font-size: 40rpx;
						font-weight: bold;
						color: #FF69B4;
						margin-bottom: 10rpx;
					}
					
					.stat-label {
						font-size: 24rpx;
						color: #666;
					}
				}
			}
		}
	}
	
	.menu-section {
		padding: 0 40rpx;
		
		.menu-grid {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 30rpx;
			
			.menu-item {
				background: white;
				border-radius: 24rpx;
				padding: 40rpx;
				text-align: center;
				box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
				transition: all 0.3s ease;
				
				&:active {
					transform: scale(0.95);
				}
				
				.menu-icon {
					width: 80rpx;
					height: 80rpx;
					margin: 0 auto 20rpx;
					background: linear-gradient(135deg, #FFB6C1 0%, #FF69B4 100%);
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					
					.iconfont {
						font-size: 40rpx;
						color: white;
					}
				}
				
				.menu-title {
					font-size: 28rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 10rpx;
				}
				
				.menu-desc {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
	}
	
	.back-home-section {
		padding: 60rpx 40rpx 40rpx;
		
		.back-home-btn {
			background: white;
			border-radius: 50rpx;
			height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
			
			.iconfont {
				font-size: 32rpx;
				color: #FF69B4;
				margin-right: 20rpx;
			}
			
			text {
				font-size: 28rpx;
				color: #333;
			}
		}
	}
</style>