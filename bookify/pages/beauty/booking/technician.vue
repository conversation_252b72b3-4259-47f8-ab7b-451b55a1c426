<template>
  <view class="technician-selection">
    <!-- 统一导航栏 -->
    <beauty-navbar 
      title="选择技师" 
      :show-back="true"
    ></beauty-navbar>
    
    <!-- 服务信息 -->
    <view class="service-info" v-if="serviceInfo.name" style="margin-top: 88px;">
      <view class="service-basic">
        <text class="service-name">{{ serviceInfo.name }}</text>
        <text class="service-price">¥{{ serviceInfo.price }}</text>
      </view>
      <view class="service-duration">
        <text class="duration-text">服务时长：{{ serviceInfo.duration }}分钟</text>
      </view>
    </view>
    
    <!-- 筛选条件 -->
    <view class="filter-section">
      <view class="filter-item">
        <text class="filter-label">排序方式：</text>
        <picker mode="selector" :value="sortIndex" :range="sortOptions" @change="onSortChange">
          <view class="picker-value">
            {{ sortOptions[sortIndex] }}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
      
      <view class="filter-item">
        <text class="filter-label">可预约时间：</text>
        <picker mode="date" :value="selectedDate" @change="onDateChange">
          <view class="picker-value">
            {{ selectedDate }}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
    </view>
    
    <!-- 技师列表 -->
    <view class="technician-list">
      <view class="list-header">
        <text class="header-title">选择技师</text>
        <text class="header-desc">您也可以选择"随机分配"让系统为您推荐</text>
      </view>
      
      <!-- 随机分配选项 -->
      <view class="random-option" @click="selectRandomTechnician">
        <view class="option-icon">
          <text class="shuffle-icon">🔀</text>
        </view>
        <view class="option-info">
          <text class="option-title">随机分配</text>
          <text class="option-desc">系统自动为您分配合适的技师</text>
        </view>
        <view class="option-price">
          <text>无需额外费用</text>
        </view>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-container" v-if="loading">
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 技师选项 -->
      <view class="technician-options" v-else>
        <!-- 空状态 -->
        <view class="empty-state" v-if="sortedTechnicians.length === 0">
          <view class="empty-content">
            <text class="empty-icon">👩‍⚕️</text>
            <text class="empty-text">暂无可用技师</text>
            <text class="empty-desc">该服务暂时没有可预约的技师</text>
          </view>
        </view>
        
        <!-- 技师列表 -->
        <view class="technician-wrapper" v-for="technician in sortedTechnicians" :key="technician.user_id || technician.id">
          <technician-card 
            :technician="technician"
            @click="selectTechnician"
            @view-detail="viewTechnicianDetail"
          />
          
          <!-- 今日可预约时间 -->
          <view class="available-times" v-if="technicianTimeSlots[technician.user_id || technician.id]">
            <text class="times-label">今日可预约时间：</text>
            <view class="time-slots">
              <text 
                class="time-slot" 
                v-for="slot in technicianTimeSlots[technician.user_id || technician.id].slice(0, 4)" 
                :key="slot.time"
                :class="{ 'unavailable': !slot.available }"
              >
                {{ slot.time }}
              </text>
              <text class="more-times" v-if="technicianTimeSlots[technician.user_id || technician.id].length > 4">
                等{{ technicianTimeSlots[technician.user_id || technician.id].length }}个时段
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 技师详情弹窗 -->
    <view class="technician-detail-modal" v-if="showDetailModal" @click="closeDetailModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">技师详情</text>
          <text class="modal-close" @click="closeDetailModal">×</text>
        </view>
        
        <view class="modal-body" v-if="selectedTechnicianDetail">
          <view class="technician-profile">
            <image :src="selectedTechnicianDetail.avatar" class="profile-avatar" mode="aspectFill"></image>
            <view class="profile-info">
              <text class="profile-name">{{ selectedTechnicianDetail.name }}</text>
              <text class="profile-level">{{ selectedTechnicianDetail.level }}</text>
              <view class="profile-rating">
                <text class="rating-score">{{ selectedTechnicianDetail.rating }}</text>
                <text class="rating-count">({{ selectedTechnicianDetail.reviewCount }}条评价)</text>
              </view>
            </view>
          </view>
          
          <view class="detail-section">
            <text class="section-title">个人简介</text>
            <text class="section-content">{{ selectedTechnicianDetail.introduction }}</text>
          </view>
          
          <view class="detail-section">
            <text class="section-title">专业技能</text>
            <view class="skill-tags">
              <text class="skill-tag" v-for="skill in selectedTechnicianDetail.specialty" :key="skill">
                {{ skill }}
              </text>
            </view>
          </view>
          
          <view class="detail-section">
            <text class="section-title">持有证书</text>
            <view class="certificate-list">
              <text class="certificate-item" v-for="cert in selectedTechnicianDetail.certificate" :key="cert">
                • {{ cert }}
              </text>
            </view>
          </view>
          
          <view class="detail-section">
            <text class="section-title">作品展示</text>
            <view class="gallery-grid">
              <image 
                v-for="(img, index) in selectedTechnicianDetail.gallery" 
                :key="index"
                :src="img" 
                class="gallery-item"
                mode="aspectFill"
                @click="previewImage(selectedTechnicianDetail.gallery, index)"
              ></image>
            </view>
          </view>
        </view>
        
        <view class="modal-footer">
          <button class="select-btn" @click="selectTechnicianFromModal">选择该技师</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getTechnicianList, getServiceDetail, getTechnicianAvailableTime, getTechnicianDetail } from '@/api/beauty/index.js'
import TechnicianCard from '@/components/beauty/technician-card.vue'

export default {
  name: 'TechnicianSelection',
  components: {
    TechnicianCard
  },
  
  data() {
    return {
      serviceId: null,
      serviceInfo: {},
      availableTechnicians: [],
      loading: true,
      sortIndex: 0,
      sortOptions: ['综合排序', '评分最高', '价格最低', '经验最多'],
      selectedDate: '',
      technicianTimeSlots: {},
      showDetailModal: false,
      selectedTechnicianDetail: null
    }
  },
  
  computed: {
    sortedTechnicians() {
      const technicians = [...this.availableTechnicians]
      
      switch (this.sortIndex) {
        case 1: // 评分最高
          return technicians.sort((a, b) => b.rating - a.rating)
        case 2: // 价格最低
          return technicians.sort((a, b) => (a.price || 0) - (b.price || 0))
        case 3: // 经验最多
          return technicians.sort((a, b) => b.experience - a.experience)
        default: // 综合排序
          return technicians.sort((a, b) => {
            if (a.isTop !== b.isTop) return b.isTop - a.isTop
            return b.rating - a.rating
          })
      }
    }
  },
  
  onLoad(options) {
    console.log('技师选择页面参数:', options)
    console.log('所有参数键:', Object.keys(options))
    console.log('TechnicianUserID值:', options.TechnicianUserID)
    console.log('technicianUserId值:', options.technicianUserId)
    
    if (options.serviceId) {
      this.serviceId = parseInt(options.serviceId)
      console.log('解析后的服务ID:', this.serviceId)
      
      // 检查是否有预选技师（支持多种参数名）
      const technicianUserId = options.TechnicianUserID || options.technicianUserId || options.technician_user_id
      if (technicianUserId) {
        const techId = parseInt(technicianUserId)
        console.log('检测到预选技师ID:', techId)
        
        // 如果有预选技师，直接跳转到时间选择页面
        const targetUrl = `/pages/beauty/booking/time?serviceId=${this.serviceId}&TechnicianUserID=${techId}&date=${this.getDefaultDate()}`
        console.log('跳转到时间选择页面:', targetUrl)
        
        uni.navigateTo({
          url: targetUrl
        })
        return
      }
      
      console.log('没有预选技师，加载技师列表')
      // 清理可能存在的预选技师缓存
      uni.removeStorageSync('preSelectedTechnicianId')
      console.log('清理预选技师缓存')
      this.initData()
    } else {
      console.log('没有服务ID参数，显示提示')
      this.loading = false
      uni.showToast({
        title: '请先选择服务',
        icon: 'none'
      })
      // 延迟跳转到服务列表
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/beauty/service/list'
        })
      }, 1500)
    }
    
    // 设置默认日期为今天
    const today = new Date()
    this.selectedDate = today.getFullYear() + '-' + 
                       String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                       String(today.getDate()).padStart(2, '0')
  },
  
  methods: {
    getDefaultDate() {
      const today = new Date()
      return today.getFullYear() + '-' + 
             String(today.getMonth() + 1).padStart(2, '0') + '-' + 
             String(today.getDate()).padStart(2, '0')
    },
    
    async initData() {
      this.loading = true
      
      console.log('开始加载技师数据，服务ID:', this.serviceId)
      
      try {
        const [serviceRes, techniciansRes] = await Promise.all([
          getServiceDetail(this.serviceId),
          getTechnicianList({ 
            service_id: this.serviceId,
            page: 1,
            page_size: 50
          })
        ])
        
        console.log('服务详情响应:', serviceRes)
        console.log('技师列表响应:', techniciansRes)
        
        this.serviceInfo = serviceRes.data
        this.availableTechnicians = techniciansRes.data.list || []
        
        console.log('设置技师列表:', this.availableTechnicians)
        console.log('技师列表长度:', this.availableTechnicians.length)
        
        // 加载技师的可预约时间
        await this.loadTechnicianTimeSlots()
        
      } catch (error) {
        console.error('加载数据失败:', error)
        console.error('错误详情:', {
          message: error.message,
          response: error.response,
          status: error.response?.status,
          data: error.response?.data
        })
        uni.showToast({
          title: '加载数据失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    async loadTechnicianTimeSlots() {
      const timeSlotPromises = this.availableTechnicians.map(async (technician) => {
        try {
          const response = await getTechnicianAvailableTime({
            technician_id: technician.user_id,
            date: this.selectedDate,
            service_id: this.serviceId
          })
          return {
            TechnicianUserID: technician.user_id,
            timeSlots: response.data.time_slots ? response.data.time_slots.filter(slot => slot.available) : []
          }
        } catch (error) {
          console.error(`加载技师${technician.name}的时间段失败:`, error)
          return {
            TechnicianUserID: technician.user_id,
            timeSlots: []
          }
        }
      })
      
      const results = await Promise.all(timeSlotPromises)
      
      this.technicianTimeSlots = {}
      results.forEach(result => {
        this.technicianTimeSlots[result.TechnicianUserID] = result.timeSlots.map(slot => ({
          time: `${slot.start_time}-${slot.end_time}`,
          available: slot.available
        }))
      })
    },
    
    onSortChange(e) {
      this.sortIndex = e.detail.value
    },
    
    async onDateChange(e) {
      this.selectedDate = e.detail.value
      await this.loadTechnicianTimeSlots()
    },
    
    selectRandomTechnician() {
      // 随机分配，不指定技师
      uni.navigateTo({
        url: `/pages/beauty/booking/time?serviceId=${this.serviceId}&date=${this.selectedDate}`
      })
    },
    
    selectTechnician(technician) {
      // 指定技师
      uni.navigateTo({
        url: `/pages/beauty/booking/time?serviceId=${this.serviceId}&TechnicianUserID=${technician.user_id}&date=${this.selectedDate}`
      })
    },
    
    async viewTechnicianDetail(technician) {
      try {
        const response = await getTechnicianDetail(technician.user_id)
        this.selectedTechnicianDetail = response.data
        this.showDetailModal = true
      } catch (error) {
        console.error('加载技师详情失败:', error)
        uni.showToast({
          title: '加载技师详情失败',
          icon: 'none'
        })
      }
    },
    
    closeDetailModal() {
      this.showDetailModal = false
      this.selectedTechnicianDetail = null
    },
    
    selectTechnicianFromModal() {
      if (this.selectedTechnicianDetail) {
        this.selectTechnician(this.selectedTechnicianDetail)
      }
      this.closeDetailModal()
    },
    
    previewImage(images, current) {
      uni.previewImage({
        urls: images,
        current: images[current]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.technician-selection {
  background: #f8f9fa;
  min-height: 100vh;
  
  .service-info {
    background: #fff;
    padding: 30rpx;
    margin-bottom: 20rpx;
    
    .service-basic {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10rpx;
      
      .service-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }
      
      .service-price {
        font-size: 36rpx;
        font-weight: 600;
        color: #FFB6C1;
      }
    }
    
    .service-duration {
      .duration-text {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
  
  .filter-section {
    background: #fff;
    padding: 30rpx;
    margin-bottom: 20rpx;
    
    .filter-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .filter-label {
        font-size: 28rpx;
        color: #333;
        font-weight: 600;
      }
      
      .picker-value {
        font-size: 28rpx;
        color: #666;
        display: flex;
        align-items: center;
        
        .picker-arrow {
          margin-left: 10rpx;
          font-size: 24rpx;
        }
      }
    }
  }
  
  .technician-list {
    .list-header {
      background: #fff;
      padding: 30rpx;
      margin-bottom: 20rpx;
      
      .header-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        display: block;
        margin-bottom: 8rpx;
      }
      
      .header-desc {
        font-size: 26rpx;
        color: #666;
      }
    }
    
    .random-option {
      background: #fff;
      padding: 30rpx;
      margin-bottom: 20rpx;
      display: flex;
      align-items: center;
      
      .option-icon {
        width: 80rpx;
        height: 80rpx;
        background: #FFE5E5;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;
        
        .shuffle-icon {
          font-size: 40rpx;
          color: #FFB6C1;
        }
      }
      
      .option-info {
        flex: 1;
        
        .option-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          display: block;
          margin-bottom: 8rpx;
        }
        
        .option-desc {
          font-size: 26rpx;
          color: #666;
        }
      }
      
      .option-price {
        text {
          font-size: 24rpx;
          color: #52C41A;
        }
      }
    }
    
    .loading-container {
      padding: 30rpx;
      text-align: center;
      .loading-text {
        font-size: 28rpx;
        color: #666;
      }
    }
    
    .empty-state {
      padding: 60rpx 30rpx;
      text-align: center;
      
      .empty-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .empty-icon {
          font-size: 80rpx;
          margin-bottom: 20rpx;
        }
        
        .empty-text {
          font-size: 32rpx;
          color: #333;
          font-weight: 600;
          margin-bottom: 10rpx;
        }
        
        .empty-desc {
          font-size: 26rpx;
          color: #666;
        }
      }
    }
    
    .technician-options {
      padding: 0 30rpx;
      
      .technician-wrapper {
        background: #fff;
        border-radius: 10rpx;
        padding: 30rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        
        .available-times {
          margin-top: 20rpx;
          padding-top: 20rpx;
          border-top: 1rpx solid #eee;
          
          .times-label {
            font-size: 28rpx;
            color: #333;
            font-weight: 600;
            margin-bottom: 10rpx;
          }
          
          .time-slots {
            display: flex;
            flex-wrap: wrap;
            gap: 10rpx;
            
            .time-slot {
              font-size: 26rpx;
              color: #333;
              background: #FFE5E5;
              border-radius: 8rpx;
              padding: 8rpx 15rpx;
              border: 1rpx solid #FFB6C1;
              
              &.unavailable {
                background: #F5F5F5;
                color: #999;
                border-color: #eee;
              }
            }
            
            .more-times {
              font-size: 26rpx;
              color: #666;
              margin-top: 10rpx;
            }
          }
        }
      }
    }
  }
  
  .technician-detail-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    
    .modal-content {
      background: #fff;
      border-radius: 15rpx;
      width: 90%;
      max-height: 80%;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      
      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx;
        border-bottom: 1rpx solid #eee;
        
        .modal-title {
          font-size: 36rpx;
          font-weight: 600;
          color: #333;
        }
        
        .modal-close {
          font-size: 40rpx;
          color: #999;
          font-weight: bold;
        }
      }
      
      .modal-body {
        flex: 1;
        padding: 30rpx;
        overflow-y: auto;
        
        .technician-profile {
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;
          
          .profile-avatar {
            width: 100rpx;
            height: 100rpx;
            border-radius: 50%;
            margin-right: 20rpx;
            border: 2rpx solid #FFB6C1;
          }
          
          .profile-info {
            flex: 1;
            
            .profile-name {
              font-size: 36rpx;
              font-weight: 600;
              color: #333;
              display: block;
              margin-bottom: 8rpx;
            }
            
            .profile-level {
              font-size: 28rpx;
              color: #666;
              display: block;
              margin-bottom: 8rpx;
            }
            
            .profile-rating {
              display: flex;
              align-items: center;
              
              .rating-score {
                font-size: 32rpx;
                font-weight: 600;
                color: #FFB6C1;
              }
              
              .rating-count {
                font-size: 24rpx;
                color: #666;
                margin-left: 10rpx;
              }
            }
          }
        }
        
        .detail-section {
          margin-bottom: 20rpx;
          
          .section-title {
            font-size: 32rpx;
            font-weight: 600;
            color: #333;
            display: block;
            margin-bottom: 10rpx;
          }
          
          .section-content {
            font-size: 28rpx;
            color: #666;
            line-height: 1.6;
          }
        }
        
        .skill-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 10rpx;
          
          .skill-tag {
            font-size: 26rpx;
            color: #FFB6C1;
            background: #FFF0F0;
            border: 1rpx solid #FFB6C1;
            border-radius: 8rpx;
            padding: 8rpx 15rpx;
          }
        }
        
        .certificate-list {
          margin-top: 10rpx;
          
          .certificate-item {
            font-size: 26rpx;
            color: #666;
            margin-bottom: 8rpx;
            
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
        
        .gallery-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 10rpx;
          
          .gallery-item {
            width: 100%;
            height: 100rpx;
            border-radius: 8rpx;
            overflow: hidden;
          }
        }
      }
      
      .modal-footer {
        padding: 30rpx;
        border-top: 1rpx solid #eee;
        
        .select-btn {
          background: #FFB6C1;
          color: #fff;
          font-size: 32rpx;
          font-weight: 600;
          padding: 20rpx 40rpx;
          border-radius: 10rpx;
          text-align: center;
          width: 100%;
        }
      }
    }
  }
}
</style>
