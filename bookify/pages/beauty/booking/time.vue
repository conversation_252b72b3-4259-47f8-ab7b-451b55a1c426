<template>
  <view class="booking-time">
    <!-- 统一导航栏 -->
    <beauty-navbar 
      title="选择时间" 
      :show-back="true"
    ></beauty-navbar>
    
    <!-- 预约信息概览 -->
    <view class="booking-summary" style="margin-top: 88px;">
      <view class="service-info">
        <text class="service-name">{{ serviceInfo.name }}</text>
        <text class="service-price">¥{{ totalPrice }}</text>
      </view>
      <view class="technician-info" v-if="technicianInfo.name">
        <text class="technician-name">技师：{{ technicianInfo.name }}</text>
        <text class="technician-level">{{ technicianInfo.level }}</text>
      </view>
    </view>
    
    <!-- 日期选择 -->
    <view class="date-section">
      <view class="section-title">
        <text>选择日期</text>
      </view>
      <view class="date-picker">
        <scroll-view scroll-x class="date-scroll">
          <view class="date-list">
            <view 
              class="date-item" 
              :class="{ active: selectedDate === date.date, disabled: date.disabled, busy: date.busy }"
              v-for="date in availableDates" 
              :key="date.date"
              @click="selectDate(date)"
            >
              <text class="date-text">{{ date.dateText }}</text>
              <text class="day-text">{{ date.dayText }}</text>
              <text class="status-text" v-if="date.busy">繁忙</text>
              <text class="status-text" v-else-if="date.disabled">休息</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
    
    <!-- 时间选择 -->
    <view class="time-section">
      <view class="section-title">
        <text>选择时间</text>
        <text class="time-tip">预计服务时长{{ serviceInfo.duration }}分钟</text>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-state" v-if="loadingTimeSlots">
        <text class="loading-text">加载时间段中...</text>
      </view>
      
      <!-- 时间网格 -->
      <view class="time-grid" v-else>
        <view 
          class="time-item" 
          :class="{ 
            active: selectedTime === time.startTime, 
            disabled: !time.available,
            selected: time.selected
          }"
          v-for="time in timeSlots" 
          :key="time.startTime"
          @click="selectTimeSlot(time)"
        >
          <text class="time-text">{{ time.startTime }}</text>
          <text class="reason-text" v-if="!time.available && time.reason">{{ time.reason }}</text>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="!loadingTimeSlots && timeSlots.length === 0">
        <text class="empty-text">该日期暂无可预约时间</text>
      </view>
    </view>
    
    <!-- 预约须知 -->
    <view class="notice-section">
      <view class="section-title">
        <text>预约须知</text>
      </view>
      <view class="notice-content">
        <text class="notice-item">• 请提前15分钟到店，避免影响服务时间</text>
        <text class="notice-item">• 如需取消或改期，请提前2小时联系客服</text>
        <text class="notice-item">• 迟到超过15分钟将视为自动取消预约</text>
        <text class="notice-item">• 特殊情况请清楚告知客服门店：400-123-4567</text>
      </view>
    </view>
    
    <!-- 底部确认按钮 -->
    <view class="confirm-footer">
      <view class="selected-info" v-if="selectedDate && selectedTime">
        <text class="selected-text">{{ formatSelectedDateTime }}</text>
        <text class="price-text">¥{{ totalPrice }}</text>
      </view>
      <button 
        class="confirm-btn"
        :class="{ disabled: !selectedDate || !selectedTime, loading: loading }"
        @click="confirmTime"
        :disabled="!selectedDate || !selectedTime"
      >
        {{ loading ? '确认中...' : '确认时间' }}
      </button>
    </view>
  </view>
</template>

<script>
import { getAvailableTime, getServiceDetail, getTechnicianDetail } from '@/api/beauty/index.js'

export default {
  name: 'BookingTime',
  
  data() {
    return {
      serviceId: null,
      TechnicianUserID: null,
      serviceInfo: {},
      technicianInfo: {},
      selectedDate: '',
      selectedTime: '',
      selectedSlot: null,
      endTime: '',
      timeSlots: [],
      loading: false,
      loadingTimeSlots: false
    }
  },

  computed: {
    totalPrice() {
      const servicePrice = this.serviceInfo.price || 0
      const technicianPrice = this.technicianInfo.extraFee || 0
      return servicePrice + technicianPrice
    },

    // 格式化选中的日期时间
    formatSelectedDateTime() {
      if (!this.selectedDate || !this.selectedTime) return ''
      
      const date = new Date(this.selectedDate)
      const today = new Date()
      const tomorrow = new Date(today)
      tomorrow.setDate(today.getDate() + 1)
      
      let dateText = ''
      if (date.toDateString() === today.toDateString()) {
        dateText = '今天'
      } else if (date.toDateString() === tomorrow.toDateString()) {
        dateText = '明天'
      } else {
        dateText = `${date.getMonth() + 1}月${date.getDate()}日`
      }
      
      return `${dateText} ${this.selectedTime}`
    },

    // 生成可选日期列表
    availableDates() {
      const dates = []
      const today = new Date()
      
      // 生成未来7天的日期
      for (let i = 0; i < 7; i++) {
        const date = new Date(today)
        date.setDate(today.getDate() + i)
        
        const dateStr = date.toISOString().split('T')[0]
        const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
        const dayText = i === 0 ? '今天' : (i === 1 ? '明天' : dayNames[date.getDay()])
        const dateText = `${date.getMonth() + 1}/${date.getDate()}`
        
        dates.push({
          date: dateStr,
          dateText: dateText,
          dayText: dayText,
          disabled: false,
          busy: false
        })
      }
      
      return dates
    }
  },

  onLoad(options) {
    // 改进参数解析和验证
    this.parseUrlParams(options)
    this.initData()
  },

  methods: {
    // 解析URL参数的方法
    parseUrlParams(options) {
      // 必需参数验证
      if (!options.serviceId) {
        uni.showToast({
          title: '服务信息缺失',
          icon: 'none'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
        return
      }
      
      this.serviceId = parseInt(options.serviceId)
      this.TechnicianUserID= options.TechnicianUserID? parseInt(options.TechnicianUserID) : null
      this.selectedDate = options.date || this.getDefaultDate()
      
      // 验证日期格式
      if (!this.isValidDate(this.selectedDate)) {
        this.selectedDate = this.getDefaultDate()
      }
    },
    
    // 验证日期格式
    isValidDate(dateString) {
      const date = new Date(dateString)
      return date instanceof Date && !isNaN(date) && dateString.match(/^\d{4}-\d{2}-\d{2}$/)
    },
    
    // 获取默认日期（明天）
    getDefaultDate() {
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      return tomorrow.toISOString().split('T')[0]
    },

    async initData() {
      // 显示加载状态
      uni.showLoading({
        title: '加载中...'
      })
      
      try {
        // 并行加载服务信息和技师信息
        const promises = [
          this.loadServiceInfo(),
          this.loadAvailableSlots()
        ]
        
        if (this.TechnicianUserID) {
          promises.push(this.loadTechnicianInfo())
        }
        
        await Promise.all(promises)
        
      } catch (error) {
        console.error('初始化数据失败:', error)
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        })
        
        // 3秒后返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 3000)
      } finally {
        uni.hideLoading()
      }
    },

    // 加载服务信息
    async loadServiceInfo() {
      try {
        const response = await getServiceDetail(this.serviceId)
        if (response.code === 200) {
          this.serviceInfo = response.data
          
          // 设置页面标题
          uni.setNavigationBarTitle({
            title: `预约 - ${this.serviceInfo.name}`
          })
        } else {
          throw new Error(response.message || '加载服务信息失败')
        }
      } catch (error) {
        console.error('加载服务信息失败:', error)
        throw error
      }
    },

    // 加载技师信息
    async loadTechnicianInfo() {
      if (!this.TechnicianUserID) return
      
      try {
        const response = await getTechnicianDetail(this.TechnicianUserID)
        if (response.code === 200) {
          this.technicianInfo = response.data
        } else {
          console.warn('加载技师信息失败:', response.message)
        }
      } catch (error) {
        console.error('加载技师信息失败:', error)
        // 技师信息加载失败不阻塞整个流程
      }
    },

    // 加载可预约时间
    async loadAvailableSlots() {
      this.loadingTimeSlots = true
      
      try {
        const params = {
          service_id: this.serviceId,
          date: this.selectedDate
        }
        
        // 只有当有技师ID时才添加
        if (this.TechnicianUserID) {
          params.technician_id = this.TechnicianUserID
        }
        
        const response = await getAvailableTime(params)
        
        if (response.code === 200) {
          // 转换后端数据结构为前端期望的格式
          const slots = response.data.available_slots || []
          this.timeSlots = slots.map(slot => ({
            startTime: slot.start_time,
            endTime: slot.end_time,
            available: slot.available,
            reason: slot.reason,
            selected: false
          }))
          this.updateSlotAvailability()
        } else {
          throw new Error(response.message || '加载时间段失败')
        }
      } catch (error) {
        console.error('加载可预约时间失败:', error)
        this.timeSlots = []
        uni.showToast({
          title: '加载时间段失败',
          icon: 'none'
        })
      } finally {
        this.loadingTimeSlots = false
      }
    },

    // 更新时间段可用性
    updateSlotAvailability() {
      const now = new Date()
      const selectedDate = new Date(this.selectedDate)
      const isToday = selectedDate.toDateString() === now.toDateString()
      
      this.timeSlots.forEach(slot => {
        if (isToday) {
          const slotTime = new Date(`${this.selectedDate} ${slot.startTime}`)
          // 如果是今天，过去的时间段不可选
          if (slotTime <= now) {
            slot.available = false
            slot.reason = '时间已过'
          }
        }
      })
    },

    // 选择日期
    async selectDate(dateItem) {
      if (dateItem.disabled) return
      
      this.selectedDate = dateItem.date
      this.selectedTime = ''
      this.selectedSlot = null
      this.endTime = ''
      
      // 重新加载该日期的可预约时间
      await this.loadAvailableSlots()
    },

    // 选择时间段
    selectTimeSlot(slot) {
      if (!slot.available) {
        uni.showToast({
          title: slot.reason || '该时间段不可预约',
          icon: 'none'
        })
        return
      }
      
      // 清除之前的选择
      this.timeSlots.forEach(s => s.selected = false)
      
      // 选择当前时间段
      slot.selected = true
      this.selectedSlot = slot
      this.selectedTime = slot.startTime
      
      // 计算预约结束时间
      this.calculateEndTime()
    },

    // 计算预约结束时间
    calculateEndTime() {
      if (!this.selectedTime || !this.serviceInfo.duration) return
      
      const startTime = new Date(`${this.selectedDate} ${this.selectedTime}`)
      const endTime = new Date(startTime.getTime() + this.serviceInfo.duration * 60000)
      
      this.endTime = endTime.toTimeString().slice(0, 5)
    },

    // 确认时间选择
    async confirmTime() {
      // 验证必需信息
      if (!this.selectedDate || !this.selectedTime) {
        uni.showToast({
          title: '请选择预约时间',
          icon: 'none'
        })
        return
      }
      if (!this.serviceInfo.id) {
        uni.showToast({
          title: '服务信息缺失',
          icon: 'none'
        })
        return
      }
      // 再次验证时间段可用性（增强体验）
      this.loading = true
      try {
        const params = {
          service_id: this.serviceId,
          date: this.selectedDate
        }
        if (this.TechnicianUserID) {
          params.technician_id = this.TechnicianUserID
        }
        const response = await getAvailableTime(params)
        if (response.code === 200) {
          const slots = response.data.available_slots || []
          const currentSlot = slots.find(slot => slot.start_time === this.selectedTime)
          if (!currentSlot || !currentSlot.available) {
            uni.showToast({
              title: (currentSlot && currentSlot.reason) ? currentSlot.reason : '该时间已被预约，请重新选择',
              icon: 'none'
            })
            // 刷新时间段数据
            this.timeSlots = slots.map(slot => ({
              startTime: slot.start_time,
              endTime: slot.end_time,
              available: slot.available,
              reason: slot.reason,
              selected: false
            }))
            this.selectedTime = ''
            this.selectedSlot = null
            this.endTime = ''
            this.loading = false
            return
          }
        } else {
          throw new Error(response.message || '校验时间段失败')
        }
        // 校验通过，正常跳转
        // 构建跳转参数
        const paramsObj = {
          serviceId: this.serviceId,
          TechnicianUserID: this.TechnicianUserID|| '',
          date: this.selectedDate,
          time: this.selectedTime,
          endTime: this.endTime,
          duration: this.serviceInfo.duration,
          price: this.totalPrice
        }
        const query = Object.keys(paramsObj)
          .filter(key => paramsObj[key] !== '' && paramsObj[key] !== null && paramsObj[key] !== undefined)
          .map(key => `${key}=${encodeURIComponent(paramsObj[key])}`)
          .join('&')
        uni.navigateTo({
          url: `/pages/beauty/booking/form?${query}`
        })
      } catch (error) {
        console.error('校验时间段失败:', error)
        uni.showToast({
          title: '校验时间段失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss">
.booking-time {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

.booking-summary {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  
  .service-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .service-name {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
    
    .service-price {
      font-size: 36rpx;
      font-weight: bold;
      color: #FFB6C1;
    }
  }
  
  .technician-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .technician-name {
      font-size: 28rpx;
      color: #666;
    }
    
    .technician-level {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.date-section, .time-section, .notice-section {
  background: #fff;
  margin-bottom: 20rpx;
  
  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    text {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
    
    .time-tip {
      font-size: 24rpx;
      color: #999;
      font-weight: normal;
    }
  }
}

.date-picker {
  .date-scroll {
    white-space: nowrap;
    
    .date-list {
      display: flex;
      padding: 30rpx;
      
      .date-item {
        flex-shrink: 0;
        width: 120rpx;
        height: 120rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        border-radius: 12rpx;
        margin-right: 20rpx;
        position: relative;
        
        &.active {
          background: #FFB6C1;
          
          .date-text, .day-text {
            color: #fff;
          }
        }
        
        &.disabled {
          opacity: 0.5;
          
          .status-text {
            color: #999;
          }
        }
        
        &.busy {
          .status-text {
            color: #ff9500;
          }
        }
        
        .date-text {
          font-size: 28rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 8rpx;
        }
        
        .day-text {
          font-size: 24rpx;
          color: #666;
        }
        
        .status-text {
          position: absolute;
          bottom: 8rpx;
          font-size: 20rpx;
          color: #ff9500;
        }
      }
    }
  }
}

.loading-state {
  padding: 60rpx;
  text-align: center;
  
  .loading-text {
    font-size: 28rpx;
    color: #999;
  }
}

.time-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  padding: 30rpx;
  
  .time-item {
    height: 100rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 12rpx;
    position: relative;
    
    &.active, &.selected {
      background: #FFB6C1;
      
      .time-text {
        color: #fff;
      }
    }
    
    &.disabled {
      opacity: 0.5;
      
      .time-text {
        color: #999;
      }
    }
    
    .time-text {
      font-size: 28rpx;
      font-weight: 600;
      color: #333;
    }
    
    .reason-text {
      font-size: 20rpx;
      color: #999;
      margin-top: 4rpx;
    }
  }
}

.empty-state {
  padding: 60rpx;
  text-align: center;
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

.notice-section {
  .notice-content {
    padding: 30rpx;
    
    .notice-item {
      display: block;
      font-size: 26rpx;
      color: #666;
      line-height: 1.6;
      margin-bottom: 16rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.confirm-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  
  .selected-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .selected-text {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }
    
    .price-text {
      font-size: 32rpx;
      font-weight: bold;
      color: #FF6B9D;
    }
  }
  
  .confirm-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #FF6B9D, #FF8E9B);
    color: #fff;
    border: none;
    border-radius: 12rpx;
    font-size: 32rpx;
    font-weight: 600;
    box-shadow: 0 4rpx 12rpx rgba(255, 107, 157, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    
    &.disabled {
      background: #e0e0e0;
      color: #999;
      box-shadow: none;
    }
    
    &.loading {
      opacity: 0.8;
    }
  }
}
</style>
