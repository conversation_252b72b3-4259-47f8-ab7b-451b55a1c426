<template>
  <view class="booking-confirm">
    <!-- 统一导航栏 -->
    <beauty-navbar 
      title="确认预约" 
      :show-back="true"
    ></beauty-navbar>
    
    <!-- 成功状态 -->
    <view class="success-status" style="margin-top: 88px;">
      <text class="success-icon">✓</text>
      <text class="success-title">预约成功</text>
      <text class="success-desc">您的预约已提交，请按时到店享受服务</text>
    </view>
    
    <!-- 预约详情 -->
    <view class="booking-detail">
      <view class="detail-header">
        <text class="header-title">预约详情</text>
        <view class="booking-status">
          <text class="status-text">{{ getStatusText() }}</text>
        </view>
      </view>
      
      <view class="detail-content">
        <view class="detail-row">
          <text class="detail-label">预约编号</text>
          <text class="detail-value">{{ booking.id }}</text>
        </view>
        
        <view class="detail-row">
          <text class="detail-label">服务项目</text>
          <text class="detail-value">{{ booking.serviceName }}</text>
        </view>
        
        <view class="detail-row" v-if="booking.technicianName">
          <text class="detail-label">指定技师</text>
          <text class="detail-value">{{ booking.technicianName }}</text>
        </view>
        
        <view class="detail-row">
          <text class="detail-label">预约时间</text>
          <text class="detail-value">{{ booking.bookingDate }} {{ booking.bookingTime }}</text>
        </view>
        
        <view class="detail-row">
          <text class="detail-label">服务时长</text>
          <text class="detail-value">{{ booking.duration }}分钟</text>
        </view>
        
        <view class="detail-row">
          <text class="detail-label">联系方式</text>
          <text class="detail-value">{{ booking.customerPhone }}</text>
        </view>
        
        <view class="detail-row">
          <text class="detail-label">支付金额</text>
          <text class="detail-value price">¥{{ booking.totalPrice }}</text>
        </view>
        
        <view class="detail-row" v-if="booking.customerNote">
          <text class="detail-label">备注信息</text>
          <text class="detail-value">{{ booking.customerNote }}</text>
        </view>
      </view>
    </view>
    
    <!-- 温馨提示 -->
    <view class="tips-section">
      <view class="tips-header">
        <text class="tips-icon">ℹ️</text>
        <text class="tips-title">温馨提示</text>
      </view>
      <view class="tips-content">
        <text class="tips-item">• 请提前15分钟到店，避免影响服务时间</text>
        <text class="tips-item">• 如需取消或改期，请提前2小时联系客服</text>
        <text class="tips-item">• 迟到超过15分钟将视为自动取消预约</text>
        <text class="tips-item">• 服务完成后请为技师评分，帮助我们提升服务质量</text>
      </view>
    </view>
    
    <!-- 联系方式 -->
    <view class="contact-section">
      <view class="contact-header">
        <text class="header-title">联系我们</text>
      </view>
      <view class="contact-content">
        <view class="contact-item" @click="callPhone">
          <text class="contact-icon">📞</text>
          <view class="contact-info">
            <text class="contact-title">客服电话</text>
            <text class="contact-value">************</text>
          </view>
        </view>
        
        <view class="contact-item" @click="openMap">
          <text class="contact-icon">📍</text>
          <view class="contact-info">
            <text class="contact-title">门店地址</text>
            <text class="contact-value">北京市朝阳区xxx路xxx号</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button 
        class="btn btn-default" 
        @click="goToBookingList"
      >
        查看我的预约
      </button>
      
      <button 
        class="btn btn-primary" 
        @click="goToHome"
      >
        返回首页
      </button>
    </view>
  </view>
</template>

<script>
import { getBookingDetail } from '@/api/beauty/index.js'

export default {
  name: 'BookingConfirm',
  
  data() {
    return {
      bookingId: null,
      booking: {}
    }
  },
  
  onLoad(options) {
    if (options.id) {
      this.bookingId = parseInt(options.id)
      this.loadBookingDetail()
    }
  },
  
  methods: {
    async loadBookingDetail() {
      try {
        const result = await getBookingDetail(this.bookingId)
        const data = result.data || {}
        // 字段映射，保证页面展示字段完整
        this.booking = {
          id: data.id,
          serviceName: data.service_name,
          technicianName: data.technician_name,
          bookingDate: data.booking_date,
          bookingTime: data.start_time,
          duration: data.duration,
          customerPhone: data.contact_phone,
          totalPrice: data.final_price,
          customerNote: data.special_requests,
          status: data.booking_status,
          // 可根据需要补充其他字段
        }
      } catch (error) {
        console.error('加载预约详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },
    
    getStatusText() {
      const statusMap = {
        'pending': '待确认',
        'confirmed': '已确认',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[this.booking.status] || '未知状态'
    },
    
    callPhone() {
      uni.makePhoneCall({
        phoneNumber: '************'
      })
    },
    
    openMap() {
      uni.openLocation({
        latitude: 39.9042,
        longitude: 116.4074,
        name: '美丽时光美容院',
        address: '北京市朝阳区xxx路xxx号'
      })
    },
    
          goToBookingList() {
        uni.switchTab({
          url: '/pages/beauty/user/booking-history'
        })
      },
    
    goToHome() {
      uni.switchTab({
        url: '/pages/beauty/index/index'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.booking-confirm {
  background-color: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

.success-status {
  background-color: #ffffff;
  text-align: center;
  padding: 60rpx 30rpx;
  margin-bottom: 20rpx;
  
  .success-icon {
    font-size: 120rpx;
    color: #52c41a;
    margin-bottom: 30rpx;
    display: block;
  }
  
  .success-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 20rpx;
    display: block;
  }
  
  .success-desc {
    font-size: 28rpx;
    color: #666666;
    display: block;
  }
}

.booking-detail {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  
  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .header-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
    }
    
    .booking-status {
      .status-text {
        font-size: 24rpx;
        color: #52c41a;
        background-color: #f6ffed;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
      }
    }
  }
  
  .detail-content {
    padding: 30rpx;
    
    .detail-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .detail-label {
        font-size: 28rpx;
        color: #666666;
      }
      
      .detail-value {
        font-size: 28rpx;
        color: #333333;
        text-align: right;
        flex: 1;
        margin-left: 20rpx;
        
        &.price {
          color: #FF6B81;
          font-weight: bold;
        }
      }
    }
  }
}

.tips-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  
  .tips-header {
    display: flex;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .tips-icon {
      font-size: 32rpx;
      margin-right: 20rpx;
    }
    
    .tips-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
    }
  }
  
  .tips-content {
    padding: 30rpx;
    
    .tips-item {
      font-size: 26rpx;
      color: #666666;
      line-height: 1.6;
      margin-bottom: 15rpx;
      display: block;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.contact-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  
  .contact-header {
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .header-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
    }
  }
  
  .contact-content {
    padding: 30rpx;
    
    .contact-item {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .contact-icon {
        font-size: 40rpx;
        margin-right: 20rpx;
      }
      
      .contact-info {
        flex: 1;
        
        .contact-title {
          font-size: 28rpx;
          color: #333333;
          margin-bottom: 10rpx;
          display: block;
        }
        
        .contact-value {
          font-size: 26rpx;
          color: #666666;
          display: block;
        }
      }
    }
  }
}

.action-buttons {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  padding: 30rpx;
  display: flex;
  gap: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    font-size: 28rpx;
    font-weight: bold;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    
    &.btn-default {
      background-color: #ffffff;
      color: #333333;
      border: 2rpx solid #e0e0e0;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    }
    
    &.btn-primary {
      background: linear-gradient(135deg, #FF6B81 0%, #FF8E9E 100%);
      color: #ffffff;
      box-shadow: 0 4rpx 12rpx rgba(255, 107, 129, 0.3);
    }
  }
}
</style>
