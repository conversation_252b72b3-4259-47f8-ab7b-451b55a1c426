<template>
  <view class="test-flow">
    <beauty-navbar title="预约流程测试" :show-back="true"></beauty-navbar>
    
    <view class="content" style="margin-top: 88px;">
      <view class="test-section">
        <view class="section-title">预约流程测试</view>
        <view class="section-desc">测试完整的预约流程功能</view>
        
        <!-- 测试步骤 -->
        <view class="test-steps">
          <view class="step-item" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
            <view class="step-number">1</view>
            <view class="step-content">
              <view class="step-title">选择服务</view>
              <view class="step-desc">选择美容服务项目</view>
            </view>
          </view>
          
          <view class="step-item" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
            <view class="step-number">2</view>
            <view class="step-content">
              <view class="step-title">选择技师</view>
              <view class="step-desc">选择指定技师或随机分配</view>
            </view>
          </view>
          
          <view class="step-item" :class="{ active: currentStep >= 3, completed: currentStep > 3 }">
            <view class="step-number">3</view>
            <view class="step-content">
              <view class="step-title">选择时间</view>
              <view class="step-desc">选择预约日期和时间</view>
            </view>
          </view>
          
          <view class="step-item" :class="{ active: currentStep >= 4, completed: currentStep > 4 }">
            <view class="step-number">4</view>
            <view class="step-content">
              <view class="step-title">填写信息</view>
              <view class="step-desc">填写联系信息和特殊需求</view>
            </view>
          </view>
          
          <view class="step-item" :class="{ active: currentStep >= 5, completed: currentStep > 5 }">
            <view class="step-number">5</view>
            <view class="step-content">
              <view class="step-title">确认预约</view>
              <view class="step-desc">确认预约信息并支付</view>
            </view>
          </view>
        </view>
        
        <!-- 测试按钮 -->
        <view class="test-buttons">
          <button class="test-btn" @click="testFullFlow">测试完整流程</button>
          <button class="test-btn secondary" @click="testServiceDetail">测试服务详情</button>
          <button class="test-btn secondary" @click="testTimeSelection">测试时间选择</button>
          <button class="test-btn secondary" @click="testBookingForm">测试预约表单</button>
          <button class="test-btn secondary" @click="testBookingFormWithMissingParams">测试预约表单（缺少参数）</button>
          <button class="test-btn secondary" @click="testBookingFormWithBadParams">测试预约表单（错误参数）</button>
        </view>
        
        <!-- 测试结果 -->
        <view class="test-results" v-if="testResults.length > 0">
          <view class="results-title">测试结果</view>
          <view class="result-item" v-for="(result, index) in testResults" :key="index">
            <view class="result-status" :class="result.status">
              {{ result.status === 'success' ? '✓' : '✗' }}
            </view>
            <view class="result-content">
              <view class="result-title">{{ result.title }}</view>
              <view class="result-desc">{{ result.message }}</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- API测试 -->
      <view class="api-test-section">
        <view class="section-title">API接口测试</view>
        
        <view class="api-test-group">
          <view class="group-title">预约相关API</view>
          <button class="api-test-btn" @click="testGetAvailableTimeSlots">测试获取可预约时间</button>
          <button class="api-test-btn" @click="testCheckTimeAvailability">测试检查时间可用性</button>
          <button class="api-test-btn" @click="testCreateBooking">测试创建预约</button>
          <button class="api-test-btn" @click="testGetBookingDetail">测试获取预约详情</button>
        </view>
        
        <view class="api-test-group">
          <view class="group-title">其他API</view>
          <button class="api-test-btn" @click="testGetAvailableCoupons">测试获取优惠券</button>
          <button class="api-test-btn" @click="testGetUserNotifications">测试获取通知</button>
          <button class="api-test-btn" @click="testRescheduleBooking">测试改期预约</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { beautyApi } from '@/api/beauty/mock.js'

export default {
  name: 'TestFlow',
  
  data() {
    return {
      currentStep: 1,
      testResults: [],
      isTestingFlow: false
    }
  },
  
  methods: {
    // 测试完整流程
    async testFullFlow() {
      if (this.isTestingFlow) return
      
      this.isTestingFlow = true
      this.testResults = []
      this.currentStep = 1
      
      try {
        // 步骤1：选择服务
        await this.simulateStep(1, '选择服务', async () => {
          const response = await beautyApi.getServiceDetail(1)
          if (response.code !== 200) throw new Error('获取服务详情失败')
          return '服务详情加载成功'
        })
        
        // 步骤2：选择技师
        await this.simulateStep(2, '选择技师', async () => {
          const response = await beautyApi.getTechnicianList({ serviceId: 1 })
          if (response.code !== 200) throw new Error('获取技师列表失败')
          return '技师列表加载成功'
        })
        
        // 步骤3：选择时间
        await this.simulateStep(3, '选择时间', async () => {
          const response = await beautyApi.getAvailableTimeSlots({
            serviceId: 1,
            TechnicianUserID: 1,
            date: '2025-01-20'
          })
          if (response.code !== 200) throw new Error('获取可预约时间失败')
          return '可预约时间加载成功'
        })
        
        // 步骤4：填写信息
        await this.simulateStep(4, '填写信息', async () => {
          const couponsResponse = await beautyApi.getAvailableCoupons({
            serviceId: 1,
            amount: 299
          })
          if (couponsResponse.code !== 200) throw new Error('获取优惠券失败')
          return '预约信息填写完成'
        })
        
        // 步骤5：确认预约
        await this.simulateStep(5, '确认预约', async () => {
          const bookingData = {
            serviceId: 1,
            TechnicianUserID: 1,
            customerName: '测试用户',
            customerPhone: '13800138000',
            bookingDate: '2025-01-20',
            bookingTime: '10:00',
            paymentMethod: 'wechat',
            finalPrice: 299
          }
          
          const response = await beautyApi.createBooking(bookingData)
          if (!response.success) throw new Error('创建预约失败')
          return '预约创建成功'
        })
        
        this.addTestResult('完整流程测试', 'success', '所有步骤都成功完成')
        
      } catch (error) {
        this.addTestResult('完整流程测试', 'error', error.message)
      } finally {
        this.isTestingFlow = false
      }
    },
    
    // 模拟步骤执行
    async simulateStep(stepNumber, stepName, testFunction) {
      this.currentStep = stepNumber
      
      // 模拟加载时间
      await new Promise(resolve => setTimeout(resolve, 800))
      
      try {
        const result = await testFunction()
        this.addTestResult(stepName, 'success', result)
      } catch (error) {
        this.addTestResult(stepName, 'error', error.message)
        throw error
      }
    },
    
    // 添加测试结果
    addTestResult(title, status, message) {
      this.testResults.push({
        title,
        status,
        message,
        timestamp: new Date().toLocaleTimeString()
      })
    },
    
    // 测试服务详情
    testServiceDetail() {
      uni.navigateTo({
        url: '/pages/beauty/service/detail?id=1'
      })
    },
    
    // 测试时间选择
    testTimeSelection() {
      uni.navigateTo({
        url: '/pages/beauty/booking/time?serviceId=1&TechnicianUserID=1&date=2025-01-20'
      })
    },
    
    // 测试预约表单
    testBookingForm() {
      uni.navigateTo({
        url: '/pages/beauty/booking/form?serviceId=1&TechnicianUserID=1&date=2025-01-20&time=10:00&price=299'
      })
    },

    // 新增：测试预约表单（缺少参数）
    testBookingFormWithMissingParams() {
      uni.navigateTo({
        url: '/pages/beauty/booking/form?serviceId=1'
      })
    },

    // 新增：测试预约表单（错误参数）
    testBookingFormWithBadParams() {
      uni.navigateTo({
        url: '/pages/beauty/booking/form?serviceId=abc&date=invalid-date&time=25:99&price=not-a-number'
      })
    },
    
    // API测试方法
    async testGetAvailableTimeSlots() {
      try {
        const response = await beautyApi.getAvailableTimeSlots({
          serviceId: 1,
          TechnicianUserID: 1,
          date: '2025-01-20'
        })
        
        this.addTestResult('获取可预约时间', 'success', `获取到${response.data.slots.length}个时间段`)
      } catch (error) {
        this.addTestResult('获取可预约时间', 'error', error.message)
      }
    },
    
    async testCheckTimeAvailability() {
      try {
        const response = await beautyApi.checkTimeAvailability({
          serviceId: 1,
          TechnicianUserID: 1,
          date: '2025-01-20',
          time: '10:00'
        })
        
        const status = response.data.available ? '可预约' : '不可预约'
        this.addTestResult('检查时间可用性', 'success', `时间段状态：${status}`)
      } catch (error) {
        this.addTestResult('检查时间可用性', 'error', error.message)
      }
    },
    
    async testCreateBooking() {
      try {
        const bookingData = {
          serviceId: 1,
          TechnicianUserID: 1,
          customerName: '测试用户',
          customerPhone: '13800138000',
          bookingDate: '2025-01-20',
          bookingTime: '10:00',
          paymentMethod: 'wechat',
          finalPrice: 299
        }
        
        const response = await beautyApi.createBooking(bookingData)
        
        if (response.success) {
          this.addTestResult('创建预约', 'success', `预约创建成功，ID：${response.data.id}`)
        } else {
          this.addTestResult('创建预约', 'error', response.message)
        }
      } catch (error) {
        this.addTestResult('创建预约', 'error', error.message)
      }
    },
    
    async testGetBookingDetail() {
      try {
        const response = await beautyApi.getBookingDetail(1)
        
        if (response.code === 200) {
          this.addTestResult('获取预约详情', 'success', `预约详情：${response.data.serviceName}`)
        } else {
          this.addTestResult('获取预约详情', 'error', response.message)
        }
      } catch (error) {
        this.addTestResult('获取预约详情', 'error', error.message)
      }
    },
    
    async testGetAvailableCoupons() {
      try {
        const response = await beautyApi.getAvailableCoupons({
          serviceId: 1,
          amount: 299
        })
        
        this.addTestResult('获取优惠券', 'success', `获取到${response.data.length}张可用优惠券`)
      } catch (error) {
        this.addTestResult('获取优惠券', 'error', error.message)
      }
    },
    
    async testGetUserNotifications() {
      try {
        const response = await beautyApi.getUserNotifications()
        
        this.addTestResult('获取通知', 'success', `获取到${response.data.length}条通知`)
      } catch (error) {
        this.addTestResult('获取通知', 'error', error.message)
      }
    },
    
    async testRescheduleBooking() {
      try {
        const response = await beautyApi.rescheduleBooking(1, '2025-01-21', '14:00', '时间调整')
        
        if (response.code === 200) {
          this.addTestResult('改期预约', 'success', '改期成功')
        } else {
          this.addTestResult('改期预约', 'error', response.message)
        }
      } catch (error) {
        this.addTestResult('改期预约', 'error', error.message)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.test-flow {
  min-height: 100vh;
  background: #f5f5f5;
}

.content {
  padding: 20px;
}

.test-section {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.section-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 24px;
}

/* 测试步骤 */
.test-steps {
  margin-bottom: 32px;
}

.step-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  position: relative;
  
  &:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 15px;
    top: 50px;
    width: 2px;
    height: 32px;
    background: #e0e0e0;
  }
  
  &.active {
    .step-number {
      background: #FFB6C1;
      color: #fff;
    }
    
    .step-title {
      color: #FFB6C1;
    }
  }
  
  &.completed {
    .step-number {
      background: #52c41a;
      color: #fff;
    }
    
    &::after {
      background: #52c41a;
    }
  }
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e0e0e0;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  margin-right: 16px;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.step-desc {
  font-size: 14px;
  color: #666;
}

/* 测试按钮 */
.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 24px;
}

.test-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  background: #FFB6C1;
  color: #fff;
  
  &.secondary {
    background: #f0f0f0;
    color: #333;
  }
}

/* 测试结果 */
.test-results {
  border-top: 1px solid #e0e0e0;
  padding-top: 24px;
}

.results-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.result-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.result-status {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  margin-right: 12px;
  flex-shrink: 0;
  
  &.success {
    background: #f6ffed;
    color: #52c41a;
  }
  
  &.error {
    background: #fff2f0;
    color: #ff4d4f;
  }
}

.result-content {
  flex: 1;
}

.result-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.result-desc {
  font-size: 12px;
  color: #666;
}

/* API测试 */
.api-test-section {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.api-test-group {
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.group-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.api-test-btn {
  display: block;
  width: 100%;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 8px;
  font-size: 14px;
  border: 1px solid #e0e0e0;
  background: #fff;
  color: #333;
  text-align: left;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &:active {
    background: #f5f5f5;
  }
}
</style> 