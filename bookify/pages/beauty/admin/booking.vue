<template>
  <view class="admin-booking">
    <!-- 统一导航栏 -->
    <beauty-navbar 
      title="预约管理" 
      :show-back="true"
    ></beauty-navbar>
    
    <view class="admin-header" style="margin-top: 88px;">
      <text class="header-title">预约管理</text>
      <text class="header-desc">管理所有预约订单</text>
    </view>
    
    <view class="admin-content">
      <u-empty text="管理后台功能开发中" mode="data">
        <template #icon>
          <u-icon name="settings" size="120" color="#ddd"></u-icon>
        </template>
      </u-empty>
    </view>
  </view>
</template>

<script>
export default {
  name: 'AdminBooking'
}
</script>

<style lang="scss" scoped>
.admin-booking {
  background: #f8f9fa;
  min-height: 100vh;
  padding: 30rpx;
  
  .admin-header {
    background: #fff;
    padding: 40rpx;
    border-radius: 16rpx;
    margin-bottom: 30rpx;
    text-align: center;
    
    .header-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
      display: block;
      margin-bottom: 8rpx;
    }
    
    .header-desc {
      font-size: 26rpx;
      color: #666;
    }
  }
  
  .admin-content {
    background: #fff;
    padding: 60rpx;
    border-radius: 16rpx;
  }
}
</style>
