<template>
  <view class="booking-management">
    <!-- 统一导航栏 -->
    <beauty-navbar 
      title="预约管理" 
      :show-back="true"
    ></beauty-navbar>

    <!-- 统计卡片 -->
    <view class="stats-cards" style="margin-top: 88px;">
      <view class="stat-card">
        <view class="stat-number">{{ stats.pending }}</view>
        <view class="stat-label">待确认</view>
      </view>
      <view class="stat-card">
        <view class="stat-number">{{ stats.confirmed }}</view>
        <view class="stat-label">已确认</view>
      </view>
      <view class="stat-card">
        <view class="stat-number">{{ stats.inService }}</view>
        <view class="stat-label">服务中</view>
      </view>
      <view class="stat-card">
        <view class="stat-number">{{ stats.completed }}</view>
        <view class="stat-label">已完成</view>
      </view>
    </view>

    <!-- 筛选栏 -->
    <view class="filter-bar">
      <view class="filter-tabs">
        <view 
          v-for="tab in statusTabs" 
          :key="tab.value"
          class="filter-tab"
          :class="{ active: currentStatus === tab.value }"
          @click="switchStatus(tab.value)"
        >
          {{ tab.label }}
        </view>
      </view>
    </view>

    <!-- 预约列表 -->
    <view class="booking-list">
      <view 
        v-for="booking in filteredBookings" 
        :key="booking.id"
        class="booking-item"
      >
        <view class="booking-header">
          <view class="booking-info">
            <view class="booking-service">{{ booking.serviceName }}</view>
            <view class="booking-time">{{ booking.bookingDate }} {{ booking.bookingTime }}</view>
          </view>
          <view class="booking-status" :class="'status-' + booking.status">
            {{ getStatusText(booking.status) }}
          </view>
        </view>
        
        <view class="booking-details">
          <view class="detail-row">
            <text class="detail-label">客户：</text>
            <text class="detail-value">{{ booking.userName }}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">技师：</text>
            <text class="detail-value">{{ booking.technicianName }}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">手机：</text>
            <text class="detail-value">{{ booking.userPhone }}</text>
          </view>
          <view class="detail-row">
            <text class="detail-label">价格：</text>
            <text class="detail-value price">¥{{ booking.price }}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="booking-actions">
          <button 
            v-if="booking.status === 'pending'"
            class="action-btn confirm-btn"
            @click="confirmBooking(booking.id)"
          >
            确认预约
          </button>
          <button 
            v-if="booking.status === 'confirmed'"
            class="action-btn start-btn"
            @click="startService(booking.id)"
          >
            开始服务
          </button>
          <button 
            v-if="booking.status === 'in_service'"
            class="action-btn complete-btn"
            @click="completeService(booking.id)"
          >
            完成服务
          </button>
          <button 
            v-if="booking.status !== 'completed' && booking.status !== 'cancelled'"
            class="action-btn cancel-btn"
            @click="cancelBooking(booking.id)"
          >
            取消预约
          </button>
          <button 
            class="action-btn detail-btn"
            @click="viewBookingDetail(booking.id)"
          >
            查看详情
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="filteredBookings.length === 0" class="empty-state">
      <text class="empty-text">暂无{{ getCurrentStatusText() }}预约</text>
    </view>

    <!-- 加载更多 -->
    <view v-if="hasMore" class="load-more" @click="loadMore">
      <text class="load-more-text">加载更多</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentStatus: 'all',
      bookings: [],
      stats: {
        pending: 0,
        confirmed: 0,
        inService: 0,
        completed: 0
      },
      statusTabs: [
        { label: '全部', value: 'all' },
        { label: '待确认', value: 'pending' },
        { label: '已确认', value: 'confirmed' },
        { label: '服务中', value: 'in_service' },
        { label: '已完成', value: 'completed' }
      ],
      hasMore: true,
      currentPage: 1
    }
  },
  
  computed: {
    filteredBookings() {
      if (this.currentStatus === 'all') {
        return this.bookings
      }
      return this.bookings.filter(booking => booking.status === this.currentStatus)
    }
  },
  
  onLoad() {
    this.loadBookings()
    this.loadStats()
  },
  
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    switchStatus(status) {
      this.currentStatus = status
      this.currentPage = 1
      this.loadBookings()
    },
    
    async loadBookings() {
      try {
        // 模拟API调用
        const response = await this.getAdminBookings(this.currentPage, this.currentStatus)
        
        if (this.currentPage === 1) {
          this.bookings = response.data
        } else {
          this.bookings = [...this.bookings, ...response.data]
        }
        
        this.hasMore = response.hasMore
      } catch (error) {
        console.error('加载预约列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      }
    },
    
    async loadStats() {
      try {
        // 模拟API调用
        const response = await this.getBookingStats()
        this.stats = response.data
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },
    
    async confirmBooking(bookingId) {
      try {
        uni.showModal({
          title: '确认预约',
          content: '确定要确认这个预约吗？',
          success: async (res) => {
            if (res.confirm) {
              await this.updateBookingStatus(bookingId, 'confirmed')
              uni.showToast({
                title: '预约已确认',
                icon: 'success'
              })
              this.loadBookings()
              this.loadStats()
            }
          }
        })
      } catch (error) {
        console.error('确认预约失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'error'
        })
      }
    },
    
    async startService(bookingId) {
      try {
        uni.showModal({
          title: '开始服务',
          content: '确定要开始为客户服务吗？',
          success: async (res) => {
            if (res.confirm) {
              await this.updateBookingStatus(bookingId, 'in_service')
              uni.showToast({
                title: '服务已开始',
                icon: 'success'
              })
              this.loadBookings()
              this.loadStats()
            }
          }
        })
      } catch (error) {
        console.error('开始服务失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'error'
        })
      }
    },
    
    async completeService(bookingId) {
      try {
        uni.showModal({
          title: '完成服务',
          content: '确定要完成这个服务吗？',
          success: async (res) => {
            if (res.confirm) {
              await this.updateBookingStatus(bookingId, 'completed')
              uni.showToast({
                title: '服务已完成',
                icon: 'success'
              })
              this.loadBookings()
              this.loadStats()
            }
          }
        })
      } catch (error) {
        console.error('完成服务失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'error'
        })
      }
    },
    
    async cancelBooking(bookingId) {
      try {
        uni.showModal({
          title: '取消预约',
          content: '确定要取消这个预约吗？此操作不可恢复。',
          success: async (res) => {
            if (res.confirm) {
              await this.updateBookingStatus(bookingId, 'cancelled')
              uni.showToast({
                title: '预约已取消',
                icon: 'success'
              })
              this.loadBookings()
              this.loadStats()
            }
          }
        })
      } catch (error) {
        console.error('取消预约失败:', error)
        uni.showToast({
          title: '操作失败',
          icon: 'error'
        })
      }
    },
    
    viewBookingDetail(bookingId) {
      uni.navigateTo({
        url: `/pages/beauty/booking/detail?id=${bookingId}`
      })
    },
    
    loadMore() {
      this.currentPage++
      this.loadBookings()
    },
    
    getStatusText(status) {
      const statusMap = {
        pending: '待确认',
        confirmed: '已确认',
        in_service: '服务中',
        completed: '已完成',
        cancelled: '已取消'
      }
      return statusMap[status] || '未知状态'
    },
    
    getCurrentStatusText() {
      const statusMap = {
        all: '',
        pending: '待确认',
        confirmed: '已确认',
        in_service: '服务中',
        completed: '已完成'
      }
      return statusMap[this.currentStatus] || ''
    },
    
    // 模拟API方法
    async getAdminBookings(page, status) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockBookings = [
            {
              id: 1,
              serviceName: '深层清洁面部护理',
              bookingDate: '2024-01-15',
              bookingTime: '14:00',
              userName: '张小姐',
              userPhone: '138****8888',
              technicianName: '李美容师',
              price: 288,
              status: 'pending',
              createdAt: '2024-01-14 10:30:00'
            },
            {
              id: 2,
              serviceName: '补水保湿护理',
              bookingDate: '2024-01-15',
              bookingTime: '16:00',
              userName: '王女士',
              userPhone: '139****9999',
              technicianName: '陈美容师',
              price: 188,
              status: 'confirmed',
              createdAt: '2024-01-14 09:15:00'
            },
            {
              id: 3,
              serviceName: '抗衰老面部护理',
              bookingDate: '2024-01-15',
              bookingTime: '10:00',
              userName: '刘女士',
              userPhone: '137****7777',
              technicianName: '赵美容师',
              price: 388,
              status: 'in_service',
              createdAt: '2024-01-13 16:45:00'
            }
          ]
          
          let filteredBookings = mockBookings
          if (status !== 'all') {
            filteredBookings = mockBookings.filter(booking => booking.status === status)
          }
          
          resolve({
            data: filteredBookings,
            hasMore: false
          })
        }, 500)
      })
    },
    
    async getBookingStats() {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            data: {
              pending: 5,
              confirmed: 8,
              inService: 2,
              completed: 25
            }
          })
        }, 300)
      })
    },
    
    async updateBookingStatus(bookingId, status) {
      return new Promise((resolve) => {
        setTimeout(() => {
          // 更新本地数据
          const booking = this.bookings.find(b => b.id === bookingId)
          if (booking) {
            booking.status = status
          }
          resolve({ success: true })
        }, 500)
      })
    }
  }
}
</script>

<style scoped>
.booking-management {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 自定义导航栏 */
.custom-navbar {
  background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 100%);
  padding-top: var(--status-bar-height);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 15px;
}

.navbar-left {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-back {
  font-size: 20px;
  color: #fff;
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
}

.navbar-right {
  width: 40px;
}

/* 统计卡片 */
.stats-cards {
  display: flex;
  padding: 15px;
  gap: 10px;
}

.stat-card {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #FFB6C1;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

/* 筛选栏 */
.filter-bar {
  background: #fff;
  padding: 0 15px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-tabs {
  display: flex;
  gap: 20px;
}

.filter-tab {
  padding: 15px 0;
  font-size: 14px;
  color: #666;
  position: relative;
  cursor: pointer;
}

.filter-tab.active {
  color: #FFB6C1;
  font-weight: 600;
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: #FFB6C1;
}

/* 预约列表 */
.booking-list {
  padding: 15px;
}

.booking-item {
  background: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.booking-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.booking-service {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.booking-time {
  font-size: 14px;
  color: #666;
}

.booking-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-confirmed {
  background: #d4edda;
  color: #155724;
}

.status-in_service {
  background: #cce5ff;
  color: #004085;
}

.status-completed {
  background: #d1ecf1;
  color: #0c5460;
}

.status-cancelled {
  background: #f8d7da;
  color: #721c24;
}

.booking-details {
  margin-bottom: 15px;
}

.detail-row {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-label {
  color: #666;
  width: 60px;
  flex-shrink: 0;
}

.detail-value {
  color: #333;
  flex: 1;
}

.detail-value.price {
  color: #FFB6C1;
  font-weight: 600;
}

.booking-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 12px;
  border: none;
  cursor: pointer;
}

.confirm-btn {
  background: #28a745;
  color: #fff;
}

.start-btn {
  background: #007bff;
  color: #fff;
}

.complete-btn {
  background: #17a2b8;
  color: #fff;
}

.cancel-btn {
  background: #dc3545;
  color: #fff;
}

.detail-btn {
  background: #6c757d;
  color: #fff;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 50px 20px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 20px;
}

.load-more-text {
  font-size: 14px;
  color: #666;
}
</style>