<template>
	<view class="pay-page">
		<!-- 支付金额 -->
		<view class="amount-box">
			<text class="label">支付金额</text>
			<text class="amount">¥{{amount}}</text>
		</view>
		
		<!-- 支付方式 -->
		<view class="pay-methods">
			<view class="title">支付方式</view>
			<view class="method-list">
				<view class="method-item" :class="{active: payType === 1}" @click="selectPayType(1)">
					<image src="/static/images/wechat-pay.png" mode="aspectFit"></image>
					<text>微信支付</text>
					<text class="iconfont icon-check" v-if="payType === 1"></text>
				</view>
				<view class="method-item" :class="{active: payType === 2}" @click="selectPayType(2)">
					<image src="/static/images/alipay.png" mode="aspectFit"></image>
					<text>支付宝支付</text>
					<text class="iconfont icon-check" v-if="payType === 2"></text>
				</view>
				<view class="method-item" :class="{active: payType === 3}" @click="selectPayType(3)">
					<image src="/static/images/balance.png" mode="aspectFit"></image>
					<text>余额支付</text>
					<text class="balance-info" v-if="userInfo.balance !== undefined">
						(当前余额：¥{{userInfo.balance}})
					</text>
					<text class="iconfont icon-check" v-if="payType === 3"></text>
				</view>
			</view>
		</view>
		
		<!-- 支付按钮 -->
		<view class="pay-btn" @click="pay">立即支付</view>
	</view>
</template>

<script>
import { getOrderDetail, payOrder } from '@/api/order'
import { getUserInfo } from '@/api/user'

export default {
	data() {
		return {
			orderId: '', // 订单ID
			amount: 0, // 支付金额
			payType: 1, // 支付方式：1-微信 2-支付宝 3-余额
			userInfo: {} // 用户信息
		}
	},
	onLoad(options) {
		if (options.orderId) {
			this.orderId = options.orderId
			if(options.amount) {
				this.amount = parseFloat(options.amount)
			}
			if(options.payType) {
				this.payType = parseInt(options.payType)
			}
			this.loadOrderInfo()
			// 只有余额支付时才需要加载用户信息
			if(this.payType === 3) {
				this.loadUserInfo()
			}
		}
	},
	methods: {
		// 加载订单信息
		async loadOrderInfo() {
			try {
				const { code, data } = await getOrderDetail(this.orderId)
				if (code === 200) {
					this.amount = data.payAmount
					this.payType = data.payType
					// 如果是余额支付，加载用户信息
					if(this.payType === 3) {
						await this.loadUserInfo()
					}
				}
			} catch (e) {
				console.error('加载订单信息失败:', e)
				uni.showToast({
					title: '加载订单信息失败',
					icon: 'none'
				})
			}
		},
		
		// 加载用户信息
		async loadUserInfo() {
			try {
				const { code, data } = await getUserInfo()
				if (code === 200) {
					this.userInfo = data
					// 检查余额是否足够
					if(this.payType === 3 && this.userInfo.balance < this.amount) {
						uni.showModal({
							title: '余额不足',
							content: `当前余额：${this.userInfo.balance}元\n还差：${(this.amount - this.userInfo.balance).toFixed(2)}元`,
							showCancel: false,
							success: () => {
								uni.redirectTo({
									url: '/pages/order/list?status=unpaid'
								})
							}
						})
					}
				}
			} catch (e) {
				console.error('加载用户信息失败:', e)
			}
		},
		
		// 选择支付方式
		selectPayType(type) {
			// 检查余额是否足够
			if(type === 3) {
				if(!this.userInfo.balance) {
					this.loadUserInfo().then(() => {
						this.checkAndSelectBalancePay(type)
					})
					return
				}
				this.checkAndSelectBalancePay(type)
				return
			}
			this.payType = type
		},

		// 检查并选择余额支付
		checkAndSelectBalancePay(type) {
			if(this.userInfo.balance < this.amount) {
				uni.showModal({
					title: '余额不足',
					content: `当前余额：${this.userInfo.balance}元\n还差：${(this.amount - this.userInfo.balance).toFixed(2)}元\n是否切换其他支付方式？`,
					success: (res) => {
						if(!res.confirm) {
							// 用户取消，保持原支付方式
							return
						}
					}
				})
				return
			}
			this.payType = type
		},
		
		// 发起支付
		async pay() {
			// 余额支付时先检查余额
			if(this.payType === 3) {
				if(!this.userInfo.balance) {
					uni.showToast({
						title: '获取余额信息失败',
						icon: 'none'
					})
					return
				}
				if(this.userInfo.balance < this.amount) {
					uni.showToast({
						title: `余额不足，还差${(this.amount - this.userInfo.balance).toFixed(2)}元`,
						icon: 'none'
					})
					return
				}
			}
			
			this.doPayment()
		},
		
		// 执行支付
		async doPayment() {
			try {
				uni.showLoading({
					title: '正在支付...'
				})
				
				const { code, data, message } = await payOrder({
					orderId: parseInt(this.orderId),
					payType: this.payType
				})
				
				uni.hideLoading()
				
				if (code === 200 && data.success) {
					if (this.payType === 1) {
						// 微信支付
						uni.requestPayment({
							provider: 'wxpay',
							...data,
							success: () => {
								this.paySuccess()
							},
							fail: (err) => {
								console.error('微信支付失败:', err)
								this.payFail()
							}
						})
					} else if (this.payType === 2) {
						// 支付宝支付
						uni.requestPayment({
							provider: 'alipay',
							orderInfo: data.payInfo,
							success: () => {
								this.paySuccess()
							},
							fail: (err) => {
								console.error('支付宝支付失败:', err)
								this.payFail()
							}
						})
					} else if (this.payType === 3) {
						// 余额支付，直接调用支付成功处理
						if(data.balance !== undefined) {
							this.userInfo.balance = data.balance
						}
						this.paySuccess()
					}
				} else {
					uni.showToast({
						title: message || '支付失败',
						icon: 'none'
					})
				}
			} catch (e) {
				uni.hideLoading()
				console.error('支付失败:', e)
				uni.showToast({
					title: e.message || '支付失败',
					icon: 'none'
				})
			}
		},
		
		// 支付成功
		paySuccess() {
			uni.showToast({
				title: '支付成功',
				icon: 'success'
			})
			
			// 延迟跳转到订单详情页
			setTimeout(() => {
				uni.redirectTo({
					url: '/pages/order/detail?id=' + this.orderId
				})
			}, 1500)
		},
		
		// 支付失败
		payFail() {
			uni.showToast({
				title: '支付失败',
				icon: 'none'
			})
		}
	}
}
</script>

<style lang="scss">
.pay-page {
	min-height: 100vh;
	padding: 30rpx;
	background: #f5f5f5;
	
	.amount-box {
		background: #fff;
		padding: 40rpx;
		border-radius: 12rpx;
		text-align: center;
		margin-bottom: 30rpx;
		
		.label {
			font-size: 28rpx;
			color: #666;
		}
		
		.amount {
			font-size: 60rpx;
			font-weight: bold;
			color: #ff6b6b;
			margin-top: 20rpx;
		}
	}
	
	.pay-methods {
		background: #fff;
		padding: 30rpx;
		border-radius: 12rpx;
		
		.title {
			font-size: 32rpx;
			font-weight: bold;
			margin-bottom: 30rpx;
		}
		
		.method-list {
			.method-item {
				display: flex;
				align-items: center;
				padding: 30rpx 0;
				border-bottom: 1px solid #eee;
				
				&:last-child {
					border-bottom: none;
				}
				
				image {
					width: 60rpx;
					height: 60rpx;
					margin-right: 20rpx;
				}
				
				text {
					flex: 1;
					font-size: 28rpx;
					
					&.icon-check {
						flex: none;
						color: #ff6b6b;
						font-size: 40rpx;
					}
				}
				
				&.active {
					color: #ff6b6b;
				}
			}
		}
	}
	
	.pay-btn {
		position: fixed;
		left: 30rpx;
		right: 30rpx;
		bottom: 50rpx;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		background: #ff6b6b;
		color: #fff;
		font-size: 32rpx;
		border-radius: 45rpx;
		
		&:active {
			opacity: 0.9;
		}
	}
}
</style> 