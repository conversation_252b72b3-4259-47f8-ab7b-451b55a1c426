<template>
	<PageLayout 
		:show-left="true"
		:show-center="false"
		:show-right="false"
		:nav-transparent="true"
		nav-text-color="#fff"
		@navLeftClick="handleBack"
	>
		<!-- 登录页面容器 -->
		<view class="login-page">
			<!-- 主要内容区域 -->
			<view class="main-content">
			<!-- 顶部Logo区域 -->
			<view class="header-section">
				<view class="logo-container">
					<view class="logo-wrapper">
						<image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
					</view>
					<text class="brand-name">wn商城</text>
				</view>
			</view>
			
			<!-- 登录注册卡片 -->
			<view class="auth-card">
				<!-- Tab切换 -->
				<view class="tab-header">
					<view 
						class="tab-item" 
						:class="{ 'active': currentTab === 'login' }"
						@click="switchTab('login')"
					>
						<text>登录</text>
					</view>
					<view 
						class="tab-item" 
						:class="{ 'active': currentTab === 'register' }"
						@click="switchTab('register')"
					>
						<text>注册</text>
					</view>
				</view>
				
				<!-- 登录表单 -->
				<view class="form-container" v-if="currentTab === 'login'">
					<!-- 用户名输入框 -->
					<view class="input-group">
						<view class="input-wrapper" :class="{ 'focused': usernameFocused }">
							<view class="input-icon">
								<text class="iconfont icon-user">👤</text>
							</view>
							<input 
								type="text"
								v-model="loginForm.username"
								placeholder="用户名/手机号"
								placeholder-class="input-placeholder"
								class="form-input"
								@focus="usernameFocused = true"
								@blur="usernameFocused = false"
							/>
						</view>
					</view>
					
					<!-- 密码输入框 -->
					<view class="input-group">
						<view class="input-wrapper" :class="{ 'focused': passwordFocused }">
							<view class="input-icon">
								<text class="iconfont icon-lock">🔒</text>
							</view>
							<input 
								type="text"
								:password="!showPassword"
								v-model="loginForm.password"
								placeholder="密码"
								placeholder-class="input-placeholder"
								class="form-input"
								@focus="passwordFocused = true"
								@blur="passwordFocused = false"
							/>
							<view class="input-action" @click="toggleLoginPassword">
								<text class="eye-icon">{{ showPassword ? '👁️' : '🙈' }}</text>
							</view>
						</view>
					</view>
					
					<!-- 选项区域 -->
					<view class="form-options">
						<view class="remember-section">
							<checkbox 
								:checked="rememberPassword" 
								@click="rememberPassword = !rememberPassword"
								color="#FF6B35"
								class="custom-checkbox"
							/>
							<text class="option-text">记住密码</text>
						</view>
						<text class="forget-link" @click="onForgetPassword">忘记密码？</text>
					</view>
					
					<!-- 登录按钮 -->
					<button 
						class="auth-btn"
						@click="handleLogin"
					>
						<text class="btn-text">登录</text>
					</button>
					
					<!-- 分割线 -->
					<view class="divider">
						<view class="divider-line"></view>
						<text class="divider-text">或</text>
						<view class="divider-line"></view>
					</view>
					
					<!-- 微信登录 -->
					<button 
						open-type="getPhoneNumber" 
						@getphonenumber="handleGetPhoneNumber"
						class="wechat-btn"
						:loading="wechatLoginLoading" 
						:disabled="!agreed || wechatLoginLoading" 
					>
						<text class="wechat-icon">💬</text>
						<text class="btn-text">微信手机号登录</text>
					</button>
				</view>
				
				<!-- 注册表单 -->
				<view class="form-container" v-if="currentTab === 'register'">
					<!-- 手机号输入框 -->
					<view class="input-group">
						<view class="input-wrapper" :class="{ 'focused': phoneFocused, 'error': errors.phone }">
							<view class="input-icon">
								<text class="iconfont icon-phone">📱</text>
							</view>
							<input 
								type="number"
								v-model="registerForm.phone"
								placeholder="请输入手机号"
								placeholder-class="input-placeholder"
								class="form-input"
								maxlength="11"
								@focus="phoneFocused = true"
								@blur="phoneFocused = false"
								@input="validatePhone"
							/>
						</view>
						<text v-if="errors.phone" class="error-text">{{ errors.phone }}</text>
					</view>
					
					<!-- 验证码输入框 -->
					<view class="input-group">
						<view class="input-wrapper" :class="{ 'focused': codeFocused }">
							<view class="input-icon">
								<text class="iconfont icon-code">🔢</text>
							</view>
							<input 
								type="number"
								v-model="registerForm.code"
								placeholder="请输入验证码"
								placeholder-class="input-placeholder"
								class="form-input"
								maxlength="6"
								@focus="codeFocused = true"
								@blur="codeFocused = false"
							/>
							<view class="input-action">
								<button 
									class="code-btn"
									:class="{ 'disabled': !canSendCode || countdown > 0 }"
									:disabled="!canSendCode || countdown > 0"
									@click="sendCode"
								>
									{{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
								</button>
							</view>
						</view>
					</view>
					
					<!-- 密码输入框 -->
					<view class="input-group">
						<view class="input-wrapper" :class="{ 'focused': regPasswordFocused, 'error': errors.password }">
							<view class="input-icon">
								<text class="iconfont icon-lock">🔒</text>
							</view>
							<input 
								type="text"
								:password="!showRegPassword"
								v-model="registerForm.password"
								placeholder="请输入密码（6-20位）"
								placeholder-class="input-placeholder"
								class="form-input"
								@focus="regPasswordFocused = true"
								@blur="regPasswordFocused = false"
								@input="validatePassword"
							/>
							<view class="input-action" @click="toggleRegPassword">
								<text class="eye-icon">{{ showRegPassword ? '👁️' : '🙈' }}</text>
							</view>
						</view>
						<text v-if="errors.password" class="error-text">{{ errors.password }}</text>
					</view>
					
					<!-- 确认密码输入框 -->
					<view class="input-group">
						<view class="input-wrapper" :class="{ 'focused': confirmPasswordFocused, 'error': errors.confirmPassword }">
							<view class="input-icon">
								<text class="iconfont icon-lock">🔐</text>
							</view>
							<input 
								type="text"
								:password="!showConfirmPassword"
								v-model="registerForm.confirmPassword"
								placeholder="请再次输入密码"
								placeholder-class="input-placeholder"
								class="form-input"
								@focus="confirmPasswordFocused = true"
								@blur="confirmPasswordFocused = false"
								@input="validateConfirmPassword"
							/>
							<view class="input-action" @click="toggleConfirmPassword">
								<text class="eye-icon">{{ showConfirmPassword ? '👁️' : '🙈' }}</text>
							</view>
						</view>
						<text v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</text>
					</view>
					
					<!-- 注册按钮 -->
					<button 
						class="auth-btn"
						@click="handleRegister"
					>
						<text class="btn-text">{{ registerLoading ? '注册中...' : '立即注册' }}</text>
					</button>
				</view>
			</view>
			
			<!-- 用户协议 -->
			<view class="agreement-section">
				<view class="agreement-wrapper">
					<checkbox 
						:checked="agreed" 
						@click="agreed = !agreed"
						color="#FF6B35"
						class="agreement-checkbox"
					/>
					<text class="agreement-text">
						登录即表示同意
						<text class="agreement-link" @click="onUserAgreement">《用户协议》</text>
						和
						<text class="agreement-link" @click="onPrivacyPolicy">《隐私政策》</text>
					</text>
				</view>
			</view>
		</view>
		</view>
	</PageLayout>
</template>

<script>
import { login, logout as logoutApi, wechatPhoneLogin, register, sendSmsCode } from '@/api/user.js'
import AuthUtils from '@/utils/auth'
import PageLayout from '@/components/layout/PageLayout.vue'
import shareMixin from '@/mixins/shareMixin.js'

// 退出登录方法
export async function logout() {
	try {
		// 1. 调用退出登录接口
		const [err, res] = await logoutApi()
		
		if (err || res.statusCode !== 200 || res.data.code !== 200) {
			throw new Error(err?.message || res?.data?.message || '退出失败')
		}
		
		// 2. 清除token和用户信息
		uni.removeStorageSync('token')
		uni.removeStorageSync('userInfo')
		
		// 3. 提示退出成功
		uni.showToast({
			title: '退出成功',
			icon: 'success',
			mask: true,
			duration: 1500
		})
		
		// 4. 跳转到登录页
		setTimeout(() => {
			uni.reLaunch({
				url: '/pages/login/login'
			})
		}, 1500)
		
	} catch (error) {
		console.error('退出失败:', error)
		uni.showToast({
			title: error.message || '退出失败',
			icon: 'none'
		})
	}
}

export default {
	components: {
		PageLayout
	},
	
	mixins: [shareMixin],
	data() {
		return {
			currentTab: 'login', // 当前选中的tab
			// 登录表单
			loginForm: {
				username: '',
				password: ''
			},
			// 注册表单
			registerForm: {
				phone: '',
				code: '',
				password: '',
				confirmPassword: ''
			},
			agreed: true,
			showPassword: false,
			showRegPassword: false,
			showConfirmPassword: false,
			rememberPassword: false,
			wechatLoginLoading: false,
			registerLoading: false,
			countdown: 0,
			timer: null,
			// 焦点状态
			usernameFocused: false,
			passwordFocused: false,
			phoneFocused: false,
			codeFocused: false,
			regPasswordFocused: false,
			confirmPasswordFocused: false,
			// 错误信息
			errors: {
				phone: '',
				password: '',
				confirmPassword: ''
			},
			showBackButton: false // 控制是否显示返回按钮
		}
	},
	computed: {
		// 是否可以发送验证码
		canSendCode() {
			return this.isValidPhone(this.registerForm.phone) && this.countdown === 0
		}
	},
	methods: {
		// 切换tab
		switchTab(tab) {
			this.currentTab = tab
			// 切换时清空错误信息
			this.errors = {
				phone: '',
				password: '',
				confirmPassword: ''
			}
		},
		
		async handleLogin() {
			try {
				// 1. 表单验证
				if (!this.loginForm.username || !this.loginForm.password) {
					uni.showToast({
						title: '请输入用户名和密码',
						icon: 'none'
					})
					return
				}
				
				if (!this.agreed) {
					uni.showToast({
						title: '请先同意用户协议',
						icon: 'none'
					})
					return
				}
				
				// 2. 调用登录接口
				const params = {
					username: this.loginForm.username,
					password: this.loginForm.password
				}
				const res = await login(params)
				
				// 3. 判断登录状态
				if (res.code !== 200) {
					uni.showToast({
						title: res.message || '登录失败',
						icon: 'none'
					})
					return
				}
				
				// 4. 记住密码
				if (this.rememberPassword) {
					uni.setStorageSync('loginInfo', this.loginForm)
				} else {
					uni.removeStorageSync('loginInfo')
				}
				
				// 5. 处理登录成功
				const redirectUrl = this.getRedirectUrl()
				AuthUtils.handleLoginSuccess(res.data, redirectUrl)
				
			} catch (error) {
				console.error('登录失败:', error)
				uni.showToast({
					title: error.message || '登录失败',
					icon: 'none'
				})
			}
		},
		
		// 添加微信手机号登录方法
		async handleGetPhoneNumber(e) {
			if (!this.agreed) {
				uni.showToast({ title: '请先同意用户协议', icon: 'none' });
				return;
			}

			// 检查用户是否拒绝授权
			if (e.detail.errMsg !== 'getPhoneNumber:ok') {
				uni.showToast({ title: '您取消了授权', icon: 'none' });
				return;
			}

			this.wechatLoginLoading = true;
			let retry = false;
			try {
				let loginRes = await uni.login({ provider: 'weixin' });
				if (loginRes.errMsg !== 'login:ok') throw new Error('微信登录失败: ' + loginRes.errMsg);
				let code = loginRes.code;
				let params = {
					code,
					encryptedData: e.detail.encryptedData,
					iv: e.detail.iv
				};
				let res = await wechatPhoneLogin(params);
				if (res.code !== 200 && !retry) {
					// 第一次失败，自动重试一次
					retry = true;
					loginRes = await uni.login({ provider: 'weixin' });
					if (loginRes.errMsg !== 'login:ok') throw new Error('微信登录失败: ' + loginRes.errMsg);
					code = loginRes.code;
					params.code = code;
					res = await wechatPhoneLogin(params);
				}
				if (res.code !== 200) {
					uni.showToast({ title: res.message || '微信登录失败', icon: 'none' });
					return;
				}
				// 登录成功逻辑...
				const redirectUrl = this.getRedirectUrl();
				AuthUtils.handleLoginSuccess(res.data, redirectUrl);
			} catch (error) {
				uni.showToast({ title: error.message || '登录时发生错误', icon: 'none' });
			} finally {
				this.wechatLoginLoading = false;
			}
		},
		
		// 验证手机号
		validatePhone() {
			if (!this.registerForm.phone) {
				this.errors.phone = ''
			} else if (!this.isValidPhone(this.registerForm.phone)) {
				this.errors.phone = '请输入正确的手机号'
			} else {
				this.errors.phone = ''
			}
		},
		
		// 验证密码
		validatePassword() {
			if (!this.registerForm.password) {
				this.errors.password = ''
			} else if (!this.isValidPassword(this.registerForm.password)) {
				this.errors.password = '密码长度为6-20位'
			} else {
				this.errors.password = ''
			}
		},
		
		// 验证确认密码
		validateConfirmPassword() {
			if (!this.registerForm.confirmPassword) {
				this.errors.confirmPassword = ''
			} else if (this.registerForm.password !== this.registerForm.confirmPassword) {
				this.errors.confirmPassword = '两次输入的密码不一致'
			} else {
				this.errors.confirmPassword = ''
			}
		},
		
		// 检查手机号格式
		isValidPhone(phone) {
			return /^1[3-9]\d{9}$/.test(phone)
		},
		
		// 检查密码格式
		isValidPassword(password) {
			return password.length >= 6 && password.length <= 20
		},
		
		// 发送验证码
		async sendCode() {
			if (!this.canSendCode) return
			
			try {
				await sendSmsCode({
					phone: this.registerForm.phone,
					type: 'register'
				})
				
				uni.showToast({
					title: '验证码已发送',
					icon: 'success'
				})
				
				// 开始倒计时
				this.startCountdown()
				
			} catch (error) {
				uni.showToast({
					title: error.message || '发送失败',
					icon: 'none'
				})
			}
		},
		
		// 开始倒计时
		startCountdown() {
			this.countdown = 60
			this.timer = setInterval(() => {
				this.countdown--
				if (this.countdown <= 0) {
					clearInterval(this.timer)
					this.timer = null
				}
			}, 1000)
		},
		
		// 处理注册逻辑
		async handleRegister() {
			// 基本验证
			if (!this.agreed) {
				uni.showToast({
					title: '请先同意用户协议',
					icon: 'none'
				})
				return
			}
			
			if (!this.registerForm.phone) {
				uni.showToast({
					title: '请输入手机号',
					icon: 'none'
				})
				return
			}
			
			if (!this.registerForm.code) {
				uni.showToast({
					title: '请输入验证码',
					icon: 'none'
				})
				return
			}
			
			if (!this.registerForm.password) {
				uni.showToast({
					title: '请输入密码',
					icon: 'none'
				})
				return
			}
			
			if (!this.registerForm.confirmPassword) {
				uni.showToast({
					title: '请确认密码',
					icon: 'none'
				})
				return
			}
			
			// 最终验证
			this.validatePhone()
			this.validatePassword()
			this.validateConfirmPassword()
			
			if (this.errors.phone || this.errors.password || this.errors.confirmPassword) {
				uni.showToast({
					title: '请检查输入信息',
					icon: 'none'
				})
				return
			}
			
			this.registerLoading = true
			
			try {
				// 获取邀请者ID
				const inviterId = this.getValidInviterId()
				
				const registerData = {
					phone: this.registerForm.phone,
					code: this.registerForm.code,
					password: this.registerForm.password
				}
				
				// 如果有邀请者ID，添加到注册数据中
				if (inviterId) {
					registerData.inviterId = parseInt(inviterId)
				}
				
				const response = await register(registerData)
				
				uni.showToast({
					title: '注册成功',
					icon: 'success',
					mask: true
				})
				
				// 清除邀请者ID
				this.clearInviterId()
				
				// 延迟切换到登录tab
				setTimeout(() => {
					this.switchTab('login')
					// 自动填入注册的手机号
					this.loginForm.username = this.registerForm.phone
				}, 1500)
				
			} catch (error) {
				uni.showToast({
					title: error.message || '注册失败',
					icon: 'none'
				})
			} finally {
				this.registerLoading = false
			}
		},
		
		onForgetPassword() {
			uni.navigateTo({
				url: '/pages/forget-password/forget-password'
			})
		},
		
		onUserAgreement() {
			uni.navigateTo({
				url: '/pages/agreement/user'
			})
		},
		
		onPrivacyPolicy() {
			uni.navigateTo({
				url: '/pages/agreement/privacy'
			})
		},
		
		// 处理导航栏返回按钮点击
		handleBack() {
			const pages = getCurrentPages()
			if (pages.length > 1) {
				uni.navigateBack()
			} else {
				uni.switchTab({
					url: '/pages/index/index'
				})
			}
		},
		
		// 处理导航栏更多按钮点击
		handleMore() {
			uni.showActionSheet({
				itemList: ['帮助', '反馈', '关于'],
				success: (res) => {
					console.log('选中了第' + (res.tapIndex + 1) + '个按钮')
				}
			})
		},
		
		// 切换登录密码显示状态
		toggleLoginPassword() {
			console.log('切换登录密码显示状态:', this.showPassword)
			this.showPassword = !this.showPassword
			console.log('切换后状态:', this.showPassword)
		},
		
		// 切换注册密码显示状态
		toggleRegPassword() {
			console.log('切换注册密码显示状态:', this.showRegPassword)
			this.showRegPassword = !this.showRegPassword
			console.log('切换后状态:', this.showRegPassword)
		},
		
		// 切换确认密码显示状态
		toggleConfirmPassword() {
			console.log('切换确认密码显示状态:', this.showConfirmPassword)
			this.showConfirmPassword = !this.showConfirmPassword
			console.log('切换后状态:', this.showConfirmPassword)
		},
		
		// 获取重定向URL
		getRedirectUrl() {
			// 从页面参数中获取重定向URL
			const pages = getCurrentPages()
			const currentPage = pages[pages.length - 1]
			const options = currentPage.options || {}
			
			// 如果有重定向参数，返回该URL
			if (options.redirect) {
				return decodeURIComponent(options.redirect)
			}
			
			// 默认跳转到美容首页
			return '/pages/beauty/index/index'
		}
	},
	
	onLoad() {
		// 读取记住的密码
		const loginInfo = uni.getStorageSync('loginInfo')
		if (loginInfo) {
			this.loginForm = loginInfo
			this.rememberPassword = true
		}
		
		// 判断是否显示返回按钮
		const pages = getCurrentPages()
		this.showBackButton = pages.length > 1
	},
	
	onUnload() {
		// 页面卸载时清除定时器
		if (this.timer) {
			clearInterval(this.timer)
		}
	}
}
</script>

<style lang="scss">
.login-page {
	min-height: 100vh;
	background: linear-gradient(135deg, #FF6B35 0%, #F7931E 50%, #FFB74D 100%);
	position: relative;
	overflow: hidden;
}



.main-content {
	position: relative;
	z-index: 2;
	padding: 0 40rpx;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

// 头部区域
.header-section {
	padding-top: 160rpx; // 为透明导航栏预留空间
	margin-bottom: 40rpx; // 减少间距
	
	.logo-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		
		.logo-wrapper {
			width: 100rpx; // 缩小logo
			height: 100rpx;
			background: rgba(255, 255, 255, 0.15);
			border-radius: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 20rpx; // 减少间距
			backdrop-filter: blur(20px);
			border: 2rpx solid rgba(255, 255, 255, 0.25);
			box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
			
			.logo {
				width: 60rpx; // 缩小logo
				height: 60rpx;
			}
		}
		
		.brand-name {
			font-size: 44rpx; // 缩小字体
			font-weight: 700;
			color: #fff;
			text-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
			letter-spacing: 2rpx;
		}
	}
}

// 登录注册卡片
.auth-card {
	background: rgba(255, 255, 255, 0.98);
	border-radius: 28rpx;
	padding: 0; // 移除内边距，由子元素控制
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
	margin-bottom: 40rpx;
	overflow: hidden;
	
	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 6rpx;
		background: linear-gradient(90deg, #FF6B35 0%, #F7931E 50%, #FFB74D 100%);
		border-radius: 40rpx 40rpx 0 0;
	}
}

// Tab头部
.tab-header {
	display: flex;
	margin-top: 6rpx; // 在渐变条下方
	
	.tab-item {
		flex: 1;
		text-align: center;
		padding: 32rpx 0;
		position: relative;
		cursor: pointer;
		transition: all 0.3s ease;
		
		text {
			font-size: 32rpx;
			font-weight: 600;
			color: #ADB5BD;
			transition: color 0.3s ease;
		}
		
		&.active {
			text {
				color: #FF6B35;
			}
			
			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 60rpx;
				height: 4rpx;
				background: linear-gradient(90deg, #FF6B35 0%, #F7931E 100%);
				border-radius: 2rpx;
			}
		}
	}
}

// 表单容器
.form-container {
	padding: 20rpx 36rpx 36rpx; // 添加内边距
	.input-group {
		margin-bottom: 30rpx; // 减少输入框间距
		
		.input-wrapper {
			display: flex;
			align-items: center;
			background: #F8F9FA;
			border-radius: 20rpx;
			border: 2rpx solid #E9ECEF;
			transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
			position: relative;
			overflow: hidden;
			min-height: 90rpx; // 确保一致的最小高度
			box-sizing: border-box;
			
			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(135deg, rgba(255, 107, 53, 0.05) 0%, rgba(247, 147, 30, 0.05) 100%);
				opacity: 0;
				transition: opacity 0.3s ease;
			}
			
			&.focused {
				border-color: #FF6B35;
				background: #fff;
				box-shadow: 0 0 0 8rpx rgba(255, 107, 53, 0.1);
				transform: translateY(-2rpx);
				
				&::before {
					opacity: 1;
				}
			}
			
			&.error {
				border-color: #ff4757;
				background: #fff;
				box-shadow: 0 0 0 8rpx rgba(255, 71, 87, 0.1);
			}
			
			.input-icon {
				padding: 0 24rpx;
				z-index: 1;
				display: flex;
				align-items: center;
				justify-content: center;
				min-width: 80rpx; // 确保图标区域宽度一致
				
				.iconfont {
					font-size: 36rpx;
					color: #6C757D;
					transition: color 0.3s ease;
				}
			}
			
			.form-input {
				flex: 1;
				height: 90rpx; // 统一高度
				font-size: 32rpx;
				color: #2C3E50;
				background: transparent;
				border: none;
				z-index: 1;
				font-weight: 500;
				padding: 0; // 确保没有额外padding
				
				&.input-placeholder {
					color: #ADB5BD;
				}
			}
			
			.input-action {
				padding: 0 24rpx;
				z-index: 1;
				display: flex;
				align-items: center;
				justify-content: center;
				min-width: 80rpx; // 确保操作区域宽度一致
				min-height: 60rpx; // 增加点击区域高度
				cursor: pointer;
				
				.eye-icon {
					font-size: 32rpx;
					color: #6C757D;
					transition: all 0.3s ease;
					cursor: pointer;
					padding: 12rpx; // 增加点击区域
					border-radius: 8rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					min-width: 44rpx;
					min-height: 44rpx;
					
					&:active {
						color: #FF6B35;
						background: rgba(255, 107, 53, 0.15);
						transform: scale(0.95);
					}
				}
			}
			
			&.focused {
				.input-icon .iconfont,
				.input-action .eye-icon {
					color: #FF6B35;
				}
			}
		}
	}
	
	.form-options {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 35rpx; // 减少间距
		
		.remember-section {
			display: flex;
			align-items: center;
			
			.custom-checkbox {
				transform: scale(1);
				margin-right: 16rpx;
			}
			
			.option-text {
				font-size: 30rpx;
				color: #6C757D;
				font-weight: 500;
			}
		}
		
		.forget-link {
			font-size: 30rpx;
			color: #FF6B35;
			font-weight: 600;
			transition: color 0.3s ease;
			
			&:active {
				color: #E55A2B;
			}
		}
	}
	
	.auth-btn {
		width: 100%;
		height: 100rpx;
		background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
		border-radius: 50rpx; // 修正圆角，使其为高度的一半
		border: none;
		box-shadow: 0 12rpx 24rpx rgba(255, 107, 53, 0.25);
		margin-bottom: 30rpx;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		position: relative;
		overflow: hidden;
		display: flex;
		align-items: center;
		justify-content: center;
		
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
			opacity: 0;
			transition: opacity 0.3s ease;
		}
		
		.btn-text {
			font-size: 32rpx;
			font-weight: 700;
			color: #fff;
			position: relative;
			z-index: 1;
			letter-spacing: 1rpx;
		}
		
		&:active {
			transform: translateY(4rpx);
			box-shadow: 0 8rpx 20rpx rgba(255, 107, 53, 0.4);
			
			&::before {
				opacity: 1;
			}
		}
		
		&.disabled {
			opacity: 0.6;
			box-shadow: 0 8rpx 16rpx rgba(255, 107, 53, 0.2);
			transform: none;
		}
		
		&::after {
			border: none;
		}
	}
	
	// 验证码按钮
	.code-btn {
		height: 56rpx;
		line-height: 56rpx;
		padding: 0 20rpx;
		font-size: 24rpx;
		color: #FF6B35;
		background: rgba(255, 107, 53, 0.1);
		border: 1rpx solid #FF6B35;
		border-radius: 28rpx;
		transition: all 0.3s ease;
		
		&.disabled {
			color: #999;
			border-color: #ddd;
			background: #f5f5f5;
		}
		
		&::after {
			border: none;
		}
	}
	
	// 错误文本
	.error-text {
		display: block;
		font-size: 24rpx;
		color: #ff4757;
		margin-top: 8rpx;
		margin-left: 20rpx;
	}
	
	.divider {
		display: flex;
		align-items: center;
		margin: 30rpx 0; // 减少分割线间距
		
		.divider-line {
			flex: 1;
			height: 2rpx;
			background: linear-gradient(90deg, transparent 0%, #E9ECEF 50%, transparent 100%);
		}
		
		.divider-text {
			padding: 0 32rpx;
			font-size: 28rpx;
			color: #ADB5BD;
			font-weight: 500;
		}
	}
	
	.wechat-btn {
		width: 100%;
		height: 100rpx;
		background: linear-gradient(135deg, #07C160 0%, #06AD5B 100%);
		border-radius: 50rpx; // 修正圆角
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 12rpx 24rpx rgba(7, 193, 96, 0.25);
		margin-bottom: 30rpx;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		position: relative;
		overflow: hidden;
		
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
			opacity: 0;
			transition: opacity 0.3s ease;
		}
		
		.wechat-icon {
			font-size: 36rpx;
			margin-right: 16rpx;
			position: relative;
			z-index: 1;
		}
		
		.btn-text {
			font-size: 34rpx;
			font-weight: 700;
			color: #fff;
			position: relative;
			z-index: 1;
			letter-spacing: 1rpx;
		}
		
		&:active {
			transform: translateY(4rpx);
			box-shadow: 0 8rpx 20rpx rgba(7, 193, 96, 0.35);
			
			&::before {
				opacity: 1;
			}
		}
		
		&[disabled] {
			opacity: 0.6;
			box-shadow: 0 8rpx 16rpx rgba(7, 193, 96, 0.2);
			transform: none;
		}
		
		&::after {
			border: none;
		}
	}
	

}

// 用户协议
.agreement-section {
	margin-top: 20rpx; // 固定间距，不使用auto
	padding-bottom: 40rpx; // 减少底部间距
	
	.agreement-wrapper {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 20rpx;
		
		.agreement-checkbox {
			transform: scale(0.9);
			margin-right: 16rpx;
		}
		
		.agreement-text {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.9);
			line-height: 1.6;
			text-align: left;
			flex: 1;
			
			.agreement-link {
				color: #fff;
				font-weight: 600;
				text-decoration: underline;
				text-underline-offset: 4rpx;
				transition: all 0.3s ease;
				
				&:active {
					color: #FFE0B2;
				}
			}
		}
	}
}
</style> 