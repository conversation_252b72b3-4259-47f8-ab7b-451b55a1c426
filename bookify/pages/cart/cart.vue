<template>
	<PageLayout :theme="currentTheme" :content-style="{paddingBottom: '120rpx', paddingTop: fromDetail ? '88rpx' : '0'}">
		<!-- 返回按钮 -->
		<view class="nav-bar glass-card" v-if="fromDetail">
			<view class="back-btn" @click="goBack">
				<text class="iconfont icon-back-light">‹</text>
			</view>
		</view>
		
		<!-- 空购物车提示 -->
		<view class="empty" v-if="!cartList.length">
			<image src="/static/images/empty-cart.png" mode="aspectFit"></image>
			<text>购物车还是空的</text>
			<button class="to-shop gradient-btn" @click="toShop">去逛逛</button>
		</view>
		
		<!-- 购物车列表 -->
		<block v-else>
			<view class="cart-list">
				<view class="cart-item glass-card" v-for="(item, index) in cartList" :key="item.id">
					<!-- 选择框 -->
					<label class="checkbox">
						<checkbox 
							:checked="item.selected" 
							:disabled="!item.status || item.stock <= 0"
							@tap="toggleSelect(index)"
							color="#667eea"
						></checkbox>
					</label>
					
					<!-- 商品信息 -->
					<view class="goods-img">
						<image 
							:src="item.image || '/static/images/default-goods.png'" 
							mode="aspectFill"
							@error="onImageError(item)"
						></image>
					</view>
					<view class="goods-info">
						<text class="title">{{item.name}}</text>
						<text class="spec tag-style" v-if="item.specs && item.specs.length">
							{{item.specs.map(spec => spec.value).join('，')}}
						</text>
						<!-- 商品状态提示 -->
						<text class="status-tip" v-if="!item.status">商品已下架</text>
						<text class="status-tip" v-else-if="item.stock <= 0">库存不足</text>
						<view class="price-box">
							<text class="price price-style">{{item.price}}</text>
							<!-- 数量控制 -->
							<view class="num-box">
								<text 
									class="minus" 
									:class="{disabled: item.quantity <= 1 || !item.status || item.stock <= 0}"
									@click="updateNum(index, -1)"
								>-</text>
								<input 
									type="number" 
									v-model="item.quantity"
									:disabled="!item.status || item.stock <= 0"
									@blur="onNumBlur(index)"
								/>
								<text 
									class="plus"
									:class="{disabled: item.quantity >= item.stock || !item.status || item.stock <= 0}"
									@click="updateNum(index, 1)"
								>+</text>
							</view>
						</view>
					</view>
					
					<!-- 删除按钮 -->
					<text class="delete" @click="deleteItem(index)">🗑️</text>
				</view>
			</view>
			
			<!-- 底部结算栏 -->
			<view class="action-bar glass-card">
				<label class="all-select">
					<checkbox 
						:checked="isAllSelected" 
						@tap="toggleSelectAll"
						color="#667eea"
					></checkbox>
					<text>全选</text>
				</label>
				
				<view class="right">
					<view class="price-box">
						<text class="total-label">合计：</text>
						<text class="total-price">¥{{totalPrice}}</text>
					</view>
					<button 
						class="submit gradient-btn" 
						:disabled="!selectedCount"
						@click="submit"
					>
						结算({{selectedCount}})
					</button>
				</view>
			</view>
		</block>
	</PageLayout>
</template>

<script>
import { getCartList, updateCartQuantity, updateCartSelected, deleteCart, clearCart } from '@/api/cart'
import AuthUtils from '@/utils/auth'
import PageLayout from '@/components/layout/PageLayout.vue'
import themeMixin from '@/mixins/themeMixin.js'

export default {
	components: {
		PageLayout
	},
	
	mixins: [themeMixin],
	
	data() {
		return {
			cartList: [],
			fromDetail: false, // 是否从商品详情页进入
			goodsId: null // 记录商品ID
		}
	},
	onShow() {
		// 检查登录状态
		if (!AuthUtils.checkLoginStatus()) {
			uni.showToast({
				title: '请先登录',
				icon: 'none'
			})
			// 跳转到登录页
			setTimeout(() => {
				uni.reLaunch({
					url: '/pages/login/login'
				})
			}, 1500)
			return
		}

		// 获取来源信息
		const cartFrom = uni.getStorageSync('cartFrom')
		if (cartFrom) {
			this.fromDetail = cartFrom.from === 'detail'
			this.goodsId = cartFrom.goodsId
			// 清除来源信息
			uni.removeStorageSync('cartFrom')
		}

		// 检查堂食外卖参数
		this.checkDiningParams()

		// 加载购物车数据
		this.loadCartList()
	},
	computed: {
		// 是否全选
		isAllSelected() {
			return this.cartList.length && this.cartList.every(item => item.selected && item.status && item.stock > 0)
		},
		// 选中的商品数量
		selectedCount() {
			return this.cartList.filter(item => item.selected && item.status && item.stock > 0).length
		},
		// 总价
		totalPrice() {
			return this.cartList.reduce((total, item) => {
				if(item.selected && item.status && item.stock > 0) {
					return total + item.price * item.quantity
				}
				return total
			}, 0).toFixed(2)
		}
	},
	methods: {
		// 检查堂食外卖参数
		checkDiningParams() {
			const diningType = uni.getStorageSync('diningType')
			const selectedTable = uni.getStorageSync('selectedTable')
			
			if (diningType) {
				let message = ''
				if (diningType === 'dine_in') {
					message = '堂食模式'
					if (selectedTable) {
						message += ` - ${selectedTable.tableNo}`
					}
				} else if (diningType === 'takeout') {
					message = '外卖模式'
				}
				
				if (message) {
					uni.showToast({
						title: message,
						icon: 'none',
						duration: 2000
					})
				}
				
				// 清除存储的参数，避免重复提示
				uni.removeStorageSync('diningType')
				uni.removeStorageSync('selectedTable')
			}
		},
		
		// 返回上一页
		goBack() {
			if(this.fromDetail && this.goodsId) {
				// 保存购物车状态
				uni.setStorageSync('cartState', {
					scrollTop: 0,
					selectedIds: this.cartList.filter(item => item.selected).map(item => item.id)
				})
				// 跳转回商品详情页
				uni.redirectTo({
					url: `/pages/goods/detail?id=${this.goodsId}`
				})
			}
		},
		// 加载购物车列表
		async loadCartList() {
			try {
				const result = await getCartList()
				this.cartList = result.data.list || []
			} catch (error) {
				uni.showToast({
					title: error.message || '加载失败',
					icon: 'none'
				})
			}
		},
		// 跳转到商城
		toShop() {
			uni.switchTab({
				url: '/pages/index/index'
			})
		},
		// 切换选中状态
		async toggleSelect(index) {
			const item = this.cartList[index]
			// 检查商品状态
			if (!item.status || item.stock <= 0) {
				uni.showToast({
					title: !item.status ? '商品已下架' : '库存不足',
					icon: 'none'
				})
				return
			}
			
			try {
				await updateCartSelected({
					ids: [item.id],
					selected: !item.selected
				})
				// 重新加载列表以获取最新数据
				await this.loadCartList()
			} catch (error) {
				uni.showToast({
					title: error.message || '操作失败',
					icon: 'none'
				})
			}
		},
		// 全选/取消全选
		async toggleSelectAll() {
			const newStatus = !this.isAllSelected
			// 过滤出可选择的商品
			const validItems = this.cartList.filter(item => item.status && item.stock > 0)
			if (!validItems.length) {
				uni.showToast({
					title: '没有可选择的商品',
					icon: 'none'
				})
				return
			}
			
			try {
				await updateCartSelected({
					ids: validItems.map(item => item.id),
					selected: newStatus
				})
				// 重新加载列表以获取最新数据
				await this.loadCartList()
			} catch (error) {
				uni.showToast({
					title: error.message || '操作失败',
					icon: 'none'
				})
			}
		},
		// 更新商品数量
		async updateNum(index, delta) {
			const item = this.cartList[index]
			// 检查商品状态
			if (!item.status || item.stock <= 0) {
				uni.showToast({
					title: !item.status ? '商品已下架' : '库存不足',
					icon: 'none'
				})
				return
			}
			
			const newNum = item.quantity + delta
			// 检查数量范围
			if(newNum < 1) {
				uni.showToast({
					title: '商品数量不能小于1',
					icon: 'none'
				})
				return
			}
			
			if(newNum > item.stock) {
				uni.showToast({
					title: '超出库存数量',
					icon: 'none'
				})
				return
			}
			
			try {
				await updateCartQuantity({
					id: item.id,
					quantity: newNum
				})
				// 重新加载列表以获取最新数据
				await this.loadCartList()
			} catch (error) {
				uni.showToast({
					title: error.message || '更新失败',
					icon: 'none'
				})
			}
		},
		// 数量输入框失焦
		async onNumBlur(index) {
			const item = this.cartList[index]
			// 检查商品状态
			if (!item.status || item.stock <= 0) {
				item.quantity = 0
				return
			}
			
			let num = parseInt(item.quantity)
			// 检查数量是否合法
			if(isNaN(num) || num < 1) {
				num = 1
			} else if(num > item.stock) {
				num = item.stock
				uni.showToast({
					title: '已调整为最大库存数量',
					icon: 'none'
				})
			}
			
			try {
				await updateCartQuantity({
					id: item.id,
					quantity: num
				})
				// 重新加载列表以获取最新数据
				await this.loadCartList()
			} catch (error) {
				uni.showToast({
					title: error.message || '更新失败',
					icon: 'none'
				})
			}
		},
		// 删除商品
		deleteItem(index) {
			const item = this.cartList[index]
			uni.showModal({
				title: '提示',
				content: '确定要删除该商品吗？',
				success: async (res) => {
					if(res.confirm) {
						try {
							await deleteCart({
								ids: [item.id]
							})
							// 重新加载列表以获取最新数据
							await this.loadCartList()
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							})
						} catch (error) {
							uni.showToast({
								title: error.message || '删除失败',
								icon: 'none'
							})
						}
					}
				}
			})
		},
		// 提交订单
		submit() {
			const selectedItems = this.cartList.filter(item => item.selected && item.status && item.stock > 0)
			if(!selectedItems.length) {
				uni.showToast({
					title: '请选择要结算的商品',
					icon: 'none'
				})
				return
			}
			
			// 跳转到确认订单页面，使用 redirectTo 而不是 navigateTo
			uni.redirectTo({
				url: '/pages/order/confirm?type=cart'
			})
		},
		// 清空购物车
		clearCart() {
			uni.showModal({
				title: '提示',
				content: '确定要清空购物车吗？',
				success: async (res) => {
					if(res.confirm) {
						try {
							await clearCart()
							this.cartList = []
							uni.showToast({
								title: '清空成功',
								icon: 'success'
							})
						} catch (error) {
							uni.showToast({
								title: error.message || '清空失败',
								icon: 'none'
							})
						}
					}
				}
			})
		},
		onImageError(item) {
			// 图片加载失败时使用默认图片
			item.image = '/static/images/default-goods.png'
		}
	}
}
</script>

<style lang="scss">
.nav-bar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 20rpx;
	z-index: 100;
	
	.back-btn {
		width: 88rpx;
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		background: rgba(102, 126, 234, 0.1);
		transition: all 0.3s ease;
		
		&:active {
			transform: scale(0.95);
			background: rgba(102, 126, 234, 0.2);
		}
		
		text {
			font-size: 44rpx;
			color: #667eea;
			font-weight: bold;
		}
	}
}

.empty {
	padding-top: 200rpx;
	text-align: center;
	
	image {
		width: 300rpx;
		height: 300rpx;
		margin-bottom: 40rpx;
		opacity: 0.8;
	}
	
	text {
		font-size: 32rpx;
		color: rgba(255, 255, 255, 0.9);
		display: block;
		margin-bottom: 40rpx;
		font-weight: 500;
	}
	
	.to-shop {
		display: inline-block;
		margin-top: 40rpx;
		width: 240rpx;
		height: 80rpx;
		line-height: 80rpx;
		font-size: 28rpx;
		font-weight: 600;
	}
}

.cart-list {
	padding: 20rpx;
	
	.cart-item {
		position: relative;
		display: flex;
		align-items: center;
		padding: 24rpx;
		margin-bottom: 20rpx;
		
		.checkbox {
			margin-right: 20rpx;
		}
		
		.goods-img {
			width: 160rpx;
			height: 160rpx;
			margin-right: 20rpx;
			border-radius: 16rpx;
			overflow: hidden;
			box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
			
			image {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}
		
		.goods-info {
			flex: 1;
			overflow: hidden;
			
			.title {
				font-size: 28rpx;
				color: #333;
				font-weight: 600;
				line-height: 1.4;
				margin-bottom: 12rpx;
			}
			
			.spec {
				font-size: 24rpx;
				margin-bottom: 12rpx;
				display: inline-block;
			}
			
			.status-tip {
				font-size: 24rpx;
				color: #ff6b6b;
				margin-bottom: 12rpx;
				background: rgba(255, 107, 107, 0.1);
				padding: 6rpx 12rpx;
				border-radius: 12rpx;
				display: inline-block;
			}
			
			.price-box {
				display: flex;
				justify-content: space-between;
				align-items: center;
				
				.num-box {
					display: flex;
					align-items: center;
					background: rgba(102, 126, 234, 0.1);
					border-radius: 24rpx;
					overflow: hidden;
					
					text {
						width: 60rpx;
						height: 50rpx;
						line-height: 50rpx;
						text-align: center;
						font-size: 28rpx;
						font-weight: 600;
						color: #667eea;
						transition: all 0.3s ease;
						
						&:active {
							background: rgba(102, 126, 234, 0.2);
						}
						
						&.disabled {
							color: #ccc;
						}
					}
					
					input {
						width: 80rpx;
						height: 50rpx;
						line-height: 50rpx;
						text-align: center;
						font-size: 28rpx;
						font-weight: 600;
						color: #333;
						background: transparent;
						border: none;
						
						&[disabled] {
							color: #ccc;
						}
					}
				}
			}
		}
		
		.delete {
			position: absolute;
			top: 20rpx;
			right: 20rpx;
			font-size: 32rpx;
			padding: 12rpx;
			border-radius: 50%;
			background: rgba(255, 107, 107, 0.1);
			transition: all 0.3s ease;
			
			&:active {
				background: rgba(255, 107, 107, 0.2);
				transform: scale(0.95);
			}
		}
	}
}

.action-bar {
	position: fixed;
	left: 20rpx;
	right: 20rpx;
	bottom: 20rpx;
	height: 100rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 30rpx;
	border-radius: 50rpx;
	z-index: 100;
	
	.all-select {
		display: flex;
		align-items: center;
		
		text {
			font-size: 28rpx;
			color: #333;
			font-weight: 500;
			margin-left: 12rpx;
		}
	}
	
	.right {
		display: flex;
		align-items: center;
		
		.price-box {
			margin-right: 20rpx;
			display: flex;
			align-items: baseline;
			
			.total-label {
				font-size: 28rpx;
				color: #666;
				font-weight: 500;
				margin-right: 8rpx;
			}
			
			.total-price {
				font-size: 32rpx;
				color: #ff6b35;
				font-weight: 700;
			}
		}
		
		.submit {
			width: 200rpx;
			height: 80rpx;
			line-height: 80rpx;
			font-size: 28rpx;
			font-weight: 600;
			border-radius: 40rpx;
			
			&[disabled] {
				background: #ccc;
				box-shadow: none;
			}
		}
	}
}
</style> 