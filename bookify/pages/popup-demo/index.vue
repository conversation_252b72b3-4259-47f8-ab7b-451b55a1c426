<template>
  <view class="demo-container">
    <view class="demo-header">
      <text class="demo-title">通用弹窗演示</text>
      <text class="demo-subtitle">展示各种弹窗配置选项</text>
    </view>

    <view class="demo-section">
      <text class="section-title">基础弹窗</text>
      <view class="button-group">
        <button class="demo-button" @click="showBasicPopup">基础弹窗</button>
        <button class="demo-button" @click="showNoTitlePopup">无标题弹窗</button>
        <button class="demo-button" @click="showLongContentPopup">长内容弹窗</button>
      </view>
    </view>

    <view class="demo-section">
      <text class="section-title">标题样式</text>
      <view class="button-group">
        <button class="demo-button" @click="showColorTitlePopup">彩色标题</button>
        <button class="demo-button" @click="showGradientTitlePopup">渐变背景</button>
        <button class="demo-button" @click="showImageTitlePopup">背景图片</button>
      </view>
    </view>

    <view class="demo-section">
      <text class="section-title">按钮配置</text>
      <view class="button-group">
        <button class="demo-button" @click="showSingleButtonPopup">单个按钮</button>
        <button class="demo-button" @click="showMultiButtonPopup">多个按钮</button>
        <button class="demo-button" @click="showCustomButtonPopup">自定义按钮</button>
      </view>
    </view>

    <view class="demo-section">
      <text class="section-title">高级功能</text>
      <view class="button-group">
        <button class="demo-button" @click="showAutoClosePopup">自动关闭</button>
        <button class="demo-button" @click="showDelayPopup">延迟显示</button>
        <button class="demo-button" @click="showNoMaskClosePopup">禁止遮罩关闭</button>
      </view>
    </view>

    <view class="demo-section">
      <text class="section-title">队列管理</text>
      <view class="button-group">
        <button class="demo-button" @click="showMultiplePopups">连续弹窗</button>
        <button class="demo-button" @click="showPriorityPopups">优先级弹窗</button>
        <button class="demo-button" @click="clearAllPopups">清空队列</button>
      </view>
    </view>

    <view class="demo-section">
      <text class="section-title">实时状态</text>
      <view class="status-info">
        <text class="status-item">队列长度: {{ popupStore.queueLength }}</text>
        <text class="status-item">当前可见: {{ popupStore.isVisible ? '是' : '否' }}</text>
        <text class="status-item">正在处理: {{ popupStore.isProcessing ? '是' : '否' }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { usePopupStore } from '@/stores/popup'

export default {
  data() {
    return {}
  },
  computed: {
    popupStore() {
      return usePopupStore()
    }
  },
  methods: {
    // 基础弹窗
    showBasicPopup() {
      this.popupStore.addPopup({
        id: 'basic',
        title: '基础弹窗',
        content: '这是一个基础的弹窗示例，包含标题、内容和确定按钮。'
      })
    },

    showNoTitlePopup() {
      this.popupStore.addPopup({
        id: 'no-title',
        content: '这是一个没有标题的弹窗，只有内容和按钮。',
        buttons: [
          {
            text: '知道了',
            type: 'primary'
          }
        ]
      })
    },

    showLongContentPopup() {
      this.popupStore.addPopup({
        id: 'long-content',
        title: '长内容弹窗',
        content: '这是一个包含很长内容的弹窗示例。当内容过长时，弹窗会自动调整高度并支持滚动。您可以在这里放置大量的文字内容，比如用户协议、详细说明、使用帮助等。弹窗会保持良好的视觉效果和用户体验。'
      })
    },

    // 标题样式
    showColorTitlePopup() {
      this.popupStore.addPopup({
        id: 'color-title',
        title: '彩色标题',
        content: '这个弹窗的标题使用了自定义颜色和背景色。',
        titleStyle: {
          color: '#fff',
          background: '#007aff'
        }
      })
    },

    showGradientTitlePopup() {
      this.popupStore.addPopup({
        id: 'gradient-title',
        title: '渐变背景标题',
        content: '这个弹窗的标题使用了渐变背景色。',
        titleStyle: {
          color: '#fff',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }
      })
    },

    showImageTitlePopup() {
      this.popupStore.addPopup({
        id: 'image-title',
        title: '背景图片标题',
        content: '这个弹窗的标题使用了背景图片。',
        titleStyle: {
          color: '#fff',
          backgroundImage: 'https://picsum.photos/400/100'
        }
      })
    },

    // 按钮配置
    showSingleButtonPopup() {
      this.popupStore.addPopup({
        id: 'single-button',
        title: '单个按钮',
        content: '这个弹窗只有一个按钮。',
        buttons: [
          {
            text: '确定',
            type: 'primary',
            onClick: () => {
              uni.showToast({ title: '点击了确定', icon: 'none' })
              this.popupStore.closePopup()
            }
          }
        ]
      })
    },

    showMultiButtonPopup() {
      this.popupStore.addPopup({
        id: 'multi-button',
        title: '多个按钮',
        content: '这个弹窗有多个按钮，您可以选择不同的操作。',
        buttons: [
          {
            text: '取消',
            type: 'default',
            onClick: () => {
              uni.showToast({ title: '点击了取消', icon: 'none' })
              this.popupStore.closePopup()
            }
          },
          {
            text: '确定',
            type: 'primary',
            onClick: () => {
              uni.showToast({ title: '点击了确定', icon: 'none' })
              this.popupStore.closePopup()
            }
          }
        ]
      })
    },

    showCustomButtonPopup() {
      this.popupStore.addPopup({
        id: 'custom-button',
        title: '自定义按钮',
        content: '这个弹窗的按钮使用了自定义样式。',
        buttons: [
          {
            text: '删除',
            type: 'danger',
            onClick: () => {
              uni.showToast({ title: '点击了删除', icon: 'none' })
              this.popupStore.closePopup()
            }
          },
          {
            text: '保存',
            type: 'success',
            onClick: () => {
              uni.showToast({ title: '点击了保存', icon: 'none' })
              this.popupStore.closePopup()
            }
          },
          {
            text: '警告',
            type: 'warning',
            style: {
              fontSize: '14px',
              fontWeight: 'bold'
            },
            onClick: () => {
              uni.showToast({ title: '点击了警告', icon: 'none' })
              this.popupStore.closePopup()
            }
          }
        ]
      })
    },

    // 高级功能
    showAutoClosePopup() {
      this.popupStore.addPopup({
        id: 'auto-close',
        title: '自动关闭',
        content: '这个弹窗将在5秒后自动关闭。',
        autoClose: 5000,
        buttons: [
          {
            text: '立即关闭',
            type: 'primary'
          }
        ]
      })
    },

    showDelayPopup() {
      uni.showToast({ title: '弹窗将在2秒后显示', icon: 'none' })
      this.popupStore.addPopup({
        id: 'delay',
        title: '延迟显示',
        content: '这个弹窗延迟了2秒才显示。',
        delay: 2000
      })
    },

    showNoMaskClosePopup() {
      this.popupStore.addPopup({
        id: 'no-mask-close',
        title: '禁止遮罩关闭',
        content: '这个弹窗不能通过点击遮罩关闭，只能点击按钮关闭。',
        maskClosable: false,
        buttons: [
          {
            text: '必须点我才能关闭',
            type: 'primary'
          }
        ]
      })
    },

    // 队列管理
    showMultiplePopups() {
      for (let i = 1; i <= 3; i++) {
        this.popupStore.addPopup({
          id: `multiple-${i}`,
          title: `第${i}个弹窗`,
          content: `这是第${i}个弹窗，会按顺序显示。`,
          buttons: [
            {
              text: '下一个',
              type: 'primary'
            }
          ]
        })
      }
    },

    showPriorityPopups() {
      // 添加低优先级弹窗
      this.popupStore.addPopup({
        id: 'low-priority',
        title: '低优先级弹窗',
        content: '这是一个低优先级的弹窗。',
        priority: 1
      })

      // 添加高优先级弹窗
      setTimeout(() => {
        this.popupStore.addPopup({
          id: 'high-priority',
          title: '高优先级弹窗',
          content: '这是一个高优先级的弹窗，会优先显示。',
          priority: 10,
          titleStyle: {
            color: '#fff',
            background: '#dc3545'
          }
        })
      }, 1000)
    },

    clearAllPopups() {
      this.popupStore.clearQueue()
      this.popupStore.closePopup()
      uni.showToast({ title: '已清空所有弹窗', icon: 'none' })
    }
  }
}
</script>

<style scoped>
.demo-container {
  padding: 20px;
  background: #f8f8f8;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-title {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.demo-subtitle {
  display: block;
  font-size: 14px;
  color: #666;
}

.demo-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #007aff;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.demo-button {
  flex: 1;
  min-width: 100px;
  padding: 12px 16px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.demo-button:active {
  background: #0056cc;
}

.status-info {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 15px;
}

.status-item {
  display: block;
  font-size: 14px;
  color: #0369a1;
  margin-bottom: 5px;
}

.status-item:last-child {
  margin-bottom: 0;
}
</style> 