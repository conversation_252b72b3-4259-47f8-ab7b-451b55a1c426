<template>
	<PageLayout :show-nav-bar="true" :nav-transparent="false" :show-center="true" nav-title="关于我们">
		<view class="about-container">
			<!-- 应用信息卡片 -->
			<view class="app-info-card">
				<view class="app-logo">
					<image src="/static/images/logo.png" mode="aspectFit" class="logo-image"></image>
				</view>
				<view class="app-details">
					<text class="app-name">WN商城</text>
					<text class="app-slogan">传承非遗文化，守护匠心精神</text>
					<view class="version-info">
						<text class="version-label">当前版本</text>
						<text class="version-number">v1.0.0</text>
					</view>
				</view>
			</view>

			<!-- 功能介绍 -->
			<view class="feature-section">
				<view class="section-title">
					<text class="title-text">产品特色</text>
				</view>
				<view class="feature-list">
					<view class="feature-item">
						<view class="feature-icon">🏺</view>
						<view class="feature-content">
							<text class="feature-title">非遗精品</text>
							<text class="feature-desc">精选全国各地非遗手工艺品</text>
						</view>
					</view>
					<view class="feature-item">
						<view class="feature-icon">🎨</view>
						<view class="feature-content">
							<text class="feature-title">匠心工艺</text>
							<text class="feature-desc">传承千年的手工制作技艺</text>
						</view>
					</view>
					<view class="feature-item">
						<view class="feature-icon">🌟</view>
						<view class="feature-content">
							<text class="feature-title">品质保证</text>
							<text class="feature-desc">严格品控，确保每件商品品质</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 菜单列表 -->
			<view class="menu-section">
				<!-- <view class="menu-item" @click="onPrivacy">
					<view class="menu-left">
						<text class="menu-icon">📄</text>
						<text class="menu-text">隐私政策</text>
					</view>
					<text class="arrow">›</text>
				</view>
				<view class="menu-item" @click="onService">
					<view class="menu-left">
						<text class="menu-icon">📋</text>
						<text class="menu-text">服务协议</text>
					</view>
					<text class="arrow">›</text>
				</view> -->
				<view class="menu-item" @click="onFeedback">
					<view class="menu-left">
						<text class="menu-icon">💬</text>
						<text class="menu-text">意见反馈</text>
					</view>
					<text class="arrow">›</text>
				</view>
				<view class="menu-item" @click="onContact">
					<view class="menu-left">
						<text class="menu-icon">📞</text>
						<text class="menu-text">联系我们</text>
					</view>
					<text class="arrow">›</text>
				</view>
			</view>

			<!-- 版权信息 -->
			<view class="copyright-section">
				<text class="copyright-text">Copyright © 2023-2024</text>
				<text class="copyright-text">WN商城 版权所有</text>
				<text class="copyright-desc">致力于传承和保护中华非物质文化遗产</text>
			</view>
		</view>
	</PageLayout>
</template>

<script>
import PageLayout from '@/components/layout/PageLayout.vue'

export default {
	components: {
		PageLayout
	},
	
	methods: {
		onPrivacy() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			})
		},
		
		onService() {
			uni.showToast({
				title: '功能开发中',
				icon: 'none'
			})
		},
		
		onFeedback() {
			uni.showModal({
				title: '意见反馈',
				content: '如有任何建议或问题，请联系客服',
				showCancel: false,
				confirmText: '知道了'
			})
		},
		
		onContact() {
			uni.showModal({
				title: '联系我们',
				content: '客服电话：400-123-4567\n工作时间：9:00-18:00',
				showCancel: false,
				confirmText: '知道了'
			})
		}
	}
}
</script>

<style lang="scss">
.about-container {
	padding: 20rpx;
	background: #f8f9fa;
	min-height: 100vh;
}

// 应用信息卡片
.app-info-card {
	background: #fff;
	border-radius: 16rpx;
	padding: 40rpx 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;
	
	.app-logo {
		margin-right: 30rpx;
		
		.logo-image {
			width: 120rpx;
			height: 120rpx;
			border-radius: 20rpx;
			box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
		}
	}
	
	.app-details {
		flex: 1;
		
		.app-name {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
			display: block;
			margin-bottom: 8rpx;
		}
		
		.app-slogan {
			font-size: 26rpx;
			color: #666;
			display: block;
			margin-bottom: 16rpx;
		}
		
		.version-info {
			display: flex;
			align-items: center;
			
			.version-label {
				font-size: 24rpx;
				color: #999;
				margin-right: 16rpx;
			}
			
			.version-number {
				font-size: 24rpx;
				color: #007aff;
				background: #f0f8ff;
				padding: 4rpx 12rpx;
				border-radius: 12rpx;
			}
		}
	}
}

// 功能介绍
.feature-section {
	background: #fff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
	overflow: hidden;
	
	.section-title {
		padding: 30rpx;
		border-bottom: 1rpx solid #f5f5f5;
		
		.title-text {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}
	}
	
	.feature-list {
		padding: 20rpx 0;
		
		.feature-item {
			display: flex;
			align-items: center;
			padding: 20rpx 30rpx;
			
			.feature-icon {
				font-size: 48rpx;
				margin-right: 24rpx;
				width: 80rpx;
				text-align: center;
			}
			
			.feature-content {
				flex: 1;
				
				.feature-title {
					font-size: 28rpx;
					font-weight: 600;
					color: #333;
					display: block;
					margin-bottom: 8rpx;
				}
				
				.feature-desc {
					font-size: 24rpx;
					color: #666;
					line-height: 1.4;
				}
			}
		}
	}
}

// 菜单列表
.menu-section {
	background: #fff;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
	overflow: hidden;
	
	.menu-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 32rpx 30rpx;
		border-bottom: 1rpx solid #f5f5f5;
		background: #fff;
		transition: background-color 0.2s ease;
		
		&:last-child {
			border-bottom: none;
		}
		
		&:active {
			background: #f8f9fa;
		}
		
		.menu-left {
			display: flex;
			align-items: center;
			
			.menu-icon {
				font-size: 32rpx;
				margin-right: 20rpx;
				width: 40rpx;
				text-align: center;
			}
			
			.menu-text {
				font-size: 28rpx;
				color: #333;
				font-weight: 500;
			}
		}
		
		.arrow {
			font-size: 32rpx;
			color: #c8c9cc;
			font-weight: normal;
		}
	}
}

// 版权信息
.copyright-section {
	background: #fff;
	border-radius: 16rpx;
	padding: 40rpx 30rpx;
	text-align: center;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
	
	.copyright-text {
		font-size: 24rpx;
		color: #999;
		line-height: 1.6;
		display: block;
		margin-bottom: 8rpx;
	}
	
	.copyright-desc {
		font-size: 22rpx;
		color: #bbb;
		line-height: 1.5;
		margin-top: 16rpx;
		display: block;
	}
}
</style> 