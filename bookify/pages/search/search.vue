<template>
	<view class="search">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="circle circle-1"></view>
			<view class="circle circle-2"></view>
			<view class="circle circle-3"></view>
		</view>
		
		<!-- 搜索栏 -->
		<view class="search-box">
			<view class="input-box">
				<text class="icon">🔍</text>
				<input 
					type="text"
					v-model="keyword"
					placeholder="搜索商品"
					placeholder-class="placeholder"
					confirm-type="search"
					@confirm="onSearch"
					focus
				/>
			</view>
			<text class="cancel" @click="back">取消</text>
		</view>
		
		<!-- 搜索历史 -->
		<view class="history" v-if="historyList.length && !keyword">
			<view class="header">
				<text class="title">搜索历史</text>
				<text class="clear" @click="clearHistory">清空</text>
			</view>
			<view class="list">
				<view 
					class="item" 
					v-for="(item, index) in historyList" 
					:key="index"
					@click="onHistoryClick(item)"
				>
					{{item}}
				</view>
			</view>
		</view>
		
		<!-- 搜索结果 -->
		<view class="result" v-if="keyword">
			<view class="goods-list">
				<view 
					class="goods-item" 
					v-for="item in searchList" 
					:key="item.id"
					@click="toDetail(item.id)"
				>
					<image :src="item.image" mode="aspectFill"></image>
					<view class="info">
						<text class="title">{{item.title}}</text>
						<text class="price">¥{{item.price}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { generateGoods } from '@/utils/mock'

export default {
	data() {
		return {
			keyword: '',
			historyList: [],
			searchList: []
		}
	},
	onLoad() {
		// 获取搜索历史
		const history = uni.getStorageSync('searchHistory') || []
		this.historyList = history
	},
	methods: {
		// 返回
		back() {
			uni.navigateBack()
		},
		// 搜索
		onSearch() {
			if(!this.keyword.trim()) return
			
			// 保存搜索历史
			let history = uni.getStorageSync('searchHistory') || []
			if(!history.includes(this.keyword)) {
				history.unshift(this.keyword)
				if(history.length > 10) {
					history = history.slice(0, 10)
				}
				uni.setStorageSync('searchHistory', history)
				this.historyList = history
			}
			
			// 模拟搜索结果
			this.searchList = generateGoods(10)
		},
		// 点击历史记录
		onHistoryClick(keyword) {
			this.keyword = keyword
			this.onSearch()
		},
		// 清空历史
		clearHistory() {
			uni.showModal({
				title: '提示',
				content: '确定要清空搜索历史吗？',
				success: (res) => {
					if(res.confirm) {
						uni.removeStorageSync('searchHistory')
						this.historyList = []
					}
				}
			})
		},
		// 跳转商品详情
		toDetail(id) {
			uni.navigateTo({
				url: `/pages/goods/detail?id=${id}`
			})
		}
	}
}
</script>

<style lang="scss">
.search {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	position: relative;
	overflow: hidden;
}

// 背景装饰
.bg-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 1;
	
	.circle {
		position: absolute;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.1);
		
		&.circle-1 {
			width: 300rpx;
			height: 300rpx;
			top: -150rpx;
			right: -150rpx;
			animation: float 6s ease-in-out infinite;
		}
		
		&.circle-2 {
			width: 200rpx;
			height: 200rpx;
			bottom: 200rpx;
			left: -100rpx;
			animation: float 8s ease-in-out infinite reverse;
		}
		
		&.circle-3 {
			width: 150rpx;
			height: 150rpx;
			top: 300rpx;
			left: 50rpx;
			animation: float 10s ease-in-out infinite;
		}
	}
}

@keyframes float {
	0%, 100% { transform: translateY(0px); }
	50% { transform: translateY(-20px); }
}

.search-box {
	position: relative;
	z-index: 2;
	display: flex;
	align-items: center;
	padding: 40rpx 30rpx 20rpx;
	
	.input-box {
		flex: 1;
		height: 80rpx;
		background: rgba(255, 255, 255, 0.9);
		backdrop-filter: blur(20px);
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
		border: 1rpx solid rgba(255, 255, 255, 0.2);
		
		.icon {
			margin-right: 16rpx;
			font-size: 32rpx;
			color: #667eea;
		}
		
		input {
			flex: 1;
			height: 80rpx;
			font-size: 28rpx;
			color: #333;
			font-weight: 500;
		}
	}
	
	.cancel {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.9);
		margin-left: 20rpx;
		padding: 12rpx 20rpx;
		border-radius: 20rpx;
		background: rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(10px);
		font-weight: 500;
		transition: all 0.3s ease;
		
		&:active {
			background: rgba(255, 255, 255, 0.3);
			transform: scale(0.95);
		}
	}
}

.history {
	position: relative;
	z-index: 2;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	padding: 30rpx;
	margin: 20rpx;
	border-radius: 24rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
		
		.title {
			font-size: 28rpx;
			color: #333;
			font-weight: 600;
		}
		
		.clear {
			font-size: 26rpx;
			color: #667eea;
			font-weight: 500;
			padding: 8rpx 16rpx;
			border-radius: 16rpx;
			background: rgba(102, 126, 234, 0.1);
			transition: all 0.3s ease;
			
			&:active {
				background: rgba(102, 126, 234, 0.2);
				transform: scale(0.95);
			}
		}
	}
	
	.list {
		display: flex;
		flex-wrap: wrap;
		
		.item {
			padding: 12rpx 24rpx;
			background: rgba(102, 126, 234, 0.1);
			backdrop-filter: blur(10px);
			border-radius: 24rpx;
			font-size: 26rpx;
			color: #667eea;
			font-weight: 500;
			margin: 0 16rpx 16rpx 0;
			border: 1rpx solid rgba(102, 126, 234, 0.2);
			transition: all 0.3s ease;
			
			&:active {
				background: rgba(102, 126, 234, 0.2);
				transform: scale(0.95);
			}
		}
	}
}

.result {
	position: relative;
	z-index: 2;
	
	.goods-list {
		padding: 0 20rpx 20rpx;
		
		.goods-item {
			display: flex;
			background: rgba(255, 255, 255, 0.95);
			backdrop-filter: blur(20px);
			padding: 24rpx;
			margin-bottom: 20rpx;
			border-radius: 24rpx;
			box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
			border: 1rpx solid rgba(255, 255, 255, 0.2);
			transition: all 0.3s ease;
			
			&:active {
				transform: scale(0.98);
				box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
			}
			
			image {
				width: 160rpx;
				height: 160rpx;
				border-radius: 16rpx;
				margin-right: 20rpx;
				box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
			}
			
			.info {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				
				.title {
					font-size: 28rpx;
					color: #333;
					font-weight: 600;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					line-height: 1.4;
				}
				
				.price {
					display: block;
					font-size: 32rpx;
					color: #ff6b6b;
					font-weight: 700;
					margin-top: 16rpx;
				}
			}
		}
	}
}

.placeholder {
	color: #999;
}
</style> 