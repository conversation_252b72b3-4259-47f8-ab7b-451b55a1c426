<template>
	<view class="container">
		<!-- 橙色渐变背景头部 -->
		<view class="header-section">
			<view class="header-content">
				<text class="page-title">推广海报</text>
				<text class="page-subtitle">生成专属推广海报，分享获得收益</text>
			</view>
		</view>
		
		<!-- 海报预览 -->
		<view class="poster-section">
			<view class="poster-header">
				<text class="poster-title">海报预览</text>
				<view class="poster-actions">
					<button class="action-btn" @click="generatePoster">
						<text class="action-icon">🔄</text>
						<text class="action-text">重新生成</text>
					</button>
				</view>
			</view>
			
			<view class="poster-preview">
				<canvas 
					class="poster-canvas" 
					canvas-id="posterCanvas"
					:style="{ width: canvasWidth + 'rpx', height: canvasHeight + 'rpx' }"
				></canvas>
				
				<!-- 加载状态 -->
				<view class="loading-overlay" v-if="generating">
					<view class="loading-spinner"></view>
					<text class="loading-text">生成中...</text>
				</view>
			</view>
		</view>
		
		<!-- 海报模板选择 -->
		<view class="template-section">
			<view class="template-header">
				<text class="template-title">选择模板</text>
			</view>
			
			<view class="template-list">
				<view 
					class="template-item" 
					:class="{ active: currentTemplate === template.id }"
					v-for="template in templates" 
					:key="template.id"
					@click="selectTemplate(template.id)"
				>
					<image class="template-image" :src="template.preview" mode="aspectFit"></image>
					<text class="template-name">{{ template.name }}</text>
				</view>
			</view>
		</view>
		
		<!-- 推广信息 -->
		<view class="info-section">
			<view class="info-header">
				<text class="info-title">推广信息</text>
			</view>
			
			<view class="info-content">
				<view class="info-item">
					<text class="info-label">推广码</text>
					<view class="info-value-wrapper">
						<text class="info-value">{{ userInfo.shareCode || '-' }}</text>
						<button class="copy-btn" @click="copyShareCode">复制</button>
					</view>
				</view>
				
				<view class="info-item">
					<text class="info-label">推广链接</text>
					<view class="info-value-wrapper">
						<text class="info-value">{{ shareUrl }}</text>
						<button class="copy-btn" @click="copyShareUrl">复制</button>
					</view>
				</view>
				
				<view class="info-item">
					<text class="info-label">二维码</text>
					<view class="qrcode-wrapper">
						<canvas 
							class="qrcode-canvas" 
							canvas-id="qrcodeCanvas"
							:style="{ width: '200rpx', height: '200rpx' }"
						></canvas>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 操作按钮 -->
		<view class="action-section">
			<button class="save-btn" @click="savePoster" :disabled="!posterGenerated">
				<text class="btn-icon">💾</text>
				<text class="btn-text">保存海报</text>
			</button>
			
			<button class="share-btn" @click="sharePoster" :disabled="!posterGenerated">
				<text class="btn-icon">📤</text>
				<text class="btn-text">分享海报</text>
			</button>
		</view>
	</view>
</template>

<script>
import { getUserInfo } from '@/api/user'

export default {
	data() {
		return {
			userInfo: {},
			currentTemplate: 1,
			templates: [
				{
					id: 1,
					name: '经典模板',
					preview: '/static/images/poster/template1.jpg'
				},
				{
					id: 2,
					name: '简约模板',
					preview: '/static/images/poster/template2.jpg'
				},
				{
					id: 3,
					name: '时尚模板',
					preview: '/static/images/poster/template3.jpg'
				}
			],
			canvasWidth: 600,
			canvasHeight: 900,
			generating: false,
			posterGenerated: false,
			shareUrl: ''
		}
	},
	
	onLoad() {
		this.loadUserInfo()
	},
	
	methods: {
		// 加载用户信息
		async loadUserInfo() {
			try {
				const { code, data } = await getUserInfo()
				if (code === 200 && data) {
					this.userInfo = data
					// 用户信息加载完成后再生成海报和二维码
					this.generateShareUrl()
					this.generatePoster()
					this.generateQRCode()
				}
			} catch (error) {
				console.error('loadUserInfo error:', error)
				// 即使加载失败也生成默认海报
				this.generateShareUrl()
				this.generatePoster()
				this.generateQRCode()
			}
		},
		
		// 生成分享链接
		generateShareUrl() {
			const baseUrl = 'https://your-domain.com'
			const shareCode = this.userInfo.shareCode || 'default'
			this.shareUrl = `${baseUrl}?share=${shareCode}`
		},
		
		// 选择模板
		selectTemplate(templateId) {
			this.currentTemplate = templateId
			this.generatePoster()
		},
		
		// 生成海报
		async generatePoster() {
			this.generating = true
			this.posterGenerated = false
			
			try {
				const ctx = uni.createCanvasContext('posterCanvas', this)
				
				// 清除画布
				ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)
				
				// 设置背景
				ctx.setFillStyle('#ffffff')
				ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)
				
				// 绘制背景渐变
				const gradient = ctx.createLinearGradient(0, 0, 0, 300)
				gradient.addColorStop(0, '#FF6B35')
				gradient.addColorStop(1, '#F7931E')
				ctx.setFillStyle(gradient)
				ctx.fillRect(0, 0, this.canvasWidth, 300)
				
				// 绘制装饰圆形
				ctx.setFillStyle('rgba(255, 255, 255, 0.1)')
				ctx.beginPath()
				ctx.arc(this.canvasWidth - 80, 80, 60, 0, 2 * Math.PI)
				ctx.fill()
				
				ctx.setFillStyle('rgba(255, 255, 255, 0.05)')
				ctx.beginPath()
				ctx.arc(80, 200, 40, 0, 2 * Math.PI)
				ctx.fill()
				
				// 绘制主标题
				ctx.setFillStyle('#ffffff')
				ctx.setFontSize(42)
				ctx.setTextAlign('center')
				ctx.setTextBaseline('middle')
				ctx.fillText('邀请您', this.canvasWidth / 2, 100)
				
				// 绘制副标题
				ctx.setFillStyle('#ffffff')
				ctx.setFontSize(32)
				ctx.fillText('注册即享优惠，', this.canvasWidth / 2, 150)
				ctx.fillText('推广还有佣金', this.canvasWidth / 2, 190)
				
				// 绘制分割线
				ctx.setStrokeStyle('rgba(255, 255, 255, 0.3)')
				ctx.setLineWidth(2)
				ctx.beginPath()
				ctx.moveTo(this.canvasWidth / 2 - 100, 250)
				ctx.lineTo(this.canvasWidth / 2 + 100, 250)
				ctx.stroke()
				
				// 绘制推荐人信息
				ctx.setFillStyle('#333333')
				ctx.setFontSize(32)
				ctx.setTextAlign('center')
				const nickname = this.userInfo.nickname || '微信用户'
				ctx.fillText(`推荐人：${nickname}`, this.canvasWidth / 2, 380)
				
				// 绘制推广码背景
				ctx.setFillStyle('#FF6B35')
				ctx.fillRect(this.canvasWidth / 2 - 150, 420, 300, 60)
				
				// 绘制推广码
				ctx.setFillStyle('#ffffff')
				ctx.setFontSize(36)
				ctx.setTextAlign('center')
				const shareCode = this.userInfo.shareCode || 'SHARE001'
				ctx.fillText(`推广码：${shareCode}`, this.canvasWidth / 2, 455)
				
				// 绘制二维码背景
				ctx.setFillStyle('#f8f9fa')
				ctx.fillRect(this.canvasWidth / 2 - 120, 520, 240, 240)
				
				// 绘制二维码边框
				ctx.setStrokeStyle('#e9ecef')
				ctx.setLineWidth(2)
				ctx.strokeRect(this.canvasWidth / 2 - 120, 520, 240, 240)
				
				// 绘制二维码占位图案
				ctx.setFillStyle('#000000')
				// 左上角定位点
				ctx.fillRect(this.canvasWidth / 2 - 100, 540, 60, 60)
				ctx.setFillStyle('#ffffff')
				ctx.fillRect(this.canvasWidth / 2 - 85, 555, 30, 30)
				ctx.setFillStyle('#000000')
				ctx.fillRect(this.canvasWidth / 2 - 75, 565, 10, 10)
				
				// 右上角定位点
				ctx.setFillStyle('#000000')
				ctx.fillRect(this.canvasWidth / 2 + 40, 540, 60, 60)
				ctx.setFillStyle('#ffffff')
				ctx.fillRect(this.canvasWidth / 2 + 55, 555, 30, 30)
				ctx.setFillStyle('#000000')
				ctx.fillRect(this.canvasWidth / 2 + 65, 565, 10, 10)
				
				// 左下角定位点
				ctx.setFillStyle('#000000')
				ctx.fillRect(this.canvasWidth / 2 - 100, 680, 60, 60)
				ctx.setFillStyle('#ffffff')
				ctx.fillRect(this.canvasWidth / 2 - 85, 695, 30, 30)
				ctx.setFillStyle('#000000')
				ctx.fillRect(this.canvasWidth / 2 - 75, 705, 10, 10)
				
				// 绘制一些随机点阵
				ctx.setFillStyle('#000000')
				for (let i = 0; i < 20; i++) {
					const x = this.canvasWidth / 2 - 60 + Math.random() * 120
					const y = 580 + Math.random() * 80
					ctx.fillRect(x, y, 8, 8)
				}
				
				// 绘制扫码提示
				ctx.setFillStyle('#666666')
				ctx.setFontSize(24)
				ctx.setTextAlign('center')
				ctx.fillText('长按识别二维码', this.canvasWidth / 2, 800)
				
				// 绘制底部信息
				ctx.setFillStyle('#999999')
				ctx.setFontSize(20)
				ctx.fillText('立即注册享受优惠', this.canvasWidth / 2, 830)
				
				ctx.draw(false, () => {
					this.generating = false
					this.posterGenerated = true
				})
			} catch (error) {
				console.error('generatePoster error:', error)
				this.generating = false
				uni.showToast({
					title: '生成海报失败',
					icon: 'none'
				})
			}
		},
		
		// 生成二维码
		generateQRCode() {
			try {
				const ctx = uni.createCanvasContext('qrcodeCanvas', this)
				
				// 简单的二维码占位
				ctx.setFillStyle('#000000')
				ctx.fillRect(0, 0, 200, 200)
				
				ctx.setFillStyle('#ffffff')
				ctx.fillRect(20, 20, 160, 160)
				
				// 绘制简单的二维码图案
				for (let i = 0; i < 8; i++) {
					for (let j = 0; j < 8; j++) {
						if ((i + j) % 2 === 0) {
							ctx.setFillStyle('#000000')
							ctx.fillRect(30 + i * 20, 30 + j * 20, 15, 15)
						}
					}
				}
				
				ctx.draw()
			} catch (error) {
				console.error('generateQRCode error:', error)
			}
		},
		
		// 复制推广码
		copyShareCode() {
			uni.setClipboardData({
				data: this.userInfo.shareCode || '',
				success: () => {
					uni.showToast({
						title: '推广码已复制',
						icon: 'success'
					})
				}
			})
		},
		
		// 复制分享链接
		copyShareUrl() {
			uni.setClipboardData({
				data: this.shareUrl,
				success: () => {
					uni.showToast({
						title: '链接已复制',
						icon: 'success'
					})
				}
			})
		},
		
		// 保存海报
		savePoster() {
			if (!this.posterGenerated) {
				uni.showToast({
					title: '海报生成中，请稍候',
					icon: 'none'
				})
				return
			}
			
			uni.canvasToTempFilePath({
				canvasId: 'posterCanvas',
				success: (res) => {
					uni.saveImageToPhotosAlbum({
						filePath: res.tempFilePath,
						success: () => {
							uni.showToast({
								title: '保存成功',
								icon: 'success'
							})
						},
						fail: () => {
							uni.showToast({
								title: '保存失败',
								icon: 'none'
							})
						}
					})
				},
				fail: () => {
					uni.showToast({
						title: '生成图片失败',
						icon: 'none'
					})
				}
			}, this)
		},
		
		// 分享海报
		sharePoster() {
			if (!this.posterGenerated) {
				uni.showToast({
					title: '海报生成中，请稍候',
					icon: 'none'
				})
				return
			}
			
			uni.canvasToTempFilePath({
				canvasId: 'posterCanvas',
				success: (res) => {
					uni.share({
						provider: 'weixin',
						scene: 'WXSceneSession',
						type: 2,
						imageUrl: res.tempFilePath,
						success: () => {
							uni.showToast({
								title: '分享成功',
								icon: 'success'
							})
						},
						fail: () => {
							uni.showToast({
								title: '分享失败',
								icon: 'none'
							})
						}
					})
				},
				fail: () => {
					uni.showToast({
						title: '生成图片失败',
						icon: 'none'
					})
				}
			}, this)
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background: #f8f9fa;
}

// 橙色渐变头部区域
.header-section {
	background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
	padding: 200rpx 30rpx 60rpx;
	position: relative;
	overflow: hidden;

	// 背景装饰效果
	&::before {
		content: '';
		position: absolute;
		top: -100rpx;
		right: -100rpx;
		width: 400rpx;
		height: 400rpx;
		background: rgba(255, 255, 255, 0.1);
		border-radius: 50%;
	}
	
	&::after {
		content: '';
		position: absolute;
		bottom: -80rpx;
		left: -80rpx;
		width: 300rpx;
		height: 300rpx;
		background: rgba(255, 255, 255, 0.05);
		border-radius: 50%;
	}
	
	.header-content {
		position: relative;
		z-index: 2;
		text-align: center;
		
		.page-title {
			display: block;
			font-size: 40rpx;
			font-weight: 700;
			color: #fff;
			margin-bottom: 12rpx;
			text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
		}
		
		.page-subtitle {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			font-weight: 500;
		}
	}
}

// 海报预览区域
.poster-section {
	margin: 30rpx;
	background: #fff;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	overflow: hidden;
	
	.poster-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f5f5f5;
		
		.poster-title {
			font-size: 32rpx;
			font-weight: 700;
			color: #333;
		}
		
		.poster-actions {
			.action-btn {
				display: flex;
				align-items: center;
				background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(247, 147, 30, 0.1) 100%);
				color: #FF6B35;
				border: 1rpx solid rgba(255, 107, 53, 0.2);
				border-radius: 16rpx;
				padding: 12rpx 20rpx;
				font-size: 24rpx;
				font-weight: 600;
				
				.action-icon {
					margin-right: 8rpx;
					font-size: 26rpx;
				}
			}
		}
	}
	
	.poster-preview {
		padding: 30rpx;
		display: flex;
		justify-content: center;
		position: relative;
		background: #f8f9fa;
		
		.poster-canvas {
			border-radius: 16rpx;
			box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
			background: #fff;
			max-width: 100%;
			height: auto;
		}
		
		.loading-overlay {
			position: absolute;
			top: 30rpx;
			left: 30rpx;
			right: 30rpx;
			bottom: 30rpx;
			background: rgba(255, 255, 255, 0.9);
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			border-radius: 16rpx;
			
			.loading-spinner {
				width: 60rpx;
				height: 60rpx;
				border: 4rpx solid #f3f3f3;
				border-top: 4rpx solid #FF6B35;
				border-radius: 50%;
				animation: spin 1s linear infinite;
				margin-bottom: 20rpx;
			}
			
			.loading-text {
				font-size: 26rpx;
				color: #666;
			}
		}
	}
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

// 模板选择区域
.template-section {
	margin: 0 30rpx 30rpx;
	background: #fff;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	overflow: hidden;
	
	.template-header {
		padding: 30rpx;
		border-bottom: 1rpx solid #f5f5f5;
		
		.template-title {
			font-size: 32rpx;
			font-weight: 700;
			color: #333;
		}
	}
	
	.template-list {
		padding: 30rpx;
		display: flex;
		gap: 20rpx;
		
		.template-item {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 20rpx;
			border-radius: 16rpx;
			border: 2rpx solid transparent;
			transition: all 0.3s ease;
			
			&.active {
				border-color: #FF6B35;
				background: rgba(255, 107, 53, 0.05);
			}
			
			.template-image {
				width: 120rpx;
				height: 160rpx;
				border-radius: 12rpx;
				margin-bottom: 12rpx;
				box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
			}
			
			.template-name {
				font-size: 24rpx;
				color: #333;
				font-weight: 600;
			}
		}
	}
}

// 推广信息区域
.info-section {
	margin: 0 30rpx 30rpx;
	background: #fff;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	overflow: hidden;
	
	.info-header {
		padding: 30rpx;
		border-bottom: 1rpx solid #f5f5f5;
		
		.info-title {
			font-size: 32rpx;
			font-weight: 700;
			color: #333;
		}
	}
	
	.info-content {
		padding: 30rpx;
		
		.info-item {
			margin-bottom: 30rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.info-label {
				font-size: 28rpx;
				color: #333;
				font-weight: 600;
				margin-bottom: 12rpx;
				display: block;
			}
			
			.info-value-wrapper {
				display: flex;
				align-items: center;
				background: #f8f9fa;
				border-radius: 12rpx;
				padding: 16rpx 20rpx;
				
				.info-value {
					flex: 1;
					font-size: 26rpx;
					color: #666;
					word-break: break-all;
				}
				
				.copy-btn {
					background: #FF6B35;
					color: #fff;
					border: none;
					border-radius: 8rpx;
					padding: 8rpx 16rpx;
					font-size: 22rpx;
					font-weight: 600;
					margin-left: 12rpx;
				}
			}
			
			.qrcode-wrapper {
				display: flex;
				justify-content: center;
				padding: 20rpx;
				background: #f8f9fa;
				border-radius: 12rpx;
				
				.qrcode-canvas {
					border-radius: 8rpx;
				}
			}
		}
	}
}

// 操作按钮区域
.action-section {
	margin: 0 30rpx 30rpx;
	display: flex;
	gap: 20rpx;
	
	.save-btn, .share-btn {
		flex: 1;
		height: 88rpx;
		border-radius: 20rpx;
		border: none;
		font-size: 28rpx;
		font-weight: 600;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
		
		.btn-icon {
			margin-right: 12rpx;
			font-size: 30rpx;
		}
		
		&:disabled {
			opacity: 0.5;
		}
	}
	
	.save-btn {
		background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
		color: #fff;
		box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
		
		&:active:not(:disabled) {
			transform: translateY(2rpx);
		}
	}
	
	.share-btn {
		background: #fff;
		color: #FF6B35;
		border: 2rpx solid #FF6B35;
		
		&:active:not(:disabled) {
			background: rgba(255, 107, 53, 0.05);
		}
	}
}
</style> 