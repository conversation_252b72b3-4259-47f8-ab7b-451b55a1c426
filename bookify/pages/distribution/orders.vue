<template>
	<view class="container">
		<!-- 橙色渐变背景头部 -->
		<view class="header-section">
			<view class="header-content">
				<text class="page-title">分销订单</text>
				<text class="page-subtitle">查看您的分销收益订单</text>
			</view>
		</view>
		
		<!-- 订单统计 -->
		<view class="stats-section">
			<view class="stat-item">
				<text class="amount">{{ orderStats.totalCount }}</text>
				<text class="label">总订单数</text>
			</view>
			<view class="stat-item">
				<text class="amount">{{ orderStats.totalIncome }}</text>
				<text class="label">总收益(元)</text>
			</view>
			<view class="stat-item">
				<text class="amount">{{ orderStats.monthIncome }}</text>
				<text class="label">本月收益(元)</text>
			</view>
		</view>
		
		<!-- 筛选标签 -->
		<view class="filter-section">
			<view 
				class="filter-item" 
				:class="{ active: currentStatus === '' }" 
				@click="switchStatus('')"
			>
				<text>全部</text>
			</view>
			<view 
				class="filter-item" 
				:class="{ active: currentStatus === 'pending' }" 
				@click="switchStatus('pending')"
			>
				<text>待结算</text>
			</view>
			<view 
				class="filter-item" 
				:class="{ active: currentStatus === 'settled' }" 
				@click="switchStatus('settled')"
			>
				<text>已结算</text>
			</view>
		</view>
		
		<!-- 订单列表 -->
		<view class="orders-section">
			<view class="section-header" v-if="orderList.length > 0">
				<text class="section-title">分销订单</text>
				<text class="order-count">共{{ orderList.length }}笔</text>
			</view>
			
			<view class="order-list" v-if="orderList.length > 0">
				<view class="order-item" v-for="(order, index) in orderList" :key="index">
					<view class="order-header">
						<view class="order-info">
							<text class="order-no">订单号：{{ order.orderNo }}</text>
							<text class="order-time">{{ formatTime(order.createTime) }}</text>
						</view>
						<view class="order-status" :class="'status-' + order.status">
							<text>{{ getStatusText(order.status) }}</text>
						</view>
					</view>
					
					<view class="order-content">
						<view class="buyer-info">
							<image class="buyer-avatar" :src="order.buyerAvatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
							<view class="buyer-details">
								<text class="buyer-name">{{ order.buyerName || '匿名用户' }}</text>
								<text class="relation-text">{{ order.level }}级下线</text>
							</view>
						</view>
						
						<view class="order-amount">
							<text class="commission">+¥{{ order.commission.toFixed(2) }}</text>
							<text class="order-total">订单金额：¥{{ order.orderAmount.toFixed(2) }}</text>
						</view>
					</view>
					
					<view class="product-info" v-if="order.productName">
						<text class="product-name">{{ order.productName }}</text>
						<text class="product-spec">{{ order.productSpec }}</text>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-else>
				<text class="empty-icon">📋</text>
				<text class="empty-title">暂无分销订单</text>
				<text class="empty-desc">快去推广商品获得收益吧</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getDistributionOrders, getDistributionOrderStats } from '@/api/distribution'

export default {
	data() {
		return {
			currentStatus: '', // 筛选状态：'', 'pending', 'settled'
			orderStats: {
				totalCount: 0,
				totalIncome: '0.00',
				monthIncome: '0.00'
			},
			orderList: [],
			loading: false
		}
	},
	
	onLoad() {
		this.loadOrderStats()
		this.loadOrderList()
	},
	
	methods: {
		// 加载订单统计
		async loadOrderStats() {
			try {
				const { code, data } = await getDistributionOrderStats()
				if (code === 200 && data) {
					this.orderStats = {
						totalCount: data.totalCount || 0,
						totalIncome: (data.totalIncome || 0).toFixed(2),
						monthIncome: (data.monthIncome || 0).toFixed(2)
					}
				}
			} catch (error) {
				console.error('loadOrderStats error:', error)
			}
		},
		
		// 加载订单列表
		async loadOrderList() {
			if (this.loading) return
			
			this.loading = true
			try {
				const { code, data } = await getDistributionOrders({
					page: 1,
					size: 100
				})
				if (code === 200 && data) {
					this.orderList = data.list || []
				}
			} catch (error) {
				console.error('loadOrderList error:', error)
				uni.showToast({
					title: '加载订单数据失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 切换状态筛选
		switchStatus(status) {
			if (this.currentStatus === status) return
			
			this.currentStatus = status
			this.loadOrderList()
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'pending': '待结算',
				'settled': '已结算',
				'cancelled': '已取消'
			}
			return statusMap[status] || '未知'
		},
		
		// 格式化时间
		formatTime(time) {
			if (!time) return ''
			
			const date = new Date(time)
			const now = new Date()
			const diff = now - date
			
			if (diff < 24 * 60 * 60 * 1000) {
				return '今天 ' + date.getHours().toString().padStart(2, '0') + ':' + 
					   date.getMinutes().toString().padStart(2, '0')
			} else if (diff < 7 * 24 * 60 * 60 * 1000) {
				return Math.floor(diff / (24 * 60 * 60 * 1000)) + '天前'
			} else {
				return date.getFullYear() + '-' + 
					   String(date.getMonth() + 1).padStart(2, '0') + '-' + 
					   String(date.getDate()).padStart(2, '0')
			}
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background: #f8f9fa;
}

// 橙色渐变头部区域
.header-section {
	background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
	padding: 200rpx 30rpx 60rpx;
	position: relative;
	overflow: hidden;

	// 背景装饰效果
	&::before {
		content: '';
		position: absolute;
		top: -100rpx;
		right: -100rpx;
		width: 400rpx;
		height: 400rpx;
		background: rgba(255, 255, 255, 0.1);
		border-radius: 50%;
	}
	
	&::after {
		content: '';
		position: absolute;
		bottom: -80rpx;
		left: -80rpx;
		width: 300rpx;
		height: 300rpx;
		background: rgba(255, 255, 255, 0.05);
		border-radius: 50%;
	}
	
	.header-content {
		position: relative;
		z-index: 2;
		text-align: center;
		
		.page-title {
			display: block;
			font-size: 40rpx;
			font-weight: 700;
			color: #fff;
			margin-bottom: 12rpx;
			text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
		}
		
		.page-subtitle {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			font-weight: 500;
		}
	}
}

// 订单统计区域
.stats-section {
	margin: 30rpx;
	background: #fff;
	border-radius: 24rpx;
	padding: 30rpx;
	display: flex;
	justify-content: space-between;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	
	.stat-item {
		flex: 1;
		text-align: center;
		position: relative;
		
		&:not(:last-child)::after {
			content: '';
			position: absolute;
			right: 0;
			top: 50%;
			transform: translateY(-50%);
			width: 1rpx;
			height: 60rpx;
			background: rgba(255, 107, 53, 0.2);
		}
		
		.amount {
			font-size: 32rpx;
			color: #FF6B35;
			font-weight: 700;
			margin-bottom: 8rpx;
			display: block;
		}
		
		.label {
			font-size: 24rpx;
			color: #666;
			font-weight: 500;
		}
	}
}

// 筛选标签区域
.filter-section {
	margin: 0 30rpx 30rpx;
	display: flex;
	background: #fff;
	border-radius: 24rpx;
	padding: 8rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	
	.filter-item {
		flex: 1;
		text-align: center;
		padding: 20rpx;
		border-radius: 16rpx;
		transition: all 0.3s ease;
		
		text {
			font-size: 28rpx;
			color: #666;
			font-weight: 600;
		}
		
		&.active {
			background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
			box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
			
			text {
				color: #fff;
			}
		}
	}
}

// 订单列表区域
.orders-section {
	margin: 0 30rpx 30rpx;
	background: #fff;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	overflow: hidden;
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f5f5f5;
		
		.section-title {
			font-size: 32rpx;
			font-weight: 700;
			color: #333;
		}
		
		.order-count {
			font-size: 26rpx;
			color: #FF6B35;
			font-weight: 600;
		}
	}
	
	.order-list {
		.order-item {
			padding: 30rpx;
			border-bottom: 1rpx solid #f5f5f5;
			
			&:last-child {
				border-bottom: none;
			}
			
			.order-header {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
				margin-bottom: 20rpx;
				
				.order-info {
					.order-no {
						font-size: 26rpx;
						color: #333;
						font-weight: 600;
						margin-bottom: 8rpx;
						display: block;
					}
					
					.order-time {
						font-size: 22rpx;
						color: #999;
					}
				}
				
				.order-status {
					padding: 6rpx 16rpx;
					border-radius: 16rpx;
					font-size: 22rpx;
					font-weight: 600;
					
					&.status-pending {
						background: rgba(24, 144, 255, 0.1);
						color: #1890ff;
					}
					
					&.status-settled {
						background: rgba(82, 196, 26, 0.1);
						color: #52c41a;
					}
					
					&.status-cancelled {
						background: rgba(245, 34, 45, 0.1);
						color: #f5222d;
					}
				}
			}
			
			.order-content {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20rpx;
				
				.buyer-info {
					display: flex;
					align-items: center;
					
					.buyer-avatar {
						width: 60rpx;
						height: 60rpx;
						border-radius: 30rpx;
						margin-right: 16rpx;
						border: 2rpx solid rgba(255, 107, 53, 0.1);
					}
					
					.buyer-details {
						.buyer-name {
							font-size: 26rpx;
							color: #333;
							font-weight: 600;
							margin-bottom: 6rpx;
							display: block;
						}
						
						.relation-text {
							font-size: 22rpx;
							color: #FF6B35;
							background: rgba(255, 107, 53, 0.1);
							padding: 2rpx 8rpx;
							border-radius: 8rpx;
							font-weight: 600;
						}
					}
				}
				
				.order-amount {
					text-align: right;
					
					.commission {
						font-size: 30rpx;
						color: #FF6B35;
						font-weight: 700;
						margin-bottom: 6rpx;
						display: block;
					}
					
					.order-total {
						font-size: 22rpx;
						color: #999;
					}
				}
			}
			
			.product-info {
				background: rgba(255, 107, 53, 0.05);
				padding: 16rpx 20rpx;
				border-radius: 12rpx;
				border-left: 4rpx solid #FF6B35;
				
				.product-name {
					font-size: 26rpx;
					color: #333;
					font-weight: 600;
					margin-bottom: 6rpx;
					display: block;
				}
				
				.product-spec {
					font-size: 22rpx;
					color: #666;
				}
			}
		}
	}
	
	// 空状态
	.empty-state {
		padding: 100rpx 30rpx;
		text-align: center;
		
		.empty-icon {
			font-size: 120rpx;
			margin-bottom: 30rpx;
			display: block;
			opacity: 0.5;
		}
		
		.empty-title {
			font-size: 32rpx;
			color: #333;
			font-weight: 600;
			margin-bottom: 12rpx;
			display: block;
		}
		
		.empty-desc {
			font-size: 26rpx;
			color: #999;
		}
	}
}
</style> 