<template>
	<PageLayout
    :show-nav-bar="true" :nav-transparent="false" 	:show-center="true" 
		nav-title="申请分销"
		nav-text-color="#333"
	>
		<view class="apply-container">
			<!-- 待审核状态组件 -->
			<PendingStatus 
				v-if="applicationStatus === 0"
				:application-data="applicationData"
			/>
			
			<!-- 已通过状态显示 -->
			<view class="status-card" v-if="applicationStatus === 1">
				<view class="status-content">
					<view class="status-icon status-approved">
						<text>✅</text>
					</view>
					<view class="status-text">
						<text class="status-title">申请已通过</text>
						<text class="status-desc">恭喜您成为分销员！现在可以开始推广赚取佣金</text>
					</view>
				</view>
				<view class="status-actions">
					<button class="btn-secondary" @click="goBack">返回首页</button>
					<button class="btn-primary" @click="goToDistribution">进入分销中心</button>
				</view>
			</view>

			<!-- 已拒绝状态显示 -->
			<view class="status-card" v-if="applicationStatus === 2">
				<view class="status-content">
					<view class="status-icon status-rejected">
						<text>❌</text>
					</view>
					<view class="status-text">
						<text class="status-title">申请已拒绝</text>
						<text class="status-desc">很抱歉，您的申请未通过审核，可以重新申请</text>
					</view>
				</view>
				<view class="reject-reason" v-if="rejectReason">
					<text class="reason-label">拒绝原因：</text>
					<text class="reason-text">{{ rejectReason }}</text>
				</view>
				<view class="status-actions">
					<button class="btn-primary" @click="reapply">重新申请</button>
				</view>
			</view>

			<!-- 申请表单组件 -->
			<ApplyForm 
				v-if="applicationStatus === null || (applicationStatus === 2 && showReapplyForm)"
				:config="config"
				:is-reapply="applicationStatus === 2"
				:initial-data="applicationData"
				@apply-success="handleApplySuccess"
			/>
		</view>
	</PageLayout>
</template>

<script>
import { getDistributorInfo, getConfig } from '@/api/distribution'
import AuthUtils from '@/utils/auth'
import PageLayout from '@/components/layout/PageLayout.vue'
import ApplyForm from '@/components/distribution/ApplyForm.vue'
import PendingStatus from '@/components/distribution/PendingStatus.vue'

export default {
	components: {
		PageLayout,
		ApplyForm,
		PendingStatus
	},
	
	data() {
		return {
			// 申请状态：null-未申请, 0-待审核, 1-已通过, 2-已拒绝
			applicationStatus: null,
			rejectReason: '',
			
			// 申请数据
			applicationData: {},
			
			// 分销配置
			config: {
				enabled: true,
				register_enabled: true,
				register_amount: 5,
				purchase_enabled: true,
				level1_rate: 10,
				level2_rate: 5,
				freeze_days: 7,
				min_withdraw: 10,
				max_withdraw: 5000,
				withdraw_fee_rate: 0,
				wechat_withdraw: true,
				alipay_withdraw: false
			},
			
			// 是否显示重新申请表单
			showReapplyForm: false
		}
	},
	
	onLoad() {
		if (!AuthUtils.checkLoginStatus()) {
			uni.navigateBack()
			return
		}
		this.initData()
	},
	
	methods: {
		async initData() {
			await Promise.all([
				this.getDistributorStatus(),
				this.getDistributionConfig()
			])
		},
		
		// 获取分销员状态
		async getDistributorStatus() {
			try {
				const { code, data } = await getDistributorInfo()
				if (code === 200 && data && data.ID > 0) {
					this.applicationStatus = data.Status
					this.rejectReason = data.RejectReason || ''
					
					// 保存申请数据
					this.applicationData = {
						realName: data.RealName || '',
						phone: data.Phone || '',
						idCard: data.IdCard || '',
						remark: data.Remark || '',
						applyTime: data.ApplyTime || data.CreateTime
					}
				}
			} catch (error) {
				console.error('获取分销员状态失败:', error)
			}
		},
		
		// 获取分销配置
		async getDistributionConfig() {
			try {
				const { code, data } = await getConfig()
				if (code === 200 && data) {
					this.config = { ...this.config, ...data }
				}
			} catch (error) {
				console.error('获取分销配置失败:', error)
			}
		},
		
		// 返回首页
		goBack() {
			uni.switchTab({
				url: '/pages/index/index'
			})
		},
		
		// 进入分销中心
		goToDistribution() {
			uni.navigateTo({
				url: '/pages/distribution/index'
			})
		},
		
		// 重新申请
		reapply() {
			this.showReapplyForm = true
		},
		
		// 申请成功回调
		handleApplySuccess() {
			// 更新状态为待审核
			this.applicationStatus = 0
			this.showReapplyForm = false
			
			// 重新获取申请数据
			this.getDistributorStatus()
		}
	}
}
</script>

<style lang="scss" scoped>
.apply-container {
	min-height: 100vh;
	background: #f5f5f5;
}

.status-card {
	background: #fff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.status-content {
	display: flex;
	align-items: center;
	margin-bottom: 32rpx;
	
	.status-icon {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 24rpx;
		font-size: 36rpx;
		color: #fff;
		
		&.status-approved {
			background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
		}
		
		&.status-rejected {
			background: linear-gradient(135deg, #F44336 0%, #E53935 100%);
		}
	}
	
	.status-text {
		flex: 1;
		
		.status-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
			display: block;
			margin-bottom: 8rpx;
		}
		
		.status-desc {
			font-size: 28rpx;
			color: #666;
			display: block;
		}
	}
}

.reject-reason {
	background: #fff5f5;
	border: 1rpx solid #ffebee;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 32rpx;
	
	.reason-label {
		font-size: 28rpx;
		color: #666;
		font-weight: 500;
	}
	
	.reason-text {
		font-size: 28rpx;
		color: #f44336;
		margin-top: 8rpx;
		display: block;
	}
}

.status-actions {
	display: flex;
	gap: 20rpx;
	
	button {
		flex: 1;
		height: 80rpx;
		border: none;
		border-radius: 40rpx;
		font-size: 30rpx;
		font-weight: 500;
	}
	
	.btn-secondary {
		background: #f5f5f5;
		color: #666;
	}
	
	.btn-primary {
		background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
		color: #fff;
	}
}
</style> 