<template>
	<view class="container">
		<!-- 橙色渐变背景头部 -->
		<view class="header-section">
			<!-- 用户信息 -->
			<view class="user-info-section">
				<image class="user-avatar" :src="userInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
				<view class="user-details">
					<text class="user-nickname">{{ userInfo.nickname || '-' }}</text>
					<view class="user-meta">
						<text class="level-badge">{{ level }}级分销商</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 收益统计 -->
		<view class="stats-section">
			<view class="stat-item">
				<text class="amount">{{ totalIncome }}</text>
				<text class="label">累计收益(元)</text>
			</view>
			<view class="stat-item">
				<text class="amount">{{ balance }}</text>
				<text class="label">可用余额(元)</text>
			</view>
			<view class="stat-item">
				<text class="amount">{{ frozenAmount }}</text>
				<text class="label">冻结金额(元)</text>
			</view>
		</view>
		
		<!-- 功能菜单 -->
		<view class="menu-section">
			<view class="menu-item" @click="onTeam">
				<view class="menu-left">
					<view class="menu-icon">👥</view>
					<text class="menu-text">我的团队</text>
				</view>
				<text class="menu-arrow">›</text>
			</view>
			
			<view class="menu-item" @click="onOrders">
				<view class="menu-left">
					<view class="menu-icon">📋</view>
					<text class="menu-text">分销订单</text>
				</view>
				<text class="menu-arrow">›</text>
			</view>
			
			<view class="menu-item" @click="onWithdraw">
				<view class="menu-left">
					<view class="menu-icon">💰</view>
					<text class="menu-text">提现</text>
				</view>
				<text class="menu-arrow">›</text>
			</view>
			
			<view class="menu-item" @click="onPoster">
				<view class="menu-left">
					<view class="menu-icon">🖼️</view>
					<text class="menu-text">推广海报</text>
				</view>
				<text class="menu-arrow">›</text>
			</view>
			
			<view class="menu-item" @click="onRules">
				<view class="menu-left">
					<view class="menu-icon">📜</view>
					<text class="menu-text">分销规则</text>
				</view>
				<text class="menu-arrow">›</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getDistributorInfo } from '@/api/distribution'

export default {
	data() {
		return {
			userInfo: {},
			level: 1,
			totalIncome: '0.00',
			balance: '0.00',
			frozenAmount: '0.00'
		}
	},
	
	onLoad() {
		this.loadUserInfo()
		this.loadDistributorInfo()
	},
	
	methods: {
		loadUserInfo() {
			const userInfo = uni.getStorageSync('userInfo')
			if (userInfo) {
				this.userInfo = userInfo
			}
		},
		
		async loadDistributorInfo() {
			try {
				const { code, data } = await getDistributorInfo()
				if (code === 200 && data) {
					this.level = data.Level || 1
					this.totalIncome = data.TotalIncome.toFixed(2)
					this.balance = data.AvailableBalance.toFixed(2)
					this.frozenAmount = data.FrozenBalance.toFixed(2)
				}
			} catch (error) {
				console.error('loadDistributorInfo error:', error)
				uni.showToast({
					title: '加载分销数据失败',
					icon: 'none'
				})
			}
		},
		
		onTeam() {
			uni.navigateTo({
				url: '/pages/distribution/team'
			})
		},
		
		onOrders() {
			uni.navigateTo({
				url: '/pages/distribution/orders'
			})
		},
		
		onWithdraw() {
			uni.navigateTo({
				url: '/pages/distribution/withdraw'
			})
		},
		
		onPoster() {
			uni.navigateTo({
				url: '/pages/distribution/poster'
			})
		},
		
		onRules() {
			uni.navigateTo({
				url: '/pages/distribution/rules'
			})
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background: #f8f9fa;
}

// 橙色渐变头部区域
.header-section {
	background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
	padding: 200rpx 30rpx 40rpx;
	position: relative;
	overflow: hidden;

	// 背景装饰效果
	&::before {
		content: '';
		position: absolute;
		top: -100rpx;
		right: -100rpx;
		width: 400rpx;
		height: 400rpx;
		background: rgba(255, 255, 255, 0.1);
		border-radius: 50%;
	}
	
	&::after {
		content: '';
		position: absolute;
		bottom: -80rpx;
		left: -80rpx;
		width: 300rpx;
		height: 300rpx;
		background: rgba(255, 255, 255, 0.05);
		border-radius: 50%;
	}
}

// 用户信息区域
.user-info-section {
	display: flex;
	align-items: center;
	position: relative;
	z-index: 2;
	
	.user-avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		border: 4rpx solid rgba(255, 255, 255, 0.3);
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
	}
	
	.user-details {
		flex: 1;
		margin-left: 24rpx;
		
		.user-nickname {
			display: block;
			font-size: 36rpx;
			font-weight: 700;
			color: #fff;
			margin-bottom: 12rpx;
			text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
		}
		
		.user-meta {
			display: flex;
			align-items: center;
			
			.level-badge {
				font-size: 22rpx;
				background: rgba(255, 255, 255, 0.25);
				color: #fff;
				padding: 6rpx 16rpx;
				border-radius: 20rpx;
				font-weight: 600;
				backdrop-filter: blur(10px);
			}
		}
	}
}

// 收益统计区域
.stats-section {
	margin: 30rpx;
	background: #fff;
	border-radius: 24rpx;
	padding: 30rpx;
	display: flex;
	justify-content: space-between;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	
	.stat-item {
		flex: 1;
		text-align: center;
		position: relative;
		
		&:not(:last-child)::after {
			content: '';
			position: absolute;
			right: 0;
			top: 50%;
			transform: translateY(-50%);
			width: 1rpx;
			height: 60rpx;
			background: rgba(255, 107, 53, 0.2);
		}
		
		.amount {
			font-size: 32rpx;
			color: #FF6B35;
			font-weight: 700;
			margin-bottom: 8rpx;
			display: block;
		}
		
		.label {
			font-size: 24rpx;
			color: #666;
			font-weight: 500;
		}
	}
}

// 功能菜单区域
.menu-section {
	margin: 30rpx;
	background: #fff;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	overflow: hidden;
	
	.menu-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 32rpx 30rpx;
		border-bottom: 1rpx solid #f5f5f5;
		transition: all 0.3s ease;
		
		&:last-child {
			border-bottom: none;
		}
		
		&:active {
			background: rgba(255, 107, 53, 0.05);
		}
		
		.menu-left {
			display: flex;
			align-items: center;
			
			.menu-icon {
				width: 60rpx;
				height: 60rpx;
				background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(247, 147, 30, 0.1) 100%);
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 24rpx;
				font-size: 32rpx;
				border: 1rpx solid rgba(255, 107, 53, 0.1);
			}
			
			.menu-text {
				font-size: 30rpx;
				color: #333;
				font-weight: 600;
			}
		}
		
		.menu-arrow {
			font-size: 28rpx;
			color: #999;
			font-weight: 300;
		}
	}
}
</style>
