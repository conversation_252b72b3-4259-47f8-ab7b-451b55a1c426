<template>
	<view class="container">
		<!-- 橙色渐变背景头部 -->
		<view class="header-section">
			<view class="header-content">
				<text class="page-title">申请提现</text>
				<text class="page-subtitle">将您的收益提现到账户</text>
			</view>
		</view>
		
		<!-- 余额信息 -->
		<view class="balance-section">
			<view class="balance-item">
				<text class="amount">{{ balanceInfo.availableBalance }}</text>
				<text class="label">可提现余额(元)</text>
			</view>
			<view class="balance-item">
				<text class="amount">{{ balanceInfo.frozenBalance }}</text>
				<text class="label">冻结金额(元)</text>
			</view>
			<view class="balance-item">
				<text class="amount">{{ balanceInfo.totalWithdraw }}</text>
				<text class="label">累计提现(元)</text>
			</view>
		</view>
		
		<!-- 提现表单 -->
		<view class="form-section">
			<view class="form-header">
				<text class="form-title">提现信息</text>
			</view>
			
			<view class="form-content">
				<!-- 提现金额 -->
				<view class="form-item">
					<text class="form-label">提现金额</text>
					<view class="amount-input-wrapper">
						<text class="currency-symbol">¥</text>
						<input 
							class="amount-input" 
							type="digit" 
							placeholder="请输入提现金额"
							v-model="withdrawForm.amount"
							@input="onAmountInput"
						/>
					</view>
					<view class="amount-tips">
						<text class="tip-text">最低提现：¥{{ withdrawConfig.minAmount }}</text>
						<text class="tip-text">最高提现：¥{{ withdrawConfig.maxAmount }}</text>
					</view>
				</view>
				
				<!-- 提现方式 -->
				<view class="form-item">
					<text class="form-label">提现方式</text>
					<view class="withdraw-methods">
						<view 
							class="method-item" 
							:class="{ active: withdrawForm.method === 'wechat' }"
							@click="selectMethod('wechat')"
							v-if="withdrawConfig.wechatEnabled"
						>
							<view class="method-icon wechat">💚</view>
							<text class="method-name">微信</text>
						</view>
						<view 
							class="method-item" 
							:class="{ active: withdrawForm.method === 'alipay' }"
							@click="selectMethod('alipay')"
							v-if="withdrawConfig.alipayEnabled"
						>
							<view class="method-icon alipay">💙</view>
							<text class="method-name">支付宝</text>
						</view>
					</view>
				</view>
				
				<!-- 收款账户 -->
				<view class="form-item" v-if="withdrawForm.method">
					<text class="form-label">{{ getAccountLabel() }}</text>
					<input 
						class="form-input" 
						type="text" 
						:placeholder="getAccountPlaceholder()"
						v-model="withdrawForm.account"
					/>
				</view>
				
				<!-- 真实姓名 -->
				<view class="form-item" v-if="withdrawForm.method">
					<text class="form-label">真实姓名</text>
					<input 
						class="form-input" 
						type="text" 
						placeholder="请输入真实姓名"
						v-model="withdrawForm.realName"
					/>
				</view>
				
				<!-- 手续费说明 -->
				<view class="fee-info" v-if="withdrawForm.amount && parseFloat(withdrawForm.amount) > 0">
					<view class="fee-item">
						<text class="fee-label">提现金额：</text>
						<text class="fee-value">¥{{ withdrawForm.amount }}</text>
					</view>
					<view class="fee-item">
						<text class="fee-label">手续费：</text>
						<text class="fee-value">¥{{ calculateFee() }}</text>
					</view>
					<view class="fee-item total">
						<text class="fee-label">实际到账：</text>
						<text class="fee-value">¥{{ calculateActualAmount() }}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 提现按钮 -->
		<view class="submit-section">
			<button 
				class="submit-btn" 
				:class="{ disabled: !canSubmit }"
				@click="onSubmit"
				:disabled="!canSubmit"
			>
				{{ submitBtnText }}
			</button>
		</view>
		
		<!-- 提现记录入口 -->
		<view class="record-section">
			<view class="record-item" @click="goToWithdrawList">
				<view class="record-left">
					<view class="record-icon">📋</view>
					<text class="record-text">提现记录</text>
				</view>
				<text class="record-arrow">›</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getWithdrawConfig, getBalanceInfo, submitWithdraw } from '@/api/distribution'

export default {
	data() {
		return {
			// 余额信息
			balanceInfo: {
				availableBalance: '0.00',
				frozenBalance: '0.00',
				totalWithdraw: '0.00'
			},
			// 提现配置
			withdrawConfig: {
				minAmount: 10,
				maxAmount: 5000,
				feeRate: 0.01,
				wechatEnabled: true,
				alipayEnabled: true
			},
			// 提现表单
			withdrawForm: {
				amount: '',
				method: '',
				account: '',
				realName: ''
			},
			submitting: false
		}
	},
	
	computed: {
		// 是否可以提交
		canSubmit() {
			return this.withdrawForm.amount && 
				   this.withdrawForm.method && 
				   this.withdrawForm.account && 
				   this.withdrawForm.realName &&
				   !this.submitting &&
				   this.isAmountValid()
		},
		
		// 提交按钮文本
		submitBtnText() {
			if (this.submitting) {
				return '提交中...'
			}
			if (!this.withdrawForm.amount) {
				return '请输入提现金额'
			}
			if (!this.isAmountValid()) {
				return '提现金额不符合要求'
			}
			if (!this.withdrawForm.method) {
				return '请选择提现方式'
			}
			if (!this.withdrawForm.account) {
				return '请输入收款账户'
			}
			if (!this.withdrawForm.realName) {
				return '请输入真实姓名'
			}
			return '申请提现'
		}
	},
	
	onLoad() {
		this.loadWithdrawConfig()
		this.loadBalanceInfo()
	},
	
	methods: {
		// 加载提现配置
		async loadWithdrawConfig() {
			try {
				const { code, data } = await getWithdrawConfig()
				if (code === 200 && data) {
					this.withdrawConfig = {
						minAmount: data.minAmount || 10,
						maxAmount: data.maxAmount || 5000,
						feeRate: data.feeRate || 0.01,
						wechatEnabled: data.wechatEnabled !== false,
						alipayEnabled: data.alipayEnabled !== false
					}
				}
			} catch (error) {
				console.error('loadWithdrawConfig error:', error)
			}
		},
		
		// 加载余额信息
		async loadBalanceInfo() {
			try {
				const { code, data } = await getBalanceInfo()
				if (code === 200 && data) {
					this.balanceInfo = {
						availableBalance: (data.availableBalance || 0).toFixed(2),
						frozenBalance: (data.frozenBalance || 0).toFixed(2),
						totalWithdraw: (data.totalWithdraw || 0).toFixed(2)
					}
				}
			} catch (error) {
				console.error('loadBalanceInfo error:', error)
			}
		},
		
		// 金额输入处理
		onAmountInput(e) {
			let value = e.detail.value
			// 只允许数字和小数点
			value = value.replace(/[^\d.]/g, '')
			// 只允许一个小数点
			const parts = value.split('.')
			if (parts.length > 2) {
				value = parts[0] + '.' + parts.slice(1).join('')
			}
			// 限制小数位数
			if (parts[1] && parts[1].length > 2) {
				value = parts[0] + '.' + parts[1].substring(0, 2)
			}
			this.withdrawForm.amount = value
		},
		
		// 选择提现方式
		selectMethod(method) {
			this.withdrawForm.method = method
			// 清空账户信息
			this.withdrawForm.account = ''
			this.withdrawForm.realName = ''
		},
		
		// 获取账户标签
		getAccountLabel() {
			return this.withdrawForm.method === 'wechat' ? '微信号' : '支付宝账号'
		},
		
		// 获取账户占位符
		getAccountPlaceholder() {
			return this.withdrawForm.method === 'wechat' ? '请输入微信号' : '请输入支付宝账号'
		},
		
		// 验证金额是否有效
		isAmountValid() {
			const amount = parseFloat(this.withdrawForm.amount)
			if (isNaN(amount) || amount <= 0) return false
			if (amount < this.withdrawConfig.minAmount) return false
			if (amount > this.withdrawConfig.maxAmount) return false
			if (amount > parseFloat(this.balanceInfo.availableBalance)) return false
			return true
		},
		
		// 计算手续费
		calculateFee() {
			const amount = parseFloat(this.withdrawForm.amount) || 0
			const fee = amount * this.withdrawConfig.feeRate
			return fee.toFixed(2)
		},
		
		// 计算实际到账金额
		calculateActualAmount() {
			const amount = parseFloat(this.withdrawForm.amount) || 0
			const fee = amount * this.withdrawConfig.feeRate
			const actualAmount = amount - fee
			return Math.max(0, actualAmount).toFixed(2)
		},
		
		// 提交提现申请
		async onSubmit() {
			if (!this.canSubmit) return
			
			this.submitting = true
			try {
				const { code, message } = await submitWithdraw({
					amount: parseFloat(this.withdrawForm.amount),
					method: this.withdrawForm.method,
					account: this.withdrawForm.account,
					realName: this.withdrawForm.realName
				})
				
				if (code === 200) {
					uni.showToast({
						title: '提现申请提交成功',
						icon: 'success'
					})
					
					// 重置表单
					this.withdrawForm = {
						amount: '',
						method: '',
						account: '',
						realName: ''
					}
					
					// 刷新余额信息
					this.loadBalanceInfo()
				} else {
					uni.showToast({
						title: message || '提现申请失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('submitWithdraw error:', error)
				uni.showToast({
					title: '提现申请失败',
					icon: 'none'
				})
			} finally {
				this.submitting = false
			}
		},
		
		// 跳转到提现记录
		goToWithdrawList() {
			uni.navigateTo({
				url: '/pages/distribution/withdraw-list'
			})
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background: #f8f9fa;
}

// 橙色渐变头部区域
.header-section {
	background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
	padding: 200rpx 30rpx 60rpx;
	position: relative;
	overflow: hidden;

	// 背景装饰效果
	&::before {
		content: '';
		position: absolute;
		top: -100rpx;
		right: -100rpx;
		width: 400rpx;
		height: 400rpx;
		background: rgba(255, 255, 255, 0.1);
		border-radius: 50%;
	}
	
	&::after {
		content: '';
		position: absolute;
		bottom: -80rpx;
		left: -80rpx;
		width: 300rpx;
		height: 300rpx;
		background: rgba(255, 255, 255, 0.05);
		border-radius: 50%;
	}
	
	.header-content {
		position: relative;
		z-index: 2;
		text-align: center;
		
		.page-title {
			display: block;
			font-size: 40rpx;
			font-weight: 700;
			color: #fff;
			margin-bottom: 12rpx;
			text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
		}
		
		.page-subtitle {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			font-weight: 500;
		}
	}
}

// 余额信息区域
.balance-section {
	margin: 30rpx;
	background: #fff;
	border-radius: 24rpx;
	padding: 30rpx;
	display: flex;
	justify-content: space-between;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	
	.balance-item {
		flex: 1;
		text-align: center;
		position: relative;
		
		&:not(:last-child)::after {
			content: '';
			position: absolute;
			right: 0;
			top: 50%;
			transform: translateY(-50%);
			width: 1rpx;
			height: 60rpx;
			background: rgba(255, 107, 53, 0.2);
		}
		
		.amount {
			font-size: 32rpx;
			color: #FF6B35;
			font-weight: 700;
			margin-bottom: 8rpx;
			display: block;
		}
		
		.label {
			font-size: 24rpx;
			color: #666;
			font-weight: 500;
		}
	}
}

// 表单区域
.form-section {
	margin: 0 30rpx 30rpx;
	background: #fff;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	overflow: hidden;
	
	.form-header {
		padding: 30rpx;
		border-bottom: 1rpx solid #f5f5f5;
		
		.form-title {
			font-size: 32rpx;
			font-weight: 700;
			color: #333;
		}
	}
	
	.form-content {
		padding: 30rpx;
		
		.form-item {
			margin-bottom: 40rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.form-label {
				font-size: 28rpx;
				color: #333;
				font-weight: 600;
				margin-bottom: 16rpx;
				display: block;
			}
			
			.amount-input-wrapper {
				display: flex;
				align-items: center;
				background: #f8f9fa;
				border-radius: 16rpx;
				padding: 0 20rpx;
				border: 2rpx solid transparent;
				transition: all 0.3s ease;
				
				&:focus-within {
					border-color: #FF6B35;
					background: #fff;
				}
				
				.currency-symbol {
					font-size: 32rpx;
					color: #FF6B35;
					font-weight: 700;
					margin-right: 12rpx;
				}
				
				.amount-input {
					flex: 1;
					height: 88rpx;
					font-size: 32rpx;
					color: #333;
					font-weight: 600;
				}
			}
			
			.form-input {
				width: 100%;
				height: 88rpx;
				background: #f8f9fa;
				border-radius: 16rpx;
				padding: 0 20rpx;
				font-size: 28rpx;
				color: #333;
				border: 2rpx solid transparent;
				transition: all 0.3s ease;
				
				&:focus {
					border-color: #FF6B35;
					background: #fff;
				}
			}
			
			.amount-tips {
				display: flex;
				justify-content: space-between;
				margin-top: 12rpx;
				
				.tip-text {
					font-size: 22rpx;
					color: #999;
				}
			}
			
			.withdraw-methods {
				display: flex;
				gap: 20rpx;
				
				.method-item {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: center;
					padding: 30rpx 20rpx;
					background: #f8f9fa;
					border-radius: 16rpx;
					border: 2rpx solid transparent;
					transition: all 0.3s ease;
					
					&.active {
						border-color: #FF6B35;
						background: rgba(255, 107, 53, 0.05);
					}
					
					.method-icon {
						font-size: 48rpx;
						margin-bottom: 12rpx;
						
						&.wechat {
							filter: drop-shadow(0 2rpx 8rpx rgba(34, 197, 94, 0.3));
						}
						
						&.alipay {
							filter: drop-shadow(0 2rpx 8rpx rgba(59, 130, 246, 0.3));
						}
					}
					
					.method-name {
						font-size: 26rpx;
						color: #333;
						font-weight: 600;
					}
				}
			}
		}
		
		.fee-info {
			background: rgba(255, 107, 53, 0.05);
			padding: 24rpx;
			border-radius: 16rpx;
			border-left: 4rpx solid #FF6B35;
			margin-top: 30rpx;
			
			.fee-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 12rpx;
				
				&:last-child {
					margin-bottom: 0;
				}
				
				&.total {
					padding-top: 12rpx;
					border-top: 1rpx solid rgba(255, 107, 53, 0.2);
					
					.fee-label, .fee-value {
						font-weight: 700;
						color: #FF6B35;
					}
				}
				
				.fee-label {
					font-size: 26rpx;
					color: #666;
				}
				
				.fee-value {
					font-size: 26rpx;
					color: #333;
					font-weight: 600;
				}
			}
		}
	}
}

// 提交按钮区域
.submit-section {
	margin: 0 30rpx 30rpx;
	
	.submit-btn {
		width: 100%;
		height: 96rpx;
		background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
		border-radius: 24rpx;
		color: #fff;
		font-size: 32rpx;
		font-weight: 700;
		border: none;
		box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.4);
		transition: all 0.3s ease;
		
		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.4);
		}
		
		&.disabled {
			background: #ccc;
			box-shadow: none;
			transform: none;
		}
	}
}

// 提现记录区域
.record-section {
	margin: 0 30rpx 30rpx;
	background: #fff;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	overflow: hidden;
	
	.record-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 32rpx 30rpx;
		transition: all 0.3s ease;
		
		&:active {
			background: rgba(255, 107, 53, 0.05);
		}
		
		.record-left {
			display: flex;
			align-items: center;
			
			.record-icon {
				width: 60rpx;
				height: 60rpx;
				background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(247, 147, 30, 0.1) 100%);
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 24rpx;
				font-size: 32rpx;
				border: 1rpx solid rgba(255, 107, 53, 0.1);
			}
			
			.record-text {
				font-size: 30rpx;
				color: #333;
				font-weight: 600;
			}
		}
		
		.record-arrow {
			font-size: 28rpx;
			color: #999;
			font-weight: 300;
		}
	}
}
</style> 