<template>
	<view class="container">
		<!-- 橙色渐变背景头部 -->
		<view class="header-section">
			<view class="header-content">
				<text class="page-title">我的团队</text>
				<text class="page-subtitle">查看您的推广团队成员</text>
			</view>
		</view>
		
		<!-- 团队统计 -->
		<view class="stats-section">
			<view class="stat-item">
				<text class="amount">{{ teamStats.level1Count }}</text>
				<text class="label">一级成员</text>
			</view>
			<view class="stat-item">
				<text class="amount">{{ teamStats.level2Count }}</text>
				<text class="label">二级成员</text>
			</view>
			<view class="stat-item">
				<text class="amount">{{ teamStats.totalIncome }}</text>
				<text class="label">团队收益(元)</text>
			</view>
		</view>
		
		<!-- 筛选标签 -->
		<view class="filter-section">
			<view 
				class="filter-item" 
				:class="{ active: currentLevel === 0 }" 
				@click="switchLevel(0)"
			>
				<text>全部</text>
			</view>
			<view 
				class="filter-item" 
				:class="{ active: currentLevel === 1 }" 
				@click="switchLevel(1)"
			>
				<text>一级成员</text>
			</view>
			<view 
				class="filter-item" 
				:class="{ active: currentLevel === 2 }" 
				@click="switchLevel(2)"
			>
				<text>二级成员</text>
			</view>
		</view>
		
		<!-- 团队成员列表 -->
		<view class="team-section">
			<view class="section-header" v-if="teamList.length > 0">
				<text class="section-title">团队成员</text>
				<text class="member-count">共{{ teamList.length }}人</text>
			</view>
			
			<view class="member-list" v-if="teamList.length > 0">
				<view class="member-item" v-for="(member, index) in teamList" :key="index">
					<view class="member-info">
						<image class="member-avatar" :src="member.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
						<view class="member-details">
							<text class="member-name">{{ member.nickname || '用户' + member.id }}</text>
							<view class="member-meta">
								<text class="level-tag" :class="'level-' + member.level">{{ member.level }}级</text>
								<text class="join-time">{{ formatTime(member.createTime) }}</text>
							</view>
						</view>
					</view>
					<view class="member-stats">
						<text class="income-amount">¥{{ member.totalIncome.toFixed(2) }}</text>
						<text class="income-label">贡献收益</text>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-else>
				<text class="empty-icon">👥</text>
				<text class="empty-title">暂无团队成员</text>
				<text class="empty-desc">快去邀请好友加入您的团队吧</text>
			</view>
		</view>
	</view>
</template>

<script>
import { getTeamMembers, getTeamStats } from '@/api/distribution'

export default {
	data() {
		return {
			currentLevel: 0, // 0-全部, 1-一级, 2-二级
			teamStats: {
				level1Count: 0,
				level2Count: 0,
				totalIncome: '0.00'
			},
			teamList: [],
			loading: false
		}
	},
	
	onLoad() {
		this.loadTeamStats()
		this.loadTeamList()
	},
	
	methods: {
		// 加载团队统计
		async loadTeamStats() {
			try {
				const { code, data } = await getTeamStats()
				if (code === 200 && data) {
					this.teamStats = {
						level1Count: data.directCount || 0,
						level2Count: data.indirectCount || 0,
						totalIncome: (data.totalIncome || 0).toFixed(2)
					}
				}
			} catch (error) {
				console.error('loadTeamStats error:', error)
			}
		},
		
		// 加载团队列表
		async loadTeamList() {
			if (this.loading) return
			
			this.loading = true
			try {
				const { code, data } = await getTeamMembers({
					page: 1,
					size: 100
				})
				if (code === 200 && data) {
					this.teamList = data.list || []
				}
			} catch (error) {
				console.error('loadTeamList error:', error)
				uni.showToast({
					title: '加载团队数据失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 切换等级筛选
		switchLevel(level) {
			if (this.currentLevel === level) return
			
			this.currentLevel = level
			this.loadTeamList()
		},
		
		// 格式化时间
		formatTime(time) {
			if (!time) return ''
			
			const date = new Date(time)
			const now = new Date()
			const diff = now - date
			
			if (diff < 24 * 60 * 60 * 1000) {
				return '今天'
			} else if (diff < 7 * 24 * 60 * 60 * 1000) {
				return Math.floor(diff / (24 * 60 * 60 * 1000)) + '天前'
			} else {
				return date.getFullYear() + '-' + 
					   String(date.getMonth() + 1).padStart(2, '0') + '-' + 
					   String(date.getDate()).padStart(2, '0')
			}
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background: #f8f9fa;
}

// 橙色渐变头部区域
.header-section {
	background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
	padding: 200rpx 30rpx 60rpx;
	position: relative;
	overflow: hidden;

	// 背景装饰效果
	&::before {
		content: '';
		position: absolute;
		top: -100rpx;
		right: -100rpx;
		width: 400rpx;
		height: 400rpx;
		background: rgba(255, 255, 255, 0.1);
		border-radius: 50%;
	}
	
	&::after {
		content: '';
		position: absolute;
		bottom: -80rpx;
		left: -80rpx;
		width: 300rpx;
		height: 300rpx;
		background: rgba(255, 255, 255, 0.05);
		border-radius: 50%;
	}
	
	.header-content {
		position: relative;
		z-index: 2;
		text-align: center;
		
		.page-title {
			display: block;
			font-size: 40rpx;
			font-weight: 700;
			color: #fff;
			margin-bottom: 12rpx;
			text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
		}
		
		.page-subtitle {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			font-weight: 500;
		}
	}
}

// 团队统计区域
.stats-section {
	margin: 30rpx;
	background: #fff;
	border-radius: 24rpx;
	padding: 30rpx;
	display: flex;
	justify-content: space-between;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	
	.stat-item {
		flex: 1;
		text-align: center;
		position: relative;
		
		&:not(:last-child)::after {
			content: '';
			position: absolute;
			right: 0;
			top: 50%;
			transform: translateY(-50%);
			width: 1rpx;
			height: 60rpx;
			background: rgba(255, 107, 53, 0.2);
		}
		
		.amount {
			font-size: 32rpx;
			color: #FF6B35;
			font-weight: 700;
			margin-bottom: 8rpx;
			display: block;
		}
		
		.label {
			font-size: 24rpx;
			color: #666;
			font-weight: 500;
		}
	}
}

// 筛选标签区域
.filter-section {
	margin: 0 30rpx 30rpx;
	display: flex;
	background: #fff;
	border-radius: 24rpx;
	padding: 8rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	
	.filter-item {
		flex: 1;
		text-align: center;
		padding: 20rpx;
		border-radius: 16rpx;
		transition: all 0.3s ease;
		
		text {
			font-size: 28rpx;
			color: #666;
			font-weight: 600;
		}
		
		&.active {
			background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
			box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
			
			text {
				color: #fff;
			}
		}
	}
}

// 团队成员区域
.team-section {
	margin: 0 30rpx 30rpx;
	background: #fff;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	overflow: hidden;
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f5f5f5;
		
		.section-title {
			font-size: 32rpx;
			font-weight: 700;
			color: #333;
		}
		
		.member-count {
			font-size: 26rpx;
			color: #FF6B35;
			font-weight: 600;
		}
	}
	
	.member-list {
		.member-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx;
			border-bottom: 1rpx solid #f5f5f5;
			
			&:last-child {
				border-bottom: none;
			}
			
			.member-info {
				display: flex;
				align-items: center;
				flex: 1;
				
				.member-avatar {
					width: 80rpx;
					height: 80rpx;
					border-radius: 40rpx;
					margin-right: 20rpx;
					border: 2rpx solid rgba(255, 107, 53, 0.1);
				}
				
				.member-details {
					.member-name {
						font-size: 28rpx;
						color: #333;
						font-weight: 600;
						margin-bottom: 8rpx;
						display: block;
					}
					
					.member-meta {
						display: flex;
						align-items: center;
						
						.level-tag {
							font-size: 20rpx;
							padding: 4rpx 12rpx;
							border-radius: 12rpx;
							margin-right: 12rpx;
							font-weight: 600;
							
							&.level-1 {
								background: rgba(24, 144, 255, 0.1);
								color: #1890ff;
							}
							
							&.level-2 {
								background: rgba(82, 196, 26, 0.1);
								color: #52c41a;
							}
						}
						
						.join-time {
							font-size: 22rpx;
							color: #999;
						}
					}
				}
			}
			
			.member-stats {
				text-align: right;
				
				.income-amount {
					font-size: 28rpx;
					color: #FF6B35;
					font-weight: 700;
					margin-bottom: 4rpx;
					display: block;
				}
				
				.income-label {
					font-size: 22rpx;
					color: #999;
				}
			}
		}
	}
	
	// 空状态
	.empty-state {
		padding: 100rpx 30rpx;
		text-align: center;
		
		.empty-icon {
			font-size: 120rpx;
			margin-bottom: 30rpx;
			display: block;
			opacity: 0.5;
		}
		
		.empty-title {
			font-size: 32rpx;
			color: #333;
			font-weight: 600;
			margin-bottom: 12rpx;
			display: block;
		}
		
		.empty-desc {
			font-size: 26rpx;
			color: #999;
		}
	}
}
</style> 