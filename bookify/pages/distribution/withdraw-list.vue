<template>
	<view class="container">
		<!-- 橙色渐变背景头部 -->
		<view class="header-section">
			<view class="header-content">
				<text class="page-title">提现记录</text>
				<text class="page-subtitle">查看您的提现申请记录</text>
			</view>
		</view>
		
		<!-- 提现统计 -->
		<view class="stats-section">
			<view class="stat-item">
				<text class="amount">{{ withdrawStats.totalCount }}</text>
				<text class="label">申请次数</text>
			</view>
			<view class="stat-item">
				<text class="amount">{{ withdrawStats.totalAmount }}</text>
				<text class="label">申请金额(元)</text>
			</view>
			<view class="stat-item">
				<text class="amount">{{ withdrawStats.successAmount }}</text>
				<text class="label">成功金额(元)</text>
			</view>
		</view>
		
		<!-- 筛选标签 -->
		<view class="filter-section">
			<view 
				class="filter-item" 
				:class="{ active: currentStatus === '' }" 
				@click="switchStatus('')"
			>
				<text>全部</text>
			</view>
			<view 
				class="filter-item" 
				:class="{ active: currentStatus === 'pending' }" 
				@click="switchStatus('pending')"
			>
				<text>审核中</text>
			</view>
			<view 
				class="filter-item" 
				:class="{ active: currentStatus === 'success' }" 
				@click="switchStatus('success')"
			>
				<text>已成功</text>
			</view>
			<view 
				class="filter-item" 
				:class="{ active: currentStatus === 'failed' }" 
				@click="switchStatus('failed')"
			>
				<text>已拒绝</text>
			</view>
		</view>
		
		<!-- 提现记录列表 -->
		<view class="records-section">
			<view class="section-header" v-if="recordList.length > 0">
				<text class="section-title">提现记录</text>
				<text class="record-count">共{{ recordList.length }}笔</text>
			</view>
			
			<view class="record-list" v-if="recordList.length > 0">
				<view class="record-item" v-for="(record, index) in recordList" :key="index">
					<view class="record-header">
						<view class="record-info">
							<text class="record-amount">¥{{ record.amount.toFixed(2) }}</text>
							<text class="record-time">{{ formatTime(record.createTime) }}</text>
						</view>
						<view class="record-status" :class="'status-' + record.status">
							<text>{{ getStatusText(record.status) }}</text>
						</view>
					</view>
					
					<view class="record-content">
						<view class="record-detail">
							<view class="detail-item">
								<text class="detail-label">提现方式：</text>
								<text class="detail-value">{{ getMethodText(record.method) }}</text>
							</view>
							<view class="detail-item">
								<text class="detail-label">收款账户：</text>
								<text class="detail-value">{{ maskAccount(record.account) }}</text>
							</view>
							<view class="detail-item">
								<text class="detail-label">手续费：</text>
								<text class="detail-value">¥{{ record.fee.toFixed(2) }}</text>
							</view>
							<view class="detail-item">
								<text class="detail-label">实际到账：</text>
								<text class="detail-value highlight">¥{{ (record.amount - record.fee).toFixed(2) }}</text>
							</view>
						</view>
					</view>
					
					<view class="record-footer" v-if="record.remark">
						<view class="remark-info">
							<text class="remark-label">备注：</text>
							<text class="remark-text">{{ record.remark }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-else>
				<text class="empty-icon">💰</text>
				<text class="empty-title">暂无提现记录</text>
				<text class="empty-desc">您还没有申请过提现</text>
				<button class="goto-withdraw-btn" @click="goToWithdraw">
					<text>去申请提现</text>
				</button>
			</view>
		</view>
	</view>
</template>

<script>
import { getWithdrawList, getWithdrawStats } from '@/api/distribution'

export default {
	data() {
		return {
			currentStatus: '', // 筛选状态：'', 'pending', 'success', 'failed'
			withdrawStats: {
				totalCount: 0,
				totalAmount: '0.00',
				successAmount: '0.00'
			},
			recordList: [],
			loading: false
		}
	},
	
	onLoad() {
		this.loadWithdrawStats()
		this.loadRecordList()
	},
	
	methods: {
		// 加载提现统计
		async loadWithdrawStats() {
			try {
				const { code, data } = await getWithdrawStats()
				if (code === 200 && data) {
					this.withdrawStats = {
						totalCount: data.totalCount || 0,
						totalAmount: (data.totalAmount || 0).toFixed(2),
						successAmount: (data.successAmount || 0).toFixed(2)
					}
				}
			} catch (error) {
				console.error('loadWithdrawStats error:', error)
			}
		},
		
		// 加载记录列表
		async loadRecordList() {
			if (this.loading) return
			
			this.loading = true
			try {
				const { code, data } = await getWithdrawList({
					page: 1,
					size: 100
				})
				if (code === 200 && data) {
					this.recordList = data.list || []
				}
			} catch (error) {
				console.error('loadRecordList error:', error)
				uni.showToast({
					title: '加载记录失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 切换状态筛选
		switchStatus(status) {
			if (this.currentStatus === status) return
			
			this.currentStatus = status
			this.loadRecordList()
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'pending': '审核中',
				'success': '已成功',
				'failed': '已拒绝'
			}
			return statusMap[status] || '未知'
		},
		
		// 获取提现方式文本
		getMethodText(method) {
			const methodMap = {
				'wechat': '微信',
				'alipay': '支付宝',
				'bank': '银行卡'
			}
			return methodMap[method] || '未知'
		},
		
		// 脱敏账户信息
		maskAccount(account) {
			if (!account) return '-'
			if (account.length <= 4) return account
			
			const start = account.substring(0, 2)
			const end = account.substring(account.length - 2)
			const middle = '*'.repeat(account.length - 4)
			return start + middle + end
		},
		
		// 格式化时间
		formatTime(time) {
			if (!time) return ''
			
			const date = new Date(time)
			const now = new Date()
			const diff = now - date
			
			if (diff < 24 * 60 * 60 * 1000) {
				return '今天 ' + date.getHours().toString().padStart(2, '0') + ':' + 
					   date.getMinutes().toString().padStart(2, '0')
			} else if (diff < 7 * 24 * 60 * 60 * 1000) {
				return Math.floor(diff / (24 * 60 * 60 * 1000)) + '天前'
			} else {
				return date.getFullYear() + '-' + 
					   String(date.getMonth() + 1).padStart(2, '0') + '-' + 
					   String(date.getDate()).padStart(2, '0')
			}
		},
		
		// 跳转到提现页面
		goToWithdraw() {
			uni.navigateTo({
				url: '/pages/distribution/withdraw'
			})
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background: #f8f9fa;
}

// 橙色渐变头部区域
.header-section {
	background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
	padding: 200rpx 30rpx 60rpx;
	position: relative;
	overflow: hidden;

	// 背景装饰效果
	&::before {
		content: '';
		position: absolute;
		top: -100rpx;
		right: -100rpx;
		width: 400rpx;
		height: 400rpx;
		background: rgba(255, 255, 255, 0.1);
		border-radius: 50%;
	}
	
	&::after {
		content: '';
		position: absolute;
		bottom: -80rpx;
		left: -80rpx;
		width: 300rpx;
		height: 300rpx;
		background: rgba(255, 255, 255, 0.05);
		border-radius: 50%;
	}
	
	.header-content {
		position: relative;
		z-index: 2;
		text-align: center;
		
		.page-title {
			display: block;
			font-size: 40rpx;
			font-weight: 700;
			color: #fff;
			margin-bottom: 12rpx;
			text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
		}
		
		.page-subtitle {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			font-weight: 500;
		}
	}
}

// 提现统计区域
.stats-section {
	margin: 30rpx;
	background: #fff;
	border-radius: 24rpx;
	padding: 30rpx;
	display: flex;
	justify-content: space-between;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	
	.stat-item {
		flex: 1;
		text-align: center;
		position: relative;
		
		&:not(:last-child)::after {
			content: '';
			position: absolute;
			right: 0;
			top: 50%;
			transform: translateY(-50%);
			width: 1rpx;
			height: 60rpx;
			background: rgba(255, 107, 53, 0.2);
		}
		
		.amount {
			font-size: 32rpx;
			color: #FF6B35;
			font-weight: 700;
			margin-bottom: 8rpx;
			display: block;
		}
		
		.label {
			font-size: 24rpx;
			color: #666;
			font-weight: 500;
		}
	}
}

// 筛选标签区域
.filter-section {
	margin: 0 30rpx 30rpx;
	display: flex;
	background: #fff;
	border-radius: 24rpx;
	padding: 8rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	
	.filter-item {
		flex: 1;
		text-align: center;
		padding: 20rpx;
		border-radius: 16rpx;
		transition: all 0.3s ease;
		
		text {
			font-size: 26rpx;
			color: #666;
			font-weight: 600;
		}
		
		&.active {
			background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
			box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
			
			text {
				color: #fff;
			}
		}
	}
}

// 提现记录区域
.records-section {
	margin: 0 30rpx 30rpx;
	background: #fff;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	overflow: hidden;
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f5f5f5;
		
		.section-title {
			font-size: 32rpx;
			font-weight: 700;
			color: #333;
		}
		
		.record-count {
			font-size: 26rpx;
			color: #FF6B35;
			font-weight: 600;
		}
	}
	
	.record-list {
		.record-item {
			padding: 30rpx;
			border-bottom: 1rpx solid #f5f5f5;
			
			&:last-child {
				border-bottom: none;
			}
			
			.record-header {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
				margin-bottom: 20rpx;
				
				.record-info {
					.record-amount {
						font-size: 32rpx;
						color: #333;
						font-weight: 700;
						margin-bottom: 8rpx;
						display: block;
					}
					
					.record-time {
						font-size: 22rpx;
						color: #999;
					}
				}
				
				.record-status {
					padding: 6rpx 16rpx;
					border-radius: 16rpx;
					font-size: 22rpx;
					font-weight: 600;
					
					&.status-pending {
						background: rgba(24, 144, 255, 0.1);
						color: #1890ff;
					}
					
					&.status-success {
						background: rgba(82, 196, 26, 0.1);
						color: #52c41a;
					}
					
					&.status-failed {
						background: rgba(245, 34, 45, 0.1);
						color: #f5222d;
					}
				}
			}
			
			.record-content {
				.record-detail {
					background: rgba(255, 107, 53, 0.05);
					padding: 20rpx;
					border-radius: 12rpx;
					border-left: 4rpx solid #FF6B35;
					
					.detail-item {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: 12rpx;
						
						&:last-child {
							margin-bottom: 0;
						}
						
						.detail-label {
							font-size: 24rpx;
							color: #666;
						}
						
						.detail-value {
							font-size: 24rpx;
							color: #333;
							font-weight: 600;
							
							&.highlight {
								color: #FF6B35;
								font-weight: 700;
							}
						}
					}
				}
			}
			
			.record-footer {
				margin-top: 20rpx;
				
				.remark-info {
					background: #f8f9fa;
					padding: 16rpx 20rpx;
					border-radius: 12rpx;
					
					.remark-label {
						font-size: 22rpx;
						color: #666;
						margin-right: 8rpx;
					}
					
					.remark-text {
						font-size: 22rpx;
						color: #333;
					}
				}
			}
		}
	}
	
	// 空状态
	.empty-state {
		padding: 100rpx 30rpx;
		text-align: center;
		
		.empty-icon {
			font-size: 120rpx;
			margin-bottom: 30rpx;
			display: block;
			opacity: 0.5;
		}
		
		.empty-title {
			font-size: 32rpx;
			color: #333;
			font-weight: 600;
			margin-bottom: 12rpx;
			display: block;
		}
		
		.empty-desc {
			font-size: 26rpx;
			color: #999;
			margin-bottom: 40rpx;
			display: block;
		}
		
		.goto-withdraw-btn {
			background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
			color: #fff;
			border: none;
			border-radius: 20rpx;
			padding: 20rpx 40rpx;
			font-size: 28rpx;
			font-weight: 600;
			box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
			transition: all 0.3s ease;
			
			&:active {
				transform: translateY(2rpx);
				box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3);
			}
		}
	}
}
</style> 