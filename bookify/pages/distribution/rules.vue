<template>
	<view class="container">
		<!-- 橙色渐变背景头部 -->
		<view class="header-section">
			<view class="header-content">
				<text class="page-title">分销规则</text>
				<text class="page-subtitle">了解分销政策与收益规则</text>
			</view>
		</view>
		
		<!-- 分销配置信息 -->
		<view class="config-section" v-if="config">
			<view class="config-header">
				<text class="config-title">分销配置</text>
			</view>
			
			<view class="config-content">
				<view class="config-item">
					<text class="config-label">分销等级</text>
					<text class="config-value">{{ config.maxLevel }}级分销</text>
				</view>
				
				<view class="config-item">
					<text class="config-label">一级佣金比例</text>
					<text class="config-value">{{ (config.level1Rate * 100).toFixed(1) }}%</text>
				</view>
				
				<view class="config-item" v-if="config.level2Rate > 0">
					<text class="config-label">二级佣金比例</text>
					<text class="config-value">{{ (config.level2Rate * 100).toFixed(1) }}%</text>
				</view>
				
				<view class="config-item">
					<text class="config-label">佣金冻结期</text>
					<text class="config-value">{{ config.freezeDays }}天</text>
				</view>
				
				<view class="config-item">
					<text class="config-label">最低提现金额</text>
					<text class="config-value">¥{{ config.minWithdraw }}</text>
				</view>
				
				<view class="config-item">
					<text class="config-label">最高提现金额</text>
					<text class="config-value">¥{{ config.maxWithdraw }}</text>
				</view>
				
				<view class="config-item">
					<text class="config-label">提现手续费</text>
					<text class="config-value">{{ (config.withdrawFeeRate * 100).toFixed(1) }}%</text>
				</view>
			</view>
		</view>
		
		<!-- 分销规则详情 -->
		<view class="rules-section">
			<view class="rule-item">
				<view class="rule-header">
					<view class="rule-icon">🎯</view>
					<text class="rule-title">如何成为分销员</text>
				</view>
				<view class="rule-content">
					<text class="rule-text">1. 注册并完善个人信息</text>
					<text class="rule-text">2. 提交分销员申请</text>
					<text class="rule-text">3. 等待管理员审核通过</text>
					<text class="rule-text">4. 开始推广获得收益</text>
				</view>
			</view>
			
			<view class="rule-item">
				<view class="rule-header">
					<view class="rule-icon">💰</view>
					<text class="rule-title">佣金计算规则</text>
				</view>
				<view class="rule-content">
					<text class="rule-text">• 一级分销：直接推荐用户购买商品获得{{ (config?.level1Rate * 100 || 0).toFixed(1) }}%佣金</text>
					<text class="rule-text" v-if="config?.level2Rate > 0">• 二级分销：间接推荐用户购买商品获得{{ (config.level2Rate * 100).toFixed(1) }}%佣金</text>
					<text class="rule-text">• 佣金按订单实付金额计算</text>
					<text class="rule-text">• 退款订单将扣除相应佣金</text>
				</view>
			</view>
			
			<view class="rule-item">
				<view class="rule-header">
					<view class="rule-icon">⏰</view>
					<text class="rule-title">佣金结算规则</text>
				</view>
				<view class="rule-content">
					<text class="rule-text">• 订单完成后佣金进入冻结期</text>
					<text class="rule-text">• 冻结期为{{ config?.freezeDays || 0 }}天</text>
					<text class="rule-text">• 冻结期结束后自动解冻可提现</text>
					<text class="rule-text">• 如有售后问题将延长冻结期</text>
				</view>
			</view>
			
			<view class="rule-item">
				<view class="rule-header">
					<view class="rule-icon">💳</view>
					<text class="rule-title">提现规则</text>
				</view>
				<view class="rule-content">
					<text class="rule-text">• 最低提现金额：¥{{ config?.minWithdraw || 0 }}</text>
					<text class="rule-text">• 最高提现金额：¥{{ config?.maxWithdraw || 0 }}</text>
					<text class="rule-text">• 提现手续费：{{ (config?.withdrawFeeRate * 100 || 0).toFixed(1) }}%</text>
					<text class="rule-text">• 提现申请1-3个工作日内审核</text>
					<text class="rule-text">• 审核通过后1个工作日内到账</text>
				</view>
			</view>
			
			<view class="rule-item">
				<view class="rule-header">
					<view class="rule-icon">⚠️</view>
					<text class="rule-title">注意事项</text>
				</view>
				<view class="rule-content">
					<text class="rule-text">• 禁止恶意刷单或虚假交易</text>
					<text class="rule-text">• 禁止通过不正当手段获取佣金</text>
					<text class="rule-text">• 违规行为将被取消分销资格</text>
					<text class="rule-text">• 平台保留最终解释权</text>
				</view>
			</view>
			
			<view class="rule-item">
				<view class="rule-header">
					<view class="rule-icon">📞</view>
					<text class="rule-title">联系客服</text>
				</view>
				<view class="rule-content">
					<text class="rule-text">如有疑问请联系客服</text>
					<view class="contact-info">
						<button class="contact-btn" @click="contactService">
							<text class="contact-icon">💬</text>
							<text class="contact-text">联系客服</text>
						</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getConfig } from '@/api/distribution'

export default {
	data() {
		return {
			config: null
		}
	},
	
	onLoad() {
		this.loadConfig()
	},
	
	methods: {
		// 加载分销配置
		async loadConfig() {
			try {
				const { code, data } = await getConfig()
				if (code === 200 && data) {
					this.config = data
				}
			} catch (error) {
				console.error('loadConfig error:', error)
			}
		},
		
		// 联系客服
		contactService() {
			uni.navigateTo({
				url: '/pages/user/service'
			})
		}
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background: #f8f9fa;
}

// 橙色渐变头部区域
.header-section {
	background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
	padding: 200rpx 30rpx 60rpx;
	position: relative;
	overflow: hidden;

	// 背景装饰效果
	&::before {
		content: '';
		position: absolute;
		top: -100rpx;
		right: -100rpx;
		width: 400rpx;
		height: 400rpx;
		background: rgba(255, 255, 255, 0.1);
		border-radius: 50%;
	}
	
	&::after {
		content: '';
		position: absolute;
		bottom: -80rpx;
		left: -80rpx;
		width: 300rpx;
		height: 300rpx;
		background: rgba(255, 255, 255, 0.05);
		border-radius: 50%;
	}
	
	.header-content {
		position: relative;
		z-index: 2;
		text-align: center;
		
		.page-title {
			display: block;
			font-size: 40rpx;
			font-weight: 700;
			color: #fff;
			margin-bottom: 12rpx;
			text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
		}
		
		.page-subtitle {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			font-weight: 500;
		}
	}
}

// 分销配置区域
.config-section {
	margin: 30rpx;
	background: #fff;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	overflow: hidden;
	
	.config-header {
		padding: 30rpx;
		border-bottom: 1rpx solid #f5f5f5;
		
		.config-title {
			font-size: 32rpx;
			font-weight: 700;
			color: #333;
		}
	}
	
	.config-content {
		padding: 30rpx;
		
		.config-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx 0;
			border-bottom: 1rpx solid #f5f5f5;
			
			&:last-child {
				border-bottom: none;
			}
			
			.config-label {
				font-size: 28rpx;
				color: #666;
				font-weight: 500;
			}
			
			.config-value {
				font-size: 28rpx;
				color: #FF6B35;
				font-weight: 700;
			}
		}
	}
}

// 分销规则区域
.rules-section {
	margin: 0 30rpx 30rpx;
	
	.rule-item {
		background: #fff;
		border-radius: 24rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		border: 1rpx solid #f0f0f0;
		overflow: hidden;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.rule-header {
			display: flex;
			align-items: center;
			padding: 30rpx;
			border-bottom: 1rpx solid #f5f5f5;
			
			.rule-icon {
				width: 60rpx;
				height: 60rpx;
				background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(247, 147, 30, 0.1) 100%);
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 20rpx;
				font-size: 32rpx;
				border: 1rpx solid rgba(255, 107, 53, 0.1);
			}
			
			.rule-title {
				font-size: 30rpx;
				color: #333;
				font-weight: 700;
			}
		}
		
		.rule-content {
			padding: 30rpx;
			
			.rule-text {
				font-size: 26rpx;
				color: #666;
				line-height: 1.6;
				margin-bottom: 16rpx;
				display: block;
				
				&:last-child {
					margin-bottom: 0;
				}
			}
			
			.contact-info {
				margin-top: 20rpx;
				
				.contact-btn {
					display: flex;
					align-items: center;
					justify-content: center;
					background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
					color: #fff;
					border-radius: 16rpx;
					padding: 20rpx 40rpx;
					border: none;
					font-size: 26rpx;
					font-weight: 600;
					box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
					transition: all 0.3s ease;
					
					&:active {
						transform: translateY(2rpx);
						box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3);
					}
					
					.contact-icon {
						margin-right: 12rpx;
						font-size: 28rpx;
					}
					
					.contact-text {
						font-size: 26rpx;
					}
				}
			}
		}
	}
}
</style> 