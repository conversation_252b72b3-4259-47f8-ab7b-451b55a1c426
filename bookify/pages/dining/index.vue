<template>
	<view class="dining-container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="circle circle-1"></view>
			<view class="circle circle-2"></view>
			<view class="circle circle-3"></view>
		</view>
		
		<!-- 导航栏 -->
		<view class="nav-bar">
			<view class="nav-left" @click="goBack">
				<text class="iconfont icon-back">‹</text>
			</view>
			<view class="nav-title">{{pageTitle}}</view>
			<view class="nav-right"></view>
		</view>
		
		<!-- 用餐类型选择 -->
		<view class="dining-types" v-if="!diningType">
			<view class="section-title">选择用餐方式</view>
			<view class="type-list">
				<view 
					class="type-item" 
					v-for="item in diningTypes" 
					:key="item.id"
					@click="selectDiningType(item)"
				>
					<view class="type-icon">
						<image :src="item.icon || '/static/images/dining-default.png'" mode="aspectFit"></image>
					</view>
					<view class="type-info">
						<text class="type-name">{{item.name}}</text>
						<text class="type-desc">{{item.description}}</text>
					</view>
					<view class="type-arrow">
						<text class="iconfont">›</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 餐桌选择 (仅堂食显示) -->
		<view class="table-selection" v-if="diningType === 'dine_in'">
			<view class="section-title">选择餐桌</view>
			<view class="table-grid">
				<view 
					class="table-item" 
					v-for="table in tableList" 
					:key="table.id"
					:class="{
						'occupied': table.status === 2,
						'reserved': table.status === 3,
						'selected': selectedTable && selectedTable.id === table.id
					}"
					@click="selectTable(table)"
				>
					<view class="table-icon">
						<text class="iconfont">🪑</text>
					</view>
					<view class="table-info">
						<text class="table-name">{{table.tableName}}</text>
						<text class="table-capacity">{{table.capacity}}人桌</text>
					</view>
					<view class="table-status">
						<text v-if="table.status === 1" class="status-free">空闲</text>
						<text v-else-if="table.status === 2" class="status-occupied">使用中</text>
						<text v-else-if="table.status === 3" class="status-reserved">已预订</text>
						<text v-else class="status-disabled">禁用</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 商品列表 -->
		<view class="goods-section" v-if="diningType">
			<view class="section-title">选择商品</view>
			<view class="goods-list">
				<view 
					class="goods-item" 
					v-for="item in goodsList" 
					:key="item.id"
					@click="goToGoodsDetail(item)"
				>
					<view class="goods-image">
						<image :src="item.image" mode="aspectFill"></image>
					</view>
					<view class="goods-info">
						<text class="goods-name">{{item.name}}</text>
						<text class="goods-desc">{{item.description}}</text>
						<view class="goods-price">
							<text class="price">¥{{item.price}}</text>
							<text class="original-price" v-if="item.originalPrice">¥{{item.originalPrice}}</text>
						</view>
					</view>
					<view class="goods-action">
						<button class="add-btn" @click.stop="addToCart(item)">加入购物车</button>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-bar" v-if="diningType">
			<view class="cart-info">
				<view class="cart-icon">
					<text class="iconfont">🛒</text>
					<view class="cart-count" v-if="cartCount > 0">{{cartCount}}</view>
				</view>
				<view class="cart-text">
					<text v-if="cartCount > 0">已选{{cartCount}}件商品</text>
					<text v-else>购物车是空的</text>
				</view>
			</view>
			<button class="checkout-btn" :disabled="cartCount === 0" @click="goToCart">
				去结算
			</button>
		</view>
	</view>
</template>

<script>
import { getDiningTypes, getDiningTables } from '@/api/dining'
import { getGoodsList } from '@/api/home'
import { getCartCount, addCart } from '@/api/cart'
import AuthUtils from '@/utils/auth'

export default {
	data() {
		return {
			diningType: '', // 用餐类型
			pageTitle: '堂食外卖',
			diningTypes: [],
			tableList: [],
			selectedTable: null,
			goodsList: [],
			cartCount: 0,
			page: 1,
			pageSize: 20
		}
	},
	
	onLoad(options) {
		if (options.diningType) {
			this.diningType = options.diningType
			this.pageTitle = options.title || (options.diningType === 'dine_in' ? '堂食' : '外卖')
			this.loadGoods()
			if (options.diningType === 'dine_in') {
				this.loadTables()
			}
		} else {
			this.loadDiningTypes()
		}
		this.loadCartCount()
	},
	
	onShow() {
		this.loadCartCount()
	},
	
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},
		
		// 加载用餐类型
		async loadDiningTypes() {
			try {
				const res = await getDiningTypes()
				if (res.code === 200) {
					this.diningTypes = res.data || []
				}
			} catch (error) {
				console.error('加载用餐类型失败:', error)
			}
		},
		
		// 选择用餐类型
		selectDiningType(item) {
			this.diningType = item.code
			this.pageTitle = item.name
			this.loadGoods()
			if (item.code === 'dine_in') {
				this.loadTables()
			}
		},
		
		// 加载餐桌列表
		async loadTables() {
			try {
				const res = await getDiningTables()
				if (res.code === 200) {
					this.tableList = res.data.list || []
				}
			} catch (error) {
				console.error('加载餐桌列表失败:', error)
			}
		},
		
		// 选择餐桌
		selectTable(table) {
			if (table.status !== 1) {
				uni.showToast({
					title: '该餐桌不可用',
					icon: 'none'
				})
				return
			}
			this.selectedTable = table
		},
		
		// 加载商品列表
		async loadGoods() {
			try {
				const res = await getGoodsList({
					page: this.page,
					pageSize: this.pageSize,
					sort: 'sales'
				})
				if (res.code === 200) {
					this.goodsList = res.data.list || []
				}
			} catch (error) {
				console.error('加载商品列表失败:', error)
			}
		},
		
		// 加载购物车数量
		async loadCartCount() {
			if (AuthUtils.checkLoginStatus()) {
				try {
					const res = await getCartCount()
					this.cartCount = res.data || 0
				} catch (error) {
					console.log('获取购物车数量失败:', error)
					this.cartCount = 0
				}
			}
		},
		
		// 跳转商品详情
		goToGoodsDetail(item) {
			uni.navigateTo({
				url: `/pages/goods/detail?id=${item.id}&diningType=${this.diningType}`
			})
		},
		
		// 添加到购物车
		async addToCart(item) {
			if (!AuthUtils.checkLoginStatus()) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				})
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/login/login'
					})
				}, 1500)
				return
			}
			
			try {
				await addCart({
					goodsId: item.id,
					skuCode: item.skuCode || 'default',
					num: 1,
					specs: '{}'
				})
				
				uni.showToast({
					title: '添加成功',
					icon: 'success'
				})
				
				this.loadCartCount()
			} catch (error) {
				uni.showToast({
					title: error.message || '添加失败',
					icon: 'none'
				})
			}
		},
		
		// 跳转购物车
		goToCart() {
			if (this.diningType === 'dine_in' && !this.selectedTable) {
				uni.showToast({
					title: '请先选择餐桌',
					icon: 'none'
				})
				return
			}
			
			// 由于购物车是tabbar页面，需要使用switchTab，但switchTab不支持参数
			// 所以先存储参数到本地存储，然后跳转
			if (this.diningType) {
				uni.setStorageSync('diningType', this.diningType)
				if (this.selectedTable) {
					uni.setStorageSync('selectedTable', {
						id: this.selectedTable.id,
						tableNo: this.selectedTable.tableNo
					})
				}
			}
			
			uni.switchTab({
				url: '/pages/cart/cart'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.dining-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #ff6b35 0%, #ff4757 50%, #ffa726 100%);
	position: relative;
}

.bg-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	overflow: hidden;
	pointer-events: none;
	
	.circle {
		position: absolute;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.1);
		
		&.circle-1 {
			width: 200rpx;
			height: 200rpx;
			top: 10%;
			right: 10%;
			animation: float 6s ease-in-out infinite;
		}
		
		&.circle-2 {
			width: 150rpx;
			height: 150rpx;
			bottom: 20%;
			left: 15%;
			animation: float 8s ease-in-out infinite reverse;
		}
		
		&.circle-3 {
			width: 100rpx;
			height: 100rpx;
			top: 50%;
			left: 5%;
			animation: float 10s ease-in-out infinite;
		}
	}
}

@keyframes float {
	0%, 100% { transform: translateY(0px); }
	50% { transform: translateY(-20px); }
}

.nav-bar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(20px);
	
	.nav-left, .nav-right {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.nav-left {
		.iconfont {
			font-size: 48rpx;
			color: #fff;
		}
	}
	
	.nav-title {
		font-size: 36rpx;
		font-weight: 600;
		color: #fff;
	}
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 30rpx;
	padding: 0 20rpx;
}

.dining-types {
	margin: 20rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 24rpx;
	padding: 30rpx;
	
	.type-list {
		.type-item {
			display: flex;
			align-items: center;
			padding: 30rpx 20rpx;
			border-radius: 16rpx;
			margin-bottom: 20rpx;
			background: #fff;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
			transition: all 0.3s ease;
			
			&:active {
				transform: scale(0.98);
			}
			
			.type-icon {
				width: 80rpx;
				height: 80rpx;
				margin-right: 20rpx;
				
				image {
					width: 100%;
					height: 100%;
				}
			}
			
			.type-info {
				flex: 1;
				
				.type-name {
					display: block;
					font-size: 32rpx;
					font-weight: 600;
					color: #333;
					margin-bottom: 8rpx;
				}
				
				.type-desc {
					display: block;
					font-size: 26rpx;
					color: #666;
				}
			}
			
			.type-arrow {
				.iconfont {
					font-size: 32rpx;
					color: #ccc;
				}
			}
		}
	}
}

.table-selection {
	margin: 20rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 24rpx;
	padding: 30rpx;
	
	.table-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20rpx;
		
		.table-item {
			background: #fff;
			border-radius: 16rpx;
			padding: 20rpx;
			text-align: center;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
			transition: all 0.3s ease;
			
			&.selected {
				border: 2rpx solid #ff6b35;
				background: rgba(255, 107, 53, 0.1);
			}
			
			&.occupied, &.reserved {
				opacity: 0.5;
				background: #f5f5f5;
			}
			
			&:not(.occupied):not(.reserved):active {
				transform: scale(0.95);
			}
			
			.table-icon {
				font-size: 48rpx;
				margin-bottom: 16rpx;
			}
			
			.table-info {
				.table-name {
					display: block;
					font-size: 28rpx;
					font-weight: 600;
					color: #333;
					margin-bottom: 8rpx;
				}
				
				.table-capacity {
					display: block;
					font-size: 24rpx;
					color: #666;
				}
			}
			
			.table-status {
				margin-top: 16rpx;
				
				.status-free {
					color: #4caf50;
					font-size: 24rpx;
				}
				
				.status-occupied {
					color: #f44336;
					font-size: 24rpx;
				}
				
				.status-reserved {
					color: #ff9800;
					font-size: 24rpx;
				}
				
				.status-disabled {
					color: #999;
					font-size: 24rpx;
				}
			}
		}
	}
}

.goods-section {
	margin: 20rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 24rpx;
	padding: 30rpx;
	margin-bottom: 120rpx;
	
	.goods-list {
		.goods-item {
			display: flex;
			align-items: center;
			padding: 20rpx;
			border-radius: 16rpx;
			margin-bottom: 20rpx;
			background: #fff;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
			
			.goods-image {
				width: 120rpx;
				height: 120rpx;
				border-radius: 12rpx;
				overflow: hidden;
				margin-right: 20rpx;
				
				image {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}
			}
			
			.goods-info {
				flex: 1;
				
				.goods-name {
					display: block;
					font-size: 28rpx;
					font-weight: 600;
					color: #333;
					margin-bottom: 8rpx;
				}
				
				.goods-desc {
					display: block;
					font-size: 24rpx;
					color: #666;
					margin-bottom: 12rpx;
				}
				
				.goods-price {
					display: flex;
					align-items: baseline;
					gap: 16rpx;
					
					.price {
						font-size: 32rpx;
						font-weight: 600;
						color: #ff6b35;
					}
					
					.original-price {
						font-size: 24rpx;
						color: #999;
						text-decoration: line-through;
					}
				}
			}
			
			.goods-action {
				.add-btn {
					background: linear-gradient(135deg, #ff6b35, #ff8e53);
					color: #fff;
					border: none;
					border-radius: 20rpx;
					padding: 16rpx 24rpx;
					font-size: 24rpx;
					font-weight: 600;
				}
			}
		}
	}
}

.bottom-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	padding: 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
	
	.cart-info {
		display: flex;
		align-items: center;
		
		.cart-icon {
			position: relative;
			margin-right: 16rpx;
			
			.iconfont {
				font-size: 48rpx;
				color: #ff6b35;
			}
			
			.cart-count {
				position: absolute;
				top: -8rpx;
				right: -8rpx;
				background: #f44336;
				color: #fff;
				font-size: 20rpx;
				min-width: 32rpx;
				height: 32rpx;
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
		
		.cart-text {
			font-size: 28rpx;
			color: #333;
		}
	}
	
	.checkout-btn {
		background: linear-gradient(135deg, #ff6b35, #ff8e53);
		color: #fff;
		border: none;
		border-radius: 24rpx;
		padding: 20rpx 40rpx;
		font-size: 28rpx;
		font-weight: 600;
		
		&:disabled {
			background: #ccc;
		}
	}
}
</style> 