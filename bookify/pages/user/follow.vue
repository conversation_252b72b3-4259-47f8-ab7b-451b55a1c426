<template>
	<view class="follow-container">
		<view class="empty" v-if="!list.length">
			<image src="/static/images/empty.png" mode="aspectFit"></image>
			<text>暂无关注店铺</text>
		</view>
		<view class="shop-list" v-else>
			<view class="shop-item" v-for="item in list" :key="item.id">
				<view class="shop-info">
					<image class="shop-logo" :src="item.logo" mode="aspectFill"></image>
					<view class="shop-detail">
						<text class="name">{{ item.name }}</text>
						<text class="desc">{{ item.desc }}</text>
					</view>
				</view>
				<button class="btn" @click="onUnfollow(item)">取消关注</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			list: [
				{
					id: 1,
					name: '景德镇陶瓷旗舰店',
					desc: '传承千年瓷都文化',
					logo: '/static/images/shop/1.png'
				},
				{
					id: 2,
					name: '苗族手工艺品店',
					desc: '民族特色手工艺品',
					logo: '/static/images/shop/2.png'
				}
			]
		}
	},
	methods: {
		onUnfollow(shop) {
			uni.showModal({
				title: '提示',
				content: '确定取消关注该店铺吗？',
				success: (res) => {
					if (res.confirm) {
						this.list = this.list.filter(item => item.id !== shop.id)
					}
				}
			})
		}
	}
}
</script>

<style lang="scss">
.follow-container {
	min-height: 100vh;
	background: #f5f5f5;
	padding: 20rpx;
	
	.empty {
		padding-top: 200rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		
		image {
			width: 200rpx;
			height: 200rpx;
			margin-bottom: 20rpx;
		}
		
		text {
			font-size: 28rpx;
			color: #999;
		}
	}
	
	.shop-list {
		.shop-item {
			margin-bottom: 20rpx;
			background: #fff;
			border-radius: 12rpx;
			padding: 30rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			
			.shop-info {
				display: flex;
				align-items: center;
				
				.shop-logo {
					width: 80rpx;
					height: 80rpx;
					border-radius: 12rpx;
					margin-right: 20rpx;
				}
				
				.shop-detail {
					.name {
						font-size: 28rpx;
						color: #333;
						margin-bottom: 10rpx;
						display: block;
					}
					
					.desc {
						font-size: 24rpx;
						color: #999;
					}
				}
			}
			
			.btn {
				font-size: 24rpx;
				color: #666;
				background: #ffffff;
				padding: 10rpx 30rpx;
				border-radius: 30rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				text-align: center;
				border: 2rpx solid #e0e0e0;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
				transition: all 0.3s ease;
				
				&:active {
					background: #f5f5f5;
					transform: scale(0.98);
				}
			}
		}
	}
}
</style> 