<template>
	<view class="vip-container">
		<view class="vip-card">
			<view class="card-header">
				<text class="level">LV.{{ userInfo.level }}</text>
				<text class="expire">有效期至: 2024-12-31</text>
			</view>
			<view class="card-body">
				<text class="title">会员特权</text>
				<view class="privilege-list">
					<view class="privilege-item" v-for="item in privileges" :key="item.id">
						<text class="iconfont" :class="item.icon"></text>
						<text class="name">{{ item.name }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			userInfo: {
				level: 1
			},
			privileges: [
				{
					id: 1,
					name: '专属客服',
					icon: 'icon-kefu'
				},
				{
					id: 2,
					name: '会员折扣',
					icon: 'icon-discount'
				},
				{
					id: 3,
					name: '生日礼包',
					icon: 'icon-gift'
				},
				{
					id: 4,
					name: '积分加倍',
					icon: 'icon-points'
				}
			]
		}
	}
}
</script>

<style lang="scss">
@import "@/styles/beauty.scss";
@import "@/styles/common.scss";

.vip-container {
	min-height: 100vh;
	background: #f5f5f5;
	padding: 30rpx;
	
	.vip-card {
		background: linear-gradient(135deg, $primary-color, $primary-dark);
		border-radius: 20rpx;
		padding: 40rpx;
		color: #fff;
		box-shadow: $box-shadow-base;
		
		.card-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 60rpx;
			
			.level {
				font-size: 48rpx;
				font-weight: bold;
				text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
			}
			
			.expire {
				font-size: 24rpx;
				opacity: 0.8;
			}
		}
		
		.card-body {
			.title {
				font-size: 32rpx;
				margin-bottom: 30rpx;
				display: block;
			}
			
			.privilege-list {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				gap: 30rpx;
				
				.privilege-item {
					display: flex;
					align-items: center;
					
					.iconfont {
						font-size: 40rpx;
						margin-right: 20rpx;
						text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
					}
					
					.name {
						font-size: 28rpx;
					}
				}
			}
		}
	}
}
</style>
