<template>
	<view class="footprint-page">
		<!-- 清空按钮 -->
		<view class="clear-btn" v-if="list.length > 0" @click="clearAll">
			<text class="iconfont icon-delete"></text>
			<text>清空足迹</text>
		</view>
		
		<!-- 商品列表 -->
		<view class="goods-list" v-if="list.length > 0">
			<view class="date-group" v-for="(group, date) in groupedList" :key="date">
				<text class="date">{{formatDate(date)}}</text>
				<view class="goods-item" v-for="item in group" :key="item.id" @click="viewGoods(item)">
					<image class="goods-img" :src="item.image" mode="aspectFill"></image>
					<view class="goods-info">
						<text class="title">{{item.name}}</text>
						<text class="price">¥{{item.price}}</text>
						<view class="action">
							<text class="time">{{formatTime(item.viewTime)}}</text>
							<text class="delete" @click.stop="deleteItem(item)">删除</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty" v-else>
			<image src="/static/images/empty-cart.png" mode="aspectFit"></image>
			<text>暂无浏览记录</text>
			<button class="go-shop" @click="goShop">去逛逛</button>
		</view>
	</view>
</template>

<script>
import { getFootprintList, deleteFootprint, clearFootprint } from '@/api/user'

export default {
	data() {
		return {
			page: 1,
			size: 10,
			list: [],
			loading: false,
			finished: false
		}
	},
	computed: {
		// 按日期分组的列表
		groupedList() {
			const groups = {}
			this.list.forEach(item => {
				const date = item.viewTime.split(' ')[0]
				if (!groups[date]) {
					groups[date] = []
				}
				groups[date].push(item)
			})
			return groups
		}
	},
	onLoad() {
		this.loadData()
	},
	onPullDownRefresh() {
		this.page = 1
		this.finished = false
		this.list = []
		this.loadData().then(() => {
			uni.stopPullDownRefresh()
		})
	},
	onReachBottom() {
		if (!this.loading && !this.finished) {
			this.page++
			this.loadData()
		}
	},
	methods: {
		// 加载数据
		async loadData() {
			if (this.loading || this.finished) return
			
			this.loading = true
			try {
				const { code, data } = await getFootprintList({
					page: this.page,
					size: this.size
				})
				
				if (code === 200) {
					if (this.page === 1) {
						this.list = data.list
					} else {
						this.list = [...this.list, ...data.list]
					}
					
					// 判断是否加载完毕
					if (data.list.length < this.size) {
						this.finished = true
					}
				}
			} catch (e) {
				console.error('加载足迹列表失败:', e)
			} finally {
				this.loading = false
			}
		},
		
		// 查看商品
		viewGoods(item) {
			uni.navigateTo({
				url: `/pages/goods/detail?id=${item.goodsId}`
			})
		},
		
		// 删除足迹
		deleteItem(item) {
			uni.showModal({
				title: '提示',
				content: '确定要删除该足迹吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							const { code } = await deleteFootprint(item.id)
							if (code === 200) {
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								})
								// 从列表中移除
								const index = this.list.findIndex(i => i.id === item.id)
								if (index > -1) {
									this.list.splice(index, 1)
								}
							}
						} catch (e) {
							console.error('删除足迹失败:', e)
							uni.showToast({
								title: '删除失败',
								icon: 'none'
							})
						}
					}
				}
			})
		},
		
		// 清空足迹
		clearAll() {
			uni.showModal({
				title: '提示',
				content: '确定要清空所有足迹吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							const { code } = await clearFootprint()
							if (code === 200) {
								uni.showToast({
									title: '清空成功',
									icon: 'success'
								})
								this.list = []
							}
						} catch (e) {
							console.error('清空足迹失败:', e)
							uni.showToast({
								title: '清空失败',
								icon: 'none'
							})
						}
					}
				}
			})
		},
		
		// 去逛逛
		goShop() {
			uni.switchTab({
				url: '/pages/index/index'
			})
		},
		
		// 格式化日期
		formatDate(date) {
			const today = new Date().toISOString().split('T')[0]
			const yesterday = new Date(Date.now() - 86400000).toISOString().split('T')[0]
			
			if (date === today) {
				return '今天'
			} else if (date === yesterday) {
				return '昨天'
			}
			return date
		},
		
		// 格式化时间
		formatTime(time) {
			return time.split(' ')[1].substring(0, 5)
		}
	}
}
</script>

<style lang="scss">
.footprint-page {
	min-height: 100vh;
	background: #f5f5f5;
	padding: 20rpx;
	
	.clear-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 80rpx;
		background: #fff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		
		text {
			font-size: 28rpx;
			color: #666;
			
			&.iconfont {
				margin-right: 10rpx;
			}
		}
	}
	
	.goods-list {
		.date-group {
			margin-bottom: 20rpx;
			
			.date {
				display: block;
				font-size: 28rpx;
				color: #999;
				padding: 20rpx 0;
			}
			
			.goods-item {
				display: flex;
				background: #fff;
				padding: 20rpx;
				border-radius: 12rpx;
				margin-bottom: 20rpx;
				
				.goods-img {
					width: 200rpx;
					height: 200rpx;
					margin-right: 20rpx;
					border-radius: 8rpx;
				}
				
				.goods-info {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					
					.title {
						font-size: 28rpx;
						color: #333;
						line-height: 1.4;
						margin-bottom: 10rpx;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
						overflow: hidden;
					}
					
					.price {
						font-size: 32rpx;
						color: #ff6b6b;
						font-weight: bold;
					}
					
					.action {
						display: flex;
						justify-content: space-between;
						align-items: center;
						
						.time {
							font-size: 24rpx;
							color: #999;
						}
						
						.delete {
							font-size: 24rpx;
							color: #666;
							padding: 4rpx 20rpx;
							border: 1px solid #ddd;
							border-radius: 24rpx;
						}
					}
				}
			}
		}
	}
	
	.empty {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 200rpx;
		
		image {
			width: 240rpx;
			height: 240rpx;
			margin-bottom: 30rpx;
		}
		
		text {
			font-size: 28rpx;
			color: #999;
			margin-bottom: 40rpx;
		}
		
		.go-shop {
			width: 240rpx;
			height: 80rpx;
			line-height: 80rpx;
			font-size: 28rpx;
			color: #fff;
			background: #ff6b6b;
			border-radius: 40rpx;
			
			&::after {
				border: none;
			}
		}
	}
}
</style> 