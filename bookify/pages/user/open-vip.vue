<template>
	<view class="open-vip-container">
		<view class="vip-intro">
			<image src="/static/images/vip-banner.png" mode="aspectFill"></image>
			<view class="intro-text">
				<text class="title">开通会员 享专属特权</text>
				<text class="desc">解锁更多会员权益</text>
			</view>
		</view>
		
		<view class="price-list">
			<view 
				class="price-item" 
				v-for="item in priceList" 
				:key="item.id"
				:class="{ active: selectedId === item.id }"
				@click="selectedId = item.id"
			>
				<view class="duration">{{ item.name }}</view>
				<view class="price">
					<text class="symbol">¥</text>
					<text class="num">{{ item.price }}</text>
				</view>
				<view class="original" v-if="item.original">原价¥{{ item.original }}</view>
			</view>
		</view>
		
		<view class="privilege-list">
			<view class="title">会员特权</view>
			<view class="privilege-item" v-for="item in privileges" :key="item.id">
				<text class="iconfont" :class="item.icon"></text>
				<view class="info">
					<text class="name">{{ item.name }}</text>
					<text class="desc">{{ item.desc }}</text>
				</view>
			</view>
		</view>
		
		<view class="bottom-bar">
			<view class="price">
				<text class="symbol">¥</text>
				<text class="num">{{ currentPrice }}</text>
			</view>
			<button class="submit-btn" @click="onSubmit">立即开通</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			selectedId: 1,
			priceList: [
				{
					id: 1,
					name: '月卡',
					price: '18.00',
					original: '30.00'
				},
				{
					id: 2,
					name: '季卡',
					price: '48.00',
					original: '90.00'
				},
				{
					id: 3,
					name: '年卡',
					price: '168.00',
					original: '360.00'
				}
			],
			privileges: [
				{
					id: 1,
					name: '专属客服',
					desc: '享受一对一贴心服务',
					icon: 'icon-kefu'
				},
				{
					id: 2,
					name: '会员折扣',
					desc: '全场商品9折优惠',
					icon: 'icon-discount'
				},
				{
					id: 3,
					name: '生日礼包',
					desc: '生日当月送专属礼包',
					icon: 'icon-gift'
				},
				{
					id: 4,
					name: '积分加倍',
					desc: '购物积分2倍累计',
					icon: 'icon-points'
				}
			]
		}
	},
	computed: {
		currentPrice() {
			const item = this.priceList.find(item => item.id === this.selectedId)
			return item ? item.price : '0.00'
		}
	},
	methods: {
		onSubmit() {
			uni.showModal({
				title: '提示',
				content: '确认开通会员吗？',
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: '支付中...'
						})
						
						setTimeout(() => {
							uni.hideLoading()
							uni.showToast({
								title: '开通成功',
								icon: 'success'
							})
							setTimeout(() => {
								uni.navigateBack()
							}, 1500)
						}, 1000)
					}
				}
			})
		}
	}
}
</script>

<style lang="scss">
.open-vip-container {
	min-height: 100vh;
	background: #f5f5f5;
	padding-bottom: 120rpx;
	
	.vip-intro {
		position: relative;
		
		image {
			width: 100%;
			height: 400rpx;
		}
		
		.intro-text {
			position: absolute;
			left: 40rpx;
			bottom: 40rpx;
			color: #fff;
			
			.title {
				font-size: 48rpx;
				font-weight: bold;
				margin-bottom: 20rpx;
				display: block;
				background: var(--gradient-primary);
				-webkit-background-clip: text;
				color: transparent;
			}
			
			.desc {
				font-size: 28rpx;
				opacity: 0.9;
			}
		}
	}
	
	.price-list {
		margin: 30rpx;
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 20rpx;
		
		.price-item {
			background: #fff;
			border-radius: 12rpx;
			padding: 30rpx 20rpx;
			text-align: center;
			border: 2rpx solid transparent;
			box-shadow: var(--card-shadow);
			
			&.active {
				background: var(--gradient-secondary);
				color: #fff;
				
				.price {
					color: #fff;
				}
				
				.original {
					color: rgba(255,255,255,0.8);
				}
			}
			
			.duration {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 20rpx;
			}
			
			.price {
				color: var(--primary-color);
				margin-bottom: 10rpx;
				
				.symbol {
					font-size: 24rpx;
				}
				
				.num {
					font-size: 40rpx;
					font-weight: bold;
				}
			}
			
			.original {
				font-size: 24rpx;
				color: #999;
				text-decoration: line-through;
			}
		}
	}
	
	.privilege-list {
		margin: 30rpx;
		background: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		box-shadow: var(--card-shadow);
		
		.title {
			font-size: 32rpx;
			color: #333;
			font-weight: bold;
			margin-bottom: 30rpx;
		}
		
		.privilege-item {
			display: flex;
			align-items: center;
			margin-bottom: 30rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.iconfont {
				font-size: 40rpx;
				color: var(--primary-color);
				margin-right: 20rpx;
				text-shadow: 0 2rpx 4rpx rgba(0,170,255,0.2);
			}
			
			.info {
				flex: 1;
				
				.name {
					font-size: 28rpx;
					color: #333;
					margin-bottom: 6rpx;
					display: block;
				}
				
				.desc {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
	}
	
	.bottom-bar {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		background: #fff;
		padding: 20rpx 30rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		
		.price {
			color: var(--primary-color);
			
			.symbol {
				font-size: 28rpx;
			}
			
			.num {
				font-size: 48rpx;
				font-weight: bold;
			}
		}
		
		.submit-btn {
			width: 240rpx;
			height: 80rpx;
			background: var(--primary-color);
			color: #fff;
			border-radius: 40rpx;
			font-size: 28rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
}
</style> 