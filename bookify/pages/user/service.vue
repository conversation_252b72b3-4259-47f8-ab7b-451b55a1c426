<template>
	<PageLayout 
		:show-left="true" 
		:show-center="true" 
		:show-right="false"
		nav-title="客服中心"
		nav-text-color="#333"
		:nav-transparent="false"
	>
		<view class="service-container">
			<!-- 联系客服区域 -->
			<view class="contact-section">
				<view class="section-title">
					<text class="title-icon">💬</text>
					<text class="title-text">联系客服</text>
				</view>
				
				<view class="contact-list">
					<view class="contact-item" @click="onPhoneCall">
						<view class="contact-left">
							<view class="contact-icon phone">
								<text class="icon">📞</text>
							</view>
							<view class="contact-info">
								<text class="contact-label">客服电话</text>
								<text class="contact-time">{{ servicePhone || '客服电话获取中...' }}</text>
							</view>
						</view>
						<text class="contact-arrow">></text>
					</view>
					
					<view class="contact-item" @click="onWechatService">
						<view class="contact-left">
							<view class="contact-icon wechat">
								<text class="icon">💬</text>
							</view>
							<view class="contact-info">
								<text class="contact-label">微信客服</text>
								<text class="contact-time">扫码添加客服微信</text>
							</view>
						</view>
						<text class="contact-arrow">></text>
					</view>
				</view>
			</view>
			
			<!-- 意见反馈区域 -->
			<view class="feedback-section">
				<view class="section-title">
					<text class="title-icon">📝</text>
					<text class="title-text">意见反馈</text>
				</view>
				
				<view class="feedback-form">
					<textarea 
						class="feedback-input" 
						v-model="feedbackText"
						placeholder="请输入您的意见或建议，我们会认真对待每一条反馈..."
						maxlength="500"
					></textarea>
					<view class="feedback-actions">
						<text class="char-count">{{ feedbackText.length }}/500</text>
						<button class="submit-btn" @click="submitFeedback" :disabled="!feedbackText.trim()">
							提交反馈
						</button>
					</view>
				</view>
			</view>
			
			<!-- 微信二维码弹窗 -->
			<uni-popup ref="qrcodePopup" type="center" :mask-click="true">
				<view class="qrcode-popup">
					<view class="popup-header">
						<text class="popup-title">微信客服</text>
						<text class="close-btn" @click="closeQrcodePopup">×</text>
					</view>
					<view class="popup-content">
						<image 
							v-if="serviceQrcode" 
							:src="serviceQrcode" 
							class="qrcode-image"
							mode="aspectFit"
						></image>
						<view v-else class="qrcode-loading">
							<text>二维码加载中...</text>
						</view>
						<text class="qrcode-tip">长按识别二维码添加客服微信</text>
					</view>
				</view>
			</uni-popup>
		</view>
	</PageLayout>
</template>

<script>
import PageLayout from '@/components/layout/PageLayout.vue'
import { getShopConfigs } from '@/api/config'
import { submitFeedback } from '@/api/feedback'

export default {
	components: {
		PageLayout
	},
	
	data() {
		return {
			feedbackText: '',
			servicePhone: '', // 客服电话
			serviceQrcode: '' // 客服二维码
		}
	},
	
	onLoad() {
		this.loadServiceConfig()
	},
	
	methods: {
		// 加载客服配置
		async loadServiceConfig() {
			try {
				const response = await getShopConfigs(['shop.service.phone', 'shop.service.qrcode'])
				console.log('客服配置响应:', response)
				if (response.code === 200 && response.data && response.data.configs) {
					this.servicePhone = response.data.configs['shop.service.phone'] || ''
					this.serviceQrcode = response.data.configs['shop.service.qrcode'] || ''
					console.log('客服电话:', this.servicePhone)
					console.log('客服二维码:', this.serviceQrcode)
				}
			} catch (error) {
				console.error('加载客服配置失败:', error)
				// 使用默认值
				this.servicePhone = '************'
			}
		},
		
		// 电话客服
		onPhoneCall() {
			if (!this.servicePhone) {
				uni.showToast({
					title: '客服电话获取中，请稍后重试',
					icon: 'none'
				})
				return
			}
			
			uni.showModal({
				title: '拨打客服电话',
				content: `客服热线：${this.servicePhone}\n服务时间：9:00-21:00`,
				confirmText: '拨打电话',
				success: (res) => {
					if (res.confirm) {
						// 去除电话号码中的特殊字符
						const phoneNumber = this.servicePhone.replace(/[^0-9]/g, '')
						uni.makePhoneCall({
							phoneNumber: phoneNumber
						})
					}
				}
			})
		},
		
		// 微信客服
		onWechatService() {
			if (!this.serviceQrcode) {
				uni.showToast({
					title: '二维码获取中，请稍后重试',
					icon: 'none'
				})
				return
			}
			
			// 显示二维码弹窗
			this.$refs.qrcodePopup.open()
		},
		
		// 关闭二维码弹窗
		closeQrcodePopup() {
			this.$refs.qrcodePopup.close()
		},
		
		// 提交反馈
		async submitFeedback() {
			if (!this.feedbackText.trim()) {
				uni.showToast({
					title: '请输入反馈内容',
					icon: 'none'
				})
				return
			}
			
			try {
				// 调用反馈API
				const response = await submitFeedback({ 
					content: this.feedbackText.trim()
				})
				
				if (response.code === 200) {
					uni.showToast({
						title: '反馈提交成功，感谢您的建议！',
						icon: 'success',
						duration: 2000
					})
					
					this.feedbackText = ''
				} else {
					uni.showToast({
						title: response.message || '提交失败，请重试',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('提交反馈失败:', error)
				uni.showToast({
					title: '提交失败，请重试',
					icon: 'none'
				})
			}
		}
	}
}
</script>

<style lang="scss">
.service-container {
	padding: 20rpx;
	background: #f8f9fa;
	min-height: 100vh;
}

// 区域标题
.section-title {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
	
	.title-icon {
		font-size: 32rpx;
		margin-right: 12rpx;
	}
	
	.title-text {
		font-size: 32rpx;
		font-weight: 700;
		color: #333;
	}
}

// 联系客服区域
.contact-section {
	margin-bottom: 30rpx;
	padding: 30rpx;
	background: #fff;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	
	.contact-list {
		.contact-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 24rpx 0;
			border-bottom: 1rpx solid #f0f0f0;
			
			&:last-child {
				border-bottom: none;
			}
			
			&:active {
				background: rgba(255, 107, 53, 0.05);
				border-radius: 12rpx;
				margin: 0 -12rpx;
				padding: 24rpx 12rpx;
			}
			
			.contact-left {
				display: flex;
				align-items: center;
				flex: 1;
				
				.contact-icon {
					width: 60rpx;
					height: 60rpx;
					border-radius: 30rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 20rpx;
					
					&.phone {
						background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
					}
					
					&.wechat {
						background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
					}
					
					.icon {
						font-size: 28rpx;
						color: #fff;
					}
				}
				
				.contact-info {
					display: flex;
					flex-direction: column;
					
					.contact-label {
						font-size: 30rpx;
						font-weight: 600;
						color: #333;
						margin-bottom: 6rpx;
					}
					
					.contact-time {
						font-size: 24rpx;
						color: #666;
					}
				}
			}
			
			.contact-arrow {
				font-size: 24rpx;
				color: #ccc;
				font-weight: bold;
			}
		}
	}
}

// 意见反馈区域
.feedback-section {
	padding: 30rpx;
	background: #fff;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	
	.feedback-form {
		.feedback-input {
			width: 100%;
			min-height: 200rpx;
			padding: 20rpx;
			border: 2rpx solid #f0f0f0;
			border-radius: 16rpx;
			font-size: 26rpx;
			line-height: 1.6;
			box-sizing: border-box;
			resize: none;
			
			&:focus {
				border-color: #FF6B35;
			}
		}
		
		.feedback-actions {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 20rpx;
			
			.char-count {
				font-size: 24rpx;
				color: #999;
			}
			
			.submit-btn {
				background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
				color: #fff;
				border: none;
				border-radius: 24rpx;
				padding: 16rpx 32rpx;
				font-size: 26rpx;
				font-weight: 700;
				box-shadow: 0 6rpx 16rpx rgba(255, 107, 53, 0.3);
				
				&:disabled {
					background: #ccc;
					box-shadow: none;
				}
				
				&:not(:disabled):active {
					transform: scale(0.95);
				}
			}
		}
	}
}

// 二维码弹窗样式
.qrcode-popup {
	width: 600rpx;
	background: #fff;
	border-radius: 24rpx;
	overflow: hidden;
	
	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		
		.popup-title {
			font-size: 32rpx;
			font-weight: 700;
			color: #333;
		}
		
		.close-btn {
			font-size: 40rpx;
			color: #999;
			line-height: 1;
		}
	}
	
	.popup-content {
		padding: 40rpx;
		text-align: center;
		
		.qrcode-image {
			width: 400rpx;
			height: 400rpx;
			border-radius: 16rpx;
			margin: 0 auto 30rpx auto;
		}
		
		.qrcode-loading {
			width: 400rpx;
			height: 400rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: #f5f5f5;
			border-radius: 16rpx;
			margin-bottom: 30rpx;
			
			text {
				color: #999;
				font-size: 28rpx;
			}
		}
		
		.qrcode-tip {
			font-size: 26rpx;
			color: #666;
			line-height: 1.5;
		}
	}
}
</style> 