<template>
	<PageLayout nav-title="修改密码" :show-left="true" :show-center="true" :nav-fixed="false" :nav-transparent="false">
		<view class="password-container">
			<view class="password-form">
			
				<view class="form-content">
					<view class="input-group">
						<text class="input-label">新密码</text>
						<view class="input-wrapper">
							<input 
								class="input-field" 
								type="password" 
								v-model="passwordForm.newPassword"
								placeholder="请输入新密码（6-20位）"
								:password="!showNewPassword"
							/>
							<text class="eye-icon" @click="showNewPassword = !showNewPassword">
								{{showNewPassword ? '👁️' : '👁️‍🗨️'}}
							</text>
						</view>
					</view>
					
					<view class="input-group">
						<text class="input-label">确认密码</text>
						<view class="input-wrapper">
							<input 
								class="input-field" 
								type="password" 
								v-model="passwordForm.confirmPassword"
								placeholder="请再次输入新密码"
								:password="!showConfirmPassword"
							/>
							<text class="eye-icon" @click="showConfirmPassword = !showConfirmPassword">
								{{showConfirmPassword ? '👁️' : '👁️‍🗨️'}}
							</text>
						</view>
					</view>
					
					<view class="password-tips">
						<text class="tip-title">密码要求：</text>
						<text class="tip-text">• 长度6-20位</text>
						<text class="tip-text">• 建议包含字母、数字</text>
						<text class="tip-text">• 使用安全性较高的密码</text>
					</view>
					
					<view class="submit-btn" @click="confirmChangePassword">
						<text class="btn-text">确认修改</text>
					</view>
				</view>
			</view>
		</view>
	</PageLayout>
</template>

<script>
import PageLayout from '@/components/layout/PageLayout.vue'
import { updatePassword as changePassword } from '@/api/user.js'

export default {
	components: {
		PageLayout
	},
	
	data() {
		return {
			passwordForm: {
				newPassword: '',
				confirmPassword: ''
			},
			showNewPassword: false,
			showConfirmPassword: false
		}
	},
	
	methods: {
		// 确认修改密码
		async confirmChangePassword() {
			// 表单验证
			if (!this.passwordForm.newPassword) {
				uni.showToast({
					title: '请输入新密码',
					icon: 'none'
				})
				return
			}
			
			if (this.passwordForm.newPassword.length < 6 || this.passwordForm.newPassword.length > 20) {
				uni.showToast({
					title: '密码长度应为6-20位',
					icon: 'none'
				})
				return
			}
			
			if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
				uni.showToast({
					title: '两次输入的密码不一致',
					icon: 'none'
				})
				return
			}
			
			try {
				uni.showLoading({
					title: '修改中...'
				})
				
				const result = await changePassword({
					newPassword: this.passwordForm.newPassword
				})
				
				if (result.code === 200) {
					uni.showToast({
						title: '密码修改成功',
						icon: 'success'
					})
					
					// 提示重新登录
					setTimeout(() => {
						uni.showModal({
							title: '提示',
							content: '密码已修改，请重新登录',
							showCancel: false,
							success: () => {
								// 清除登录信息
								uni.removeStorageSync('token')
								uni.removeStorageSync('userInfo')
								
								// 跳转到登录页
								uni.reLaunch({
									url: '/pages/login/login'
								})
							}
						})
					}, 1000)
				} else {
					uni.showToast({
						title: result.message || '修改失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('修改密码失败:', error)
				uni.showToast({
					title: '修改失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		}
	}
}
</script>

<style lang="scss">
.password-container {
	padding: 40rpx 30rpx;
	min-height: calc(100vh - 120rpx);
	background: #f8f9fa;
}

.password-form {
	background: #fff;
	border-radius: 24rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.form-header {
	padding: 60rpx 40rpx 40rpx;
	text-align: center;
	background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
	color: #fff;
	
	.title {
		display: block;
		font-size: 36rpx;
		font-weight: 600;
		margin-bottom: 16rpx;
	}
	
	.subtitle {
		display: block;
		font-size: 26rpx;
		opacity: 0.9;
		line-height: 1.4;
	}
}

.form-content {
	padding: 50rpx 40rpx 60rpx;
}

.input-group {
	margin-bottom: 40rpx;
	
	.input-label {
		display: block;
		font-size: 28rpx;
		color: #333;
		margin-bottom: 20rpx;
		font-weight: 500;
	}
	
	.input-wrapper {
		position: relative;
		
		.input-field {
			width: 100%;
			height: 100rpx;
			padding: 0 80rpx 0 24rpx;
			border: 2rpx solid #e5e5e5;
			border-radius: 16rpx;
			font-size: 28rpx;
			color: #333;
			box-sizing: border-box;
			background: #fafafa;
			transition: all 0.3s ease;
			
			&:focus {
				border-color: #FF6B35;
				background: #fff;
				box-shadow: 0 0 0 4rpx rgba(255, 107, 53, 0.1);
			}
		}
		
		.eye-icon {
			position: absolute;
			right: 24rpx;
			top: 50%;
			transform: translateY(-50%);
			width: 48rpx;
			height: 48rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 32rpx;
			color: #999;
			border-radius: 50%;
			transition: all 0.2s ease;
			
			&:active {
				background: #f0f0f0;
				transform: translateY(-50%) scale(0.95);
			}
		}
	}
}

.password-tips {
	background: #f8f9fa;
	padding: 30rpx;
	border-radius: 16rpx;
	margin-bottom: 50rpx;
	border-left: 6rpx solid #FF6B35;
	
	.tip-title {
		display: block;
		font-size: 26rpx;
		color: #333;
		font-weight: 600;
		margin-bottom: 16rpx;
	}
	
	.tip-text {
		display: block;
		font-size: 24rpx;
		color: #666;
		line-height: 1.6;
		margin-bottom: 10rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
	}
}

.submit-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.3);
	transition: all 0.3s ease;
	
	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.4);
	}
	
	.btn-text {
		font-size: 32rpx;
		color: #fff;
		font-weight: 600;
		letter-spacing: 2rpx;
	}
}
</style> 