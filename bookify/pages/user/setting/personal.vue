<template>
	<PageLayout 
		nav-title="个人信息"
		:show-left="true"
		:show-center="true"
		:nav-fixed="false"
		:nav-transparent="false"
	>
		<view class="personal-container">
			<!-- 头像 -->
			<view class="info-item">
				<button class="avatar-button" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
					<view class="item-content">
						<text class="label">头像</text>
						<view class="value-section">
							<image class="avatar" :src="userInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
							<text class="arrow">›</text>
						</view>
					</view>
				</button>
			</view>
			
			<!-- 昵称 -->
			<view class="info-item">
				<view class="item-content">
					<text class="label">昵称</text>
					<view class="value-section">
						<input 
							type="nickname" 
							v-model="userInfo.nickname" 
							placeholder="请输入昵称" 
							class="nickname-input"
							@blur="onNicknameBlur"
						/>
						<text class="arrow">›</text>
					</view>
				</view>
			</view>
			
			<!-- 手机号 -->
			<view class="info-item" @click="onPhoneChange">
				<view class="item-content">
					<text class="label">手机号</text>
					<view class="value-section">
						<text class="value">{{userInfo.phone || '未绑定'}}</text>
						<text class="arrow">›</text>
					</view>
				</view>
			</view>
			
			<!-- 生日 -->
			<view class="info-item" @click="onBirthdayChange">
				<view class="item-content">
					<text class="label">生日</text>
					<view class="value-section">
						<text class="value">{{userInfo.birthday || '未设置'}}</text>
						<text class="arrow">›</text>
					</view>
				</view>
			</view>
			
			<!-- 性别 -->
			<view class="info-item" @click="onGenderChange">
				<view class="item-content">
					<text class="label">性别</text>
					<view class="value-section">
						<text class="value">{{getGenderText(userInfo.gender)}}</text>
						<text class="arrow">›</text>
					</view>
				</view>
			</view>
		</view>
		

		
		<!-- 生日选择弹窗 -->
		<uni-popup ref="birthdayPopup" type="bottom">
			<view class="birthday-picker">
				<view class="picker-header">
					<text class="cancel-btn" @click="closeBirthdayPopup">取消</text>
					<text class="title">选择生日</text>
					<text class="confirm-btn" @click="confirmBirthday">确定</text>
				</view>
				<picker-view class="picker-view" :value="birthdayValue" @change="onBirthdayPickerChange">
					<picker-view-column>
						<view v-for="(year, index) in years" :key="index" class="picker-item">{{year}}年</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="(month, index) in months" :key="index" class="picker-item">{{month}}月</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="(day, index) in days" :key="index" class="picker-item">{{day}}日</view>
					</picker-view-column>
				</picker-view>
			</view>
		</uni-popup>
		
		<!-- 性别选择弹窗 -->
		<uni-popup ref="genderPopup" type="bottom">
			<view class="gender-picker">
				<view class="picker-header">
					<text class="cancel-btn" @click="closeGenderPopup">取消</text>
					<text class="title">选择性别</text>
					<text class="confirm-btn" @click="confirmGender">确定</text>
				</view>
				<view class="gender-options">
					<view class="gender-option" :class="{active: tempGender === 1}" @click="tempGender = 1">
						<text class="gender-text">男</text>
						<text class="check-icon" v-if="tempGender === 1">✓</text>
					</view>
					<view class="gender-option" :class="{active: tempGender === 2}" @click="tempGender = 2">
						<text class="gender-text">女</text>
						<text class="check-icon" v-if="tempGender === 2">✓</text>
					</view>
					<view class="gender-option" :class="{active: tempGender === 0}" @click="tempGender = 0">
						<text class="gender-text">保密</text>
						<text class="check-icon" v-if="tempGender === 0">✓</text>
					</view>
				</view>
			</view>
		</uni-popup>
	</PageLayout>
</template>

<script>
import PageLayout from '@/components/layout/PageLayout.vue'
import { updateUserInfo, uploadAvatar } from '@/api/user.js'

export default {
	components: {
		PageLayout
	},
	
	data() {
		return {
			userInfo: uni.getStorageSync('userInfo') || {},
			tempGender: 0,
			birthdayValue: [0, 0, 0],
			years: [],
			months: [],
			days: []
		}
	},
	

	
	onLoad() {
		this.initBirthdayPicker()
	},
	
	methods: {
		// 处理微信头像选择
		onChooseAvatar(e) {
			console.log('头像选择事件触发:', e);
			const { avatarUrl } = e.detail;
			if (avatarUrl) {
				console.log('获取到头像URL:', avatarUrl);
				this.handleUploadAvatar(avatarUrl);
			} else {
				console.log('未获取到头像URL');
				uni.showToast({
					title: '未选择头像',
					icon: 'none'
				});
			}
		},
		

		
		// 上传头像
		async handleUploadAvatar(filePath) {
			try {
				uni.showLoading({
					title: '上传中...'
				})
				
				const res = await uploadAvatar(filePath)
				console.log('上传头像响应:', res)
				
				if (res.code === 200) {
					// 根据后端实际返回的数据结构来获取URL
					const avatarUrl = res.data?.url || res.data?.avatar || res.data
					if (avatarUrl) {
						this.userInfo.avatar = avatarUrl
						this.updateUserStorage()
						uni.showToast({
							title: '头像更新成功',
							icon: 'success'
						})
					} else {
						throw new Error('未获取到头像URL')
					}
				} else {
					throw new Error(res.message || res.msg || '上传失败')
				}
			} catch (e) {
				console.error('上传头像失败:', e)
				uni.showToast({
					title: e.message || '上传失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},
		
		// 处理昵称失去焦点
		onNicknameBlur(e) {
			const newNickname = e.detail.value.trim();
			if (newNickname && newNickname !== this.userInfo.nickname) {
				this.updateUserField('nickname', newNickname);
			}
		},
		

		
		// 手机号修改
		onPhoneChange() {
			uni.showToast({
				title: '请联系客服修改',
				icon: 'none'
			})
		},
		
		// 生日修改
		onBirthdayChange() {
			this.initBirthdayValue()
			this.$refs.birthdayPopup.open()
		},
		
		initBirthdayPicker() {
			// 生成年份（1950-当前年份）
			const currentYear = new Date().getFullYear()
			this.years = []
			for (let i = 1950; i <= currentYear; i++) {
				this.years.push(i)
			}
			
			// 生成月份
			this.months = []
			for (let i = 1; i <= 12; i++) {
				this.months.push(i)
			}
			
			// 生成日期
			this.updateDays()
		},
		
		initBirthdayValue() {
			if (this.userInfo.birthday) {
				const date = new Date(this.userInfo.birthday)
				const year = date.getFullYear()
				const month = date.getMonth() + 1
				const day = date.getDate()
				
				this.birthdayValue = [
					this.years.indexOf(year),
					month - 1,
					day - 1
				]
			} else {
				// 默认选择1990年1月1日
				this.birthdayValue = [
					this.years.indexOf(1990),
					0,
					0
				]
			}
			this.updateDays()
		},
		
		onBirthdayPickerChange(e) {
			this.birthdayValue = e.detail.value
			this.updateDays()
		},
		
		updateDays() {
			const year = this.years[this.birthdayValue[0]]
			const month = this.months[this.birthdayValue[1]]
			const daysInMonth = new Date(year, month, 0).getDate()
			
			this.days = []
			for (let i = 1; i <= daysInMonth; i++) {
				this.days.push(i)
			}
		},
		
		confirmBirthday() {
			const year = this.years[this.birthdayValue[0]]
			const month = this.months[this.birthdayValue[1]]
			const day = this.days[this.birthdayValue[2]]
			
			const birthday = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
			this.updateUserField('birthday', birthday)
			this.$refs.birthdayPopup.close()
		},
		
		closeBirthdayPopup() {
			this.$refs.birthdayPopup.close()
		},
		
		// 性别修改
		onGenderChange() {
			this.tempGender = this.userInfo.gender || 0
			this.$refs.genderPopup.open()
		},
		
		confirmGender() {
			this.updateUserField('gender', this.tempGender)
			this.$refs.genderPopup.close()
		},
		
		closeGenderPopup() {
			this.$refs.genderPopup.close()
		},
		
		// 更新用户信息
		async updateUserField(field, value) {
			try {
				uni.showLoading({
					title: '更新中...'
				})
				
				const data = {
					[field]: value
				}
				
				const result = await updateUserInfo(data)
				if (result.code === 200) {
					this.userInfo[field] = value
					this.updateUserStorage()
					uni.showToast({
						title: '更新成功',
						icon: 'success'
					})
				} else {
					uni.showToast({
						title: result.message || '更新失败',
						icon: 'none'
					})
				}
			} catch (error) {
				uni.showToast({
					title: '更新失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},
		
		// 更新本地存储
		updateUserStorage() {
			uni.setStorageSync('userInfo', this.userInfo)
		},
		
		// 工具方法
		getGenderText(gender) {
			switch(gender) {
				case 1: return '男'
				case 2: return '女'
				default: return '保密'
			}
		}
	}
}
</script>

<style lang="scss">
.personal-container {
	background: #fff;
	margin: 20rpx;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.info-item {
	border-bottom: 1rpx solid #f5f5f5;
	transition: background-color 0.2s ease;
	
	&:last-child {
		border-bottom: none;
	}
	
	&:active {
		background: #f8f9fa;
	}
	
	.item-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 32rpx 30rpx;
		min-height: 88rpx;
		box-sizing: border-box;
		
		.label {
			font-size: 32rpx;
			color: #333;
			font-weight: 400;
			width: 120rpx;
			flex-shrink: 0;
		}
		
		.value-section {
			display: flex;
			align-items: center;
			flex: 1;
			justify-content: flex-end;
			
			.value {
				font-size: 28rpx;
				color: #666;
				margin-right: 16rpx;
				text-align: right;
			}
			
			.nickname-input {
				font-size: 28rpx;
				color: #333;
				text-align: right;
				border: none;
				background: transparent;
				outline: none;
				width: 200rpx;
				margin-right: 16rpx;
			}
			
			.avatar {
				width: 60rpx;
				height: 60rpx;
				border-radius: 50%;
				margin-right: 16rpx;
				border: 2rpx solid #f0f0f0;
			}
			
			.arrow {
				font-size: 32rpx;
				color: #ccc;
				font-weight: 300;
				width: 20rpx;
				text-align: center;
			}
		}
	}
}

/* 微信头像选择按钮样式 */
.avatar-button {
	background: none !important;
	padding: 0 !important;
	margin: 0 !important;
	border: none !important;
	line-height: normal !important;
	border-radius: 0 !important;
	width: 100% !important;
	text-align: left !important;
	font-size: inherit !important;
	color: inherit !important;
	display: block !important;
	
	&::after {
		border: none !important;
		display: none !important;
	}
	
	&:active {
		background: #f8f9fa !important;
	}
}

// 生日选择器样式
.birthday-picker {
	background: #fff;
	border-radius: 24rpx 24rpx 0 0;
	
	.picker-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		
		.cancel-btn, .confirm-btn {
			font-size: 28rpx;
			color: #007aff;
		}
		
		.title {
			font-size: 32rpx;
			color: #333;
			font-weight: 600;
		}
	}
	
	.picker-view {
		height: 400rpx;
		
		.picker-item {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 80rpx;
			font-size: 28rpx;
			color: #333;
		}
	}
}

// 性别选择器样式
.gender-picker {
	background: #fff;
	border-radius: 24rpx 24rpx 0 0;
	
	.picker-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		
		.cancel-btn, .confirm-btn {
			font-size: 28rpx;
			color: #007aff;
		}
		
		.title {
			font-size: 32rpx;
			color: #333;
			font-weight: 600;
		}
	}
	
	.gender-options {
		padding: 20rpx 0;
		
		.gender-option {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx;
			transition: background-color 0.2s ease;
			
			&:active {
				background: #f8f9fa;
			}
			
			&.active {
				background: rgba(0, 122, 255, 0.1);
				
				.gender-text {
					color: #007aff;
				}
			}
			
			.gender-text {
				font-size: 32rpx;
				color: #333;
			}
			
			.check-icon {
				font-size: 32rpx;
				color: #007aff;
				font-weight: bold;
			}
		}
	}
}


</style> 