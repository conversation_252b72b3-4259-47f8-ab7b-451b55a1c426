<template>
	<PageLayout :show-nav-bar="true" :nav-transparent="false" 	:show-center="true"  nav-title="设置">
		<view class="setting-container">
		<!-- 个人信息 -->
			<view class="menu-item" @click="onPersonalInfo" @tap="onPersonalInfo">
				<text class="menu-text">个人信息</text>
						<text class="arrow">›</text>
		</view>
		
			<!-- 修改密码 -->
			<view class="menu-item" @click="onChangePassword">
				<text class="menu-text">修改密码</text>
						<text class="arrow">›</text>
		</view>
		
			<!-- 收货地址 -->
			<view class="menu-item" @click="onAddressManage">
				<text class="menu-text">收货地址</text>
						<text class="arrow">›</text>
		</view>
		
			<!-- 清理缓存 -->
				<view class="menu-item" @click="onClearCache">
				<text class="menu-text">清理缓存</text>
						<text class="arrow">›</text>
					</view>
			
			<!-- 版本号 -->
			<view class="menu-item version-item">
				<text class="menu-text">版本号</text>
				<text class="version-text">0.6.25</text>
		</view>
		
			<!-- 退出登录 -->
			<view class="logout-btn" @click="onLogout">
				<text class="logout-text">退出登录</text>
			</view>
		</view>
	</PageLayout>
</template>

<script>
import { logout } from '@/api/user'
import PageLayout from '@/components/layout/PageLayout.vue'

export default {
	components: {
		PageLayout
	},
	
	data() {
		return {
			userInfo: uni.getStorageSync('userInfo') || {}
		}
	},
	
	methods: {
		// 个人信息
		onPersonalInfo() {
			console.log('点击个人信息')
			uni.showToast({
				title: '点击了个人信息',
				icon: 'none'
			})
			uni.navigateTo({
				url: '/pages/user/setting/personal',
				success: () => {
					console.log('导航成功')
				},
				fail: (err) => {
					console.error('导航失败:', err)
					uni.showToast({
						title: '页面跳转失败',
						icon: 'none'
					})
				}
			})
		},
		
		// 修改密码
		onChangePassword() {
			uni.navigateTo({
				url: '/pages/user/setting/change-password'
			})
		},
		
		// 收货地址
		onAddressManage() {
			uni.navigateTo({
				url: '/pages/user/address/list'
			})
		},
		
		// 清理缓存
		onClearCache() {
			uni.showModal({
				title: '清理缓存',
				content: '确定要清除所有缓存数据吗？',
				success: (res) => {
					if (res.confirm) {
						// TODO: 清除缓存逻辑
						uni.showToast({
							title: '缓存已清除',
							icon: 'success'
						})
					}
				}
			})
		},
		
		// 退出登录
		async onLogout() {
			uni.showModal({
				title: '提示',
				content: '确定要退出登录吗？',
				confirmColor: '#ff6b6b',
				success: async (res) => {
					if (res.confirm) {
						try {
							// 1. 调用退出登录接口
							const { code, message } = await logout()
							
							if (code === 200) {
								// 2. 清除本地存储
								uni.removeStorageSync('token')
								uni.removeStorageSync('userInfo')
								
								// 3. 提示退出成功
								uni.showToast({
									title: '退出成功',
									icon: 'success',
									duration: 1500
								})
								
								// 4. 跳转到登录页
								setTimeout(() => {
									uni.reLaunch({
										url: '/pages/login/login'
									})
								}, 1500)
							} else {
								throw new Error(message || '退出失败')
							}
						} catch (error) {
							console.error('logout error:', error)
							
							// 即使接口调用失败，也清除本地数据
							uni.removeStorageSync('token')
							uni.removeStorageSync('userInfo')
							
							uni.showToast({
								title: '退出成功',
								icon: 'success',
								duration: 1500
							})
							
							setTimeout(() => {
								uni.reLaunch({
									url: '/pages/login/login'
								})
							}, 1500)
						}
					}
				}
			})
		}
	}
}
</script>

<style lang="scss">
.setting-container {
	background: #fff;
	margin: 20rpx;
	border-radius: 16rpx;
		overflow: hidden;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
	position: relative;
	z-index: 1;
}
		
		.menu-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
	padding: 32rpx 30rpx;
	border-bottom: 1rpx solid #f5f5f5;
	background: #fff;
	transition: background-color 0.2s ease;
	cursor: pointer;
			
	&:last-of-type {
				border-bottom: none;
			}
			
			&:active {
		background: #f8f9fa;
			}
			
	&.version-item {
		cursor: default;
		&:active {
			background: #fff;
				}
			}
			
	.menu-text {
		font-size: 32rpx;
		color: #333;
		font-weight: 400;
				}
				
				.arrow {
		font-size: 36rpx;
		color: #ccc;
		font-weight: 300;
				}
	
	.version-text {
		font-size: 28rpx;
		color: #999;
		font-weight: 300;
	}
}

.logout-btn {
	margin: 40rpx 20rpx;
	padding: 32rpx;
	background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
	border-radius: 16rpx;
	text-align: center;
	box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.3);
	transition: all 0.3s ease;
		
		&:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.4);
		}
		
		.logout-text {
		font-size: 32rpx;
			color: #fff;
		font-weight: 600;
	}
}
</style> 