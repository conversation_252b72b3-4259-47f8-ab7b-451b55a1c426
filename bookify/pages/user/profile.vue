<template>
	<view class="profile-page">
		<!-- 个人信息表单 -->
		<view class="form-group">
			<view class="form-item avatar-item" @click="chooseAvatar">
				<text class="label">头像</text>
				<view class="avatar-box">
					<image class="avatar" :src="userInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
					<text class="iconfont icon-right"></text>
				</view>
			</view>
			<view class="form-item">
				<text class="label">昵称</text>
				<input class="input" type="text" v-model="userInfo.nickname" placeholder="请输入昵称" />
			</view>
			<view class="form-item">
				<text class="label">性别</text>
				<view class="gender-group">
					<text 
						class="gender-item" 
						:class="{active: userInfo.gender === 1}"
						@click="userInfo.gender = 1"
					>男</text>
					<text 
						class="gender-item" 
						:class="{active: userInfo.gender === 2}"
						@click="userInfo.gender = 2"
					>女</text>
				</view>
			</view>
			<view class="form-item">
				<text class="label">手机号</text>
				<input class="input" type="number" v-model="userInfo.phone" placeholder="请输入手机号" maxlength="11" />
			</view>
			<view class="form-item">
				<text class="label">生日</text>
				<picker 
					mode="date" 
					:value="userInfo.birthday" 
					:end="endDate"
					@change="onBirthdayChange"
				>
					<view class="picker">
						<text>{{userInfo.birthday || '请选择生日'}}</text>
						<text class="iconfont icon-right"></text>
					</view>
				</picker>
			</view>
		</view>

		<!-- 保存按钮 -->
		<button class="save-btn" @click="saveProfile">保存</button>
	</view>
</template>

<script>
import { getUserInfo, updateUserInfo } from '@/api/user'
import { uploadAvatar } from '@/api/user'
import config from '../../config';

export default {
	data() {
		return {
			userInfo: {
				avatar: '',
				nickname: '',
				gender: 0,
				phone: '',
				birthday: ''
			},
			endDate: new Date().toISOString().split('T')[0] // 生日选择器的结束日期（今天）
		}
	},
	onLoad() {
		this.loadUserInfo()
	},
	methods: {
		// 加载用户信息
		async loadUserInfo() {
			try {
				const { code, data } = await getUserInfo()
				data.avatar = config.apiHost + data.avatar
				if (code === 200) {
					this.userInfo = {
						...this.userInfo,
						...data
					}
				}
			} catch (e) {
				console.error('获取用户信息失败:', e)
			}
		},
		
		// 选择头像
		chooseAvatar() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: async (res) => {
					try {
						// 显示上传中
						uni.showLoading({
							title: '上传中...',
							mask: true
						})
						
						// 上传头像
						const { code, data, message } = await uploadAvatar(res.tempFilePaths[0])
						console.log(data,config)
						// 隐藏loading
						uni.hideLoading()
						if (code === 200) {
							// 更新头像
							this.userInfo.avatar = config.apiHost + data.avatar
							uni.showToast({
								title: '上传成功',
								icon: 'success'
							})

						} else {
							uni.showToast({
								title: message || '上传失败',
								icon: 'none'
							})
						}
					} catch (e) {
						uni.hideLoading()
						uni.showToast({
							title: e.message || '上传失败',
							icon: 'none'
						})
					}
				}
			})
		},
		
		// 生日改变
		onBirthdayChange(e) {
			this.userInfo.birthday = e.detail.value
		},
		
		// 保存资料
		async saveProfile() {
			// 表单验证
			if (!this.userInfo.nickname) {
				uni.showToast({
					title: '请输入昵称',
					icon: 'none'
				})
				return
			}
			
			if (this.userInfo.phone && !/^1\d{10}$/.test(this.userInfo.phone)) {
				uni.showToast({
					title: '手机号格式不正确',
					icon: 'none'
				})
				return
			}
			
			try {
				const { code, message } = await updateUserInfo(this.userInfo)
				if (code === 200) {
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					})
					// 延迟返回上一页
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} else {
					uni.showToast({
						title: message || '保存失败',
						icon: 'none'
					})
				}
			} catch (e) {
				console.error('保存用户信息失败:', e)
				uni.showToast({
					title: '保存失败',
					icon: 'none'
				})
			}
		}
	}
}
</script>

<style lang="scss">
.profile-page {
	min-height: 100vh;
	background: #f5f5f5;
	padding: 20rpx;
	
	.form-group {
		background: #fff;
		border-radius: 12rpx;
		padding: 0 30rpx;
		
		.form-item {
			display: flex;
			align-items: center;
			min-height: 100rpx;
			border-bottom: 1px solid #eee;
			
			&:last-child {
				border-bottom: none;
			}
			
			.label {
				width: 140rpx;
				font-size: 28rpx;
				color: #333;
			}
			
			.input {
				flex: 1;
				font-size: 28rpx;
				color: #333;
			}
			
			.picker {
				flex: 1;
				font-size: 28rpx;
				color: #333;
				display: flex;
				align-items: center;
				justify-content: space-between;
				
				.icon-right {
					color: #999;
					font-size: 24rpx;
				}
			}
			
			&.avatar-item {
				.avatar-box {
					flex: 1;
					display: flex;
					align-items: center;
					justify-content: flex-end;
					
					.avatar {
						width: 80rpx;
						height: 80rpx;
						border-radius: 50%;
						margin-right: 20rpx;
					}
					
					.icon-right {
						color: #999;
						font-size: 24rpx;
					}
				}
			}
			
			.gender-group {
				flex: 1;
				display: flex;
				align-items: center;
				
				.gender-item {
					margin-right: 40rpx;
					font-size: 28rpx;
					color: #666;
					padding: 10rpx 30rpx;
					border-radius: 30rpx;
					background: #f5f5f5;
					
					&.active {
						color: #fff;
						background: var(--primary-color);
					}
				}
			}
		}
	}
	
	.save-btn {
		width: 90%;
		height: 80rpx;
		line-height: 80rpx;
		background: var(--primary-color);
		color: #fff;
		font-size: 28rpx;
		border-radius: 40rpx;
		margin-top: 60rpx;
		
		&::after {
			border: none;
		}
	}
}
</style> 