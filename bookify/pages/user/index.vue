<template>
	<PageLayout
		:show-left="true"
		:show-center="false"
		:show-right="false"
		:nav-transparent="true"
		nav-text-color="#fff"
	>

	<view class="my-container">
		<!-- 橙色渐变背景头部 -->
		<view class="header-section">
			<!-- 右上角功能菜单 -->
			<view class="top-menu">
				<view class="menu-btn" @click="onCollect">
					<text class="menu-btn-icon">❤️收藏</text>
					<view class="collect-badge" v-if="userData.collect > 0">{{ userData.collect }}</view>
				</view>
				<view class="menu-btn" @click="onSetting">
					<text class="menu-btn-icon">⚙️设置</text>
				</view>
			</view>
			
			<!-- 用户信息区域 -->
			<view class="user-info-section" @click="onLogin">
				<image class="user-avatar" :src="userInfo ? userInfo.avatar : '/static/images/default-avatar.png'" mode="aspectFill"></image>
				<view class="user-details">
					<text class="user-nickname">{{ userInfo ? userInfo.nickname : '立即登录' }}</text>
					<view class="user-meta" v-if="userInfo">
						<text class="level-badge">LV.{{ userInfo.level || 1 }}</text>
						<text class="user-id">ID: {{ userInfo.id }}</text>
					</view>
					<text class="login-tip" v-else>点击登录享受更多服务</text>
				</view>
			</view>
			
		</view>
		
		<!-- 我的订单 -->
		<view class="order-section">
			<view class="section-header">
				<text class="section-title">我的订单</text>
				<view class="view-all" @click="viewAllOrders">
					<text>查看全部</text>
					<text class="arrow">›</text>
				</view>
			</view>
			<view class="order-grid">
				<view class="order-item" @click="onOrderType(1)">
					<view class="order-icon-wrapper">
						<text class="order-icon">💳</text>
						<view class="order-badge" v-if="orderCount.unpaid">{{ orderCount.unpaid }}</view>
				</view>
					<text class="order-label">待付款</text>
				</view>
				<view class="order-item" @click="onOrderType(2)">
					<view class="order-icon-wrapper">
						<text class="order-icon">📦</text>
						<view class="order-badge" v-if="orderCount.unshipped">{{ orderCount.unshipped }}</view>
				</view>
					<text class="order-label">待发货</text>
				</view>
				<view class="order-item" @click="onOrderType(3)">
					<view class="order-icon-wrapper">
						<text class="order-icon">🚚</text>
						<view class="order-badge" v-if="orderCount.unreceived">{{ orderCount.unreceived }}</view>
				</view>
					<text class="order-label">待收货</text>
			</view>
				<view class="order-item" @click="onOrderType(4)">
					<view class="order-icon-wrapper">
						<text class="order-icon">💬</text>
						<view class="order-badge" v-if="orderCount.unevaluated">{{ orderCount.unevaluated }}</view>
		</view>
					<text class="order-label">待评价</text>
				</view>
				<view class="order-item" @click="onOrderType(5)">
					<view class="order-icon-wrapper">
						<text class="order-icon">🔧</text>
						<view class="order-badge" v-if="orderCount.afterSale">{{ orderCount.afterSale }}</view>
			</view>
					<text class="order-label">售后</text>
				</view>
			</view>
			</view>
			
		<!-- 功能菜单 -->
		<view class="menu-section">
			<view class="menu-item" @click="onAddress">
				<view class="menu-left">
					<view class="menu-icon">📍</view>
					<text class="menu-text">收货地址</text>
				</view>
				<text class="menu-arrow">›</text>
			</view>
			<view class="menu-item" @click="onService">
				<view class="menu-left">
					<view class="menu-icon">💬</view>
					<text class="menu-text">联系客服</text>
				</view>
				<text class="menu-arrow">›</text>
			</view>
			<view class="menu-item" @click="onAbout">
				<view class="menu-left">
					<view class="menu-icon">ℹ️</view>
					<text class="menu-text">关于我们</text>
				</view>
				<text class="menu-arrow">›</text>
			</view>
			<view class="menu-item" @click="onPopupDemo">
				<view class="menu-left">
					<view class="menu-icon">🎯</view>
					<text class="menu-text">弹窗功能演示</text>
				</view>
				<text class="menu-arrow">›</text>
			</view>
		</view>
		
		<!-- 技师工作台入口 -->
		<view class="menu-section" v-if="isTechnician">
			<view class="menu-item special" @click="goToTechnicianDashboard">
				<view class="menu-left">
					<view class="menu-icon special">💆‍♀️</view>
					<text class="menu-text">技师工作台</text>
				</view>
				<view class="menu-right">
					<text class="technician-tag">技师</text>
					<text class="menu-arrow">›</text>
				</view>
			</view>
		</view>
		
		<!-- 分销入口 -->
		<view class="menu-section" v-if="distributionEnabled">
			<view class="menu-item special" @click="handleDistribution">
				<view class="menu-left">
					<view class="menu-icon special">💰</view>
					<text class="menu-text">{{ distributionStatusText }}</text>
				</view>
				<view class="menu-right">
					<text v-if="isDistributor" class="distributor-tag">分销商</text>
					<text v-else-if="distributorInfo && distributorInfo.ID > 0 && distributorInfo.Status === 0" class="status-tag pending">审核中</text>
					<text v-else-if="distributorInfo && distributorInfo.ID > 0 && distributorInfo.Status === 2" class="status-tag rejected">已拒绝</text>
					<text class="menu-arrow">›</text>
				</view>
			</view>
		</view>
		
		<!-- 我的订单入口 -->
		<view class="menu-section">
			<view class="menu-item" @click="goOrderList">
				<view class="menu-left">
					<view class="menu-icon">📦</view>
					<text class="menu-text">我的订单</text>
				</view>
				<text class="menu-arrow">›</text>
			</view>
			<!-- 我的预约入口 -->
			<view class="menu-item" @click="goBookingList">
				<view class="menu-left">
					<view class="menu-icon">💆‍♀️</view>
					<text class="menu-text">我的预约</text>
				</view>
				<text class="menu-arrow">›</text>
			</view>
		</view>

		</view>
</PageLayout>
</template>

<script>
import { getUserInfo, getUserStats, getOrderStats, logout } from '@/api/user'
import AuthUtils from '@/utils/auth'
import { getConfig, getDistributorInfo, applyDistributor } from '@/api/distribution'
import PageLayout from '@/components/layout/PageLayout.vue'
export default {
	components: {
		PageLayout
	},
	data() {
		console.log('my.vue data() called')
		return {
			// 用户信息
			userInfo: null,
			// 用户数据
			userData: {
				collect: 0
			},
			// 订单数量
			orderCount: {
				unpaid: 0,
				unshipped: 0,
				unreceived: 0,
				unevaluated: 0,
				afterSale: 0
			},
			distributionEnabled: false, // 是否启用分销功能
			isDistributor: false, // 是否是分销商
			distributorInfo: null, // 分销商信息
			isTechnician: false, // 是否是技师
			technicianInfo: null // 技师信息
		}
	},
	
	computed: {
		// 分销状态文本
		distributionStatusText() {
			if (this.isDistributor) {
				return '分销中心'
			}
			// 只有当ID > 0时才表示有真实的分销员记录
			if (this.distributorInfo && this.distributorInfo.ID > 0) {
				if (this.distributorInfo.Status === 0) {
					return '申请审核中'
				} else if (this.distributorInfo.Status === 2) {
					return '重新申请分销'
				}
			}
			return '申请分销'
		}
	},
	
	onLoad() {
		console.log('my.vue onLoad() called')
		// 页面首次加载时初始化数据
		this.initData()
	},
	
	onShow() {
		console.log('my.vue onShow() called')
		// 每次显示页面时更新数据
		this.initData()
	},
	
	methods: {
		// 初始化数据
		async initData() {
			console.log('initData() called, starting to fetch data...')
			console.log('1. Getting user info...')
			await this.getUserInfo()
			console.log('After getUserInfo(), userInfo:', this.userInfo)
			
			// 先获取分销配置（不需要登录）
			await this.getDistributionConfig()
			
			if(this.userInfo) {
				console.log('2. User is logged in, fetching additional data...')
				await Promise.all([
					this.getUserData(),
					this.getOrderCount(),
					this.checkTechnicianStatus()
				])
				console.log('3. All data fetched successfully')
			} else {
				console.log('2. User not logged in, skipping additional data fetch')
			}
		},
		
		// 获取用户信息
		async getUserInfo() {
			const token = uni.getStorageSync('token')
			console.log('getUserInfo() called, token:', token ? 'exists' : 'not found')
			
			if (!token) {
				console.log('No token found, setting userInfo to null')
				this.userInfo = null
				return
			}
			
			try {
				console.log('Calling getUserInfo API...')
				const { code, data, message } = await getUserInfo()
				console.log('getUserInfo API response:', { code, data, message })
				
				if (code === 200) {
					this.userInfo = data
					// 更新本地存储
					uni.setStorageSync('userInfo', data)
					console.log('User info updated successfully')
				} else {
					console.log('Failed to get user info:', message)
					uni.showToast({
						title: message || '获取用户信息失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('getUserInfo API error:', error)
				uni.showToast({
					title: '获取用户信息失败',
					icon: 'none'
				})
			}
		},
		
		// 获取用户数据
		async getUserData() {
			console.log('getUserData() called')
			if (!this.userInfo) {
				console.log('No user info, skipping getUserData')
				return
			}
			
			try {
				console.log('Calling getUserStats API...')
				const { code, data, message } = await getUserStats()
				console.log('getUserStats API response:', { code, data, message })
				
				if (code === 200) {
					this.userData = {
						collect: data.collectCount || 0
					}
					console.log('User data updated:', this.userData)
				} else {
					console.error('Failed to get user stats:', message)
				}
			} catch (error) {
				console.error('getUserStats API error:', error)
			}
		},
		
		// 获取订单数量
		async getOrderCount() {
			console.log('getOrderCount() called')
			if (!this.userInfo) {
				console.log('No user info, skipping getOrderCount')
				return
			}
			
			try {
				console.log('Calling getOrderStats API...')
				const { code, data, message } = await getOrderStats()
				console.log('getOrderStats API response:', { code, data, message })
				
				if (code === 200) {
					this.orderCount = {
						unpaid: data.unpaidCount || 0,
						unshipped: data.unshippedCount || 0,
						unreceived: data.unreceivedCount || 0,
						unevaluated: data.unevaluatedCount || 0,
						afterSale: data.afterSaleCount || 0
					}
					console.log('Order count updated:', this.orderCount)
				} else {
					console.error('Failed to get order stats:', message)
				}
			} catch (error) {
				console.error('getOrderStats API error:', error)
			}
		},
		
		// 获取分销配置
		async getDistributionConfig() {
			console.log('getDistributionConfig() called')
			
			try {
				console.log('Calling getConfig API...')
				const { code, data, message } = await getConfig()
				console.log('getConfig API response:', { code, data, message })
				
				if (code === 200) {
					// 更新分销配置状态
					this.distributionEnabled = data.enabled
					console.log('Distribution config updated:', { 
						enabled: this.distributionEnabled,
						registerEnabled: data.register_enabled,
						registerAmount: data.register_amount,
						purchaseEnabled: data.purchase_enabled,
						level1Rate: data.level1_rate,
						level2Rate: data.level2_rate,
						freezeDays: data.freeze_days,
						minWithdraw: data.min_withdraw,
						maxWithdraw: data.max_withdraw,
						withdrawFeeRate: data.withdraw_fee_rate,
						wechatWithdraw: data.wechat_withdraw,
						alipayWithdraw: data.alipay_withdraw
					})
					
					// 如果分销功能已启用且用户已登录，获取分销员信息
					if (this.distributionEnabled && this.userInfo) {
						await this.getDistributorInfo()
					}
				} else {
					console.error('Failed to get distribution config:', message)
				}
			} catch (error) {
				console.error('getDistributionConfig API error:', error)
			}
		},
		
		// 获取分销员信息
		async getDistributorInfo() {
			try {
				console.log('Calling getDistributorInfo API...')
				const { code, data, message } = await getDistributorInfo()
				console.log('getDistributorInfo API response:', { code, data, message })
				
				if (code === 200) {
					console.log('Raw data received:', data)
					console.log('Data fields check:', {
						hasData: !!data,
						ID: data ? data.ID : 'undefined',
						Status: data ? data.Status : 'undefined',
						UserId: data ? data.UserId : 'undefined'
					})
					
					// 只有审核通过的用户才算是分销员 (Status = 1)
					const isDistributorResult = data && data.ID > 0 && data.Status === 1
					this.isDistributor = isDistributorResult
					this.distributorInfo = data
					
					console.log('Distributor status calculation:', {
						hasData: !!data,
						hasValidID: data && data.ID > 0,
						statusIs1: data && data.Status === 1,
						finalIsDistributor: isDistributorResult
					})
					
					console.log('Distributor info updated:', { 
						isDistributor: this.isDistributor,
						distributorInfo: this.distributorInfo,
						status: data ? data.Status : 'no data'
					})
				} else if (code === 404) {
					// 用户暂未申请分销
					console.log('User has not applied for distribution')
					this.isDistributor = false
					this.distributorInfo = null
				} else {
					console.error('Failed to get distributor info:', message)
					this.isDistributor = false
					this.distributorInfo = null
				}
			} catch (error) {
				console.error('getDistributorInfo API error:', error)
				this.isDistributor = false
				this.distributorInfo = null
			}
		},
		
		// 跳转到收藏页面
		onCollect() {
			if (!AuthUtils.checkLoginStatus()) {
				return
			}
			uni.navigateTo({
				url: '/pages/user/collect'
			})
		},
		
		// 点击登录
		onLogin() {
			if (!this.userInfo) {
				uni.navigateTo({
					url: '/pages/login/login'
				})
			}
		},
		
		// 点击设置
		onSetting() {
			uni.navigateTo({
				url: '/pages/user/setting/setting'
			})
		},
		

		
		// 查看全部订单
		viewAllOrders() {
			if (!AuthUtils.checkLoginStatus()) {
				return
			}
			uni.navigateTo({
				url: '/pages/order/list'
			})
		},
		
		// 点击订单类型
		onOrderType(type) {
			if (!AuthUtils.checkLoginStatus()) {
				return
			}
			uni.navigateTo({
				url: `/pages/order/list?type=${type}`
			})
		},
		
		// 点击收货地址
		onAddress() {
			if (!AuthUtils.checkLoginStatus()) {
				return
			}
			uni.navigateTo({
				url: '/pages/user/address/list'
			})
		},
		
		// 点击客服
		onService() {
			uni.navigateTo({
				url: '/pages/user/service'
			})
		},
		
		// 点击关于我们
		onAbout() {
			uni.navigateTo({
				url: '/pages/about/about'
			})
		},
		
		// 点击弹窗功能演示
		onPopupDemo() {
			uni.navigateTo({
				url: '/pages/popup-demo/index'
			})
		},
		
		// 退出登录
		async onLogout() {
			try {
				const { code, message } = await logout()
				if (code === 200) {
					// 清除本地存储
					uni.removeStorageSync('token')
					uni.removeStorageSync('userInfo')
					// 重置数据
					this.userInfo = null
					this.userData = {
						collect: 0
					}
					this.orderCount = {
						unpaid: 0,
						unshipped: 0,
						unreceived: 0,
						unevaluated: 0,
						afterSale: 0
					}
					uni.showToast({
						title: '退出成功',
						icon: 'success'
					})
				} else {
					uni.showToast({
						title: message || '退出失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('logout error:', error)
				uni.showToast({
					title: '退出失败',
					icon: 'none'
				})
			}
		},
		
		// 检查技师状态
		async checkTechnicianStatus() {
			if (!this.userInfo) {
				this.isTechnician = false
				this.technicianInfo = null
				return
			}
			
			try {
				// 这里应该调用后端API检查当前用户是否是技师
				// 暂时使用模拟数据，可以根据用户ID或其他标识来判断
				const response = await this.getTechnicianByUserId(this.userInfo.id)
				
				if (response.code === 200 && response.data) {
					this.isTechnician = true
					this.technicianInfo = response.data
					console.log('用户是技师:', this.technicianInfo)
				} else {
					this.isTechnician = false
					this.technicianInfo = null
				}
			} catch (error) {
				console.error('检查技师状态失败:', error)
				this.isTechnician = false
				this.technicianInfo = null
			}
		},
		
		// 模拟API：根据用户ID获取技师信息
		async getTechnicianByUserId(userId) {
			// 模拟API调用，实际应该调用后端接口
			return new Promise((resolve) => {
				setTimeout(() => {
					// 模拟：假设用户ID为1的是技师
					if (userId === 1) {
						resolve({
							code: 200,
							data: {
								id: 1,
								userId: userId,
								name: '李美容师',
								level: '高级技师',
								rating: 4.9,
								status: 1 // 1: 在职
							}
						})
					} else {
						resolve({
							code: 404,
							message: '用户不是技师'
						})
					}
				}, 300)
			})
		},
		
		// 跳转到技师工作台
		goToTechnicianDashboard() {
			if (!this.isTechnician) {
				uni.showToast({
					title: '您不是技师',
					icon: 'none'
				})
				return
			}
			
			uni.navigateTo({
				url: '/pages/beauty/technician/dashboard/index'
			})
		},
		
		// 处理分销相关操作
		async handleDistribution() {
			console.log('handleDistribution called with current state:', {
				isDistributor: this.isDistributor,
				distributorInfo: this.distributorInfo,
				distributorStatus: this.distributorInfo ? this.distributorInfo.Status : 'no info',
				userInfo: !!this.userInfo
			})
			
			if (!this.userInfo) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				})
				return
			}
			
			// 如果是已审核通过的分销员，直接进入分销中心
			if (this.isDistributor) {
				console.log('User is approved distributor, navigating to distribution center')
				uni.navigateTo({
					url: '/pages/distribution/index'
				})
				return
			}
			
			// 检查申请状态
			if (this.distributorInfo && this.distributorInfo.ID > 0) {
				if (this.distributorInfo.Status === 0) {
					// 申请待审核
					uni.showModal({
						title: '申请审核中',
						content: '您的分销员申请正在审核中，请耐心等待管理员审核。',
						showCancel: false,
						confirmText: '我知道了'
						})
					return
				} else if (this.distributorInfo.Status === 2) {
					// 申请被拒绝，可以重新申请
					uni.showModal({
						title: '申请被拒绝',
						content: '您的分销员申请被拒绝，是否重新申请？',
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/distribution/apply'
								})
							}
						}
					})
					return
				}
			}
			
			// 首次申请或其他情况，跳转到申请页面
			uni.navigateTo({
				url: '/pages/distribution/apply'
					})
		},

		goOrderList() {
			uni.navigateTo({ url: '/pages/order/list' })
		},
		goBookingList() {
			uni.navigateTo({ url: '/pages/beauty/user/booking-list/index' })
		}
	}
}
</script>

<style lang="scss">
.my-container {
	min-height: 100vh;
	background: #f8f9fa;
}

// 橙色渐变头部区域
.header-section {
	background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
	padding: 200rpx 30rpx 40rpx;
	position: relative;
	overflow: hidden;

	// 背景装饰效果
	&::before {
		content: '';
	position: absolute;
		top: -100rpx;
		right: -100rpx;
		width: 400rpx;
		height: 400rpx;
		background: rgba(255, 255, 255, 0.1);
		border-radius: 50%;
	}
	
	&::after {
		content: '';
		position: absolute;
		bottom: -80rpx;
		left: -80rpx;
			width: 300rpx;
			height: 300rpx;
		background: rgba(255, 255, 255, 0.05);
		border-radius: 50%;
	}
}

// 右上角功能菜单
.top-menu {
	position: absolute;
	top: 170rpx;
	right: 30rpx;
	display: flex;
	gap: 12rpx;
	z-index: 3;
	
	.menu-btn {
		min-width: 100rpx;
		height: 60rpx;
		padding: 0 18rpx;
		background: rgba(255, 255, 255, 0.2);
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		backdrop-filter: blur(15px);
		border: 1rpx solid rgba(255, 255, 255, 0.1);
		transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
		position: relative;
		
		.menu-btn-icon {
			font-size: 18rpx;
			color: #fff;
			white-space: nowrap;
			font-weight: 600;
			letter-spacing: 0.5rpx;
		}
		
		.collect-badge {
			position: absolute;
			top: -8rpx;
			right: -8rpx;
			min-width: 32rpx;
			height: 32rpx;
			background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
			border-radius: 16rpx;
			color: #fff;
			font-size: 20rpx;
			font-weight: 700;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 0 8rpx;
			box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.4);
			border: 2rpx solid #fff;
		}
		
		&:active {
			background: rgba(255, 255, 255, 0.3);
			transform: scale(0.95);
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
		}
		
		&:hover {
			background: rgba(255, 255, 255, 0.25);
			box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
		}
	}
}

// 用户信息区域
.user-info-section {
		display: flex;
		align-items: center;
	margin-bottom: 40rpx;
		position: relative;
	z-index: 2;
		
	.user-avatar {
			width: 120rpx;
			height: 120rpx;
		border-radius: 60rpx;
		border: 4rpx solid rgba(255, 255, 255, 0.3);
			box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
		}
		
	.user-details {
			flex: 1;
			margin-left: 24rpx;
			
		.user-nickname {
			display: block;
				font-size: 36rpx;
			font-weight: 700;
			color: #fff;
				margin-bottom: 12rpx;
			text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
			}
			
		.user-meta {
				display: flex;
				align-items: center;
				
			.level-badge {
					font-size: 22rpx;
				background: rgba(255, 255, 255, 0.25);
				color: #fff;
					padding: 6rpx 16rpx;
					border-radius: 20rpx;
					margin-right: 16rpx;
				font-weight: 600;
					backdrop-filter: blur(10px);
				}
				
			.user-id {
					font-size: 24rpx;
				color: rgba(255, 255, 255, 0.9);
				font-weight: 500;
			}
				}
				
		.login-tip {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			margin-top: 8rpx;
			}
		}
		
	.setting-icon {
			width: 60rpx;
			height: 60rpx;
		background: rgba(255, 255, 255, 0.2);
		border-radius: 30rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			backdrop-filter: blur(10px);
		border: 1rpx solid rgba(255, 255, 255, 0.1);
			
		text {
			font-size: 28rpx;
				color: #fff;
			}
		
		&:active {
			background: rgba(255, 255, 255, 0.3);
			transform: scale(0.95);
		}
	}
}





// 我的订单区域
.order-section {
	margin: 30rpx;
	background: #fff;
	border-radius: 24rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
		
		.section-title {
			font-size: 32rpx;
			font-weight: 700;
			color: #333;
		}
		
		.view-all {
			display: flex;
			align-items: center;
			font-size: 26rpx;
			color: #FF6B35;
			font-weight: 600;
			
			.arrow {
				font-size: 24rpx;
				margin-left: 4rpx;
			}
		}
	}
	
	.order-grid {
		display: flex;
		justify-content: space-between;
		
		.order-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			transition: all 0.3s ease;
			padding: 12rpx;
			border-radius: 16rpx;
			
			&:active {
				background: rgba(255, 107, 53, 0.1);
				transform: scale(0.95);
			}
			
			.order-icon-wrapper {
				position: relative;
				margin-bottom: 12rpx;
				
				.order-icon {
				font-size: 48rpx;
					filter: drop-shadow(0 2rpx 8rpx rgba(255, 107, 53, 0.2));
			}
			
				.order-badge {
				position: absolute;
					top: -8rpx;
					right: -8rpx;
				min-width: 32rpx;
				height: 32rpx;
					background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
					border-radius: 16rpx;
				color: #fff;
				font-size: 20rpx;
					font-weight: 700;
				display: flex;
				align-items: center;
				justify-content: center;
					padding: 0 8rpx;
					box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.4);
				}
			}
			
			.order-label {
				font-size: 24rpx;
				color: #666;
				font-weight: 600;
			}
		}
	}
}

// 功能菜单区域
.menu-section {
	margin: 30rpx;
	background: #fff;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid #f0f0f0;
	overflow: hidden;
	
	.menu-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 32rpx 30rpx;
		border-bottom: 1rpx solid #f5f5f5;
		transition: all 0.3s ease;
		position: relative;
		
		&:last-child {
			border-bottom: none;
		}
		
		&:active {
			background: rgba(255, 107, 53, 0.05);
		}
		
		&.special {
			background: linear-gradient(135deg, rgba(255, 107, 53, 0.05) 0%, rgba(247, 147, 30, 0.05) 100%);
			border: none;
			
			&:active {
				background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(247, 147, 30, 0.1) 100%);
			}
		}
		
		.menu-left {
			display: flex;
			align-items: center;
			
			.menu-icon {
				width: 60rpx;
				height: 60rpx;
				background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(247, 147, 30, 0.1) 100%);
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 24rpx;
				font-size: 32rpx;
				border: 1rpx solid rgba(255, 107, 53, 0.1);
				
				&.special {
					background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
					border: none;
					box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
					filter: none;
				}
			}
			
			.menu-text {
				font-size: 30rpx;
				color: #333;
				font-weight: 600;
			}
		}
		
		.menu-right {
			display: flex;
			align-items: center;
			
			.distributor-tag {
				font-size: 22rpx;
				color: #FF6B35;
				background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(247, 147, 30, 0.1) 100%);
				padding: 8rpx 16rpx;
				border-radius: 20rpx;
				margin-right: 12rpx;
				font-weight: 700;
				border: 1rpx solid rgba(255, 107, 53, 0.2);
			}
			
			.technician-tag {
				font-size: 22rpx;
				color: #FFB6C1;
				background: linear-gradient(135deg, rgba(255, 182, 193, 0.1) 0%, rgba(255, 105, 180, 0.1) 100%);
				padding: 8rpx 16rpx;
				border-radius: 20rpx;
				margin-right: 12rpx;
				font-weight: 700;
				border: 1rpx solid rgba(255, 182, 193, 0.2);
			}
			
			.status-tag {
				font-size: 22rpx;
				padding: 8rpx 16rpx;
				border-radius: 20rpx;
				margin-right: 12rpx;
				font-weight: 700;
				
				&.pending {
					color: #1890ff;
					background: rgba(24, 144, 255, 0.1);
					border: 1rpx solid rgba(24, 144, 255, 0.2);
				}
				
				&.rejected {
					color: #f5222d;
					background: rgba(245, 34, 45, 0.1);
					border: 1rpx solid rgba(245, 34, 45, 0.2);
				}
			}
		}
		
		.menu-arrow {
			font-size: 28rpx;
			color: #999;
			font-weight: 300;
		}
	}
}


</style> 