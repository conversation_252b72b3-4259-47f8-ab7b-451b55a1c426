<template>
	<view class="collect-page">
		<!-- 商品列表 -->
		<view class="goods-list" v-if="list.length > 0">
			<view class="goods-item" v-for="item in list" :key="item.id" @click="viewGoods(item)">
				<image class="goods-img" :src="item.image" mode="aspectFill"></image>
				<view class="goods-info">
					<text class="title">{{item.name}}</text>
					<text class="price">¥{{item.price}}</text>
					<view class="action">
						<text class="time">{{item.collectTime}}</text>
						<text class="delete" @click.stop="deleteItem(item)">删除</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty" v-else>
			<image src="/static/images/empty-cart.png" mode="aspectFit"></image>
			<text>暂无收藏商品</text>
			<button class="go-shop" @click="goShop">去逛逛</button>
		</view>
	</view>
</template>

<script>
import { getCollectList, deleteCollect } from '@/api/user'

export default {
	data() {
		return {
			page: 1,
			size: 10,
			list: [],
			loading: false,
			finished: false
		}
	},
	onLoad() {
		this.loadData()
	},
	onPullDownRefresh() {
		this.page = 1
		this.finished = false
		this.list = []
		this.loadData().then(() => {
			uni.stopPullDownRefresh()
		})
	},
	onReachBottom() {
		if (!this.loading && !this.finished) {
			this.page++
			this.loadData()
		}
	},
	methods: {
		// 加载数据
		async loadData() {
			if (this.loading || this.finished) return
			
			this.loading = true
			try {
				const { code, data } = await getCollectList({
					page: this.page,
					size: this.size
				})
				
				if (code === 200) {
					if (this.page === 1) {
						this.list = data.list
					} else {
						this.list = [...this.list, ...data.list]
					}
					
					// 判断是否加载完毕
					if (data.list.length < this.size) {
						this.finished = true
					}
				}
			} catch (e) {
				console.error('加载收藏列表失败:', e)
			} finally {
				this.loading = false
			}
		},
		
		// 查看商品
		viewGoods(item) {
			uni.navigateTo({
				url: `/pages/goods/detail?id=${item.goodsId}`
			})
		},
		
		// 删除收藏
		deleteItem(item) {
			uni.showModal({
				title: '提示',
				content: '确定要删除该收藏吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							const { code } = await deleteCollect(item.id)
							if (code === 200) {
								uni.showToast({
									title: '删除成功',
									icon: 'success'
								})
								// 从列表中移除
								const index = this.list.findIndex(i => i.id === item.id)
								if (index > -1) {
									this.list.splice(index, 1)
								}
							}
						} catch (e) {
							console.error('删除收藏失败:', e)
							uni.showToast({
								title: '删除失败',
								icon: 'none'
							})
						}
					}
				}
			})
		},
		
		// 去逛逛
		goShop() {
			uni.switchTab({
				url: '/pages/index/index'
			})
		}
	}
}
</script>

<style lang="scss">
.collect-page {
	min-height: 100vh;
	background: #f5f5f5;
	padding: 20rpx;
	
	.goods-list {
		.goods-item {
			display: flex;
			background: #fff;
			padding: 20rpx;
			border-radius: 12rpx;
			margin-bottom: 20rpx;
			
			.goods-img {
				width: 200rpx;
				height: 200rpx;
				margin-right: 20rpx;
				border-radius: 8rpx;
			}
			
			.goods-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				
				.title {
					font-size: 28rpx;
					color: #333;
					line-height: 1.4;
					margin-bottom: 10rpx;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
				}
				
				.price {
					font-size: 32rpx;
					color: #ff6b6b;
					font-weight: bold;
				}
				
				.action {
					display: flex;
					justify-content: space-between;
					align-items: center;
					
					.time {
						font-size: 24rpx;
						color: #999;
					}
					
					.delete {
						font-size: 24rpx;
						color: #666;
						padding: 4rpx 20rpx;
						border: 1px solid #ddd;
						border-radius: 24rpx;
					}
				}
			}
		}
	}
	
	.empty {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 200rpx;
		
		image {
			width: 240rpx;
			height: 240rpx;
			margin-bottom: 30rpx;
		}
		
		text {
			font-size: 28rpx;
			color: #999;
			margin-bottom: 40rpx;
		}
		
		.go-shop {
			width: 240rpx;
			height: 80rpx;
			line-height: 80rpx;
			font-size: 28rpx;
			color: #fff;
			background: #ff6b6b;
			border-radius: 40rpx;
			
			&::after {
				border: none;
			}
		}
	}
}
</style> 