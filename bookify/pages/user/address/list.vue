<template>
	<PageLayout 
		:show-nav-bar="true" 
		:nav-transparent="false" 
		:show-left="true"
		:show-center="true"  
		nav-title="收货地址"
	>
			<view class="address-list">
		
		<!-- 地址列表 -->
		<view class="list">
			<view 
				class="item" 
				v-for="(item, index) in addressList" 
				:key="item.id"
				@click="selectAddress(item)"
			>
				<view class="info">
					<view class="user">
						<text class="name">{{item.receiver_name}}</text>
						<text class="phone">{{item.receiver_mobile}}</text>
						<text class="tag" v-if="item.is_default">默认</text>
					</view>
					<view class="address">
						{{item.province}}{{item.city}}{{item.district}}{{item.detail}}
					</view>
				</view>
				<view class="action">
					<view class="edit" @click.stop="editAddress(item)">✏️ 编辑</view>
					<view class="delete" @click.stop="deleteAddress(index)">🗑️ 删除</view>
					<view class="use" v-if="fromOrder" @click.stop="useAddress(item)">✅ 使用</view>
				</view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty" v-if="!addressList.length">
			<text class="icon">📍</text>
			<text>暂无收货地址</text>
		</view>
		
		<!-- 底部按钮 -->
		<view class="footer">
			<button class="add-btn" @click="addAddress">新增收货地址</button>
		</view>
	</view>
</PageLayout>
</template>

<script>
import { getAddressList, deleteAddress, setDefaultAddress } from '@/api/address'
import PageLayout from '@/components/layout/PageLayout.vue'

export default {
	components: {
		PageLayout
	},
	data() {
		return {
			addressList: [],
			// 是否从订单确认页进入
			fromOrder: false
		}
	},
	onLoad(options) {
		this.fromOrder = !!options.from
		this.loadAddressList()
	},
	methods: {
		// 加载地址列表
		async loadAddressList() {
			try {
				uni.showLoading({
					title: '加载中...'
				})
				const { code, data } = await getAddressList()
				if (code === 200) {
					this.addressList = data
				}
			} catch (e) {
				console.error(e)
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},
		// 选择地址
		selectAddress(address) {
			if(this.fromOrder) {
				this.useAddress(address)
			}
		},
		// 使用地址
		useAddress(address) {
			if(this.fromOrder) {
				// 选择地址后返回订单页
				const pages = getCurrentPages()
				const prevPage = pages[pages.length - 2]
				prevPage.$vm.address = {
					id: address.id,
					receiver_name: address.receiver_name,
					receiver_mobile: address.receiver_mobile,
					province: address.province,
					city: address.city,
					district: address.district,
					detail: address.detail,
					is_default: address.is_default
				}
				uni.navigateBack()
			}
		},
		// 新增地址
		addAddress() {
			uni.navigateTo({
				url: '/pages/user/address/edit'
			})
		},
		// 编辑地址
		editAddress(address) {
			uni.navigateTo({
				url: `/pages/user/address/edit?id=${address.id}`
			})
		},
		// 删除地址
		async deleteAddress(index) {
			uni.showModal({
				title: '提示',
				content: '确定要删除该地址吗？',
				success: async res => {
					if(res.confirm) {
						try {
							uni.showLoading({
								title: '删除中...'
							})
							const { code } = await deleteAddress(this.addressList[index].id)
							if (code === 200) {
								uni.showToast({
									title: '删除成功'
								})
								this.addressList.splice(index, 1)
							}
						} catch (e) {
							console.error(e)
							uni.showToast({
								title: '删除失败',
								icon: 'none'
							})
						} finally {
							uni.hideLoading()
						}
					}
				}
			})
		}
	}
}
</script>

<style lang="scss">
.address-list {
	min-height: 100vh;
	background: #f8f9fa;
	padding-bottom: 120rpx;
}

.list {
	padding: 20rpx 30rpx;
	
	.item {
		background: #fff;
		border-radius: 24rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		border: 1rpx solid #f0f0f0;
		transition: all 0.3s ease;
		
		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
		}
		
		.info {
			.user {
				margin-bottom: 16rpx;
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				
				.name {
					font-size: 30rpx;
					color: #333;
					font-weight: 600;
					margin-right: 20rpx;
				}
				
				.phone {
					font-size: 28rpx;
					color: #666;
					font-weight: 500;
					margin-right: 20rpx;
				}
				
				.tag {
					font-size: 22rpx;
					color: #fff;
					background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
					padding: 6rpx 16rpx;
					border-radius: 16rpx;
					font-weight: 600;
					box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3);
				}
			}
			
			.address {
				font-size: 28rpx;
				color: #333;
				font-weight: 500;
				line-height: 1.4;
				background: rgba(255, 107, 53, 0.05);
				padding: 12rpx 16rpx;
				border-radius: 12rpx;
			}
		}
		
		.action {
			display: flex;
			justify-content: flex-end;
			margin-top: 20rpx;
			gap: 16rpx;
			
			view {
				font-size: 26rpx;
				font-weight: 600;
				padding: 12rpx 20rpx;
				border-radius: 20rpx;
				transition: all 0.3s ease;
				
				&.edit {
					color: #FF6B35;
					background: rgba(255, 107, 53, 0.1);
					border: 1rpx solid rgba(255, 107, 53, 0.2);
					
					&:active {
						background: rgba(255, 107, 53, 0.2);
						transform: scale(0.95);
					}
				}
				
				&.delete {
					color: #ff6b6b;
					background: rgba(255, 107, 107, 0.1);
					border: 1rpx solid rgba(255, 107, 107, 0.2);
					
					&:active {
						background: rgba(255, 107, 107, 0.2);
						transform: scale(0.95);
					}
				}
				
				&.use {
					color: #52c41a;
					background: rgba(82, 196, 26, 0.1);
					border: 1rpx solid rgba(82, 196, 26, 0.2);
					
					&:active {
						background: rgba(82, 196, 26, 0.2);
						transform: scale(0.95);
					}
				}
			}
		}
	}
}

.empty {
	padding-top: 200rpx;
	text-align: center;
	
	.icon {
		font-size: 120rpx;
		display: block;
		margin-bottom: 30rpx;
		opacity: 0.8;
	}
	
	text {
		font-size: 28rpx;
		color: #999;
		font-weight: 500;
	}
}

.footer {
	position: fixed;
	left: 30rpx;
	right: 30rpx;
	bottom: 30rpx;
	padding: 20rpx 0;
	z-index: 100;
	
	.add-btn {
		height: 88rpx;
		line-height: 88rpx;
		background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
		color: #fff;
		font-size: 30rpx;
		font-weight: 700;
		border-radius: 24rpx;
		box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.4);
		transition: all 0.3s ease;
		
		&::after {
			border: none;
		}
		
		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.5);
		}
	}
}
</style> 