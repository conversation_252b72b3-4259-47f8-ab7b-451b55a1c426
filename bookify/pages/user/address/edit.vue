<template>
	<PageLayout 
		:show-nav-bar="true" 
		:nav-transparent="false" 
		:show-center="true"  
		:nav-title="addressId ? '编辑地址' : '新增地址'"
	>
		<view class="address-edit">
		<view class="form">
			<!-- 联系人 -->
			<view class="form-item">
				<text class="label">收货人</text>
				<input 
					type="text"
					v-model="form.receiver_name"
					placeholder="请输入收货人姓名"
					placeholder-class="input-placeholder"
				/>
			</view>
			
			<!-- 手机号 -->
			<view class="form-item">
				<text class="label">手机号码</text>
				<input 
					type="number"
					v-model="form.receiver_mobile"
					maxlength="11"
					placeholder="请输入手机号码"
					placeholder-class="input-placeholder"
				/>
			</view>
			
			<!-- 所在地区 -->
			<view class="form-item">
				<text class="label">所在地区</text>
				<view class="region">
					<view class="region-content" @click="showCityPicker">
						<text v-if="form.province">{{form.province}}{{form.city}}{{form.district}}</text>
						<text v-else class="placeholder">请选择所在地区</text>
						<text class="icon">></text>
					</view>
				</view>
			</view>
			
			<!-- 详细地址 -->
			<view class="form-item">
				<text class="label">详细地址</text>
				<textarea
					v-model="form.detail"
					placeholder="请输入详细地址"
					placeholder-class="input-placeholder"
				></textarea>
			</view>
			
			<!-- 设为默认 -->
			<view class="form-item default">
				<text>设为默认地址</text>
				<switch 
					:checked="form.is_default"
					@change="onDefaultChange"
					color="#ff6b6b"
				/>
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="footer">
			<button class="save-btn" @click="saveAddress">保存</button>
		</view>
		
		<!-- 城市选择弹窗 -->
		<uni-popup ref="cityPopup" type="bottom">
			<view class="city-picker">
				<view class="header">
					<text class="cancel" @click="closeCityPicker">取消</text>
					<text class="title">选择地区</text>
					<text class="confirm" @click="confirmCity">确定</text>
				</view>
				<view class="tabs">
					<view 
						class="tab-item" 
						:class="{active: activeTab === 0}"
						@click="switchTab(0)"
					>
						<text>{{selectedProvince.name || '请选择'}}</text>
					</view>
					<view 
						class="tab-item" 
						:class="{active: activeTab === 1}"
						@click="switchTab(1)"
						v-if="selectedProvince.cityId"
					>
						<text>{{selectedCity.name || '请选择'}}</text>
					</view>
					<view 
						class="tab-item" 
						:class="{active: activeTab === 2}"
						@click="switchTab(2)"
						v-if="selectedCity.cityId"
					>
						<text>{{selectedDistrict.name || '请选择'}}</text>
					</view>
				</view>
				<scroll-view 
					class="city-list"
					scroll-y
				>
					<view 
						class="city-item"
						v-for="item in cityList"
						:key="item.id"
						@click="selectCity(item)"
						:class="{active: isSelected(item)}"
					>
						<text>{{item.name}}</text>
						<text class="icon" v-if="isSelected(item)">√</text>
					</view>
				</scroll-view>
			</view>
		</uni-popup>
	</view>
</PageLayout>
</template>

<script>
import { getCityList } from '@/api/city'
import { addAddress, updateAddress, getAddressDetail } from '@/api/address'
import PageLayout from '@/components/layout/PageLayout.vue'

export default {
	components: {
		PageLayout
	},
	data() {
		return {
			addressId: '',
			form: {
				id: 0,
				receiver_name: '',
				receiver_mobile: '',
				province: '',
				city: '',
				district: '',
				detail: '',
				is_default: false
			},
			activeTab: 0,
			cityList: [],
			selectedProvince: {},
			selectedCity: {},
			selectedDistrict: {},
			showPicker: false
		}
	},
	onLoad(options) {
		if(options.id) {
			this.addressId = options.id
			this.loadAddress()
		}
	},
	methods: {
		// 加载地址详情
		async loadAddress() {
			try {
				uni.showLoading({
					title: '加载中...'
				})
				const { code, data } = await getAddressDetail(this.addressId)
				if (code === 200) {
					this.form = {
						receiver_name: data.receiver_name,
						receiver_mobile: data.receiver_mobile,
						province: data.province,
						city: data.city,
						district: data.district,
						detail: data.detail,
						is_default: data.is_default
					}
					if (this.addressId) {
						this.form.id = parseInt(data.id)
					}
				}
			} catch (e) {
				console.error(e)
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},
		// 显示城市选择器
		showCityPicker() {
			this.activeTab = 0
			this.loadCityList()
			this.$refs.cityPopup.open()
		},
		
		// 关闭城市选择器
		closeCityPicker() {
			this.$refs.cityPopup.close()
		},
		
		// 加载城市列表
		async loadCityList(parentId) {
			try {
				uni.showLoading({
					title: '加载中...'
				})
				const { code, data } = await getCityList(parentId || 0)
				if (code === 200) {
					this.cityList = data
				}
			} catch (e) {
				console.error(e)
				uni.showToast({
					title: '加载城市列表失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},
		
		// 切换标签页
		switchTab(index) {
			this.activeTab = index
			switch(index) {
				case 0: // 省份
					this.loadCityList(0)
					break
				case 1: // 城市
					if (this.selectedProvince.cityId) {
						this.loadCityList(this.selectedProvince.cityId)
					}
					break
				case 2: // 区县
					if (this.selectedCity.cityId) {
						this.loadCityList(this.selectedCity.cityId)
					}
					break
			}
		},
		
		// 选择城市
		async selectCity(item) {
			switch(this.activeTab) {
				case 0: // 选择省份
					this.selectedProvince = item
					this.selectedCity = {}
					this.selectedDistrict = {}
					await this.loadCityList(item.cityId) // 先加载该省份的城市列表
					this.activeTab = 1 // 再切换到城市标签
					break
				case 1: // 选择城市
					this.selectedCity = item
					this.selectedDistrict = {}
					await this.loadCityList(item.cityId) // 先加载该城市的区县列表
					this.activeTab = 2 // 再切换到区县标签
					break
				case 2: // 选择区县
					this.selectedDistrict = item
					this.confirmCity()
					break
			}
		},
		
		// 确认选择
		confirmCity() {
			if (!this.selectedProvince.cityId || !this.selectedCity.cityId || !this.selectedDistrict.cityId) {
				uni.showToast({
					title: '请选择完整的地区信息',
					icon: 'none'
				})
				return
			}
			
			this.form.province = this.selectedProvince.name
			this.form.city = this.selectedCity.name
			this.form.district = this.selectedDistrict.name
			
			this.closeCityPicker()
		},
		
		// 判断是否选中
		isSelected(item) {
			switch(this.activeTab) {
				case 0:
					return item.cityId === this.selectedProvince.cityId
				case 1:
					return item.cityId === this.selectedCity.cityId
				case 2:
					return item.cityId === this.selectedDistrict.cityId
			}
			return false
		},
		// 默认地址切换
		onDefaultChange(e) {
			this.form.is_default = e.detail.value
		},
		// 保存地址
		async saveAddress() {
			// 表单验证
			if(!this.form.receiver_name) {
				uni.showToast({
					title: '请输入收货人姓名',
					icon: 'none'
				})
				return
			}
			if(!this.form.receiver_mobile || !/^1[3-9]\d{9}$/.test(this.form.receiver_mobile)) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				})
				return
			}
			if(!this.form.province) {
				uni.showToast({
					title: '请选择所在地区',
					icon: 'none'
				})
				return
			}
			if(!this.form.detail) {
				uni.showToast({
					title: '请输入详细地址',
					icon: 'none'
				})
				return
			}
			
			try {
				uni.showLoading({
					title: '保存中...'
				})
				
				const api = this.addressId ? updateAddress : addAddress
				const formData = {...this.form}
				formData.id = this.addressId ? parseInt(this.addressId) : 0
				const { code } = await api(formData)
				
				if (code === 200) {
					uni.showToast({
						title: '保存成功'
					})
					// 返回上一页并刷新列表
					const pages = getCurrentPages()
					const prevPage = pages[pages.length - 2]
					prevPage.$vm.loadAddressList()
					uni.navigateBack()
				}
			} catch(e) {
				console.error(e)
				uni.showToast({
					title: '保存失败，请重试',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		}
	}
}
</script>

<style lang="scss">
.address-edit {
	min-height: 100vh;
	background: #f8f9fa;
	padding: 20rpx 30rpx 120rpx;
	
	.form {
		background: #fff;
		border-radius: 24rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		overflow: hidden;
		
		.form-item {
			display: flex;
			align-items: flex-start;
			padding: 30rpx;
			border-bottom: 1rpx solid #f0f0f0;
			
			&:last-child {
				border-bottom: none;
			}
			
			.label {
				width: 160rpx;
				font-size: 30rpx;
				color: #333;
				font-weight: 600;
				padding-top: 4rpx;
			}
			
			input {
				flex: 1;
				font-size: 28rpx;
				color: #333;
			}
			
			.region {
				flex: 1;
				display: flex;
				align-items: center;
				
				.region-content {
					flex: 1;
					display: flex;
					align-items: center;
					
					text {
						flex: 1;
						font-size: 28rpx;
						color: #333;
						
						&.placeholder {
							color: #999;
						}
					}
					
					.icon {
						flex: none;
						font-size: 32rpx;
						color: #ccc;
						margin-left: 20rpx;
					}
				}
			}
			
			textarea {
				flex: 1;
				height: 160rpx;
				font-size: 28rpx;
			}
			
			&.default {
				justify-content: space-between;
				align-items: center;
				
				text {
					font-size: 28rpx;
					color: #333;
				}
			}
		}
	}
	
	.footer {
		position: fixed;
		left: 30rpx;
		right: 30rpx;
		bottom: 30rpx;
		padding: 20rpx 0;
		z-index: 100;
		
		.save-btn {
			height: 88rpx;
			line-height: 88rpx;
			background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
			color: #fff;
			font-size: 30rpx;
			font-weight: 700;
			border-radius: 24rpx;
			box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.4);
			transition: all 0.3s ease;
			
			&::after {
				border: none;
			}
			
			&:active {
				transform: translateY(2rpx);
				box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.5);
			}
		}
	}
}

.input-placeholder {
	color: #999;
}

.city-picker {
	background: #fff;
	border-radius: 24rpx 24rpx 0 0;
	
	.header {
		display: flex;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		
		.cancel, .confirm {
			font-size: 28rpx;
			color: #666;
		}
		
		.title {
			flex: 1;
			text-align: center;
			font-size: 32rpx;
			font-weight: 700;
			color: #333;
		}
		
		.confirm {
			color: #FF6B35;
			font-weight: 600;
		}
	}
	
	.tabs {
		display: flex;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		
		.tab-item {
			padding: 10rpx 20rpx;
			margin-right: 20rpx;
			font-size: 28rpx;
			font-weight: 500;
			color: #666;
			transition: all 0.3s ease;
			
			&.active {
				color: #FF6B35;
				font-weight: 600;
				position: relative;
				
				&::after {
					content: '';
					position: absolute;
					left: 0;
					right: 0;
					bottom: -20rpx;
					height: 4rpx;
					background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
					border-radius: 2rpx;
				}
			}
		}
	}
	
	.city-list {
		height: 600rpx;
		
		.city-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx;
			font-size: 28rpx;
			color: #333;
			border-bottom: 1rpx solid #f0f0f0;
			transition: all 0.3s ease;
			
			&:active {
				background: rgba(255, 107, 53, 0.05);
			}
			
			&.active {
				color: #FF6B35;
				background: rgba(255, 107, 53, 0.05);
				font-weight: 600;
				
				.icon {
					color: #FF6B35;
				}
			}
		}
	}
}
</style> 