<template>
  <view class="order-detail-page">
    <view v-if="loading" class="loading">加载中...</view>
    <view v-else-if="!order">
      <view class="empty">订单不存在</view>
    </view>
    <view v-else>
      <!-- 订单状态 -->
      <view class="status-section">
        <view class="status-text">{{order.statusText}}</view>
        <view class="status-desc">{{order.statusDesc}}</view>
      </view>

      <!-- 订单基本信息 -->
      <view class="section">
        <view class="section-title">订单信息</view>
        <view class="row">
          <text class="label">订单号：</text>
          <text class="value">{{order.orderNo}}</text>
        </view>
        <view class="row">
          <text class="label">订单类型：</text>
          <text class="value">{{order.businessTypeText}}</text>
        </view>
        <view class="row">
          <text class="label">订单金额：</text>
          <text class="value price">￥{{order.payAmount}}</text>
        </view>
        <view class="row">
          <text class="label">支付方式：</text>
          <text class="value">{{order.payTypeText}}</text>
        </view>
        <view class="row">
          <text class="label">下单时间：</text>
          <text class="value">{{order.createTime}}</text>
        </view>
        <view class="row" v-if="order.remark">
          <text class="label">订单备注：</text>
          <text class="value">{{order.remark}}</text>
        </view>
      </view>

      <!-- 商品信息 -->
      <view class="section" v-if="order.goods && order.goods.length > 0">
        <view class="section-title">商品信息</view>
        <view class="goods-list">
          <view v-for="item in order.goods" :key="item.id" class="goods-item">
            <image :src="item.image" class="goods-img" mode="aspectFill" />
            <view class="goods-info">
              <view class="goods-name">{{item.goodsName}}</view>
              <view class="goods-specs" v-if="item.specs && item.specs !== '{}'">规格：{{item.specs}}</view>
              <view class="goods-price-qty">
                <text class="goods-price">￥{{item.price}}</text>
                <text class="goods-qty">x{{item.quantity}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 预约信息 -->
      <view class="section" v-if="order.bookingInfo">
        <view class="section-title">预约信息</view>
        <view class="row">
          <text class="label">服务项目：</text>
          <text class="value">{{order.bookingInfo.serviceName}}</text>
        </view>
        <view class="row">
          <text class="label">技师：</text>
          <text class="value">{{order.bookingInfo.technician}}</text>
        </view>
        <view class="row">
          <text class="label">预约日期：</text>
          <text class="value">{{order.bookingInfo.bookingDate}}</text>
        </view>
        <view class="row">
          <text class="label">预约时间：</text>
          <text class="value">{{order.bookingInfo.startTime}} - {{order.bookingInfo.endTime}}</text>
        </view>
        <view class="row">
          <text class="label">预约状态：</text>
          <text class="value status">{{order.bookingInfo.status}}</text>
        </view>
      </view>

      <!-- 套餐卡信息 -->
      <view class="section" v-if="order.cardInfo">
        <view class="section-title">套餐卡信息</view>
        <view class="row">
          <text class="label">卡名称：</text>
          <text class="value">{{order.cardInfo.cardName}}</text>
        </view>
        <view class="row">
          <text class="label">有效期：</text>
          <text class="value">{{order.cardInfo.validFrom}} ~ {{order.cardInfo.validTo}}</text>
        </view>
        <view class="row">
          <text class="label">剩余次数：</text>
          <text class="value">{{order.cardInfo.remainCount}}</text>
        </view>
        <view class="row">
          <text class="label">卡状态：</text>
          <text class="value status">{{order.cardInfo.status}}</text>
        </view>
      </view>

      <!-- 收货地址 -->
      <view class="section" v-if="order.address">
        <view class="section-title">收货地址</view>
        <view class="address-info">
          <view class="receiver">
            <text class="receiver-name">{{order.address.receiverName}}</text>
            <text class="receiver-phone">{{order.address.receiverMobile}}</text>
          </view>
          <view class="address-detail">
            {{order.address.province}} {{order.address.city}} {{order.address.district}} {{order.address.detail}}
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button 
          v-if="order.status === 'unpaid'" 
          class="btn btn-primary" 
          :class="{ disabled: paying }"
          @click="payOrder"
          :disabled="paying"
        >
          {{ paying ? '支付中...' : `立即支付 ¥${order.payAmount}` }}
        </button>
        <button v-if="order.status === 'unpaid'" class="btn btn-secondary" @click="cancelOrder">取消订单</button>
        <button v-if="order.status === 'delivered'" class="btn btn-primary" @click="confirmReceive">确认收货</button>
        <button v-if="order.status === 'completed'" class="btn btn-secondary" @click="deleteOrder">删除订单</button>
      </view>
    </view>
  </view>
</template>

<script>
import { getOrderDetail, cancelOrder, confirmReceive, deleteOrder, payOrder } from '@/api/order'

export default {
  data() {
    return {
      order: null,
      loading: false,
      paying: false
    }
  },
  onLoad(options) {
    this.fetchDetail(options.id)
  },
  methods: {
    fetchDetail(id) {
      this.loading = true
      getOrderDetail(id).then(res => {
        this.order = res.data
      }).catch(err => {
        uni.showToast({
          title: err.message || '获取订单详情失败',
          icon: 'none'
        })
      }).finally(() => {
        this.loading = false
      })
    },
    async payOrder() {
      if (this.paying) return
      
      this.paying = true
      try {
        const response = await payOrder({
          order_id: this.order.id,
          pay_type: 1 // 微信支付
        })

        console.log('支付接口响应:', response)

        if (response.code === 200) {
          // 微信支付
          this.handleWechatPayment(response.data)
        } else if (response.code === 400 && response.message === '订单已支付') {
          // 订单已经支付，刷新页面
          this.fetchDetail(this.order.id)
          uni.showToast({
            title: '订单已支付',
            icon: 'success'
          })
        } else {
          throw new Error(response.message || '支付失败')
        }
      } catch (error) {
        console.error('支付失败:', error)
        uni.showToast({
          title: error.message || '支付失败',
          icon: 'none'
        })
      } finally {
        this.paying = false
      }
    },

    // 处理微信支付
    handleWechatPayment(payData) {
      console.log('支付数据:', payData)
      
      // 后端返回的数据结构是 {success: true, data: {...}, message: "支付成功"}
      // 实际的支付参数在 payData.data 中
      const wechatPayData = payData.data || payData
      
      console.log('微信支付数据:', wechatPayData)
      
      // 检查支付数据是否包含必要的参数
      if (!wechatPayData.appId || !wechatPayData.timeStamp || !wechatPayData.nonceStr || !wechatPayData.package || !wechatPayData.signType || !wechatPayData.paySign) {
        console.error('微信支付参数不完整:', wechatPayData)
        uni.showToast({
          title: '支付参数错误',
          icon: 'none'
        })
        return
      }
      
      console.log('调用微信支付，参数:', {
        provider: 'wxpay',
        appId: wechatPayData.appId,
        timeStamp: wechatPayData.timeStamp,
        nonceStr: wechatPayData.nonceStr,
        package: wechatPayData.package,
        signType: wechatPayData.signType,
        paySign: wechatPayData.paySign
      })
      
      uni.requestPayment({
        provider: 'wxpay',
        appId: wechatPayData.appId,
        timeStamp: wechatPayData.timeStamp,
        nonceStr: wechatPayData.nonceStr,
        package: wechatPayData.package,
        signType: wechatPayData.signType,
        paySign: wechatPayData.paySign,
        success: () => {
          console.log('微信支付成功')
          this.paySuccess()
        },
        fail: (err) => {
          console.error('微信支付失败:', err)
          uni.showToast({
            title: '支付失败',
            icon: 'none'
          })
        }
      })
    },

    // 支付成功
    paySuccess() {
      uni.showToast({
        title: '支付成功',
        icon: 'success'
      })
      
      setTimeout(() => {
        // 刷新订单详情
        this.fetchDetail(this.order.id)
      }, 1500)
    },
    cancelOrder() {
      uni.showModal({
        title: '确认取消',
        content: '确定要取消这个订单吗？',
        success: (res) => {
          if (res.confirm) {
            cancelOrder(this.order.id).then(() => {
              uni.showToast({
                title: '订单已取消',
                icon: 'success'
              })
              this.fetchDetail(this.order.id)
            }).catch(err => {
              uni.showToast({
                title: err.message || '取消订单失败',
                icon: 'none'
              })
            })
          }
        }
      })
    },
    confirmReceive() {
      uni.showModal({
        title: '确认收货',
        content: '确认已收到商品吗？',
        success: (res) => {
          if (res.confirm) {
            confirmReceive(this.order.id).then(() => {
              uni.showToast({
                title: '确认收货成功',
                icon: 'success'
              })
              this.fetchDetail(this.order.id)
            }).catch(err => {
              uni.showToast({
                title: err.message || '确认收货失败',
                icon: 'none'
              })
            })
          }
        }
      })
    },
    deleteOrder() {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个订单吗？删除后无法恢复。',
        success: (res) => {
          if (res.confirm) {
            deleteOrder(this.order.id).then(() => {
              uni.showToast({
                title: '订单已删除',
                icon: 'success'
              })
              uni.navigateBack()
            }).catch(err => {
              uni.showToast({
                title: err.message || '删除订单失败',
                icon: 'none'
              })
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.order-detail-page {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 20px;
}

.status-section {
  background: linear-gradient(135deg, #e43c59, #ff6b6b);
  color: white;
  padding: 30px 20px;
  text-align: center;
}

.status-text {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.status-desc {
  font-size: 14px;
  opacity: 0.9;
}

.section {
  background: white;
  margin: 12px;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.label {
  color: #666;
  min-width: 80px;
}

.value {
  color: #333;
  text-align: right;
  flex: 1;
}

.price {
  color: #e43c59;
  font-weight: bold;
}

.status {
  color: #e43c59;
}

.goods-list {
  margin-top: 8px;
}

.goods-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.goods-item:last-child {
  border-bottom: none;
}

.goods-img {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  margin-right: 12px;
}

.goods-info {
  flex: 1;
}

.goods-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.4;
}

.goods-specs {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.goods-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  color: #e43c59;
  font-weight: bold;
}

.goods-qty {
  color: #666;
  font-size: 12px;
}

.address-info {
  margin-top: 8px;
}

.receiver {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.receiver-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-right: 12px;
}

.receiver-phone {
  color: #666;
}

.address-detail {
  color: #333;
  line-height: 1.5;
}

.action-buttons {
  padding: 20px 12px;
  display: flex;
  gap: 12px;
}

.btn {
  flex: 1;
  padding: 12px 0;
  border-radius: 6px;
  font-size: 16px;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #FF6B81 0%, #FF8E9E 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 129, 0.3);
  transition: all 0.3s ease;
}

.btn-primary:active {
  transform: scale(0.98);
}

.btn-primary.disabled {
  background: #ccc;
  box-shadow: none;
}

.btn-secondary {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.empty, .loading {
  text-align: center;
  color: #999;
  margin-top: 100px;
  font-size: 16px;
}
</style> 