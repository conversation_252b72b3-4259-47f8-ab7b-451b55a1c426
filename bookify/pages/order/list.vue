<template>
  <view class="order-list-page">
    <view class="order-tabs">
      <button v-for="(item, idx) in tabs" :key="item.value" :class="['tab-btn', idx===tabIndex?'active':'']" @click="changeTab(idx)">{{item.label}}</button>
    </view>
    <view v-if="loading" class="loading">加载中...</view>
    <view v-else>
      <view v-if="orders.length === 0" class="empty">暂无订单</view>
      <view v-for="order in orders" :key="order.id" class="order-item" @click="goDetail(order.id)">
        <view class="order-row">
          <text>订单号：</text><text>{{order.orderNo}}</text>
        </view>
        <view class="order-row">
          <text>金额：</text><text class="price">￥{{order.payAmount}}</text>
        </view>
        <view class="order-row">
          <text>状态：</text><text>{{order.statusText}}</text>
        </view>
        <view class="order-row">
          <text>下单时间：</text><text>{{order.createTime}}</text>
        </view>
        <!-- 预约订单展示 -->
        <view v-if="order.businessType === 'booking' && order.bookingInfo" class="booking-info">
          <view>服务：{{order.bookingInfo.serviceName}}</view>
          <view>技师：{{order.bookingInfo.technician}}</view>
          <view>预约时间：{{order.bookingInfo.bookingDate}} {{order.bookingInfo.startTime}}-{{order.bookingInfo.endTime}}</view>
          <view>预约状态：{{order.bookingInfo.status}}</view>
        </view>
        <!-- 套餐卡订单展示 -->
        <view v-else-if="order.businessType === 'card' && order.cardInfo" class="card-info">
          <view>卡名称：{{order.cardInfo.cardName}}</view>
          <view>有效期：{{order.cardInfo.validFrom}} ~ {{order.cardInfo.validTo}}</view>
          <view>剩余次数：{{order.cardInfo.remainCount}}</view>
        </view>
        <!-- 商品订单展示 -->
        <view v-else-if="order.goods && order.goods.length > 0" class="goods-list">
          <view v-for="g in order.goods" :key="g.id" class="goods-item">
            <image :src="g.image" class="goods-img" mode="aspectFill" />
            <view class="goods-info">
              <view class="goods-name">{{g.goodsName}}</view>
              <view class="goods-specs" v-if="g.specs && g.specs !== '{}'">规格：{{g.specs}}</view>
              <view class="goods-qty">数量：{{g.quantity}}</view>
              <view class="goods-price">单价：￥{{g.price}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getOrderList } from '@/api/order'
export default {
  data() {
    return {
      tabs: [
        { label: '全部', value: '' },
        { label: '预约', value: 'booking' },
        { label: '套餐卡', value: 'card' },
        { label: '商品', value: 'goods' }
      ],
      tabIndex: 0,
      orders: [],
      loading: false
    }
  },
  onLoad() {
    this.fetchOrders()
  },
  methods: {
    changeTab(idx) {
      this.tabIndex = idx
      this.fetchOrders()
    },
    fetchOrders() {
      this.loading = true
      const businessType = this.tabs[this.tabIndex].value
      getOrderList(businessType ? { businessType } : {}).then(res => {
        this.orders = res.data.list || []
      }).finally(() => {
        this.loading = false
      })
    },
    goDetail(id) {
      uni.navigateTo({ url: `/pages/order/detail?id=${id}` })
    }
  }
}
</script>

<style scoped>
.order-list-page { padding: 16px; }
.order-tabs { display: flex; margin-bottom: 12px; }
.tab-btn { flex: 1; padding: 8px 0; border: none; background: #f5f5f5; color: #888; }
.tab-btn.active { background: #ffb6c1; color: #fff; font-weight: bold; }
.order-item { background: #fff; border-radius: 8px; margin-bottom: 12px; padding: 12px; box-shadow: 0 2px 8px #f5f5f5; }
.order-row { display: flex; justify-content: space-between; margin-bottom: 4px; }
.price { color: #e43c59; font-weight: bold; }
.empty { text-align: center; color: #bbb; margin-top: 40px; }
.loading { text-align: center; color: #888; margin-top: 40px; }
.goods-list { margin-top: 8px; }
.goods-item { display: flex; align-items: center; margin-bottom: 8px; background: #fafafa; border-radius: 6px; padding: 6px; }
.goods-img { width: 48px; height: 48px; border-radius: 4px; margin-right: 10px; background: #f0f0f0; }
.goods-info { flex: 1; }
.goods-name { font-size: 15px; font-weight: 500; color: #333; margin-bottom: 2px; }
.goods-specs { font-size: 12px; color: #888; margin-bottom: 2px; }
.goods-qty, .goods-price { font-size: 12px; color: #888; display: inline-block; margin-right: 10px; }
.booking-info, .card-info { background: #f9f9f9; border-radius: 6px; padding: 8px 10px; margin-top: 8px; font-size: 14px; color: #555; }
</style> 