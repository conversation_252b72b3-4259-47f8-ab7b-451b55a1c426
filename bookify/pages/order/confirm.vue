<template>
	<PageLayout
				:show-nav-bar="true" 
		:nav-transparent="false" 
		:show-left="true"
		:show-center="true"  
		nav-title="确认订单"
		@navLeftClick="handleBack"
	>
	<view class="order-confirm">
		<!-- 渐变背景 -->
		<view class="background-gradient"></view>

		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- 收货地址 -->
			<view class="section address-section">
				<view class="section-header">
					<text class="header-icon">📍</text>
					<text class="header-title">收货地址</text>
				</view>
				<view class="address-content" @click="chooseAddress">
					<view v-if="address.receiver_name" class="address-info">
						<view class="user-row">
							<text class="user-name">{{address.receiver_name}}</text>
							<text class="user-phone">{{address.receiver_mobile}}</text>
							<view class="default-badge" v-if="address.is_default">默认</view>
						</view>
						<text class="address-detail">{{address.province}}{{address.city}}{{address.district}}{{address.detail}}</text>
					</view>
					<view v-else class="no-address">
						<text class="no-address-text">请选择收货地址</text>
						<text class="no-address-tip">点击添加收货地址</text>
					</view>
					<text class="arrow-right">›</text>
				</view>
			</view>

			<!-- 商品清单 -->
			<view class="section goods-section">
				<view class="section-header">
					<text class="header-icon">🛒</text>
					<text class="header-title">商品清单</text>
					<text class="goods-count">({{orderGoods.length}}件)</text>
				</view>
				<view class="goods-list">
					<view class="goods-item" v-for="item in orderGoods" :key="item.id">
						<image 
							class="goods-image" 
							:src="item.image || '/static/images/default-goods.png'" 
							mode="aspectFill"
							@error="onImageError"
						></image>
						<view class="goods-detail">
							<text class="goods-name">{{item.title}}</text>
							<text class="goods-spec" v-if="item.specText">{{item.specText}}</text>
							<view class="price-row">
								<text class="goods-price">¥{{item.price}}</text>
								<text class="goods-quantity">×{{item.num}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 订单金额 -->
			<view class="section amount-section">
				<view class="section-header">
					<text class="header-icon">💰</text>
					<text class="header-title">订单金额</text>
				</view>
				<view class="amount-list">
					<view class="amount-row">
						<text class="amount-label">商品总价</text>
						<text class="amount-value">¥{{totalPrice}}</text>
					</view>
					<view class="amount-row">
						<text class="amount-label">运费</text>
						<text class="amount-value">¥{{freight}}</text>
					</view>
					<view class="amount-row" v-if="discountAmount > 0">
						<text class="amount-label">优惠金额</text>
						<text class="amount-value discount">-¥{{discountAmount}}</text>
					</view>
					<view class="amount-row total-row">
						<text class="amount-label total-label">实付金额</text>
						<text class="amount-value total-value">¥{{actualPrice}}</text>
					</view>
				</view>
			</view>

			<!-- 订单备注 -->
			<view class="section remark-section">
				<view class="section-header">
					<text class="header-icon">💬</text>
					<text class="header-title">订单备注</text>
				</view>
				<view class="remark-wrapper">
					<textarea 
						v-model="remark"
						placeholder="选填，请先和商家协商一致（限100字）"
						placeholder-class="remark-placeholder"
						maxlength="100"
						auto-height
						class="remark-input"
					></textarea>
				</view>
			</view>

			<!-- 支付方式 -->
			<view class="section payment-section">
				<view class="section-header">
					<text class="header-icon">💳</text>
					<text class="header-title">支付方式</text>
				</view>
				<view class="payment-options">
					<view 
						class="payment-option" 
						:class="{active: payType === item.type, disabled: item.disabled}"
						v-for="item in payTypes" 
						:key="item.type"
						@click="selectPayType(item.type)"
					>
						<view class="payment-info">
							<image :src="item.icon" mode="aspectFit" class="payment-icon"></image>
							<text class="payment-name">{{item.name}}</text>
						</view>
						<view class="payment-extra">
							<text class="balance-info" v-if="item.type === 'balance'">
								余额：¥{{userInfo.balance || '0.00'}}
							</text>
							<view class="radio-button" :class="{checked: payType === item.type}">
								<view class="radio-dot" v-if="payType === item.type"></view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部提交区域 -->
		<view class="submit-section">
			<view class="price-summary">
				<text class="summary-label">合计：</text>
				<text class="summary-price">¥{{actualPrice}}</text>
			</view>
			<button 
				class="submit-button" 
				:class="{disabled: !canSubmit}"
				@click="submitOrder"
				:disabled="!canSubmit"
			>
				<text class="submit-text">{{submitBtnText}}</text>
			</button>
		</view>

		<!-- 加载遮罩 -->
		<view class="loading-overlay" v-if="loading">
			<view class="loading-card">
				<view class="loading-spinner"></view>
				<text class="loading-text">{{loadingText}}</text>
			</view>
		</view>

	</view>
	</PageLayout>
</template>

<script>
import { calculateOrder, createOrder, payOrder } from '@/api/order'
import { getUserInfo } from '@/api/user'
import { getDefaultAddress } from '@/api/address'
import { getCartList } from '@/api/cart'
import { getGoodsDetail } from '@/api/goods'
import { getShopConfigs } from '@/api/config'
import PageLayout from '@/components/layout/PageLayout.vue'
export default {
	components: {
		PageLayout
	},
	data() {
		return {
			// 地址信息
			address: {
				id: 0,
				receiver_name: '',
				receiver_mobile: '',
				province: '',
				city: '',
				district: '',
				detail: '',
				is_default: false
			},
			// 订单商品列表
			orderGoods: [],
			// 订单信息
			remark: '',
			freight: 0,
			discountAmount: 0,
			// 支付信息
			payType: 'wxpay',
			payTypes: [
				{
					type: 'wxpay',
					name: '微信支付',
					icon: '/static/images/payment/wechat-pay.png',
					disabled: false
				},
				{
					type: 'alipay',
					name: '支付宝支付',
					icon: '/static/images/payment/alipay.png',
					disabled: false
				},
				{
					type: 'balance',
					name: '余额支付',
					icon: '/static/images/payment/balance.png',
					disabled: false
				}
			],
			// 用户信息
			userInfo: {
				balance: 0
			},
			// 订单类型
			type: 'buy',
					// 页面状态
		loading: false,
		loadingText: '加载中...',
		pageReady: false,
		
		// 订单信息
		orderInfo: {},
		// 是否已创建订单
		hasCreatedOrder: false
		}
	},
	
	computed: {
		// 商品总价
		totalPrice() {
			return this.orderGoods.reduce((total, item) => {
				return total + (parseFloat(item.price) * parseInt(item.num))
			}, 0).toFixed(2)
		},
		
		// 实付金额
		actualPrice() {
			const total = parseFloat(this.totalPrice) + parseFloat(this.freight) - parseFloat(this.discountAmount)
			return Math.max(0, total).toFixed(2)
		},
		
		// 是否可以提交订单
		canSubmit() {
			return this.pageReady && 
				   this.address.receiver_name && 
				   this.orderGoods.length > 0 && 
				   !this.loading
		},
		
		// 提交按钮文字
		submitBtnText() {
			if (!this.pageReady) return '加载中...'
			if (!this.address.receiver_name) return '请选择收货地址'
			if (this.orderGoods.length === 0) return '暂无商品'
			if (this.loading) return '处理中...'
			if (this.hasCreatedOrder) return '立即支付'
			return '提交订单'
		}
	},
	
	onLoad(options) {
		console.log('订单确认页面参数：', options)
		this.initPage(options)
	},
	
	methods: {
		// 初始化页面
		async initPage(options) {
			try {
				this.loading = true
				this.loadingText = '正在加载订单信息...'
				
				// 解析参数并获取订单商品
				await this.parseOptionsAndLoadGoods(options)
				
				// 并行加载用户信息和地址信息
				await Promise.all([
					this.loadUserInfo(),
					this.loadDefaultAddress()
				])
				
				// 计算订单金额
				if (this.orderGoods.length > 0) {
					await this.calculateOrderAmount()
				}
				
				this.pageReady = true
			} catch (error) {
				console.error('页面初始化失败:', error)
				this.showError(error.message || '页面加载失败')
			} finally {
				this.loading = false
			}
		},
		
		// 解析参数并加载商品
		async parseOptionsAndLoadGoods(options) {
			if (options.type === 'cart') {
				// 从购物车下单
				this.type = 'cart'
				await this.loadCartGoods()
			} else if (options.data) {
				// 从商品详情页面直接购买
				this.type = 'buy'
				await this.loadDirectBuyGoods(options.data)
			} else if (options.goodsId) {
				// 兼容旧格式的直接购买
				this.type = 'buy'
				await this.loadLegacyBuyGoods(options)
			} else {
				throw new Error('订单参数错误')
			}
		},
		
		// 加载购物车商品
		async loadCartGoods() {
			const { code, data } = await getCartList({ selected: true })
			if (code !== 200 || !data || !data.list || data.list.length === 0) {
				throw new Error('购物车中没有选中的商品')
			}
			
			this.orderGoods = data.list.map(item => ({
				id: item.goodsId,
				skuCode: item.skuCode || '',
				title: item.name,
				image: item.image,
				price: parseFloat(item.price).toFixed(2),
				num: parseInt(item.quantity),
				specText: this.formatSpecs(item.specs)
			}))
		},
		
		// 加载直接购买商品
		async loadDirectBuyGoods(dataStr) {
			const orderData = JSON.parse(decodeURIComponent(dataStr))
			const { code, data } = await getGoodsDetail({ id: orderData.goodsId })
			
			if (code !== 200 || !data) {
				throw new Error('获取商品信息失败')
			}
			
			// 构建规格文本
			let specText = ''
			if (orderData.comboSpecs) {
				specText = Object.entries(orderData.comboSpecs)
					.map(([key, value]) => `${key}：${value}`)
					.join('，')
			} else if (orderData.specs) {
				specText = Object.entries(orderData.specs)
					.map(([key, value]) => `${key}：${value}`)
					.join('，')
			}
			
			this.orderGoods = [{
				id: data.id,
				skuCode: orderData.skuCode || '',
				title: data.name,
				image: data.images && data.images[0] ? data.images[0] : '/static/images/default-goods.png',
				price: (parseFloat(orderData.totalPrice) / parseInt(orderData.quantity)).toFixed(2),
				num: parseInt(orderData.quantity),
				specText: specText
			}]
		},
		
		// 加载兼容格式的直接购买商品
		async loadLegacyBuyGoods(options) {
			const goodsId = parseInt(options.goodsId)
			const { code, data } = await getGoodsDetail({ id: goodsId })
			
			if (code !== 200 || !data) {
				throw new Error('获取商品信息失败')
			}
			
			let price = parseFloat(data.price)
			let specText = decodeURIComponent(options.specText || '')
			
			// 处理多规格商品
			if (data.specType === 2 && data.specInfo && data.specInfo.skus && options.skuCode) {
				const skuInfo = data.specInfo.skus.find(sku => sku.skuCode === options.skuCode)
				if (skuInfo) {
					price = parseFloat(skuInfo.price)
					if (!specText) {
						specText = Object.entries(skuInfo.specs || {})
							.map(([key, value]) => `${key}：${value}`)
							.join('，')
					}
				}
			}
			
			this.orderGoods = [{
				id: data.id,
				skuCode: options.skuCode || '',
				title: data.name,
				image: data.images && data.images[0] ? data.images[0] : '/static/images/default-goods.png',
				price: price.toFixed(2),
				num: parseInt(options.num) || 1,
				specText: specText
			}]
		},
		
		// 格式化规格信息
		formatSpecs(specs) {
			if (!specs || !Array.isArray(specs)) return ''
			return specs.map(spec => `${spec.name}:${spec.value}`).join('，')
		},
		
		// 加载用户信息
		async loadUserInfo() {
			try {
				const { code, data } = await getUserInfo()
				if (code === 200 && data) {
					this.userInfo = {
						...data,
						balance: parseFloat(data.balance || 0).toFixed(2)
					}
				} else {
					// 用户信息获取失败时使用默认值
					this.userInfo = { balance: '0.00' }
					console.warn('获取用户信息失败，使用默认值')
				}
			} catch (error) {
				console.error('获取用户信息失败:', error)
				this.userInfo = { balance: '0.00' }
			}
		},
		
		// 加载默认收货地址
		async loadDefaultAddress() {
			try {
				const { code, data } = await getDefaultAddress()
				if (code === 200 && data) {
					this.address = {
						id: data.id,
						receiver_name: data.receiver_name || data.receiverName,
						receiver_mobile: data.receiver_mobile || data.receiverMobile,
						province: data.province,
						city: data.city,
						district: data.district,
						detail: data.detail,
						is_default: data.is_default || data.isDefault
					}
				}
			} catch (error) {
				console.error('获取默认地址失败:', error)
			}
		},
		
		// 计算订单金额
		async calculateOrderAmount() {
			try {
				const { code, data } = await calculateOrder({
					goods: this.orderGoods.map(item => ({
						goodsId: item.id,
						skuCode: item.skuCode,
						num: item.num
					}))
				})
				
				if (code === 200 && data) {
					this.freight = parseFloat(data.freightAmount || 0).toFixed(2)
					this.discountAmount = '0.00' // 固定设置为0
				} else {
					// 如果计算订单接口失败，从配置获取运费
					console.warn('计算订单接口失败，从配置获取运费')
					this.freight = await this.getFreightFromConfig()
					this.discountAmount = '0.00'
				}
			} catch (error) {
				console.error('计算订单金额失败:', error)
				// 从配置获取运费作为备用方案
				this.freight = await this.getFreightFromConfig()
				this.discountAmount = '0.00'
			}
		},
		
		// 从配置获取运费
		async getFreightFromConfig() {
			try {
				const { code, data } = await getShopConfigs(['shop.yunfei.shunfeng'])
				if (code === 200 && data && data.configs) {
					const freightConfig = data.configs['shop.yunfei.shunfeng']
					if (freightConfig !== undefined) {
						console.log('从配置获取运费:', freightConfig)
						return parseFloat(freightConfig).toFixed(2)
					}
				}
			} catch (error) {
				console.error('获取运费配置失败:', error)
			}
			console.log('使用默认运费: 5.00')
			return '5.00' // 使用数据库中的默认运费值
		},
		
		// 选择收货地址
		chooseAddress() {
			uni.navigateTo({
				url: '/pages/user/address/list?from=order'
			})
		},
		
		// 选择支付方式
		selectPayType(type) {
			if (type === 'balance') {
				const balance = parseFloat(this.userInfo.balance || 0)
				const actualPrice = parseFloat(this.actualPrice)
				
				if (balance < actualPrice) {
					uni.showToast({
						title: '余额不足',
						icon: 'none'
					})
					return
				}
			}
			
			this.payType = type
		},
		
		// 提交订单
		async submitOrder() {
			if (!this.canSubmit) return
			
			// 最终校验
			if (!this.address.receiver_name) {
				uni.showToast({
					title: '请选择收货地址',
					icon: 'none'
				})
				return
			}
			
			if (this.orderGoods.length === 0) {
				uni.showToast({
					title: '订单中没有商品',
					icon: 'none'
				})
				return
			}
			
			try {
				this.loading = true
				
				// 如果已经创建过订单，直接发起支付
				if (this.hasCreatedOrder && this.orderInfo.orderId) {
					console.log('订单已存在，直接发起支付:', this.orderInfo.orderId)
					this.loadingText = '正在发起支付...'
					await this.directPay(this.orderInfo)
					return
				}
				
				// 创建新订单
				this.loadingText = '正在创建订单...'
				
				const orderData = {
					receiverName: this.address.receiver_name,
					receiverPhone: this.address.receiver_mobile,
					receiverProvince: this.address.province,
					receiverCity: this.address.city,
					receiverDistrict: this.address.district,
					receiverAddress: this.address.detail,
					payType: this.convertPayType(this.payType),
					remark: this.remark.trim(),
					type: this.type,
					goods: this.orderGoods.map(item => ({
						goodsId: item.id,
						skuCode: item.skuCode,
						num: item.num
					}))
				}
				
				console.log('提交订单数据：', orderData)
				
				const { code, data, message } = await createOrder(orderData)
				
				if (code !== 200 || !data || !data.orderId) {
					throw new Error(message || '创建订单失败')
				}
				
				// 标记已创建订单
				this.hasCreatedOrder = true
				this.orderInfo = data
				
				// 订单创建成功，直接发起支付
				this.loadingText = '正在发起支付...'
				await this.directPay(data)
				
			} catch (error) {
				console.error('提交订单失败:', error)
				this.showError(error.message || '订单提交失败，请重试')
			} finally {
				this.loading = false
			}
		},
		
		// 转换支付方式
		convertPayType(type) {
			const payTypeMap = {
				wxpay: 1,
				alipay: 2,
				balance: 3
			}
			return payTypeMap[type] || 1
		},
		
		// 显示错误信息
		showError(message) {
			uni.showToast({
				title: message,
				icon: 'none',
				duration: 3000
			})
		},
		
		// 图片加载失败处理
		onImageError() {
			// 可以在这里处理图片加载失败的情况
			console.log('商品图片加载失败')
		},
		
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},
		
		// 处理导航栏返回按钮点击
		handleBack() {
			uni.navigateBack()
		},
		

		
		// 处理微信支付
		handleWechatPay(payData) {
			if (payData.success) {
				uni.requestPayment({
					provider: 'wxpay',
					timeStamp: payData.timeStamp,
					nonceStr: payData.nonceStr,
					package: payData.package,
					signType: payData.signType,
					paySign: payData.paySign,
					success: () => {
						this.paySuccess(this.orderInfo.orderId)
					},
					fail: (err) => {
						console.error('微信支付失败:', err)
						this.payFail(err)
					}
				})
			} else {
				throw new Error(payData.message || '微信支付参数错误')
			}
		},
		
		// 处理支付宝支付
		handleAlipay(payData) {
			if (payData.success) {
				uni.requestPayment({
					provider: 'alipay',
					orderInfo: payData.orderInfo,
					success: () => {
						this.paySuccess(this.orderInfo.orderId)
					},
					fail: (err) => {
						console.error('支付宝支付失败:', err)
						this.payFail(err)
					}
				})
			} else {
				throw new Error(payData.message || '支付宝支付参数错误')
			}
		},
		
		// 处理余额支付
		handleBalancePay(payData) {
			if (payData.success) {
				this.paySuccess(this.orderInfo.orderId)
			} else {
				throw new Error(payData.message || '余额支付失败')
			}
		},
		
		// 支付成功
		paySuccess(orderId) {
			uni.showToast({
				title: '支付成功',
				icon: 'success'
			})
			
			// 跳转到订单详情或订单列表
			setTimeout(() => {
				uni.redirectTo({
					url: `/pages/order/detail?id=${orderId}`
				})
			}, 1500)
		},
		
		// 支付失败
		payFail(error) {
			console.error('支付失败:', error)
			uni.showToast({
				title: '支付失败，请重新支付',
				icon: 'none'
			})
			// 支付失败后保持在当前页面，让用户可以重新支付
		},
		
		// 直接发起支付
		async directPay(orderInfo) {
			try {
				// 保存订单信息
				this.orderInfo = orderInfo
				const payType = this.convertPayType(this.payType)
				
				// 如果是余额支付，检查余额是否足够
				if (payType === 3) {
					const balance = parseFloat(this.userInfo.balance || 0)
					const payAmount = parseFloat(orderInfo.payAmount || this.actualPrice)
					
					if (balance < payAmount) {
						uni.showToast({
							title: '余额不足，请选择其他支付方式',
							icon: 'none'
						})
						// 余额不足时保持在当前页面，让用户选择其他支付方式
						return
					}
				}
				
				// 准备支付参数
				let payParams = {
					orderId: orderInfo.orderId,
					payType: payType
				}
				
				// 如果是微信支付，先获取微信登录code
				if (payType === 1) {
					this.loadingText = '正在获取微信授权...'
					const wechatCode = await this.getWechatLoginCode()
					if (!wechatCode) {
						throw new Error('获取微信授权失败，请重试')
					}
					payParams.wechatCode = wechatCode
				}
				
				// 调用支付接口
				this.loadingText = '正在发起支付...'
				const { code, data, message } = await payOrder(payParams)
				
				if (code !== 200) {
					throw new Error(message || '支付失败')
				}
				
				// 根据支付方式处理
				if (payType === 1) {
					// 微信支付
					this.handleWechatPay(data)
				} else if (payType === 2) {
					// 支付宝支付
					this.handleAlipay(data)
				} else if (payType === 3) {
					// 余额支付
					this.handleBalancePay(data)
				}
				
			} catch (error) {
				console.error('支付失败:', error)
				uni.showToast({
					title: error.message || '支付失败，请重新支付',
					icon: 'none'
				})
				// 支付失败后保持在当前页面，让用户可以重新支付
			}
		},
		
		// 获取微信登录code
		async getWechatLoginCode() {
			return new Promise((resolve) => {
				uni.login({
					provider: 'weixin',
					success: (res) => {
						console.log('微信登录成功:', res)
						if (res.code) {
							resolve(res.code)
						} else {
							console.error('微信登录失败:', res)
							resolve(null)
						}
					},
					fail: (err) => {
						console.error('微信登录失败:', err)
						resolve(null)
					}
				})
			})
		},

	}
}
</script>

<style lang="scss">
.order-confirm {
	min-height: 100vh;
	position: relative;
	overflow: hidden;
}



// 主要内容区域
.main-content {
	position: relative;
	z-index: 1;
	background: rgba(255, 255, 255, 0.95);
	margin: 20rpx;
	border-radius: 24rpx;
	overflow: hidden;
	box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(20px);
	margin-bottom: 200rpx; /* 增加底部间距，避免被提交按钮遮挡 */
}

// 通用区块样式
.section {
	position: relative;
	
	&:not(:last-child) {
		border-bottom: 1rpx solid rgba(233, 236, 239, 0.3);
	}
	
	.section-header {
		display: flex;
		align-items: center;
		padding: 32rpx 32rpx 20rpx;
		background: rgba(248, 249, 250, 0.5);
		
		.header-icon {
			font-size: 32rpx;
			margin-right: 16rpx;
		}
		
		.header-title {
			font-size: 32rpx;
			font-weight: 700;
			color: #2C3E50;
			flex: 1;
		}
		
		.goods-count {
			font-size: 24rpx;
			color: #FF6B35;
			font-weight: 600;
		}
	}
}

// 收货地址区块
.address-section {
	.address-content {
		padding: 0 32rpx 32rpx;
		display: flex;
		align-items: center;
		
		.address-info {
			flex: 1;
			
			.user-row {
				display: flex;
				align-items: center;
				margin-bottom: 16rpx;
				
				.user-name {
					font-size: 32rpx;
					font-weight: 700;
					color: #2C3E50;
					margin-right: 20rpx;
				}
				
				.user-phone {
					font-size: 28rpx;
					color: #6C757D;
					margin-right: 16rpx;
				}
				
				.default-badge {
					background: linear-gradient(135deg, #FF6B35, #F7931E);
					color: #fff;
					font-size: 20rpx;
					font-weight: 600;
					padding: 6rpx 16rpx;
					border-radius: 12rpx;
					box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
				}
			}
			
			.address-detail {
				font-size: 28rpx;
				color: #6C757D;
				line-height: 1.6;
			}
		}
		
		.no-address {
			flex: 1;
			
			.no-address-text {
				font-size: 32rpx;
				color: #2C3E50;
				font-weight: 600;
				display: block;
				margin-bottom: 8rpx;
			}
			
			.no-address-tip {
				font-size: 24rpx;
				color: #ADB5BD;
			}
		}
		
		.arrow-right {
			font-size: 32rpx;
			color: #ADB5BD;
			margin-left: 20rpx;
		}
	}
}

// 商品清单区块
.goods-section {
	.goods-list {
		padding: 0 32rpx 32rpx;
		
		.goods-item {
			display: flex;
			padding: 20rpx 0;
			border-bottom: 1rpx solid rgba(233, 236, 239, 0.6);
			
			&:last-child {
				border-bottom: none;
			}
			
			.goods-image {
				width: 120rpx;
				height: 120rpx;
				border-radius: 16rpx;
				margin-right: 24rpx;
				flex-shrink: 0;
				box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
			}
			
			.goods-detail {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				
				.goods-name {
					font-size: 28rpx;
					font-weight: 600;
					color: #2C3E50;
					margin-bottom: 8rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
				
				.goods-spec {
					font-size: 24rpx;
					color: #ADB5BD;
					margin-bottom: 16rpx;
					background: rgba(255, 107, 53, 0.1);
					padding: 8rpx 16rpx;
					border-radius: 12rpx;
					display: inline-block;
					max-width: fit-content;
				}
				
				.price-row {
					display: flex;
					justify-content: space-between;
					align-items: center;
					
					.goods-price {
						font-size: 32rpx;
						font-weight: 700;
						color: #FF6B35;
					}
					
					.goods-quantity {
						font-size: 28rpx;
						color: #6C757D;
						font-weight: 500;
					}
				}
			}
		}
	}
}

// 订单金额区块
.amount-section {
	.amount-list {
		padding: 0 32rpx 32rpx;
		
		.amount-row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 16rpx 0;
			border-bottom: 1rpx solid rgba(233, 236, 239, 0.6);
			
			&:last-child {
				border-bottom: none;
			}
			
			&.total-row {
				padding-top: 24rpx;
				margin-top: 16rpx;
				border-top: 2rpx solid rgba(255, 107, 53, 0.2);
				border-bottom: none;
			}
			
			.amount-label {
				font-size: 28rpx;
				color: #6C757D;
				font-weight: 500;
				
				&.total-label {
					font-size: 32rpx;
					font-weight: 700;
					color: #2C3E50;
				}
			}
			
			.amount-value {
				font-size: 28rpx;
				color: #2C3E50;
				font-weight: 600;
				
				&.discount {
					color: #28A745;
				}
				
				&.total-value {
					font-size: 36rpx;
					font-weight: 700;
					color: #FF6B35;
				}
			}
		}
	}
}

// 订单备注区块
.remark-section {
	.remark-wrapper {
		padding: 0 32rpx 32rpx;
		
		.remark-input {
			width: 100%;
			min-height: 80rpx;
			font-size: 28rpx;
			color: #2C3E50;
			line-height: 1.6;
			background: rgba(248, 249, 250, 0.8);
			border: 2rpx solid rgba(233, 236, 239, 0.8);
			border-radius: 16rpx;
			padding: 20rpx;
			transition: all 0.3s ease;
			
			&:focus {
				border-color: #FF6B35;
				background: #fff;
				box-shadow: 0 0 0 8rpx rgba(255, 107, 53, 0.1);
			}
		}
		
		.remark-placeholder {
			color: #ADB5BD;
			font-size: 26rpx;
		}
	}
}

// 支付方式区块
.payment-section {
	.payment-options {
		padding: 0 32rpx 32rpx;
		
		.payment-option {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 24rpx 0;
			border-bottom: 1rpx solid rgba(233, 236, 239, 0.6);
			transition: all 0.3s ease;
			
			&:last-child {
				border-bottom: none;
			}
			
			&.active {
				background: rgba(255, 107, 53, 0.05);
				border-radius: 16rpx;
				padding: 24rpx 20rpx;
				margin: 0 -20rpx;
			}
			
			&.disabled {
				opacity: 0.5;
				pointer-events: none;
			}
			
			.payment-info {
				display: flex;
				align-items: center;
				flex: 1;
				
				.payment-icon {
					width: 48rpx;
					height: 48rpx;
					margin-right: 20rpx;
					border-radius: 8rpx;
				}
				
				.payment-name {
					font-size: 28rpx;
					color: #2C3E50;
					font-weight: 600;
				}
			}
			
			.payment-extra {
				display: flex;
				align-items: center;
				
				.balance-info {
					font-size: 24rpx;
					color: #6C757D;
					margin-right: 16rpx;
				}
				
				.radio-button {
					width: 32rpx;
					height: 32rpx;
					border: 3rpx solid #E9ECEF;
					border-radius: 50%;
					position: relative;
					transition: all 0.3s ease;
					display: flex;
					align-items: center;
					justify-content: center;
					
					&.checked {
						border-color: #FF6B35;
						background: #fff;
						
						.radio-dot {
							width: 16rpx;
							height: 16rpx;
							background: #FF6B35;
							border-radius: 50%;
						}
					}
				}
			}
		}
	}
}

// 底部提交区域
.submit-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20px);
	padding: 30rpx 30rpx;
	padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
	border-top: 1rpx solid rgba(233, 236, 239, 0.6);
	display: flex;
	align-items: center;
	justify-content: space-between;
	min-height: 120rpx; /* 确保最小高度 */
	
	.price-summary {
		flex: 1;
		
		.summary-label {
			font-size: 28rpx;
			color: #6C757D;
			font-weight: 500;
		}
		
		.summary-price {
			font-size: 36rpx;
			font-weight: 700;
			color: #FF6B35;
			margin-left: 8rpx;
		}
	}
	
	.submit-button {
		background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
		color: #fff;
		border: none;
		border-radius: 32rpx;
		font-size: 28rpx;
		font-weight: 700;
		padding: 24rpx 48rpx;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		box-shadow: 0 12rpx 24rpx rgba(255, 107, 53, 0.25);
		position: relative;
		overflow: hidden;
		
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
			opacity: 0;
			transition: opacity 0.3s ease;
		}
		
		.submit-text {
			position: relative;
			z-index: 1;
			letter-spacing: 1rpx;
		}
		
		&:active {
			transform: translateY(4rpx);
			box-shadow: 0 8rpx 20rpx rgba(255, 107, 53, 0.4);
			
			&::before {
				opacity: 1;
			}
		}
		
		&.disabled {
			background: #ADB5BD;
			box-shadow: 0 8rpx 16rpx rgba(173, 181, 189, 0.2);
			transform: none;
			
			&:active {
				transform: none;
				box-shadow: 0 8rpx 16rpx rgba(173, 181, 189, 0.2);
			}
		}
		
		&::after {
			border: none;
		}
	}
}

// 加载遮罩
.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	background: rgba(0, 0, 0, 0.5);
	backdrop-filter: blur(10px);
	display: flex;
	align-items: center;
	justify-content: center;
	
	.loading-card {
		background: rgba(255, 255, 255, 0.95);
		border-radius: 24rpx;
		padding: 48rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		border: 2rpx solid rgba(255, 255, 255, 0.3);
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
		backdrop-filter: blur(20px);
		
		.loading-spinner {
			width: 60rpx;
			height: 60rpx;
			border: 4rpx solid rgba(255, 107, 53, 0.2);
			border-top: 4rpx solid #FF6B35;
			border-radius: 50%;
			animation: spin 1s linear infinite;
			margin-bottom: 24rpx;
		}
		
		.loading-text {
			font-size: 28rpx;
			color: #6C757D;
			font-weight: 500;
		}
	}
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}


</style> 