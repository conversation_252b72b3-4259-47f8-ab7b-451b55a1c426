<template>
	<view class="comment-page">
		<view class="goods-info">
			<image class="goods-image" :src="goods.image" mode="aspectFill"></image>
			<view class="goods-detail">
				<text class="goods-name">{{goods.name}}</text>
				<text class="goods-price">¥{{goods.price}}</text>
			</view>
		</view>
		
		<view class="comment-form">
			<view class="form-item">
				<text class="label">评分</text>
				<uni-rate v-model="form.rate" size="25" active-color="#ff4d4f"></uni-rate>
			</view>
			
			<view class="form-item">
				<text class="label">评价内容</text>
				<textarea 
					class="content-input" 
					v-model="form.content"
					placeholder="请输入您的评价内容"
					maxlength="500"
					:auto-height="true"
				></textarea>
				<text class="word-count">{{form.content.length}}/500</text>
			</view>
			
			<view class="form-item">
				<text class="label">上传图片</text>
				<view class="upload-list">
					<view 
						class="upload-item" 
						v-for="(item, index) in form.images" 
						:key="index"
					>
						<image 
							:src="item" 
							mode="aspectFill"
							@click="previewImage(index)"
						></image>
						<text 
							class="delete-btn iconfont icon-close"
							@click="deleteImage(index)"
						></text>
					</view>
					<view 
						class="upload-btn" 
						v-if="form.images.length < 9"
						@click="chooseImage"
					>
						<text class="iconfont icon-camera"></text>
						<text class="text">上传图片</text>
					</view>
				</view>
				<text class="tip">最多上传9张图片</text>
			</view>
			
			<view class="form-item">
				<label class="checkbox">
					<checkbox 
						:checked="form.isAnonymous" 
						@tap="form.isAnonymous = !form.isAnonymous"
						color="#ff4d4f"
					></checkbox>
					<text>匿名评价</text>
				</label>
			</view>
		</view>
		
		<view class="submit-btn" @click="submitComment">
			<text>提交评价</text>
		</view>
	</view>
</template>

<script>
import commentApi from '@/api/comment'

export default {
	data() {
		return {
			orderId: 0,
			goodsId: 0,
			goods: {
				image: '',
				name: '',
				price: 0
			},
			form: {
				rate: 5,
				content: '',
				images: [],
				isAnonymous: false
			}
		}
	},
	onLoad(options) {
		if (options.orderId && options.goodsId) {
			this.orderId = parseInt(options.orderId)
			this.goodsId = parseInt(options.goodsId)
			this.goods = {
				image: decodeURIComponent(options.image || ''),
				name: decodeURIComponent(options.name || ''),
				price: parseFloat(options.price) || 0
			}
		} else {
			uni.showToast({
				title: '参数错误',
				icon: 'none'
			})
			setTimeout(() => {
				uni.navigateBack()
			}, 1500)
		}
	},
	methods: {
		// 选择图片
		chooseImage() {
			uni.chooseImage({
				count: 9 - this.form.images.length,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					// 上传图片到服务器
					this.uploadImages(res.tempFilePaths)
				}
			})
		},
		
		// 上传图片
		async uploadImages(tempFilePaths) {
			uni.showLoading({
				title: '上传中...'
			})
			
			try {
				const uploadTasks = tempFilePaths.map(path => {
					return new Promise((resolve, reject) => {
						uni.uploadFile({
							url: '/api/upload',
							filePath: path,
							name: 'file',
							success: (res) => {
								const data = JSON.parse(res.data)
								if (data.code === 200) {
									resolve(data.data.url)
								} else {
									reject(new Error(data.msg))
								}
							},
							fail: reject
						})
					})
				})
				
				const urls = await Promise.all(uploadTasks)
				this.form.images = [...this.form.images, ...urls]
			} catch (error) {
				console.error('上传图片失败:', error)
				uni.showToast({
					title: '上传图片失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},
		
		// 删除图片
		deleteImage(index) {
			this.form.images.splice(index, 1)
		},
		
		// 预览图片
		previewImage(index) {
			uni.previewImage({
				urls: this.form.images,
				current: this.form.images[index]
			})
		},
		
		// 提交评论
		async submitComment() {
			if (!this.form.content.trim()) {
				uni.showToast({
					title: '请输入评价内容',
					icon: 'none'
				})
				return
			}
			
			uni.showLoading({
				title: '提交中...'
			})
			
			try {
				const res = await commentApi.createComment({
					orderId: this.orderId,
					goodsId: this.goodsId,
					content: this.form.content.trim(),
					images: this.form.images,
					rate: this.form.rate,
					isAnonymous: this.form.isAnonymous
				})
				
				if (res.code === 200) {
					uni.showToast({
						title: '评价成功',
						icon: 'success'
					})
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				} else {
					throw new Error(res.msg)
				}
			} catch (error) {
				console.error('提交评论失败:', error)
				uni.showToast({
					title: error.message || '提交评论失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		}
	}
}
</script>

<style lang="scss">
.comment-page {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding-bottom: 120rpx;
	
	.goods-info {
		display: flex;
		padding: 20rpx;
		background-color: #fff;
		margin-bottom: 20rpx;
		
		.goods-image {
			width: 160rpx;
			height: 160rpx;
			border-radius: 8rpx;
			margin-right: 20rpx;
		}
		
		.goods-detail {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			
			.goods-name {
				font-size: 28rpx;
				color: #333;
				line-height: 1.4;
			}
			
			.goods-price {
				font-size: 32rpx;
				color: #ff4d4f;
				font-weight: bold;
			}
		}
	}
	
	.comment-form {
		background-color: #fff;
		padding: 20rpx;
		
		.form-item {
			margin-bottom: 30rpx;
			
			.label {
				display: block;
				font-size: 28rpx;
				color: #333;
				margin-bottom: 16rpx;
			}
			
			.content-input {
				width: 100%;
				min-height: 200rpx;
				padding: 20rpx;
				font-size: 28rpx;
				color: #333;
				line-height: 1.5;
				border: 1rpx solid #ddd;
				border-radius: 8rpx;
				box-sizing: border-box;
			}
			
			.word-count {
				display: block;
				text-align: right;
				font-size: 24rpx;
				color: #999;
				margin-top: 8rpx;
			}
			
			.upload-list {
				display: flex;
				flex-wrap: wrap;
				margin: 0 -10rpx;
				
				.upload-item,
				.upload-btn {
					width: 160rpx;
					height: 160rpx;
					margin: 10rpx;
					border-radius: 8rpx;
					overflow: hidden;
				}
				
				.upload-item {
					position: relative;
					
					image {
						width: 100%;
						height: 100%;
					}
					
					.delete-btn {
						position: absolute;
						top: 0;
						right: 0;
						width: 40rpx;
						height: 40rpx;
						background-color: rgba(0, 0, 0, 0.5);
						color: #fff;
						font-size: 24rpx;
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}
				
				.upload-btn {
					border: 1rpx dashed #ddd;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					
					.iconfont {
						font-size: 48rpx;
						color: #999;
						margin-bottom: 8rpx;
					}
					
					.text {
						font-size: 24rpx;
						color: #999;
					}
				}
			}
			
			.tip {
				display: block;
				font-size: 24rpx;
				color: #999;
				margin-top: 8rpx;
			}
			
			.checkbox {
				display: flex;
				align-items: center;
				
				text {
					font-size: 28rpx;
					color: #333;
					margin-left: 8rpx;
				}
			}
		}
	}
	
	.submit-btn {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		height: 100rpx;
		background-color: #ff4d4f;
		color: #fff;
		font-size: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}
</style> 