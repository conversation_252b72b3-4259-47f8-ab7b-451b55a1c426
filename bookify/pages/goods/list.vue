<template>
	<PageLayout :show-nav-bar="true" :nav-transparent="false" 	:show-center="true"  nav-title="点餐">
	<view class="goods-list">
		<!-- 主要内容区域 -->
		<view class="main-container" >
			<!-- 左侧分类栏 -->
			<view class="category-sidebar">
				<scroll-view scroll-y="true" class="category-scroll">
					<view 
						class="category-item"
						:class="{active: currentCategory === 0}"
						@click="selectCategory(0)"
					>
						<text>全部</text>
					</view>
					<view 
						class="category-item"
						:class="{active: currentCategory === item.id}"
						v-for="item in categoryList" 
						:key="item.id"
						@click="selectCategory(item.id)"
					>
						<text>{{item.name}}</text>
					</view>
				</scroll-view>
			</view>

			<!-- 右侧商品区域 -->
			<view class="goods-content">
				<!-- 排序筛选 -->
				<view class="sort-filter">
					<view 
						class="sort-item"
						:class="{active: sortType === 'default'}"
						@click="changeSort('default')"
					>
						<text>综合</text>
					</view>
					<view 
						class="sort-item"
						:class="{active: sortType === 'sales'}"
						@click="changeSort('sales')"
					>
						<text>销量</text>
					</view>
					<view 
						class="sort-item"
						:class="{active: sortType === 'price'}"
						@click="changeSort('price')"
					>
						<text>价格</text>
						<text class="sort-arrow" :class="{desc: priceDesc}">{{priceDesc ? '↓' : '↑'}}</text>
					</view>
				</view>

				<!-- 商品列表 -->
				<scroll-view scroll-y="true" class="goods-scroll" @scrolltolower="loadMore">
					<view class="goods-container">

						
						<!-- 优惠券区域 -->
				

						<!-- 分类标题 -->
						<view class="category-title" v-if="getCurrentCategoryName">
							<text>{{getCurrentCategoryName}}</text>
						</view>

						<!-- 商品列表 -->
						<view class="goods-list-content">
							<view 
								class="goods-item" 
								v-for="item in goodsList" 
								:key="item.id"
								@click="toDetail(item.id)"
							>
								<!-- 商品图片 -->
								<view class="goods-image">
									<image :src="item.image || '/static/images/category/smart.png'" mode="aspectFill"></image>
									<view class="goods-badge" v-if="item.isHot">
										<text>热销</text>
									</view>
								</view>
								
								<!-- 商品信息 -->
								<view class="goods-info">
									<text class="goods-title">{{item.name}}</text>
									<text class="goods-desc" v-if="item.description">{{item.description}}</text>
									
									<!-- 商品标签 -->
									<view class="goods-tags" v-if="item.tags && item.tags.length > 0">
										<view class="tag-item" v-for="tag in item.tags.slice(0, 2)" :key="tag">
											<text>{{tag}}</text>
										</view>
									</view>
									
									<view class="goods-meta">
										<view class="price-row">
											<text class="goods-price">¥{{item.price}}</text>
											<text class="original-price" v-if="item.originalPrice && item.originalPrice > item.price">¥{{item.originalPrice}}</text>
										</view>
										<text class="goods-sales">{{item.sales || 0}}人购买</text>
									</view>
								</view>

								<!-- 操作按钮 -->
								<view class="goods-actions">
									<view class="like-btn" @click.stop="handleLike(item)">
										<text class="like-icon" :class="{liked: item.isLiked}">♥</text>
									</view>
									
																	<!-- 单规格商品：显示加减按钮 -->
								<view v-if="item.specType === 1" class="quantity-controls" @click.stop>
									<view 
										class="quantity-btn minus" 
										:class="{disabled: getCartQuantity(item.id) <= 0}" 
										@click="updateCartQuantity(item, -1)"
									>
										<text>-</text>
									</view>
									<text class="quantity-display">{{getCartQuantity(item.id) || 0}}</text>
									<view 
										class="quantity-btn plus" 
										@click="updateCartQuantity(item, 1)"
									>
										<text>+</text>
									</view>
								</view>
									
									<!-- 多规格/套餐商品：显示选规格按钮 -->
									<button 
										v-else
										class="add-cart-btn" 
										@click.stop="handleAddToCart(item)"
									>
										<text>选规格</text>
									</button>
								</view>
							</view>
						</view>

						<!-- 加载更多 -->
						<view class="load-more" v-if="goodsList.length > 0">
							<text v-if="loadMoreStatus === 'loading'">加载中...</text>
							<text v-else-if="loadMoreStatus === 'noMore'">没有更多商品了</text>
						</view>

						<!-- 空状态 -->
						<view class="empty-state" v-if="goodsList.length === 0 && !loading">
							<image src="/static/images/empty-cart.png" mode="aspectFit"></image>
							<text class="empty-text">暂无商品</text>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>

		<!-- 底部购物车栏 -->
		<view class="bottom-cart">
			<view class="cart-info" @click="showCartPopup">
				<view class="cart-icon-wrapper">
					<text class="cart-icon">🛒</text>
					<view class="cart-count" v-if="cartCount > 0">{{cartCount}}</view>
				</view>
				<view class="cart-text">
					<view class="cart-info-row">
						<text class="cart-title" v-if="cartCount > 0">已选择{{cartCount}}件商品</text>
						<text class="cart-title" v-else>尚未选购商品</text>
					</view>
					<text class="cart-price">¥{{cartTotalPrice}}</text>
				</view>
			</view>
			<view class="checkout-btn" :class="{disabled: cartCount === 0}" @click="checkout">
				<text>去结算</text>
			</view>
		</view>

		<!-- 购物车弹窗 -->
		<view class="cart-popup-mask" v-if="showCart" @click="hideCartPopup">
			<view class="cart-popup" @click.stop>
				<!-- 购物车头部 -->
				<view class="cart-header">
					<text class="cart-title">购物车</text>
					<text class="clear-cart" @click="clearCartItems" v-if="cartList.length > 0">清空购物车</text>
				</view>

				<!-- 购物车商品列表 -->
				<scroll-view scroll-y="true" class="cart-content" v-if="cartList.length > 0">
					<view class="cart-item" v-for="(item, index) in cartList" :key="item.id">
						<view class="item-info">
							<image :src="item.image || '/static/images/default-goods.png'" class="item-image"></image>
							<view class="item-details">
								<text class="item-name">{{item.name}}</text>
								<text class="item-specs" v-if="item.specs && item.specs.length">
									{{formatSpecs(item.specs)}}
								</text>
								<view class="item-price-row">
									<text class="item-price">¥{{item.price}}</text>
									<view class="quantity-control">
										<text class="quantity-btn minus" 
											:class="{disabled: item.quantity <= 0}" 
											@click="updateQuantity(index, -1)">-</text>
										<text class="quantity-num">{{item.quantity}}</text>
										<text class="quantity-btn plus" 
											:class="{disabled: item.quantity >= item.stock}" 
											@click="updateQuantity(index, 1)">+</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>

				<!-- 空购物车 -->
				<view class="empty-cart" v-else>
					<image src="/static/images/empty-cart.png" mode="aspectFit"></image>
					<text>购物车还是空的</text>
				</view>

				<!-- 购物车底部 -->
				<view class="cart-bottom" v-if="cartList.length > 0">
					<view class="total-info">
						<text class="total-text">合计：</text>
						<text class="total-price">¥{{cartTotalPrice}}</text>
						<text class="total-desc">已优惠¥{{totalDiscount}}</text>
					</view>
					<view class="action-buttons">
						<button class="continue-shopping" @click="hideCartPopup">继续购物</button>
						<button class="checkout-now" @click="checkout">去结算</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</PageLayout>
</template>

<script>
import { getGoodsList, getCategoryList } from '@/api/home'
import { getCartCount, getCartList, updateCartQuantity, deleteCart, clearCart, addToCart } from '@/api/cart'
import { getGoodsDetail, collectGoods, likeGoods } from '@/api/goods'
import AuthUtils from '@/utils/auth'
import collectMixin from '@/mixins/collectMixin'
import PageLayout from '@/components/layout/PageLayout.vue'
export default {
	mixins: [collectMixin],
	components: {
		PageLayout
	},
	data() {
		return {
			keyword: '',
			page: 1,
			pageSize: 10,
			goodsList: [],
			loadMoreStatus: 'more', // more-加载前 loading-加载中 noMore-没有更多
			currentCategory: 0,
			categoryList: [],
			sortType: 'default',
			priceDesc: false,
			loading: false,
			cartCount: 0,
			statusBarHeight: 0,
			// 购物车弹窗相关
			showCart: false,
			cartList: [],
			cartLoading: false,
			// 商品在购物车中的数量映射
			cartQuantityMap: {}
		}
	},
	
	onLoad(options) {
		// 设置状态栏高度
		const systemInfo = uni.getSystemInfoSync()
		this.statusBarHeight = systemInfo.statusBarHeight || 0
		
		// 检查登录状态
		const token = uni.getStorageSync('token')
		console.log('页面加载时的登录状态:', !!token)
		
		// 获取传入的分类ID
		if (options.categoryId) {
			this.currentCategory = parseInt(options.categoryId)
		}
		// 获取传入的关键词
		if (options.keyword) {
			this.keyword = options.keyword
		}
		
		this.loadCategories()
		this.loadGoods()
		// 统一使用loadCartQuantityMap来同时更新数量映射和总数量
		this.loadCartQuantityMap()
		this.loadCartList() // 初始化时就加载购物车列表
	},
	
	onShow() {
		// 统一使用loadCartQuantityMap来同时更新数量映射和总数量
		this.loadCartQuantityMap()
		this.loadCartList() // 每次显示页面时都刷新购物车列表
		
		// 检查是否有堂食外卖参数
		this.checkDiningParams()
		
		// 检查是否需要显示购物车（从详情页返回）
		this.checkShowCartFromDetail()
	},
	
	// 下拉刷新
	onPullDownRefresh() {
		this.page = 1
		this.goodsList = []
		this.loadGoods().then(() => {
			this.loadCartQuantityMap()
			uni.stopPullDownRefresh()
		})
	},
	
	// 触底加载更多
	onReachBottom() {
		if(this.loadMoreStatus === 'loading') return
		this.loadMore()
	},
	
	methods: {
		// 检查堂食外卖参数
		checkDiningParams() {
			const diningType = uni.getStorageSync('diningType')
			const diningTitle = uni.getStorageSync('diningTitle')
			
			if (diningType && diningTitle) {
				// 显示堂食外卖提示
				uni.showToast({
					title: `欢迎选择${diningTitle}`,
					icon: 'none',
					duration: 2000
				})
				
				// 清除存储的参数，避免重复提示
				uni.removeStorageSync('diningType')
				uni.removeStorageSync('diningTitle')
				
				// 这里可以根据diningType做一些特殊处理
				// 比如筛选适合堂食或外卖的商品
				if (diningType === 'dine_in') {
					console.log('堂食模式')
					// 可以添加堂食相关的逻辑
				} else if (diningType === 'takeout') {
					console.log('外卖模式')
					// 可以添加外卖相关的逻辑
				}
			}
		},

		// 检查是否需要显示购物车（从详情页返回）
		checkShowCartFromDetail() {
			const showCartFlag = uni.getStorageSync('showCartAfterAddToCart')
			if (showCartFlag) {
				// 清除标识
				uni.removeStorageSync('showCartAfterAddToCart')
				
				// 延迟显示购物车，确保页面已经完全加载
				setTimeout(() => {
					this.showCartPopup()
				}, 500)
			}
		},
		
		// 加载分类列表
		async loadCategories() {
			try {
				const res = await getCategoryList({ level: 1 })
				if (res.code === 200 && res.data) {
					this.categoryList = res.data
				}
			} catch (error) {
				console.error('加载分类失败:', error)
			}
		},

		// 加载商品列表
		async loadGoods() {
			this.loading = true
			this.loadMoreStatus = 'loading'
			
			try {
				const params = {
					page: this.page,
					pageSize: this.pageSize,
					categoryId: this.currentCategory || '',
					keyword: this.keyword,
					sort: this.getSortParam()
				}
				
				const res = await getGoodsList(params)
				
				if (res.code === 200 && res.data) {
					const goods = res.data.list || []
					// 添加一些模拟数据
					const processedGoods = goods.map(item => ({
						...item,
						isHot: Math.random() > 0.7,
						isLiked: item.isLiked || false, // 使用后端返回的点赞状态
						isCollected: item.isCollected || false, // 使用后端返回的收藏状态
						originalPrice: item.originalPrice || (parseFloat(item.price) * (1.2 + Math.random() * 0.5)).toFixed(2),
						rating: (4.0 + Math.random() * 1.0).toFixed(1),
						// 确保specType字段存在，默认为1（单规格）
						specType: item.specType || 1,
						// 确保stock字段存在
						stock: item.stock || 0
					}))

					// 直接使用处理后的商品数据，不再调用批量状态查询
					if (this.page === 1) {
						this.goodsList = processedGoods
					} else {
						this.goodsList.push(...processedGoods)
					}
					
					// 判断是否还有更多数据
					this.loadMoreStatus = goods.length < this.pageSize ? 'noMore' : 'more'
				} else {
					this.loadMoreStatus = 'noMore'
				}
			} catch (error) {
				console.error('加载商品失败:', error)
				this.loadMoreStatus = 'more'
				
				// 如果是第一页且没有数据，显示模拟数据
				if (this.page === 1 && this.goodsList.length === 0) {
					this.goodsList = this.getMockGoods()
					this.loadMoreStatus = 'noMore'
				}
			} finally {
				this.loading = false
			}
		},

		// 获取模拟商品数据
		getMockGoods() {
			const promotionTags = ['自选多拼', '可选新品', '限时特惠', '买一送一', '新品上市', '热销爆款']
			const categories = ['手机数码', '电脑办公', '数码配件', '游戏设备', '智能家居']
			
			return Array(8).fill().map((_, index) => {
				// 随机生成规格类型：1-单规格（80%概率）、2-多规格（15%概率）、3-套餐（5%概率）
				const rand = Math.random()
				let specType = 1
				if (rand > 0.8 && rand <= 0.95) {
					specType = 2
				} else if (rand > 0.95) {
					specType = 3
				}
				
				return {
					id: index + 1,
					name: `精选商品 ${index + 1}`,
					description: '精选优质商品，品质保证，限时优惠',
					price: (Math.random() * 500 + 50).toFixed(1),
					originalPrice: (Math.random() * 200 + 600).toFixed(1),
					sales: Math.floor(Math.random() * 1000) + 100,
					stock: Math.floor(Math.random() * 100) + 10, // 添加库存字段
					image: `/static/images/category/${['smart', 'mobile', 'computer', 'game', 'accessories'][index % 5]}.png`,
					isHot: Math.random() > 0.6,
					isLiked: false,
					rating: (4.0 + Math.random() * 1.0).toFixed(1),
					specType: specType, // 添加规格类型
					tags: [
						promotionTags[Math.floor(Math.random() * promotionTags.length)],
						promotionTags[Math.floor(Math.random() * promotionTags.length)]
					].filter((tag, index, arr) => arr.indexOf(tag) === index) // 去重
				}
			})
		},

		// 获取排序参数
		getSortParam() {
			switch (this.sortType) {
				case 'sales':
					return 'sales'
				case 'price':
					return this.priceDesc ? 'price_desc' : 'price_asc'
				case 'time':
					return 'time'
				default:
					return 'default'
			}
		},

		// 加载购物车数量
		async loadCartCount() {
			if (!AuthUtils.checkLoginStatus()) {
				this.cartCount = 0
				return
			}
			
			try {
				const res = await getCartCount()
				if (res.code === 200) {
					this.cartCount = res.data || 0
				} else {
					console.error('获取购物车数量失败:', res.message)
					this.cartCount = 0
				}
			} catch (error) {
				console.error('获取购物车数量失败:', error)
				this.cartCount = 0
			}
		},

		// 搜索商品
		searchGoods() {
			this.page = 1
			this.goodsList = []
			this.loadGoods()
		},

		// 加载更多
		loadMore() {
			if (this.loadMoreStatus === 'loading' || this.loadMoreStatus === 'noMore') return
			this.page++
			this.loadGoods()
		},

		// 选择分类
		selectCategory(id) {
			this.currentCategory = id
			this.page = 1
			this.goodsList = []
			this.loadGoods()
		},

		// 改变排序
		changeSort(type) {
			if (type === 'price' && this.sortType === 'price') {
				this.priceDesc = !this.priceDesc
			} else {
				this.sortType = type
				this.priceDesc = false
			}
			this.page = 1
			this.goodsList = []
			this.loadGoods()
		},

		// 切换点赞状态
		async handleLike(item) {
			const result = await this.toggleLike(item.id, 'goods', item.isLiked)
			item.isLiked = result.isLiked
			item.likes = result.count
		},

		// 获取商品在购物车中的数量
		getCartQuantity(goodsId) {
			return this.cartQuantityMap[goodsId] || 0
		},

		// 更新商品购物车数量
		async updateCartQuantity(item, delta) {
			console.log('updateCartQuantity 被调用:', item.name, delta)
			
			// 点击时检查登录状态
			if (!AuthUtils.checkLoginStatus()) {
				console.log('用户未登录，跳转到登录页')
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				})
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/login/login'
					})
				}, 1500)
				return
			}
			
			console.log('用户已登录，继续执行添加操作')

			const currentQuantity = this.getCartQuantity(item.id)
			const newQuantity = Math.max(0, currentQuantity + delta)

			// 检查库存（只在增加时检查）
			if (delta > 0 && item.stock <= 0) {
				uni.showToast({
					title: '商品库存不足',
					icon: 'none'
				})
				return
			}
			
			// 检查减少时的边界条件
			if (delta < 0 && currentQuantity <= 0) {
				uni.showToast({
					title: '商品数量不能小于0',
					icon: 'none'
				})
				return
			}

			try {
				if (newQuantity === 0 && currentQuantity > 0) {
					// 减少到0，需要删除购物车项
					await this.removeFromCart(item)
				} else if (newQuantity > 0) {
					if (currentQuantity === 0) {
						// 从0增加，需要添加到购物车
						await this.addToCartDirectly(item)
					} else {
						// 更新已存在的购物车项数量
						await this.updateExistingCartItem(item, newQuantity)
					}
				}
			} catch (error) {
				console.error('更新购物车数量失败:', error)
				// 操作失败时恢复本地状态
				this.loadCartQuantityMap()
			}
		},

		// 处理加入购物车操作
		async handleAddToCart(item) {
			// 点击时检查登录状态
			if (!AuthUtils.checkLoginStatus()) {
				console.log('用户未登录，跳转到登录页')
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				})
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/login/login'
					})
				}, 1500)
				return
			}
			
			// 多规格或套餐商品跳转到详情页
			this.selectSpecs(item)
		},

		// 选择规格（跳转到商品详情页）
		selectSpecs(item) {
			// 跳转到商品详情页进行规格选择
			uni.navigateTo({
				url: `/pages/goods/detail?id=${item.id}`
			})
		},

		// 单规格商品直接加入购物车
		async addToCartDirectly(item) {
			console.log('addToCartDirectly 被调用:', item.name)
			
			try {
				// 对于单规格商品，先获取详情获取SKU编码
				console.log('开始获取商品详情:', item.id)
				const detailRes = await getGoodsDetail({ id: item.id })
				console.log('商品详情响应:', detailRes)
				
				if (detailRes.code !== 200 || !detailRes.data) {
					console.error('获取商品详情失败:', detailRes)
					uni.showToast({
						title: '获取商品信息失败',
						icon: 'none'
					})
					return
				}

				const goodsDetail = detailRes.data
				let skuCode = 'DEFAULT_SKU'
				
				// 如果有单规格数据，使用其SKU编码
				if (goodsDetail.singleSpecData && goodsDetail.singleSpecData.sku) {
					skuCode = goodsDetail.singleSpecData.sku.skuCode
					console.log('使用单规格SKU:', skuCode)
				} else if (goodsDetail.specInfo && goodsDetail.specInfo.skus && goodsDetail.specInfo.skus.length > 0) {
					// 兼容旧格式，使用第一个SKU
					skuCode = goodsDetail.specInfo.skus[0].skuCode
					console.log('使用第一个SKU:', skuCode)
				} else {
					console.log('使用默认SKU:', skuCode)
				}

				const requestData = {
					goodsId: item.id,
					quantity: 1,
					skuCode: skuCode,
					specsJson: JSON.stringify({}) // 单规格无规格信息
				}
				
				console.log('准备发送加入购物车请求:', requestData)
				const res = await addToCart(requestData)
				console.log('加入购物车响应:', res)

				if (res.code === 200) {
					// 更新本地购物车数量映射
					this.cartQuantityMap[item.id] = (this.cartQuantityMap[item.id] || 0) + 1
					this.cartCount = (this.cartCount || 0) + 1
					this.$forceUpdate() // 强制更新视图
					
					// 更新购物车列表
					this.loadCartList()
					
					uni.showToast({
						title: '已加入购物车',
						icon: 'success',
						duration: 1500
					})
				} else {
					uni.showToast({
						title: res.message || '加入失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加入购物车失败:', error)
				uni.showToast({
					title: '加入失败',
					icon: 'none'
				})
			}
		},

		// 更新已存在的购物车项数量
		async updateExistingCartItem(item, newQuantity) {
			try {
				// 查找购物车中对应的项目
				const cartItems = await this.getCartItemsByGoodsId(item.id)
				if (cartItems.length === 0) {
					console.error('未找到购物车项目')
					return
				}

				const cartItem = cartItems[0] // 单规格商品只有一个购物车项
				const res = await updateCartQuantity({
					id: cartItem.id,
					quantity: newQuantity
				})

				if (res.code === 200) {
					// 更新本地数量映射
					const oldQuantity = this.cartQuantityMap[item.id] || 0
					this.cartQuantityMap[item.id] = newQuantity
					this.cartCount = (this.cartCount || 0) - oldQuantity + newQuantity
					this.$forceUpdate()
					
					// 更新购物车列表
					this.loadCartList()
				} else {
					uni.showToast({
						title: res.message || '更新失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('更新购物车失败:', error)
				uni.showToast({
					title: '更新失败',
					icon: 'none'
				})
			}
		},

		// 从购物车删除商品
		async removeFromCart(item) {
			try {
				const cartItems = await this.getCartItemsByGoodsId(item.id)
				if (cartItems.length === 0) {
					return
				}

				const cartItem = cartItems[0]
				const res = await deleteCart({ ids: [cartItem.id] })

				if (res.code === 200) {
					// 更新本地数量映射
					const oldQuantity = this.cartQuantityMap[item.id] || 0
					this.cartQuantityMap[item.id] = 0
					this.cartCount = Math.max(0, (this.cartCount || 0) - oldQuantity)
					this.$forceUpdate()
					
					// 更新购物车列表
					this.loadCartList()
				}
			} catch (error) {
				console.error('删除购物车项失败:', error)
			}
		},

		// 根据商品ID获取购物车项
		async getCartItemsByGoodsId(goodsId) {
			try {
				const res = await getCartList()
				if (res.code === 200 && res.data && res.data.list) {
					return res.data.list.filter(item => item.goodsId === goodsId)
				}
			} catch (error) {
				console.error('获取购物车列表失败:', error)
			}
			return []
		},

		// 检查登录状态
		checkLogin() {
			const token = uni.getStorageSync('token')
			return !!token
		},
		
		// 加载购物车数量映射
		async loadCartQuantityMap() {
			if (!AuthUtils.checkLoginStatus()) {
				console.log('用户未登录，重置购物车数据')
				this.cartQuantityMap = {}
				this.cartCount = 0
				return
			}

			try {
				const res = await getCartList()
				console.log('购物车列表响应:', res)
				
				if (res.code === 200 && res.data && res.data.list) {
					const quantityMap = {}
					let totalCount = 0
					res.data.list.forEach(item => {
						quantityMap[item.goodsId] = (quantityMap[item.goodsId] || 0) + item.quantity
						totalCount += item.quantity
					})
					this.cartQuantityMap = quantityMap
					this.cartCount = totalCount // 同步更新总数量
					console.log('购物车数量映射:', quantityMap, '总数量:', totalCount)
					this.$forceUpdate()
				} else {
					console.log('购物车数据为空或格式错误')
					this.cartQuantityMap = {}
					this.cartCount = 0
				}
			} catch (error) {
				console.error('加载购物车数量映射失败:', error)
				this.cartQuantityMap = {}
				this.cartCount = 0
			}
		},

		// 基于已加载的购物车列表同步数量映射
		syncCartQuantityMap() {
			const quantityMap = {}
			let totalCount = 0
			if (this.cartList && this.cartList.length > 0) {
				this.cartList.forEach(item => {
					quantityMap[item.goodsId] = (quantityMap[item.goodsId] || 0) + item.quantity
					totalCount += item.quantity
				})
			}
			this.cartQuantityMap = quantityMap
			this.cartCount = totalCount // 同步更新总数量
			this.$forceUpdate()
		},

		// 跳转商品详情
		toDetail(id) {
			uni.navigateTo({
				url: `/pages/goods/detail?id=${id}`
			})
		},

		// 返回
		goBack() {
			uni.navigateBack()
		},

		// 显示购物车弹窗
		async showCartPopup() {
			if (!AuthUtils.checkLoginStatus()) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				})
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/login/login'
					})
				}, 1500)
				return
			}
			
			this.showCart = true
			await this.loadCartList()
		},

		// 隐藏购物车弹窗
		hideCartPopup() {
			this.showCart = false
		},

		// 加载购物车列表
		async loadCartList() {
			if (this.cartLoading) return
			
			this.cartLoading = true
			try {
				const res = await getCartList()
				if (res.code === 200 && res.data) {
					// 确保每个商品都有selected属性，默认为true
					this.cartList = (res.data.list || []).map(item => ({
						...item,
						selected: item.selected !== undefined ? item.selected : true,
						originalPrice: item.originalPrice || (parseFloat(item.price) * 1.2).toFixed(2)
					}))
					
					// 同步更新数量映射
					this.syncCartQuantityMap()
				}
			} catch (error) {
				console.error('加载购物车列表失败:', error)
				uni.showToast({
					title: '加载购物车失败',
					icon: 'none'
				})
			} finally {
				this.cartLoading = false
			}
		},

		// 格式化规格信息
		formatSpecs(specs) {
			if (!specs || !Array.isArray(specs)) return ''
			return specs.map(spec => `${spec.name}:${spec.value}`).join('，')
		},

		// 更新商品数量
		async updateQuantity(index, delta) {
			const item = this.cartList[index]
			if (!item) return
			
			const newQuantity = item.quantity + delta
			if (newQuantity < 1) {
				// 数量为0时删除商品
				await this.removeCartItem(index)
				return
			}
			
			if (newQuantity > item.stock) {
				uni.showToast({
					title: '库存不足',
					icon: 'none'
				})
				return
			}
			
			try {
				await updateCartQuantity({
					id: item.id,
					quantity: newQuantity
				})
				
				// 更新本地数据
				const oldQuantity = this.cartList[index].quantity
				this.cartList[index].quantity = newQuantity
				
				// 更新购物车数量和数量映射
				this.cartCount = (this.cartCount || 0) - oldQuantity + newQuantity
				this.syncCartQuantityMap()
			} catch (error) {
				console.error('更新数量失败:', error)
				uni.showToast({
					title: '更新失败',
					icon: 'none'
				})
			}
		},

		// 删除购物车商品
		async removeCartItem(index) {
			const item = this.cartList[index]
			if (!item) return
			
			try {
				await deleteCart({ ids: [item.id] })
				
				// 更新本地数据
				const removedItem = this.cartList.splice(index, 1)[0]
				
				// 更新购物车数量和数量映射
				this.cartCount = Math.max(0, (this.cartCount || 0) - removedItem.quantity)
				this.syncCartQuantityMap()
				
				uni.showToast({
					title: '已删除',
					icon: 'success'
				})
			} catch (error) {
				console.error('删除失败:', error)
				uni.showToast({
					title: '删除失败',
					icon: 'none'
				})
			}
		},

		// 清空购物车
		async clearCartItems() {
			if (this.cartList.length === 0) return
			
			uni.showModal({
				title: '提示',
				content: '确定要清空购物车吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							await clearCart()
							this.cartList = []
							this.cartCount = 0
							this.cartQuantityMap = {}
							this.$forceUpdate()
							
							uni.showToast({
								title: '已清空',
								icon: 'success'
							})
						} catch (error) {
							console.error('清空购物车失败:', error)
							uni.showToast({
								title: '清空失败',
								icon: 'none'
							})
						}
					}
				}
			})
		},

		// 去结算
		checkout() {
			if (!AuthUtils.checkLoginStatus()) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				})
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/login/login'
					})
				}, 1500)
				return
			}
			
			if (this.cartCount === 0) {
				uni.showToast({
					title: '购物车为空',
					icon: 'none'
				})
				return
			}
			
			// 隐藏购物车弹窗
			this.hideCartPopup()
			
			// 跳转到订单确认页，传递购物车类型参数
			uni.navigateTo({
				url: '/pages/order/confirm?type=cart'
			})
		},

		// 跳转购物车
		goToCart() {
			this.checkout()
		},
		
		// 跳转到登录页
		goToLogin() {
			uni.navigateTo({
				url: '/pages/login/login'
			})
		}
	},
	
	computed: {
		getCurrentCategoryName() {
			if (this.currentCategory === 0) {
				return ''
			}
			const category = this.categoryList.find(item => item.id === this.currentCategory)
			return category ? category.name : ''
		},
		
		// 购物车总价
		cartTotalPrice() {
			return this.cartList.reduce((total, item) => {
				if (item.selected) {
					return total + (item.price * item.quantity)
				}
				return total
			}, 0).toFixed(2)
		},
		
		// 总优惠金额
		totalDiscount() {
			return this.cartList.reduce((total, item) => {
				if (item.selected && item.originalPrice > item.price) {
					return total + ((item.originalPrice - item.price) * item.quantity)
				}
				return total
			}, 0).toFixed(2)
		}
	}
}
</script>

<style lang="scss">
.goods-list {
	min-height: 100vh;
	background: #f5f5f5;
	overflow: hidden; // 防止整体滚动
}


// 主要内容区域
.main-container {
	display: flex;
	height: 100vh;
	// 适配底部安全区域
	padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
	box-sizing: border-box;
}

// 左侧分类栏
.category-sidebar {
	width: 200rpx;
	background: #f8f8f8;
	border-right: 1rpx solid #eee;
	flex-shrink: 0; // 防止被压缩
	
	.category-scroll {
		height: 100%;
		
		.category-item {
			padding: 32rpx 20rpx;
			text-align: center;
			font-size: 28rpx;
			color: #666;
			background: #f8f8f8;
			border-bottom: 1rpx solid #eee;
			transition: all 0.3s ease;
			position: relative;
			
			&.active {
				background: #fff;
				color: #ff6b35;
				font-weight: 600;
				
				&::before {
					content: '';
					position: absolute;
					left: 0;
					top: 50%;
					transform: translateY(-50%);
					width: 6rpx;
					height: 40rpx;
					background: #ff6b35;
					border-radius: 0 6rpx 6rpx 0;
				}
			}
			
			text {
				display: block;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
	}
}

// 右侧商品区域
.goods-content {
	flex: 1;
	background: #fff;
	display: flex;
	flex-direction: column;
	min-width: 0; // 防止flex子元素超出
	overflow: hidden; // 防止内容溢出
}

// 排序筛选
.sort-filter {
	background: #fff;
	display: flex;
	border-bottom: 1rpx solid #eee;
	padding: 0 20rpx;
	flex-shrink: 0; // 防止被压缩
	
	.sort-item {
		flex: 1;
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #666;
		position: relative;
		transition: all 0.3s ease;
		
		&.active {
			color: #ff6b35;
			font-weight: 600;
		}
		
		.sort-arrow {
			margin-left: 8rpx;
			font-size: 24rpx;
			transition: transform 0.3s ease;
			
			&.desc {
				transform: rotate(180deg);
			}
		}
	}
}

// 商品滚动区域
.goods-scroll {
	flex: 1;
	height: 0; // 配合flex: 1使用，确保正确的高度计算
	overflow: hidden; // 防止内容溢出
}

// 商品容器
.goods-container {
	padding: 20rpx;
	
	.coupon-section {
		padding: 20rpx;
		background: #fff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		
		.coupon-card {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20rpx;
			background: linear-gradient(135deg, #ff6b35, #ff4757);
			border-radius: 12rpx;
			
			.coupon-left {
				.coupon-count {
					font-size: 32rpx;
					font-weight: 900;
					color: #fff;
				}
				
				.coupon-text {
					font-size: 24rpx;
					color: #fff;
					font-weight: 500;
				}
			}
			
			.coupon-btn {
				background: #fff;
				color: #ff6b35;
				border: none;
				border-radius: 32rpx;
				font-size: 24rpx;
				font-weight: 600;
				padding: 12rpx 24rpx;
				min-width: 120rpx;
				transition: all 0.3s ease;
				
				&:active {
					transform: scale(0.98);
					opacity: 0.8;
				}
			}
		}
	}
	
	.category-title {
		padding: 20rpx 0;
		border-bottom: 1rpx solid #eee;
		margin-bottom: 20rpx;
		
		text {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
		}
	}
	
	.goods-list-content {
		.goods-item {
			display: flex;
			padding: 20rpx 0;
			border-bottom: 1rpx solid #f5f5f5;
			transition: all 0.3s ease;
			
			&:active {
				background: #f9f9f9;
			}
			
			.goods-image {
				width: 160rpx;
				height: 160rpx;
				border-radius: 12rpx;
				overflow: hidden;
				margin-right: 20rpx;
				position: relative;
				flex-shrink: 0;
				
				image {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}
				
				.goods-badge {
					position: absolute;
					top: 8rpx;
					left: 8rpx;
					background: linear-gradient(135deg, #ff6b35, #ff4757);
					color: #fff;
					font-size: 20rpx;
					font-weight: 700;
					padding: 4rpx 12rpx;
					border-radius: 8rpx;
					box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3);
					z-index: 3;
				}
			}
			
			.goods-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				margin-right: 20rpx;
				min-width: 0; // 防止文字溢出
				
				.goods-title {
					font-size: 32rpx;
					font-weight: 600;
					color: #333;
					margin-bottom: 8rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					line-height: 1.4;
				}
				
				.goods-desc {
					font-size: 24rpx;
					color: #999;
					margin-bottom: 16rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
				
				.goods-tags {
					margin-bottom: 8rpx;
					display: flex;
					gap: 8rpx;
					
					.tag-item {
						background: #f0f0f0;
						color: #333;
						font-size: 20rpx;
						font-weight: 500;
						padding: 4rpx 8rpx;
						border-radius: 4rpx;
						flex-shrink: 0;
						
						&:first-child {
							background: #ff6b35;
							color: #fff;
						}
						
						&:nth-child(2) {
							background: #4CAF50;
							color: #fff;
						}
						
						text {
							display: block;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}
					}
				}
				
				.goods-meta {
					.price-row {
						display: flex;
						align-items: baseline;
						margin-bottom: 8rpx;
						
						.goods-price {
							font-size: 36rpx;
							font-weight: 900;
							color: #ff6b35;
							margin-right: 16rpx;
						}
						
						.original-price {
							font-size: 24rpx;
							color: #999;
							text-decoration: line-through;
						}
					}
					
					.goods-sales {
						font-size: 24rpx;
						color: #999;
						font-weight: 500;
					}
				}
			}
			
			.goods-actions {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				align-items: flex-end;
				flex-shrink: 0; // 防止被压缩
				
				.like-btn {
					width: 60rpx;
					height: 60rpx;
					background: rgba(255, 255, 255, 0.9);
					backdrop-filter: blur(10rpx);
					border-radius: 30rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
					margin-bottom: 20rpx;
					
					.like-icon {
						font-size: 32rpx;
						color: #ccc;
						transition: all 0.3s ease;
						
						&.liked {
							color: #ff4757;
							transform: scale(1.2);
						}
					}
				}
				
				.add-cart-btn {
					background: linear-gradient(135deg, #ff6b35, #ff4757);
					color: #fff;
					border: none;
					border-radius: 32rpx;
					font-size: 24rpx;
					font-weight: 600;
					padding: 12rpx 24rpx;
					min-width: 120rpx;
					transition: all 0.3s ease;
					
					&:active {
						transform: scale(0.98);
						opacity: 0.8;
					}
				}
				
				// 单规格商品的加减控制器
				.quantity-controls {
					display: flex;
					align-items: center;
					background: #fff;
					border-radius: 32rpx;
					border: 2rpx solid #ff6b35;
					overflow: hidden;
					
					.quantity-btn {
						width: 56rpx;
						height: 56rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 28rpx;
						font-weight: 700;
						transition: all 0.3s ease;
						
						&.minus {
							background: #fff;
							color: #ff6b35;
							
							&.disabled {
								color: #ccc;
								cursor: not-allowed;
							}
							
							&:not(.disabled):active {
								background: #f0f0f0;
							}
						}
						
						&.plus {
							background: #ff6b35;
							color: #fff;
							
							&.disabled {
								background: #ccc;
								cursor: not-allowed;
							}
							
							&:not(.disabled):active {
								background: #e85a2f;
							}
						}
					}
					
					.quantity-display {
						min-width: 60rpx;
						text-align: center;
						font-size: 26rpx;
						font-weight: 600;
						color: #333;
						padding: 0 8rpx;
						border-left: 1rpx solid #eee;
						border-right: 1rpx solid #eee;
						background: #fff;
					}
				}
			}
		}
	}
}

// 加载更多
.load-more {
	padding: 40rpx;
	text-align: center;
	font-size: 28rpx;
	color: #999;
}

// 空状态
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	
	image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 40rpx;
		opacity: 0.6;
	}
	
	.empty-text {
		font-size: 28rpx;
		color: #999;
		font-weight: 500;
	}
}

// 底部购物车栏
.bottom-cart {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: #fff;
	padding: 20rpx;
	// 适配底部安全区域
	padding-bottom: calc( env(safe-area-inset-bottom));
	box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;
	justify-content: space-between;
	
	.cart-info {
		display: flex;
		align-items: center;
		gap: 20rpx;
		flex: 1;
		
		.cart-icon-wrapper {
			position: relative;
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.cart-icon {
				font-size: 40rpx;
				color: #fff;
			}
			
			.cart-count {
				position: absolute;
				top: -8rpx;
				right: -8rpx;
				background: #ff4757;
				color: #fff;
				font-size: 20rpx;
				font-weight: 700;
				min-width: 32rpx;
				height: 32rpx;
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 0 8rpx;
				border: 2rpx solid #fff;
			}
		}
		
		.cart-text {
			display: flex;
			flex-direction: column;
			gap: 4rpx;
			flex: 1;
			
			.cart-info-row {
				display: flex;
				align-items: center;
				justify-content: space-between;
			}
			
			.cart-title {
				font-size: 26rpx;
				font-weight: 500;
				color: #666;
			}
			
			.cart-price {
				font-size: 36rpx;
				font-weight: 900;
				color: #ff6b35;
				line-height: 1.2;
			}
		}
	}
	
	.checkout-btn {
		background: linear-gradient(135deg, #ff6b35, #ff4757);
		color: #fff;
		border: none;
		border-radius: 32rpx;
		font-size: 28rpx;
		font-weight: 600;
		padding: 20rpx 40rpx;
		transition: all 0.3s ease;
		
		&:active {
			transform: scale(0.98);
			opacity: 0.8;
		}
		
		&.disabled {
			background: #ccc;
			cursor: not-allowed;
		}
	}
}

// 购物车弹窗
.cart-popup-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 2000;
	display: flex;
	align-items: flex-end;
	
	.cart-popup {
		width: 100%;
		max-height: 75vh;
		min-height: 300rpx;
		background: #fff;
		border-radius: 32rpx 32rpx 0 0;
		display: flex;
		flex-direction: column;
		animation: slideUp 0.3s ease-out;
		overflow: hidden;
		box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
		
		@keyframes slideUp {
			from {
				transform: translateY(100%);
			}
			to {
				transform: translateY(0);
			}
		}
		
		.cart-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 32rpx 40rpx 20rpx;
			border-bottom: 1rpx solid #f0f0f0;
			
			.cart-title {
				font-size: 36rpx;
				font-weight: 700;
				color: #333;
			}
			
			.clear-cart {
				font-size: 28rpx;
				color: #999;
				padding: 12rpx 24rpx;
				border: 1rpx solid #ddd;
				border-radius: 20rpx;
				transition: all 0.3s ease;
				
				&:active {
					background: #f5f5f5;
				}
			}
		}
		
		.cart-content {
			flex: 1;
			max-height: 45vh;
			padding: 0 40rpx;
			overflow-y: auto;
			-webkit-overflow-scrolling: touch;
			box-sizing: border-box;
			.cart-item {
				padding: 32rpx 0;
				border-bottom: 1rpx solid #f5f5f5;
				
				&:last-child {
					border-bottom: none;
				}
				
				.item-info {
					display: flex;
					gap: 24rpx;
					
					.item-image {
						width: 120rpx;
						height: 120rpx;
						border-radius: 12rpx;
						flex-shrink: 0;
					}
					
					.item-details {
						flex: 1;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						
						.item-name {
							font-size: 32rpx;
							font-weight: 600;
							color: #333;
							margin-bottom: 8rpx;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}
						
						.item-specs {
							font-size: 24rpx;
							color: #999;
							margin-bottom: 16rpx;
							background: #f5f5f5;
							padding: 8rpx 16rpx;
							border-radius: 8rpx;
							display: inline-block;
							max-width: fit-content;
						}
						
						.item-price-row {
							display: flex;
							align-items: center;
							justify-content: space-between;
							
							.item-price {
								font-size: 32rpx;
								font-weight: 900;
								color: #ff6b35;
							}
							
															.quantity-control {
									display: flex;
									align-items: center;
									gap: 16rpx;
									
									.quantity-btn {
										width: 56rpx;
										height: 56rpx;
										border-radius: 50%;
										display: flex;
										align-items: center;
										justify-content: center;
										font-size: 28rpx;
										font-weight: 700;
										transition: all 0.3s ease;
										border: 2rpx solid transparent;
										
										&.minus {
											background: #f0f0f0;
											color: #333;
											border-color: #e0e0e0;
											
											&.disabled {
												background: #f8f8f8;
												color: #ccc;
												border-color: #f0f0f0;
												cursor: not-allowed;
											}
										}
										
										&.plus {
											background: linear-gradient(135deg, #ff6b35, #ff4757);
											color: #fff;
											border-color: transparent;
											
											&.disabled {
												background: #ccc;
												border-color: #bbb;
												cursor: not-allowed;
											}
										}
										
										&:not(.disabled):active {
											transform: scale(0.95);
										}
									}
									
									.quantity-num {
										font-size: 28rpx;
										font-weight: 600;
										color: #333;
										min-width: 64rpx;
										text-align: center;
										padding: 8rpx 12rpx;
										background: #f9f9f9;
										border-radius: 8rpx;
										border: 1rpx solid #e0e0e0;
									}
								}
						}
					}
				}
			}
		}
		
		.empty-cart {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 80rpx 40rpx;
			
			image {
				width: 200rpx;
				height: 200rpx;
				margin-bottom: 32rpx;
				opacity: 0.6;
			}
			
			text {
				font-size: 28rpx;
				color: #999;
				font-weight: 500;
			}
		}
		
		.cart-bottom {
			padding: 32rpx 40rpx;
			border-top: 1rpx solid #f0f0f0;
			background: #fff;
			flex-shrink: 0;
			// 适配底部安全区域
			padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
			
			.total-info {
				display: flex;
				align-items: baseline;
				gap: 16rpx;
				margin-bottom: 32rpx;
				
				.total-text {
					font-size: 28rpx;
					color: #333;
				}
				
				.total-price {
					font-size: 40rpx;
					font-weight: 900;
					color: #ff6b35;
					flex: 1;
				}
				
				.total-desc {
					font-size: 24rpx;
					color: #4CAF50;
				}
			}
			
			.action-buttons {
				display: flex;
				gap: 20rpx;
				
				.continue-shopping {
					flex: 1;
					background: #f5f5f5;
					color: #333;
					border: none;
					border-radius: 32rpx;
					font-size: 28rpx;
					font-weight: 600;
					padding: 24rpx;
					transition: all 0.3s ease;
					
					&:active {
						background: #e0e0e0;
					}
				}
				
				.checkout-now {
					flex: 2;
					background: linear-gradient(135deg, #ff6b35, #ff4757);
					color: #fff;
					border: none;
					border-radius: 32rpx;
					font-size: 28rpx;
					font-weight: 600;
					padding: 24rpx;
					transition: all 0.3s ease;
					
					&:active {
						transform: scale(0.98);
						opacity: 0.8;
					}
				}
			}
		}
	}
}
</style> 