<template>
	<PageLayout 
		:show-left="true"
		:show-center="false"
		:show-right="false"
		@navLeftClick="handleBack"
	>
	<view class="goods-detail">
		<!-- 商品轮播图 -->
		<view class="swiper-container">
			<swiper class="swiper" 
				:indicator-dots="true" 
				:autoplay="true"
				:interval="3000"
				:duration="300"
				indicator-active-color="#FF6B35"
				indicator-color="rgba(255,255,255,0.6)"
			>
				<swiper-item v-for="(item, index) in goods.images" :key="index">
					<image :src="item" mode="aspectFill" @click="previewImage(goods.images, index)"></image>
				</swiper-item>
			</swiper>
		</view>
		
		<!-- 商品基本信息 -->
		<view class="info-card">
			<!-- 价格区域 -->
			<view class="price-section">
				<view class="price-main">
					<text class="currency">¥</text>
					<text class="price">{{currentPrice}}</text>
					<text class="original-price" v-if="goods.originalPrice > currentPrice">¥{{goods.originalPrice}}</text>
				</view>
				<view class="quantity-controls">
					<view class="quantity-btn" :class="{ 'disabled': quantity <= 1 }" @click="decreaseQuantity">
						<text class="btn-text">-</text>
					</view>
					<view class="quantity-display">
						<text>{{quantity}}</text>
					</view>
					<view class="quantity-btn" :class="{ 'disabled': quantity >= currentStock }" @click="increaseQuantity">
						<text class="btn-text">+</text>
					</view>
				</view>
			</view>
			
			<!-- 商品标题和销量 -->
			<view class="title-section">
				<text class="title">{{goods.name}}</text>
				<view class="meta-info">
					<text class="desc" v-if="goods.description">{{goods.description}}</text>
					<text class="sales">已售{{goods.totalSales}}份</text>
				</view>
			</view>
		</view>

		<!-- 套餐规格选择 -->
		<view class="spec-card" v-if="goods.comboSpecData && goods.comboSpecData.comboGroups && goods.comboSpecData.comboGroups.length > 0">
			<view class="card-title">
				<text class="title-text">选择套餐</text>
				<text class="title-desc">（任选1项）</text>
			</view>
			
			<view class="combo-groups">
				<view class="combo-group" v-for="(group, groupIndex) in goods.comboSpecData.comboGroups" :key="groupIndex">
					<view class="group-title">
						<text class="group-name">{{group.name}}</text>
						<text class="group-required" v-if="group.isRequired">（必选{{group.minSelect}}项）</text>
					</view>
					
					<view class="combo-options">
						<view 
							class="combo-option" 
							:class="{ 'active': selectedComboSpecs[group.name] === option.name }"
							v-for="(option, optionIndex) in group.options" 
							:key="optionIndex"
							@click="selectComboSpec(group.name, option)"
						>
							<view class="option-left">
								<image 
									class="option-image" 
									:src="option.image || '/static/images/default-goods.png'" 
									mode="aspectFill"
								></image>
								<view class="option-info">
									<text class="option-name">{{option.name}}</text>
									<text class="option-desc" v-if="option.description">{{option.description}}</text>
								</view>
							</view>
							<view class="option-right">
								<text class="option-price" v-if="option.addPrice > 0">+¥{{option.addPrice}}</text>
								<view class="option-select" :class="{ 'selected': selectedComboSpecs[group.name] === option.name }">
									<text class="select-icon" v-if="selectedComboSpecs[group.name] === option.name">✓</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>



		<!-- 商品详情 -->
		<view class="detail-card">
			<view class="card-title">
				<text class="title-text">商品详情</text>
			</view>
			<view class="detail-content">
				<rich-text :nodes="goods.detailDesc"></rich-text>
			</view>
		</view>

		<!-- 用户评价 -->
		<view class="comment-card">
			<view class="card-title">
				<text class="title-text">用户评价</text>
				<text class="comment-count">({{totalComments}})</text>
			</view>
			<view class="comment-list" v-if="commentList.length > 0">
				<view class="comment-item" v-for="item in commentList.slice(0, 3)" :key="item.id">
					<view class="comment-header">
						<image class="user-avatar" :src="item.avatar" mode="aspectFill"></image>
						<view class="user-info">
							<text class="user-name">{{item.nickname}}</text>
							<view class="rating">
								<text class="star" v-for="n in 5" :key="n" :class="{ 'active': n <= item.rate }">★</text>
							</view>
						</view>
						<text class="comment-time">{{formatTime(item.time)}}</text>
					</view>
					<text class="comment-text">{{item.content}}</text>
				</view>
				<view class="view-more" @click="viewAllComments" v-if="totalComments > 3">
					<text>查看全部评价</text>
				</view>
			</view>
			<view class="no-comment" v-else>
				<text>暂无评价</text>
			</view>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="action-bar">
			<view class="action-buttons">
				<button class="btn btn-cart" @click="addToCart">加入购物车</button>
				<button class="btn btn-buy" @click="buyNow">立即购买</button>
			</view>
		</view>
	</view>
</PageLayout>
</template>

<script>
import { getGoodsDetail, getGoodsComments, collectGoods, addToCart } from '@/api/goods'
import AuthUtils from '@/utils/auth'
import PageLayout from '@/components/layout/PageLayout.vue'
export default {
	components: {
		PageLayout
	},
	data() {
		return {
			goodsId: 0,
			goods: {
				name: '',
				description: '',
				originalPrice: 0,
				price: 0,
				totalSales: 0,
				stock: 0,
				images: [],
				detailDesc: '',
				comboSpecData: null
			},
			// 套餐规格选择
			selectedComboSpecs: {},
			// 多规格选择
			selectedSpecs: {},
			// 商品数量
			quantity: 1,
			// 评论数据
			commentList: [],
			totalComments: 0,
			// 加载状态
			loading: false
		}
	},
	
	computed: {
		// 当前价格（基础价格 + 套餐加价）
		currentPrice() {
			let price = 0
			
			// 套餐商品从comboSpecData.baseSku获取基础价格
			if (this.goods.comboSpecData && this.goods.comboSpecData.baseSku) {
				price = this.goods.comboSpecData.baseSku.price || 0
			} else {
				// 其他商品类型使用goods.price
				price = this.goods.price || 0
			}
			
			// 计算套餐加价
			if (this.goods.comboSpecData && this.goods.comboSpecData.comboGroups) {
				this.goods.comboSpecData.comboGroups.forEach(group => {
					const selectedOption = this.selectedComboSpecs[group.name]
					if (selectedOption) {
						const option = group.options.find(opt => opt.name === selectedOption)
						if (option && option.addPrice > 0) {
							price += option.addPrice
						}
					}
				})
			}
			
			return price.toFixed(2)
		},
		
		// 当前库存
		currentStock() {
			// 套餐商品从comboSpecData.baseSku获取库存
			if (this.goods.comboSpecData && this.goods.comboSpecData.baseSku) {
				return this.goods.comboSpecData.baseSku.stock || 0
			}
			
			// 其他商品类型使用goods.stock
			return this.goods.stock || 0
		},
		
		// 获取套餐商品的SKU代码
		comboSkuCode() {
			// 套餐商品的特点：
			// 1. 只有一个基础SKU，不会因为套餐选项组合而产生新的SKU
			// 2. 套餐选项的组合只影响价格（基础价格+加价），不影响SKU
			// 3. 库存管理使用基础SKU的库存
			
			// 套餐商品直接从comboSpecData.baseSku获取基础SKU
			if (this.goods.comboSpecData && this.goods.comboSpecData.baseSku) {
				console.log('套餐商品使用基础SKU:', this.goods.comboSpecData.baseSku.skuCode)
				return this.goods.comboSpecData.baseSku.skuCode
			}
			
			// 如果comboSpecData中没有baseSku，返回默认值（后端会自动查找正确的SKU）
			console.log('套餐商品使用默认SKU，后端会自动处理: COMBO_DEFAULT')
			return 'COMBO_DEFAULT'
		}
	},
	
	onLoad(options) {
		if (options.id) {
			this.goodsId = parseInt(options.id)
			this.loadGoodsDetail()
			this.loadComments()
		}
	},
	
	methods: {
		// 加载商品详情
		async loadGoodsDetail() {
			if (this.loading) return
			
			this.loading = true
			try {
				const { code, data, message } = await getGoodsDetail({
					id: this.goodsId
				})
				
				if (code === 200) {
					this.goods = data
					
					// 初始化套餐规格选择
					if (this.goods.comboSpecData && this.goods.comboSpecData.comboGroups) {
						this.goods.comboSpecData.comboGroups.forEach(group => {
							if (group.isRequired && group.options.length > 0) {
								// 默认选择第一个选项
								this.$set(this.selectedComboSpecs, group.name, group.options[0].name)
							}
						})
					}
					
					// 初始化多规格选择
					if (this.goods.multiSpecData && this.goods.multiSpecData.defaultSelected) {
						this.selectedSpecs = { ...this.goods.multiSpecData.defaultSelected }
					}
				} else {
					uni.showToast({
						title: message || '获取商品详情失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('获取商品详情失败:', error)
				uni.showToast({
					title: '获取商品详情失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 加载评论
		async loadComments() {
			try {
				const { code, data, message } = await getGoodsComments({
					goodsId: this.goodsId,
					page: 1,
					size: 10
				})
				
				if (code === 200) {
					this.commentList = data.list || []
					this.totalComments = data.total || 0
				}
			} catch (error) {
				console.error('获取评论失败:', error)
			}
		},
		
		// 选择套餐规格
		selectComboSpec(groupName, option) {
			this.$set(this.selectedComboSpecs, groupName, option.name)
		},
		
		// 增加数量
		increaseQuantity() {
			if (this.quantity < this.currentStock) {
				this.quantity++
			}
		},
		
		// 减少数量
		decreaseQuantity() {
			if (this.quantity > 1) {
				this.quantity--
			}
		},
		
		// 预览图片
		previewImage(images, current) {
			uni.previewImage({
				urls: images,
				current: current
			})
		},
		
		// 查看全部评价
		viewAllComments() {
			uni.navigateTo({
				url: `/pages/goods/comment?id=${this.goodsId}`
			})
		},
		
		// 加入购物车
		async addToCart() {
			if (!AuthUtils.checkLoginStatus()) return
			
			if (!this.validateSelection()) return
			
			try {
				// 根据商品规格类型构建不同的数据格式
				let cartData;
				
				if (this.goods.comboSpecData && this.goods.comboSpecData.comboGroups && this.goods.comboSpecData.comboGroups.length > 0) {
					// 套餐规格商品
					cartData = {
						goodsId: this.goodsId,
						quantity: this.quantity,
						skuCode: this.comboSkuCode, // 从数据中获取套餐SKU
						specsJson: JSON.stringify(this.selectedComboSpecs)
					}
				} else if (this.goods.multiSpecData && this.goods.multiSpecData.skus && this.goods.multiSpecData.skus.length > 0) {
					// 多规格商品 - 需要选择具体的SKU
					// 这里需要根据选择的规格找到对应的SKU
					const selectedSpecValues = Object.values(this.selectedSpecs);
					const specCombination = selectedSpecValues.join('_');
					const skuCode = this.goods.multiSpecData.specCombinationToSku[specCombination];
					
					if (!skuCode) {
						uni.showToast({
							title: '请选择完整的商品规格',
							icon: 'none'
						})
						return
					}
					
					cartData = {
						goodsId: this.goodsId,
						quantity: this.quantity,
						skuCode: skuCode,
						specsJson: JSON.stringify(this.selectedSpecs)
					}
				} else {
					// 单规格商品
					const defaultSku = this.goods.singleSpecData?.sku || this.goods.specInfo?.skus?.[0];
					cartData = {
						goodsId: this.goodsId,
						quantity: this.quantity,
						skuCode: defaultSku?.skuCode || 'DEFAULT_SKU',
						specsJson: JSON.stringify({})
					}
				}
				
				const { code, message } = await addToCart(cartData)
				
				if (code === 200) {
					uni.showToast({
						title: '已加入购物车',
						icon: 'success'
					})
					
					// 设置标识，返回列表页时显示购物车
					uni.setStorageSync('showCartAfterAddToCart', true)
					
					// 延迟返回列表页
					setTimeout(() => {
						uni.navigateBack()
					}, 1000)
				} else {
					uni.showToast({
						title: message || '加入购物车失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加入购物车失败:', error)
				uni.showToast({
					title: '加入购物车失败',
					icon: 'none'
				})
			}
		},
		
		// 立即购买
		async buyNow() {
			if (!AuthUtils.checkLoginStatus()) return
			
			if (!this.validateSelection()) return
			
			try {
				// 先加入购物车
				let cartData;
				
				if (this.goods.comboSpecData && this.goods.comboSpecData.comboGroups && this.goods.comboSpecData.comboGroups.length > 0) {
					// 套餐规格商品
					cartData = {
						goodsId: this.goodsId,
						quantity: this.quantity,
						skuCode: this.comboSkuCode, // 从数据中获取套餐SKU
						specsJson: JSON.stringify(this.selectedComboSpecs)
					}
				} else if (this.goods.multiSpecData && this.goods.multiSpecData.skus && this.goods.multiSpecData.skus.length > 0) {
					// 多规格商品 - 需要选择具体的SKU
					const selectedSpecValues = Object.values(this.selectedSpecs);
					const specCombination = selectedSpecValues.join('_');
					const skuCode = this.goods.multiSpecData.specCombinationToSku[specCombination];
					
					if (!skuCode) {
						uni.showToast({
							title: '请选择完整的商品规格',
							icon: 'none'
						})
						return
					}
					
					cartData = {
						goodsId: this.goodsId,
						quantity: this.quantity,
						skuCode: skuCode,
						specsJson: JSON.stringify(this.selectedSpecs)
					}
				} else {
					// 单规格商品
					const defaultSku = this.goods.singleSpecData?.sku || this.goods.specInfo?.skus?.[0];
					cartData = {
						goodsId: this.goodsId,
						quantity: this.quantity,
						skuCode: defaultSku?.skuCode || 'DEFAULT_SKU',
						specsJson: JSON.stringify({})
					}
				}
				
				// 调用加入购物车接口
				const { code, message, data } = await addToCart(cartData)
				
				if (code === 200) {
					// 加入购物车成功后，设置购物车选中状态
					// 1. 先取消所有商品的选中状态
					// 2. 只选中刚加入的商品
					await this.setCartSelectionForBuyNow(data?.cartId)
					
					// 跳转到订单确认页面（购物车模式）
					uni.navigateTo({
						url: '/pages/order/confirm?type=cart'
					})
				} else {
					uni.showToast({
						title: message || '操作失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('立即购买失败:', error)
				uni.showToast({
					title: '操作失败，请重试',
					icon: 'none'
				})
			}
		},
		
		// 设置购物车选中状态（立即购买专用）
		async setCartSelectionForBuyNow(newCartId) {
			try {
				// 导入购物车相关API
				const { getCartList, updateCartSelected } = require('@/api/cart')
				
				// 获取购物车列表
				const cartRes = await getCartList()
				if (cartRes.code === 200 && cartRes.data && cartRes.data.list) {
					const cartList = cartRes.data.list
					
					// 取消所有商品的选中状态
					const allCartIds = cartList.map(item => item.id)
					if (allCartIds.length > 0) {
						await updateCartSelected({
							ids: allCartIds,
							selected: false
						})
					}
					
					// 只选中刚加入的商品
					if (newCartId) {
						await updateCartSelected({
							ids: [newCartId],
							selected: true
						})
					} else {
						// 如果没有返回cartId，选中最新的商品（通常是最后一个）
						const latestCartId = cartList[cartList.length - 1]?.id
						if (latestCartId) {
							await updateCartSelected({
								ids: [latestCartId],
								selected: true
							})
						}
					}
				}
			} catch (error) {
				console.error('设置购物车选中状态失败:', error)
				// 即使设置选中状态失败，也不影响主流程
			}
		},
		
		// 检查登录状态
		checkLoginStatus() {
			const token = uni.getStorageSync('token')
			if (!token) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				})
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/login/login'
					})
				}, 1500)
				return false
			}
			return true
		},
		
		// 验证选择
		validateSelection() {
			// 检查套餐规格是否全部选择
			if (this.goods.comboSpecData && this.goods.comboSpecData.comboGroups) {
				for (let group of this.goods.comboSpecData.comboGroups) {
					if (group.isRequired && !this.selectedComboSpecs[group.name]) {
						uni.showToast({
							title: `请选择${group.name}`,
							icon: 'none'
						})
						return false
					}
				}
			}
			
			// 检查库存
			if (this.quantity > this.currentStock) {
				uni.showToast({
					title: '库存不足',
					icon: 'none'
				})
				return false
			}
			
			return true
		},
		
		// 格式化时间
		formatTime(timestamp) {
			const date = new Date(timestamp)
			const now = new Date()
			const diff = now - date
			
			if (diff < 60000) {
				return '刚刚'
			} else if (diff < 3600000) {
				return Math.floor(diff / 60000) + '分钟前'
			} else if (diff < 86400000) {
				return Math.floor(diff / 3600000) + '小时前'
			} else {
				return Math.floor(diff / 86400000) + '天前'
			}
		}
	}
}
</script>

<style lang="scss">
.goods-detail {
	min-height: 100vh;
	background: #f8f9fa;
	
	.swiper-container {
		width: 100%;
		height: 750rpx;
		background: #fff;
		position: relative;
	}
	
	.swiper {
		width: 100%;
		height: 100%;
		
		image {
			width: 100%;
			height: 100%;
		}
	}
	
	.info-card {
		background: #fff;
		padding: 32rpx;
		margin-bottom: 16rpx;
		border-radius: 24rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		
		.price-section {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 24rpx;
			
			.price-main {
				display: flex;
				align-items: baseline;
				flex: 1;
				
				.currency {
					font-size: 28rpx;
					color: #FF6B35;
					font-weight: 700;
				}
				
				.price {
					font-size: 60rpx;
					color: #FF6B35;
					font-weight: 800;
					margin-left: 4rpx;
				}
				
				.original-price {
					font-size: 28rpx;
					color: #999;
					text-decoration: line-through;
					margin-left: 16rpx;
				}
			}
			
			.quantity-controls {
				display: flex;
				align-items: center;
				gap: 16rpx;
				
				.quantity-btn {
					width: 60rpx;
					height: 60rpx;
					border: 2rpx solid #FF6B35;
					border-radius: 30rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background: #fff;
					transition: all 0.3s ease;
					
					&.disabled {
						border-color: #ddd;
						opacity: 0.4;
					}
					
					&:not(.disabled):active {
						background: #FF6B35;
						transform: scale(0.95);
						
						.btn-text {
							color: #fff;
						}
					}
					
					.btn-text {
						font-size: 28rpx;
						color: #FF6B35;
						font-weight: 700;
					}
				}
				
				.quantity-display {
					min-width: 80rpx;
					height: 60rpx;
					background: rgba(255, 107, 53, 0.1);
					border-radius: 30rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					border: 2rpx solid rgba(255, 107, 53, 0.2);
					
					text {
						font-size: 28rpx;
						color: #FF6B35;
						font-weight: 700;
					}
				}
			}
		}
		
		.title-section {
			.title {
				font-size: 36rpx;
				font-weight: 700;
				color: #333;
				line-height: 1.4;
				margin-bottom: 12rpx;
			}
			
			.meta-info {
				display: flex;
				justify-content: space-between;
				align-items: center;
				
				.desc {
					font-size: 26rpx;
					color: #666;
					line-height: 1.4;
					flex: 1;
				}
				
				.sales {
					font-size: 24rpx;
					color: #666;
					background: rgba(255, 107, 53, 0.1);
					padding: 8rpx 16rpx;
					border-radius: 20rpx;
					border: 1rpx solid rgba(255, 107, 53, 0.2);
					margin-left: 16rpx;
				}
			}
		}
	}
	
	.spec-card {
		background: #fff;
		margin-bottom: 16rpx;
		padding: 32rpx;
		border-radius: 24rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		
		.card-title {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 32rpx;
			
			.title-text {
				font-size: 32rpx;
				font-weight: 700;
				color: #333;
			}
			
			.title-desc {
				font-size: 24rpx;
				color: #FF6B35;
				font-weight: 600;
			}
		}
		
		.combo-groups {
			.combo-group {
				margin-bottom: 40rpx;
				
				&:last-child {
					margin-bottom: 0;
				}
				
				.group-title {
					display: flex;
					align-items: center;
					margin-bottom: 24rpx;
					
					.group-name {
						font-size: 28rpx;
						color: #333;
						font-weight: 600;
					}
					
					.group-required {
						font-size: 22rpx;
						color: #FF6B35;
						margin-left: 12rpx;
						background: rgba(255, 107, 53, 0.1);
						padding: 4rpx 12rpx;
						border-radius: 12rpx;
					}
				}
				
				.combo-options {
					display: grid;
					grid-template-columns: repeat(3, 1fr);
					gap: 12rpx;
					
					.combo-option {
						padding: 16rpx;
						border: 2rpx solid #e8e8e8;
						border-radius: 12rpx;
						transition: all 0.3s ease;
						display: flex;
						flex-direction: column;
						align-items: center;
						text-align: center;
						position: relative;
						min-height: 180rpx;
						
						&.active {
							border-color: #FF6B35;
							background: linear-gradient(135deg, rgba(255, 107, 53, 0.05) 0%, rgba(247, 147, 30, 0.05) 100%);
							box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.2);
						}
						
						.option-left {
							display: flex;
							flex-direction: column;
							align-items: center;
							width: 100%;
							
							.option-image {
								width: 100rpx;
								height: 100rpx;
								border-radius: 10rpx;
								margin-bottom: 12rpx;
								background: #f5f5f5;
							}
							
							.option-info {
								width: 100%;
								
								.option-name {
									font-size: 24rpx;
									color: #333;
									font-weight: 600;
									margin-bottom: 6rpx;
									line-height: 1.2;
									text-align: center;
								}
								
								.option-desc {
									font-size: 20rpx;
									color: #666;
									line-height: 1.2;
									text-align: center;
									margin-bottom: 8rpx;
								}
							}
						}
						
						.option-right {
							display: flex;
							flex-direction: column;
							align-items: center;
							gap: 8rpx;
							margin-top: auto;
							
							.option-price {
								font-size: 22rpx;
								color: #FF6B35;
								font-weight: 700;
							}
							
							.option-select {
								position: absolute;
								top: 12rpx;
								right: 12rpx;
								width: 32rpx;
								height: 32rpx;
								border: 2rpx solid #ddd;
								border-radius: 50%;
								display: flex;
								align-items: center;
								justify-content: center;
								transition: all 0.3s ease;
								background: #fff;
								
								&.selected {
									background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
									border-color: #FF6B35;
									
									.select-icon {
										color: #fff;
										font-size: 18rpx;
										font-weight: 700;
									}
								}
							}
						}
					}
				}
			}
		}
	}


	
	.detail-card {
		background: #fff;
		margin-bottom: 16rpx;
		padding: 32rpx;
		border-radius: 24rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		
		.card-title {
			margin-bottom: 24rpx;
			padding-bottom: 16rpx;
			border-bottom: 2rpx solid #f5f5f5;
			
			.title-text {
				font-size: 32rpx;
				font-weight: 700;
				color: #333;
			}
		}
		
		.detail-content {
			line-height: 1.8;
			
			:deep(img) {
				max-width: 100%;
				height: auto;
				border-radius: 12rpx;
				margin: 16rpx 0;
			}
			
			:deep(p) {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 16rpx;
			}
		}
	}
	
	.comment-card {
		background: #fff;
		margin-bottom: 16rpx;
		padding: 32rpx;
		border-radius: 24rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		
		.card-title {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 24rpx;
			padding-bottom: 16rpx;
			border-bottom: 2rpx solid #f5f5f5;
			
			.title-text {
				font-size: 32rpx;
				font-weight: 700;
				color: #333;
			}
			
			.comment-count {
				font-size: 24rpx;
				color: #666;
			}
		}
		
		.comment-list {
			.comment-item {
				margin-bottom: 32rpx;
				padding: 24rpx;
				background: #f8f9fa;
				border-radius: 16rpx;
				border: 1rpx solid #f0f0f0;
				
				&:last-child {
					margin-bottom: 0;
				}
				
				.comment-header {
					display: flex;
					align-items: center;
					margin-bottom: 16rpx;
					
					.user-avatar {
						width: 64rpx;
						height: 64rpx;
						border-radius: 32rpx;
						margin-right: 16rpx;
						background: #f5f5f5;
					}
					
					.user-info {
						flex: 1;
						
						.user-name {
							font-size: 28rpx;
							color: #333;
							font-weight: 600;
							margin-bottom: 8rpx;
						}
						
						.rating {
							display: flex;
							align-items: center;
							
							.star {
								font-size: 24rpx;
								color: #ddd;
								margin-right: 4rpx;
								
								&.active {
									color: #FFD700;
								}
							}
						}
					}
					
					.comment-time {
						font-size: 24rpx;
						color: #999;
					}
				}
				
				.comment-text {
					font-size: 28rpx;
					color: #333;
					line-height: 1.6;
				}
			}
			
			.view-more {
				text-align: center;
				padding: 20rpx;
				background: rgba(255, 107, 53, 0.1);
				border-radius: 12rpx;
				margin-top: 16rpx;
				
				text {
					font-size: 26rpx;
					color: #FF6B35;
					font-weight: 600;
				}
			}
		}
		
		.no-comment {
			text-align: center;
			padding: 60rpx 0;
			
			text {
				font-size: 26rpx;
				color: #999;
			}
		}
	}
	
	.action-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 120rpx;
		background: #fff;
		display: flex;
		align-items: center;
		padding: 0 32rpx;
		box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
		z-index: 1000;
		
		.action-buttons {
			flex: 1;
			display: flex;
			gap: 20rpx;
			
			.btn {
				flex: 1;
				height: 80rpx;
				border-radius: 40rpx;
				border: none;
				font-size: 28rpx;
				font-weight: 700;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s ease;
				
				&.btn-cart {
					color: #FF6B35;
					background: rgba(255, 107, 53, 0.1);
					border: 2rpx solid #FF6B35;
					
					&:active {
						background: rgba(255, 107, 53, 0.2);
						transform: scale(0.98);
					}
				}
				
				&.btn-buy {
					color: #fff;
					background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
					box-shadow: 0 6rpx 20rpx rgba(255, 107, 53, 0.4);
					
					&:active {
						transform: scale(0.98);
						box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.5);
					}
				}
			}
		}
	}
}

page {
	padding-bottom: 140rpx;
}
</style> 