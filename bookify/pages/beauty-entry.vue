<template>
  <view class="beauty-entry">
    <!-- 头部介绍 -->
    <view class="entry-header">
      <image src="/static/beauty/logo.png" class="logo" mode="aspectFit"></image>
      <text class="title">美容院预约系统</text>
      <text class="subtitle">专业美容服务，一键预约</text>
    </view>
    
    <!-- 功能入口 -->
    <view class="entry-features">
      <view class="feature-item" @click="goToHome">
        <view class="feature-icon">
          <u-icon name="home" size="60" color="#FFB6C1"></u-icon>
        </view>
        <text class="feature-name">美容首页</text>
        <text class="feature-desc">浏览服务和技师</text>
      </view>
      
      <view class="feature-item" @click="goToServices">
        <view class="feature-icon">
          <u-icon name="list" size="60" color="#FFB6C1"></u-icon>
        </view>
        <text class="feature-name">服务列表</text>
        <text class="feature-desc">查看所有服务项目</text>
      </view>
      
      <view class="feature-item" @click="goToTechnicians">
        <view class="feature-icon">
          <u-icon name="account" size="60" color="#FFB6C1"></u-icon>
        </view>
        <text class="feature-name">技师列表</text>
        <text class="feature-desc">选择专业技师</text>
      </view>
      
      <view class="feature-item" @click="goToBookings">
        <view class="feature-icon">
          <u-icon name="calendar" size="60" color="#FFB6C1"></u-icon>
        </view>
        <text class="feature-name">我的预约</text>
        <text class="feature-desc">管理预约记录</text>
      </view>
    </view>
    
    <!-- 系统说明 -->
    <view class="entry-description">
      <view class="desc-title">
        <text>系统特色</text>
      </view>
      <view class="desc-content">
        <view class="desc-item">
          <u-icon name="checkmark" size="24" color="#52C41A"></u-icon>
          <text>在线预约，方便快捷</text>
        </view>
        <view class="desc-item">
          <u-icon name="checkmark" size="24" color="#52C41A"></u-icon>
          <text>专业技师，服务保障</text>
        </view>
        <view class="desc-item">
          <u-icon name="checkmark" size="24" color="#52C41A"></u-icon>
          <text>多种服务，满足需求</text>
        </view>
        <view class="desc-item">
          <u-icon name="checkmark" size="24" color="#52C41A"></u-icon>
          <text>会员优惠，超值体验</text>
        </view>
      </view>
    </view>
    
    <!-- 快速预约按钮 -->
    <view class="entry-actions">
      <u-button 
        type="primary" 
        size="large" 
        color="#FFB6C1"
        @click="quickBooking"
      >
        立即预约
      </u-button>
    </view>
  </view>
</template>

<script>
export default {
  name: 'BeautyEntry',
  
  methods: {
    goToHome() {
      uni.navigateTo({
        url: '/pages/beauty/index/index'
      })
    },
    
    goToServices() {
      uni.switchTab({
        url: '/pages/beauty/service/list'
      })
    },
    
    goToTechnicians() {
      uni.navigateTo({
        url: '/pages/beauty/technician/list'
      })
    },
    
          goToBookings() {
        uni.switchTab({
          url: '/pages/beauty/user/booking-history'
        })
      },
    
    quickBooking() {
      uni.navigateTo({
        url: '/pages/beauty/index/index'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.beauty-entry {
  background: linear-gradient(135deg, #FFB6C1 0%, #FF91A4 100%);
  min-height: 100vh;
  padding: 60rpx 30rpx;
  
  .entry-header {
    text-align: center;
    margin-bottom: 60rpx;
    
    .logo {
      width: 120rpx;
      height: 120rpx;
      margin-bottom: 30rpx;
    }
    
    .title {
      font-size: 48rpx;
      font-weight: 600;
      color: #fff;
      display: block;
      margin-bottom: 16rpx;
    }
    
    .subtitle {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }
  
  .entry-features {
    background: #fff;
    border-radius: 20rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 40rpx;
    
    .feature-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      
      .feature-icon {
        width: 100rpx;
        height: 100rpx;
        background: #FFE5E5;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20rpx;
      }
      
      .feature-name {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
      }
      
      .feature-desc {
        font-size: 24rpx;
        color: #666;
        line-height: 1.4;
      }
    }
  }
  
  .entry-description {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;
    
    .desc-title {
      text-align: center;
      margin-bottom: 30rpx;
      
      text {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }
    }
    
    .desc-content {
      .desc-item {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        text {
          font-size: 28rpx;
          color: #333;
          margin-left: 16rpx;
        }
      }
    }
  }
  
  .entry-actions {
    .u-button {
      border-radius: 50rpx;
      font-size: 32rpx;
      font-weight: 600;
    }
  }
}
</style>
