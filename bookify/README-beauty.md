# 美容院预约系统 - 前端实现

## 项目概述

基于 uniapp + Vue.js 开发的美容院预约系统前端，包含完整的用户预约流程和管理功能。

## 功能特性

### 核心功能
- ✅ 美容服务浏览和搜索
- ✅ 技师信息展示和选择
- ✅ 在线预约流程（选择服务 → 选择技师 → 选择时间 → 填写信息 → 确认预约）
- ✅ 用户预约管理（查看、取消、重新预约）
- ✅ 服务评价和反馈
- ✅ 优惠券和支付功能

### 页面结构
```
美容院预约系统
├── 系统入口页 (pages/beauty-entry.vue)
├── 美容首页 (pages/beauty/index/index.vue)
├── 服务模块
│   ├── 服务列表 (pages/beauty/service/list.vue)
│   └── 服务详情 (pages/beauty/service/detail.vue)
├── 技师模块
│   ├── 技师列表 (pages/beauty/technician/list.vue)
│   └── 技师详情 (pages/beauty/technician/detail.vue)
├── 预约流程
│   ├── 选择时间 (pages/beauty/booking/time.vue)
│   ├── 填写信息 (pages/beauty/booking/form.vue)
│   └── 确认预约 (pages/beauty/booking/confirm.vue)
└── 用户中心
    ├── 我的预约 (pages/beauty/user/booking-list.vue)
    └── 预约详情 (pages/beauty/user/booking-detail.vue)
```

### 组件库
- `components/beauty/service-card.vue` - 服务卡片组件
- `components/beauty/technician-card.vue` - 技师卡片组件
- `components/beauty/booking-card.vue` - 预约卡片组件

## 技术栈

- **框架**: uniapp + Vue.js 3
- **UI组件**: uView 2.0
- **状态管理**: 内置状态管理
- **样式**: SCSS + CSS变量
- **数据**: Mock数据（api/beauty/mock.js）

## 快速开始

### 1. 访问系统
在浏览器中访问：`/pages/beauty-entry`

### 2. 主要入口
- **美容首页**: `/pages/beauty/index/index`
- **服务列表**: `/pages/beauty/service/list`
- **技师列表**: `/pages/beauty/technician/list`
- **我的预约**: `/pages/beauty/user/booking-list`

### 3. 预约流程
1. 选择服务项目
2. 选择专业技师（可选）
3. 选择预约时间
4. 填写个人信息
5. 确认预约并支付

## 数据结构

### 服务数据 (Service)
```javascript
{
  id: 1,
  name: "深层清洁护理",
  price: 168,
  originalPrice: 268,
  memberPrice: 148,
  categoryId: 1,
  rating: 4.8,
  reviewCount: 156,
  duration: 60,
  images: ["..."],
  tags: ["深层清洁", "毛孔收缩"],
  description: "...",
  suitableFor: ["油性肌肤", "混合性肌肤"],
  notice: ["护理前请卸妆", "..."],
  effects: ["清洁毛孔", "控油平衡"],
  process: ["卸妆清洁", "蒸汽开孔", "..."]
}
```

### 技师数据 (Technician)
```javascript
{
  id: 1,
  name: "李美美",
  avatar: "...",
  level: "高级技师",
  experience: 5,
  rating: 4.9,
  reviewCount: 342,
  specialty: ["面部护理", "抗衰护理"],
  introduction: "...",
  certificate: ["高级美容师证书", "..."],
  workTime: "09:00-18:00",
  restDay: ["周一"],
  price: 50,
  services: [1, 2, 3, 4],
  gallery: ["..."]
}
```

### 预约数据 (Booking)
```javascript
{
  id: 1,
  userId: 1,
  serviceId: 1,
  serviceName: "深层清洁护理",
  TechnicianUserID: 1,
  technicianName: "李美美",
  bookingDate: "2024-01-15",
  bookingTime: "14:00",
  duration: 60,
  totalPrice: 218,
  status: "confirmed", // pending, confirmed, in_service, completed, cancelled
  customerName: "张小姐",
  customerPhone: "138****8888",
  customerNote: "...",
  createdAt: "2024-01-10 10:30:00"
}
```

## 样式规范

### 颜色主题
- 主色调: `#FFB6C1` (粉色)
- 浅色: `#FFE5E5`
- 深色: `#FF91A4`
- 成功: `#52C41A`
- 警告: `#FA8C16`
- 错误: `#F5222D`

### 字体规范
- 超小: 20rpx
- 小: 24rpx
- 基础: 28rpx
- 大: 32rpx
- 超大: 36rpx
- 标题: 40rpx

### 间距规范
- 基础间距: 16rpx
- 组件间距: 20rpx
- 页面边距: 30rpx

## 功能说明

### 1. 首页功能
- 轮播图展示活动信息
- 服务分类快速导航
- 推荐服务横向滚动
- 明星技师展示
- 快捷入口（我的预约、会员中心、客服）

### 2. 服务功能
- 服务列表展示和筛选
- 服务详情查看
- 服务评价和图片展示
- 可选技师列表
- 一键预约功能

### 3. 技师功能
- 技师列表和筛选
- 技师详情和作品展示
- 技师评价和证书
- 可提供服务列表
- 指定技师预约

### 4. 预约功能
- 智能时间选择
- 个人信息填写
- 优惠券选择
- 支付方式选择
- 预约确认和管理

### 5. 用户功能
- 预约记录查看
- 预约状态跟踪
- 预约取消和重新预约
- 服务评价功能

## 扩展功能

### 即将支持
- [ ] 会员系统
- [ ] 积分系统
- [ ] 营销活动
- [ ] 在线客服
- [ ] 消息推送
- [ ] 分享功能

### 管理后台
- [ ] 预约管理
- [ ] 技师管理
- [ ] 服务管理
- [ ] 数据统计

## 注意事项

1. **数据来源**: 当前使用 Mock 数据，实际使用时需要对接真实 API
2. **支付功能**: 支付相关功能需要对接真实支付平台
3. **用户认证**: 需要集成用户登录和权限管理系统
4. **图片资源**: 需要准备真实的服务和技师图片资源
5. **地图功能**: 需要申请地图服务的 API Key

## 开发建议

1. **组件复用**: 充分利用封装的组件，保持代码一致性
2. **样式统一**: 使用全局样式文件，保持视觉一致性
3. **错误处理**: 完善错误处理和用户提示
4. **性能优化**: 图片懒加载、列表虚拟滚动等
5. **测试覆盖**: 编写单元测试和集成测试

## 联系方式

如有问题或建议，请联系开发团队。

---

**美容院预约系统** - 让美丽触手可及 💄✨
