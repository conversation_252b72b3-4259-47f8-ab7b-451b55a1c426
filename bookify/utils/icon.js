// 将iconfont图标转换为base64
function iconToBase64(iconUnicode, color = '#999999', size = 50) {
    // 在小程序环境中使用uni.createCanvasContext
    const ctx = uni.createCanvasContext('iconCanvas')
    
    // 设置画布大小
    ctx.width = size
    ctx.height = size
    
    // 清空画布
    ctx.clearRect(0, 0, size, size)
    
    // 设置字体和颜色
    ctx.font = `${size}px iconfont`
    ctx.fillStyle = color
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    
    // 绘制图标
    ctx.fillText(iconUnicode, size/2, size/2)
    
    // 转换为图片
    return new Promise((resolve, reject) => {
        ctx.draw(false, () => {
            uni.canvasToTempFilePath({
                canvasId: 'iconCanvas',
                success: (res) => {
                    resolve(res.tempFilePath)
                },
                fail: reject
            })
        })
    })
}

// 生成tabbar图标
export async function generateTabBarIcons() {
    const icons = [
        {
            name: 'shangcheng',
            unicode: '\ue682'
        },
        {
            name: 'gouwuchekong',
            unicode: '\ue600'
        },
        {
            name: 'shangcheng-wode',
            unicode: '\ue633'
        }
    ]
    
    // 创建临时canvas
    const canvasView = document.createElement('canvas-view')
    canvasView.id = 'iconCanvas'
    canvasView.style.position = 'fixed'
    canvasView.style.left = '-999px'
    canvasView.style.top = '-999px'
    document.body.appendChild(canvasView)
    
    try {
        for (const icon of icons) {
            // 生成普通图标
            const normalIcon = await iconToBase64(icon.unicode, '#999999')
            await uni.saveFile({
                tempFilePath: normalIcon,
                filePath: `/static/iconfont/${icon.name}.png`
            })
            
            // 生成选中图标
            const activeIcon = await iconToBase64(icon.unicode, '#ff6b6b')
            await uni.saveFile({
                tempFilePath: activeIcon,
                filePath: `/static/iconfont/${icon.name}-active.png`
            })
        }
    } finally {
        // 移除临时canvas
        document.body.removeChild(canvasView)
    }
}

// 直接使用iconfont类名
export function useIconfont() {
    // 在App.vue中引入iconfont.css后，可以直接使用class="iconfont icon-xxx"
    return {
        home: 'icon-shangcheng',
        cart: 'icon-gouwuchekong',
        user: 'icon-shangcheng-wode'
    }
} 