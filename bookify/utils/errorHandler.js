/**
 * 全局错误处理工具类
 */
class ErrorHandler {
  /**
   * 安全的存储操作
   * @param {Function} storageOperation 存储操作函数
   * @param {string} operationName 操作名称
   * @returns {any} 存储操作结果
   */
  static safeStorage(storageOperation, operationName = '存储操作') {
    try {
      return storageOperation()
    } catch (error) {
      console.warn(`${operationName}失败:`, error)
      return null
    }
  }

  /**
   * 安全的Toast显示
   * @param {Object} options Toast选项
   * @param {string} fallbackMessage 备用消息
   */
  static safeToast(options, fallbackMessage = '操作失败') {
    try {
      uni.showToast({
        title: options.title || fallbackMessage,
        icon: options.icon || 'none',
        duration: options.duration || 2000
      })
    } catch (error) {
      console.warn('Toast显示失败:', error)
    }
  }

  /**
   * 安全的系统信息获取
   * @param {string} key 系统信息键
   * @param {any} defaultValue 默认值
   * @returns {any} 系统信息值
   */
  static safeGetSystemInfo(key, defaultValue = null) {
    try {
      const systemInfo = uni.getSystemInfoSync()
      return systemInfo[key] || defaultValue
    } catch (error) {
      console.warn('获取系统信息失败:', error)
      return defaultValue
    }
  }

  /**
   * 安全导航方法
   * @param {Function} navigationOperation 导航操作函数
   * @param {string} operationName 操作名称
   */
  static safeNavigation(navigationOperation, operationName = '导航操作') {
    try {
      navigationOperation()
    } catch (error) {
      console.warn(`${operationName}失败:`, error)
      this.safeToast({
        title: '页面跳转失败',
        icon: 'none'
      }, '页面跳转失败')
    }
  }

  /**
   * 简单的导航方法，避免复杂的错误处理
   * @param {string} url 目标页面路径
   * @param {string} type 导航类型 'navigateTo' | 'switchTab' | 'redirectTo'
   */
  static simpleNavigate(url, type = 'navigateTo') {
    try {
      switch (type) {
        case 'navigateTo':
          uni.navigateTo({ url })
          break
        case 'switchTab':
          // 对于switchTab，只使用switchTab，不尝试navigateTo
          uni.switchTab({ url })
          break
        case 'redirectTo':
          uni.redirectTo({ url })
          break
        default:
          uni.navigateTo({ url })
      }
    } catch (error) {
      console.warn('导航失败:', error)
      // 尝试使用最简单的跳转方式
      try {
        uni.navigateTo({ url })
      } catch (fallbackError) {
        console.error('备用导航也失败:', fallbackError)
      }
    }
  }

  /**
   * 最简单的导航方法，避免所有复杂的错误处理
   * @param {string} url 目标页面路径
   */
  static simpleNavigateDirect(url) {
    // 直接调用，不做任何错误处理
    uni.navigateTo({ url })
  }

  /**
   * 处理网络错误
   * @param {Error} error 错误对象
   * @param {string} operationName 操作名称
   */
  static handleNetworkError(error, operationName = '网络请求') {
    console.error(`${operationName}失败:`, error)
    this.safeToast({
      title: '网络连接失败，请检查网络设置',
      icon: 'none',
      duration: 2000
    }, '网络连接失败')
  }

  /**
   * 处理业务错误
   * @param {Object} response 响应对象
   * @param {string} operationName 操作名称
   */
  static handleBusinessError(response, operationName = '业务操作') {
    console.error(`${operationName}失败:`, response)
    const message = response.message || '操作失败'
    this.safeToast({
      title: message,
      icon: 'none'
    }, message)
  }

  /**
   * 处理系统错误
   * @param {Error} error 错误对象
   * @param {string} operationName 操作名称
   */
  static handleSystemError(error, operationName = '系统操作') {
    console.error(`${operationName}失败:`, error)
    this.safeToast({
      title: '系统错误，请稍后重试',
      icon: 'none'
    }, '系统错误')
  }

  /**
   * 安全地执行异步操作
   * @param {Function} asyncOperation 异步操作函数
   * @param {string} operationName 操作名称
   * @param {Function} errorHandler 错误处理函数
   * @returns {Promise} 异步操作结果
   */
  static async safeAsync(asyncOperation, operationName = '异步操作', errorHandler = null) {
    try {
      return await asyncOperation()
    } catch (error) {
      if (errorHandler) {
        errorHandler(error, operationName)
      } else {
        this.handleSystemError(error, operationName)
      }
      throw error
    }
  }

  /**
   * 检查网络状态
   * @returns {Promise<boolean>} 网络是否可用
   */
  static async checkNetworkStatus() {
    return new Promise((resolve) => {
      uni.getNetworkType({
        success: (res) => {
          const isConnected = res.networkType !== 'none'
          if (!isConnected) {
            this.safeToast({
              title: '网络连接失败，请检查网络设置',
              icon: 'none',
              duration: 2000
            }, '网络连接失败')
          }
          resolve(isConnected)
        },
        fail: () => {
          this.safeToast({
            title: '网络状态检查失败',
            icon: 'none',
            duration: 2000
          }, '网络状态检查失败')
          resolve(false)
        }
      })
    })
  }

  /**
   * 安全的tabBar页面跳转
   * @param {string} url 目标页面路径
   */
  static safeSwitchTab(url) {
    try {
      // 检查当前页面栈
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      
      // 如果当前页面就是目标页面，不进行跳转
      if (currentPage && currentPage.route === url.replace('/', '')) {
        console.log('当前已在目标页面，无需跳转')
        return
      }
      
      // 尝试switchTab
      uni.switchTab({ 
        url,
        success: () => {
          console.log('switchTab成功:', url)
        },
        fail: (error) => {
          console.error('switchTab失败:', error)
          // 对于tabBar页面，switchTab失败时不尝试navigateTo
          // 只记录错误，不进行备用跳转
        }
      })
    } catch (error) {
      console.error('导航异常:', error)
      // 对于tabBar页面，不尝试备用方案
    }
  }

  /**
   * 安全跳转到需要登录的页面
   * @param {string} url 目标页面路径
   * @param {Object} options 跳转选项
   */
  static async safeNavigateToProtected(url, options = {}) {
    try {
      const app = getApp()
      const RouterGuard = app.$routerGuard
      if (RouterGuard) {
        await RouterGuard.navigateToProtectedPage(url, options)
      } else {
        // 如果路由守卫未初始化，使用普通跳转
        uni.navigateTo({ url, ...options })
      }
    } catch (error) {
      console.error('跳转失败:', error)
      this.safeToast({
        title: '页面跳转失败',
        icon: 'none'
      }, '页面跳转失败')
    }
  }
}

export default ErrorHandler 