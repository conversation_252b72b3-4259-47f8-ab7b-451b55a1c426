/**
 * 支付工具类
 * 统一处理各种支付场景的跳转和参数构建
 */

/**
 * 跳转到统一支付页面
 * @param {Object} options 支付参数
 * @param {string} options.type 支付类型：booking(预约), card(套餐卡), order(商品订单)
 * @param {string|number} options.id 订单ID/预约ID/套餐卡ID
 * @param {Object} options.orderInfo 订单信息（可选，主要用于套餐卡）
 * @param {number} options.payType 默认支付方式：1(微信), 2(支付宝), 3(余额)
 * @param {number} options.amount 支付金额（可选）
 */
export function navigateToPayment(options) {
  const { type, id, orderInfo, payType, amount } = options
  
  if (!type || !id) {
    console.error('支付参数不完整:', options)
    uni.showToast({
      title: '支付参数错误',
      icon: 'none'
    })
    return
  }
  
  // 构建URL参数
  let url = `/pages/common/payment?type=${type}`
  
  // 根据支付类型添加对应的ID参数
  if (type === 'booking') {
    url += `&bookingId=${id}`
  } else if (type === 'card') {
    url += `&cardId=${id}`
    // 如果有订单信息，传递给支付页面
    if (orderInfo) {
      url += `&cardInfo=${encodeURIComponent(JSON.stringify(orderInfo))}`
    }
  } else if (type === 'order') {
    url += `&orderId=${id}`
  }
  
  // 添加可选参数
  if (payType) {
    url += `&payType=${payType}`
  }
  if (amount) {
    url += `&amount=${amount}`
  }
  
  console.log('跳转到支付页面:', url)
  
  uni.navigateTo({
    url: url,
    fail: (err) => {
      console.error('跳转支付页面失败:', err)
      uni.showToast({
        title: '跳转失败',
        icon: 'none'
      })
    }
  })
}

/**
 * 美容预约支付
 * @param {number} bookingId 预约ID
 * @param {number} payType 支付方式
 */
export function payForBooking(bookingId, payType = 1) {
  navigateToPayment({
    type: 'booking',
    id: bookingId,
    payType: payType
  })
}

/**
 * 套餐卡支付
 * @param {number} cardId 套餐卡ID
 * @param {Object} cardInfo 套餐卡信息
 * @param {number} payType 支付方式
 */
export function payForCard(cardId, cardInfo, payType = 1) {
  navigateToPayment({
    type: 'card',
    id: cardId,
    orderInfo: cardInfo,
    payType: payType
  })
}

/**
 * 商品订单支付
 * @param {number} orderId 订单ID
 * @param {number} amount 支付金额
 * @param {number} payType 支付方式
 */
export function payForOrder(orderId, amount, payType = 1) {
  navigateToPayment({
    type: 'order',
    id: orderId,
    amount: amount,
    payType: payType
  })
}

/**
 * 检查支付参数有效性
 * @param {Object} params 支付参数
 * @returns {boolean} 是否有效
 */
export function validatePaymentParams(params) {
  const { type, id, amount } = params
  
  if (!type || !id) {
    return false
  }
  
  if (amount !== undefined && (isNaN(amount) || amount <= 0)) {
    return false
  }
  
  const validTypes = ['booking', 'card', 'order']
  if (!validTypes.includes(type)) {
    return false
  }
  
  return true
}

/**
 * 格式化支付金额
 * @param {number} amount 金额
 * @returns {string} 格式化后的金额字符串
 */
export function formatPaymentAmount(amount) {
  if (isNaN(amount)) return '0.00'
  return Number(amount).toFixed(2)
}

/**
 * 获取支付方式名称
 * @param {number} payType 支付方式代码
 * @returns {string} 支付方式名称
 */
export function getPaymentTypeName(payType) {
  const payTypeMap = {
    1: '微信支付',
    2: '支付宝支付',
    3: '余额支付'
  }
  return payTypeMap[payType] || '未知支付方式'
}

/**
 * 支付成功后的通用处理
 * @param {string} type 支付类型
 * @param {Object} result 支付结果
 */
export function handlePaymentSuccess(type, result) {
  console.log(`${type}支付成功:`, result)
  
  // 可以在这里添加通用的支付成功处理逻辑
  // 比如埋点统计、用户行为记录等
  
  uni.showToast({
    title: '支付成功',
    icon: 'success'
  })
}

/**
 * 支付失败后的通用处理
 * @param {string} type 支付类型
 * @param {Object} error 错误信息
 */
export function handlePaymentError(type, error) {
  console.error(`${type}支付失败:`, error)
  
  // 可以在这里添加通用的支付失败处理逻辑
  // 比如错误上报、用户提示等
  
  uni.showToast({
    title: error.message || '支付失败',
    icon: 'none'
  })
}

/**
 * 构建支付参数
 * @param {string} type 支付类型
 * @param {Object} data 原始数据
 * @returns {Object} 标准化的支付参数
 */
export function buildPaymentParams(type, data) {
  const baseParams = {
    pay_type: data.payType || 1
  }
  
  switch (type) {
    case 'booking':
      return {
        ...baseParams,
        booking_id: parseInt(data.bookingId || data.id)
      }
    case 'card':
      return {
        ...baseParams,
        user_card_id: data.cardId || data.id
      }
    case 'order':
      return {
        ...baseParams,
        orderId: parseInt(data.orderId || data.id)
      }
    default:
      return baseParams
  }
}

export default {
  navigateToPayment,
  payForBooking,
  payForCard,
  payForOrder,
  validatePaymentParams,
  formatPaymentAmount,
  getPaymentTypeName,
  handlePaymentSuccess,
  handlePaymentError,
  buildPaymentParams
}
