import AuthUtils from '@/utils/auth.js'
import <PERSON><PERSON><PERSON><PERSON>and<PERSON> from '@/utils/errorHandler'

/**
 * 需要登录的页面路径
 */
const LOGIN_REQUIRED_PATHS = [
  // 美容预约相关页面
  '/pages/beauty/service/detail',
  '/pages/beauty/booking/form',
  '/pages/beauty/booking/confirm',
  '/pages/beauty/booking/list',
  '/pages/beauty/booking/detail',
  '/pages/beauty/technician/detail',
  '/pages/beauty/user/profile',
  '/pages/beauty/user/booking-list',
  
  // 商城相关页面
  '/pages/user/index',
  '/pages/user/profile',
  '/pages/order/list',
  '/pages/order/detail',
  '/pages/cart/index',
  '/pages/collect/index',
  '/pages/address/list',
  '/pages/address/edit',
  
  // 支付相关页面
  '/pages/payment/index',
  '/pages/payment/result',
  '/pages/common/payment'
]

/**
 * 不需要登录检查的页面路径
 */
const LOGIN_FREE_PATHS = [
  '/pages/login/login',
  '/pages/index/index',
  '/pages/beauty/index/index',
  '/pages/beauty/service/list',
  '/pages/beauty/technician/list',
  '/pages/goods/list',
  '/pages/goods/detail',
  '/pages/category/index',
  '/pages/home/<USER>'
]

/**
 * 路由守卫类
 */
class RouterGuard {
  /**
   * 检查页面是否需要登录
   * @param {string} path - 页面路径
   * @returns {boolean}
   */
  static isLoginRequired(path) {
    // 检查是否在需要登录的页面列表中
    return LOGIN_REQUIRED_PATHS.some(requiredPath => 
      path.startsWith(requiredPath)
    )
  }

  /**
   * 检查页面是否不需要登录检查
   * @param {string} path - 页面路径
   * @returns {boolean}
   */
  static isLoginFree(path) {
    // 检查是否在不需要登录检查的页面列表中
    return LOGIN_FREE_PATHS.some(freePath => 
      path.startsWith(freePath)
    )
  }

  /**
   * 路由守卫主函数
   * @param {string} url - 目标页面URL
   * @param {Object} options - 跳转选项
   * @returns {boolean} - 是否允许跳转
   */
  static async beforeRoute(url, options = {}) {
    console.log('路由守卫检查:', url)
    
    // 解析页面路径
    const path = this.parsePath(url)
    
    // 如果是不需要登录检查的页面，直接允许跳转
    if (this.isLoginFree(path)) {
      console.log('页面无需登录检查:', path)
      return true
    }
    
    // 检查是否需要登录
    if (this.isLoginRequired(path)) {
      console.log('页面需要登录:', path)
      
      // 检查登录状态
      const isLoggedIn = AuthUtils.isLoggedIn()
      
      if (!isLoggedIn) {
        console.log('用户未登录，跳转到登录页')
        
        // 显示提示
        ErrorHandler.safeToast({
          title: '请先登录',
          icon: 'none'
        }, '请先登录')
        
        // 跳转到登录页，并传递重定向参数
        setTimeout(() => {
          const redirectUrl = encodeURIComponent(url)
          ErrorHandler.safeNavigation(() => {
            uni.navigateTo({
              url: `/pages/login/login?redirect=${redirectUrl}`
            })
          }, '跳转登录页')
        }, 1500)
        
        return false
      }
      
      // 已登录，检查用户信息是否完整
      try {
        const userInfo = AuthUtils.getUserInfo()
        if (!userInfo) {
          console.log('用户信息不完整，刷新用户信息')
          await AuthUtils.refreshUserInfo()
        }
      } catch (error) {
        console.error('刷新用户信息失败:', error)
        // 如果刷新失败，清除登录信息并跳转到登录页
        AuthUtils.clearLoginInfo()
        ErrorHandler.safeNavigation(() => {
          uni.navigateTo({
            url: `/pages/login/login?redirect=${encodeURIComponent(url)}`
          })
        }, '跳转登录页')
        return false
      }
    }
    
    console.log('路由守卫检查通过:', path)
    return true
  }

  /**
   * 解析页面路径
   * @param {string} url - 完整URL
   * @returns {string} - 页面路径
   */
  static parsePath(url) {
    // 移除查询参数
    const path = url.split('?')[0]
    return path
  }

  /**
   * 安全跳转方法
   * @param {string} url - 目标页面URL
   * @param {Object} options - 跳转选项
   */
  static async safeNavigate(url, options = {}) {
    const canNavigate = await this.beforeRoute(url, options)
    
    if (canNavigate) {
      const { method = 'navigateTo', ...navigateOptions } = options
      
      switch (method) {
        case 'navigateTo':
          uni.navigateTo({ url, ...navigateOptions })
          break
        case 'redirectTo':
          uni.redirectTo({ url, ...navigateOptions })
          break
        case 'reLaunch':
          uni.reLaunch({ url, ...navigateOptions })
          break
        case 'switchTab':
          uni.switchTab({ url, ...navigateOptions })
          break
        default:
          uni.navigateTo({ url, ...navigateOptions })
      }
    }
  }

  /**
   * 安全跳转到需要登录的页面
   * @param {string} url - 目标页面URL
   * @param {Object} options - 跳转选项
   */
  static async navigateToProtectedPage(url, options = {}) {
    // 检查登录状态
    if (!AuthUtils.isLoggedIn()) {
      console.log('用户未登录，跳转到登录页')
      
      // 显示提示
      ErrorHandler.safeToast({
        title: '请先登录',
        icon: 'none'
      }, '请先登录')
      
      // 跳转到登录页，并传递重定向参数
      setTimeout(() => {
        const redirectUrl = encodeURIComponent(url)
        ErrorHandler.safeNavigation(() => {
          uni.navigateTo({
            url: `/pages/login/login?redirect=${redirectUrl}`
          })
        }, '跳转登录页')
      }, 1500)
      
      return false
    }
    
    // 已登录，直接跳转
    return this.safeNavigate(url, options)
  }
}

export default RouterGuard 