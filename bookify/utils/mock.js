// 生成指定范围的随机数
function randomNum(min, max) {
	return Math.floor(Math.random() * (max - min + 1) + min)
}

// 生成随机订单号
export function generateOrderNo() {
	const date = new Date()
	const year = date.getFullYear()
	const month = String(date.getMonth() + 1).padStart(2, '0')
	const day = String(date.getDate()).padStart(2, '0')
	const random = String(randomNum(1000, 9999))
	return `DD${year}${month}${day}${random}`
}

// 生成随机商品数据
export function generateGoods(num = 1) {
	return Array(num).fill().map((_, index) => ({
		id: randomNum(1000, 9999),
		title: `测试商品${index + 1}`,
		price: (randomNum(1000, 99900) / 100).toFixed(2),
		num: randomNum(1, 5),
		image: `https://mp-a667b617-c5f1-4a2d-9a54-683a67cff588.cdn.bspapp.com/cloudstorage/goods${randomNum(1, 6)}.jpg`,
		specText: ['红色', '蓝色', '黑色'][randomNum(0, 2)] + ', ' + ['S', 'M', 'L', 'XL'][randomNum(0, 3)]
	}))
}

// 生成随机订单数据
export function generateOrders(num = 10) {
	const statusMap = {
		unpaid: '待付款',
		undelivered: '待发货',
		delivered: '待收货',
		completed: '已完成',
		cancelled: '已取消'
	}
	const statusList = Object.keys(statusMap)
	
	return Array(num).fill().map(() => {
		const goodsList = generateGoods(randomNum(1, 3))
		const status = statusList[randomNum(0, statusList.length - 1)]
		const totalNum = goodsList.reduce((sum, item) => sum + item.num, 0)
		const totalAmount = goodsList.reduce((sum, item) => sum + item.price * item.num, 0).toFixed(2)
		
		return {
			id: randomNum(1000, 9999),
			orderNo: generateOrderNo(),
			status,
			statusText: statusMap[status],
			totalNum,
			totalAmount,
			goodsList,
			createTime: '2024-03-15 12:00:00'
		}
	})
} 