import { getUserInfo } from '@/api/user'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@/utils/errorHandler'

/**
 * 用户认证工具类
 */
class AuthUtils {
  /**
   * 检查用户是否已登录
   * @returns {boolean}
   */
  static isLoggedIn() {
    try {
      const token = uni.getStorageSync('token')
      return !!token
    } catch (error) {
      console.error('检查登录状态失败:', error)
      return false
    }
  }

  /**
   * 获取用户信息
   * @returns {Object|null}
   */
  static getUserInfo() {
    try {
      return uni.getStorageSync('userInfo')
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  }

  /**
   * 获取用户Token
   * @returns {string|null}
   */
  static getToken() {
    try {
      return uni.getStorageSync('token')
    } catch (error) {
      console.error('获取Token失败:', error)
      return null
    }
  }

  /**
   * 保存用户登录信息
   * @param {Object} loginData - 登录返回的数据
   */
  static saveLoginInfo(loginData) {
    try {
      // 保存token
      uni.setStorageSync('token', loginData.token)
      
      // 保存用户信息
      const userInfo = {
        id: loginData.id,
        username: loginData.username,
        nickname: loginData.nickname,
        avatar: loginData.avatar,
        phone: loginData.phone,
        gender: loginData.gender,
        birthday: loginData.birthday,
        balance: loginData.balance
      }
      uni.setStorageSync('userInfo', userInfo)
      
      console.log('登录信息保存成功:', userInfo)
    } catch (error) {
      console.error('保存登录信息失败:', error)
    }
  }

  /**
   * 清除登录信息
   */
  static clearLoginInfo() {
    try {
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
      console.log('登录信息清除成功')
    } catch (error) {
      console.error('清除登录信息失败:', error)
    }
  }

  /**
   * 刷新用户信息
   * @returns {Promise<Object>}
   */
  static async refreshUserInfo() {
    try {
      const response = await getUserInfo()
      if (response.code === 200) {
        const userInfo = {
          id: response.data.id,
          username: response.data.username,
          nickname: response.data.nickname,
          avatar: response.data.avatar,
          phone: response.data.phone,
          gender: response.data.gender,
          birthday: response.data.birthday,
          balance: response.data.balance
        }
        uni.setStorageSync('userInfo', userInfo)
        return userInfo
      } else {
        throw new Error(response.message || '获取用户信息失败')
      }
    } catch (error) {
      console.error('刷新用户信息失败:', error)
      // 如果获取用户信息失败，可能是token过期，清除登录信息
      this.clearLoginInfo()
      throw error
    }
  }

  /**
   * 检查登录状态，如果未登录则跳转到登录页
   * @param {boolean} redirect - 是否跳转到登录页
   * @returns {boolean} - 是否已登录
   */
  static checkLoginStatus(redirect = true) {
    const isLoggedIn = this.isLoggedIn()
    
    if (!isLoggedIn && redirect) {
      ErrorHandler.safeToast({
        title: '请先登录',
        icon: 'none'
      }, '请先登录')
      
      // 延迟跳转，让用户看到提示
      setTimeout(() => {
        ErrorHandler.safeNavigation(() => {
          uni.navigateTo({
            url: '/pages/login/login'
          })
        }, '跳转登录页')
      }, 1500)
    }
    
    return isLoggedIn
  }

  /**
   * 检查登录状态并获取用户信息
   * @param {boolean} redirect - 是否跳转到登录页
   * @returns {Promise<Object|null>} - 用户信息
   */
  static async checkLoginAndGetUser(redirect = true) {
    if (!this.checkLoginStatus(redirect)) {
      return null
    }
    
    try {
      // 先尝试从本地获取
      let userInfo = this.getUserInfo()
      
      // 如果本地没有，则从服务器获取
      if (!userInfo) {
        userInfo = await this.refreshUserInfo()
      }
      
      return userInfo
    } catch (error) {
      console.error('获取用户信息失败:', error)
      if (redirect) {
        ErrorHandler.safeNavigation(() => {
          uni.navigateTo({
            url: '/pages/login/login'
          })
        }, '跳转登录页')
      }
      return null
    }
  }

  /**
   * 处理登录成功
   * @param {Object} loginData - 登录返回的数据
   * @param {string} redirectUrl - 登录成功后跳转的页面
   */
  static handleLoginSuccess(loginData, redirectUrl = null) {
    // 保存登录信息
    this.saveLoginInfo(loginData)
    // 显示成功提示
    ErrorHandler.safeToast({
      title: '登录成功',
      icon: 'success'
    }, '登录成功')
    // 跳转到指定页面或返回上一页
    setTimeout(() => {
      if (redirectUrl) {
        ErrorHandler.safeNavigation(() => {
          // 判断是否是tabbar页面
          const tabbarPages = [
            '/pages/index/index',
            '/pages/cart/cart',
            '/pages/user/index',
            '/pages/beauty/index/index',
            // 如有更多tabbar页面请补充
          ]
          if (tabbarPages.includes(redirectUrl)) {
            uni.switchTab({ url: redirectUrl })
          } else {
            uni.redirectTo({ url: redirectUrl })
          }
        }, '跳转目标页面')
      } else {
        ErrorHandler.safeNavigation(() => {
          uni.navigateBack()
        }, '返回上一页')
      }
    }, 1500)
  }

  /**
   * 处理登录失败
   * @param {Error} error - 错误信息
   */
  static handleLoginError(error) {
    console.error('登录失败:', error)
    ErrorHandler.safeToast({
      title: error.message || '登录失败',
      icon: 'none'
    }, error.message || '登录失败')
  }
}

export default AuthUtils 