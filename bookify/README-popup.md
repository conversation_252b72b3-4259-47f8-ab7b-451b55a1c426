# 通用弹窗组件文档

## 概述

这是一个基于Pinia的通用弹窗管理系统，支持高度自定义的弹窗配置，包括标题样式、内容、按钮等各个部分的自定义。

## 文件结构

```
web/
├── stores/
│   └── popup.js                 # Pinia弹窗状态管理
├── components/
│   └── popup/
│       └── BasePopup.vue        # 通用弹窗组件
├── components/layout/
│   └── PageLayout.vue           # 页面布局组件(集成弹窗)
├── pages/
│   └── popup-demo/
│       └── index.vue            # 弹窗功能演示页面
└── README-popup.md              # 本文档
```

## 快速开始

### 1. 基础使用

```javascript
// 在Vue组件中使用
import { usePopupStore } from '@/stores/popup'

export default {
  methods: {
    showBasicPopup() {
      const popupStore = usePopupStore()
      
      popupStore.addPopup({
        id: 'basic',
        title: '基础弹窗',
        content: '这是一个基础的弹窗示例'
      })
    }
  }
}
```

### 2. 自动集成

任何使用 `PageLayout.vue` 的页面都会自动具备弹窗功能，无需额外配置。

## 完整API文档

### addPopup(popupConfig)

添加弹窗到队列中。

#### 参数配置 (popupConfig)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| id | String | `Date.now().toString()` | 弹窗唯一标识，防止重复 |
| title | String | `''` | 弹窗标题，为空则不显示标题区域 |
| content | String | `''` | 弹窗内容 |
| titleStyle | Object | `{}` | 标题样式配置 |
| titleStyle.color | String | `'#333'` | 标题文字颜色 |
| titleStyle.background | String | `'#fff'` | 标题背景色，支持渐变 |
| titleStyle.backgroundImage | String | `''` | 标题背景图片URL |
| buttons | Array | `[{text: '确定', type: 'primary'}]` | 底部按钮配置 |
| priority | Number | `0` | 优先级，数字越大优先级越高 |
| autoClose | Number | `0` | 自动关闭时间(毫秒)，0表示不自动关闭 |
| delay | Number | `0` | 延迟显示时间(毫秒) |
| maskClosable | Boolean | `true` | 点击遮罩是否关闭弹窗 |
| onShow | Function | `null` | 显示时回调函数 |
| onClose | Function | `null` | 关闭时回调函数 |

#### 按钮配置 (buttons数组项)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| text | String | `'确定'` | 按钮文字 |
| type | String | `'default'` | 按钮类型：default/primary/success/warning/danger |
| style | Object | `{}` | 自定义按钮样式 |
| onClick | Function | `null` | 点击回调函数 |

### 其他方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| closePopup(result) | result: 关闭结果 | 关闭当前弹窗 |
| clearQueue() | - | 清空弹窗队列 |
| removePopup(id) | id: 弹窗ID | 移除指定弹窗 |

### 状态获取

| 属性名 | 类型 | 说明 |
|--------|------|------|
| queueLength | Number | 队列中弹窗数量 |
| hasWaitingPopup | Boolean | 是否有待显示的弹窗 |
| getCurrentPopup | Object | 当前弹窗信息 |
| isVisible | Boolean | 弹窗是否可见 |
| isProcessing | Boolean | 是否正在处理弹窗 |

## 使用示例

### 1. 基础弹窗

```javascript
popupStore.addPopup({
  id: 'basic',
  title: '基础弹窗',
  content: '这是一个基础的弹窗示例'
})
```

### 2. 自定义标题样式

```javascript
// 彩色标题
popupStore.addPopup({
  title: '彩色标题',
  content: '标题使用了自定义颜色',
  titleStyle: {
    color: '#fff',
    background: '#007aff'
  }
})

// 渐变背景
popupStore.addPopup({
  title: '渐变背景',
  content: '标题使用了渐变背景',
  titleStyle: {
    color: '#fff',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  }
})

// 背景图片
popupStore.addPopup({
  title: '背景图片',
  content: '标题使用了背景图片',
  titleStyle: {
    color: '#fff',
    backgroundImage: 'https://example.com/bg.jpg'
  }
})
```

### 3. 自定义按钮

```javascript
// 多个按钮
popupStore.addPopup({
  title: '确认操作',
  content: '确定要删除这个项目吗？',
  buttons: [
    {
      text: '取消',
      type: 'default',
      onClick: () => {
        console.log('取消操作')
        popupStore.closePopup()
      }
    },
    {
      text: '确定',
      type: 'danger',
      onClick: () => {
        console.log('确认删除')
        // 执行删除操作
        popupStore.closePopup()
      }
    }
  ]
})

// 自定义样式按钮
popupStore.addPopup({
  title: '自定义按钮',
  content: '按钮使用了自定义样式',
  buttons: [
    {
      text: '自定义按钮',
      type: 'primary',
      style: {
        fontSize: '18px',
        fontWeight: 'bold',
        borderRadius: '20px'
      },
      onClick: () => popupStore.closePopup()
    }
  ]
})
```

### 4. 高级功能

```javascript
// 自动关闭
popupStore.addPopup({
  title: '自动关闭',
  content: '这个弹窗5秒后自动关闭',
  autoClose: 5000
})

// 延迟显示
popupStore.addPopup({
  title: '延迟显示',
  content: '这个弹窗延迟2秒显示',
  delay: 2000
})

// 禁止遮罩关闭
popupStore.addPopup({
  title: '重要通知',
  content: '必须点击按钮才能关闭',
  maskClosable: false
})

// 优先级控制
popupStore.addPopup({
  title: '高优先级',
  content: '这个弹窗会优先显示',
  priority: 10
})
```

### 5. 回调函数

```javascript
popupStore.addPopup({
  title: '带回调的弹窗',
  content: '演示显示和关闭回调',
  onShow: () => {
    console.log('弹窗已显示')
  },
  onClose: (result) => {
    console.log('弹窗已关闭，结果:', result)
  },
  buttons: [
    {
      text: '确定',
      type: 'primary',
      onClick: () => {
        popupStore.closePopup('confirmed')
      }
    }
  ]
})
```

## 按钮类型样式

| 类型 | 外观 | 使用场景 |
|------|------|----------|
| default | 灰色背景 | 取消、关闭等次要操作 |
| primary | 蓝色背景 | 确定、提交等主要操作 |
| success | 绿色背景 | 成功、保存等正面操作 |
| warning | 黄色背景 | 警告、注意等提醒操作 |
| danger | 红色背景 | 删除、危险等负面操作 |

## 最佳实践

1. **合理使用优先级**：重要通知使用高优先级，普通提示使用默认优先级
2. **避免过度使用**：不要同时添加太多弹窗，影响用户体验
3. **提供明确的操作**：按钮文字要清晰明确，让用户知道操作结果
4. **适当的自动关闭**：信息类弹窗可以设置自动关闭，操作类弹窗建议手动关闭
5. **一致的视觉风格**：在同一应用中保持弹窗样式的一致性

## 注意事项

1. 弹窗会按优先级排队显示，同一时间只显示一个弹窗
2. 相同ID的弹窗会被替换而不是重复添加
3. 弹窗关闭后会自动显示队列中的下一个弹窗
4. 组件销毁时会自动清理定时器，防止内存泄漏

## 演示页面

访问 `/pages/popup-demo/index` 查看完整的功能演示和使用示例。