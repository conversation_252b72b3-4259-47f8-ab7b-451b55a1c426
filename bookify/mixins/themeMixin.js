export default {
	data() {
		return {
			currentTheme: 'default'
		}
	},
	
	onLoad() {
		// 从本地存储获取主题设置
		const savedTheme = uni.getStorageSync('app_theme') || 'default'
		this.currentTheme = savedTheme
	},
	
	methods: {
		// 切换主题
		switchTheme(theme) {
			if (['default', 'dark', 'light'].includes(theme)) {
				this.currentTheme = theme
				uni.setStorageSync('app_theme', theme)
				
				// 触发全局主题变更事件
				uni.$emit('themeChanged', theme)
				
				uni.showToast({
					title: `已切换到${this.getThemeName(theme)}主题`,
					icon: 'success'
				})
			}
		},
		
		// 获取主题名称
		getThemeName(theme) {
			const themeNames = {
				'default': '默认',
				'dark': '深色',
				'light': '浅色'
			}
			return themeNames[theme] || '默认'
		},
		
		// 获取当前主题
		getCurrentTheme() {
			return this.currentTheme
		},
		
		// 判断是否为深色主题
		isDarkTheme() {
			return this.currentTheme === 'dark'
		},
		
		// 判断是否为浅色主题
		isLightTheme() {
			return this.currentTheme === 'light'
		}
	},
	
	created() {
		// 监听主题变更事件
		uni.$on('themeChanged', (theme) => {
			this.currentTheme = theme
		})
	},
	
	beforeDestroy() {
		// 移除事件监听
		uni.$off('themeChanged')
	}
} 