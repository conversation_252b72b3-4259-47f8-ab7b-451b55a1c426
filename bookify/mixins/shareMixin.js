export default {
	// #ifdef MP-WEIXIN
	// 微信小程序右上角分享
	async onShareAppMessage(res) {
		let inviterId = null
		try {
			const userInfo = uni.getStorageSync('userInfo')
			if (userInfo && userInfo.id) {
				inviterId = userInfo.id
			}
		} catch (error) {
			console.error('[ShareMixin] Error accessing storage:', error)
		}
		
		// 获取分享标题
		const title = this.shareTitle || '发现好物，一起分享'
		
		// 优先使用页面定义的 sharePath，否则使用当前页面路径
		let basePath = ''
		if (this.sharePath) {
			basePath = this.sharePath
		} else {
			const pages = getCurrentPages()
			if (pages.length > 0) {
				basePath = '/' + pages[pages.length - 1].route
			}
		}
		
		let finalPath = basePath
		const params = []
		
		// 添加邀请者 ID
		if (inviterId) {
			params.push(`inviterId=${inviterId}`)
		}
		
		// 拼接参数到路径
		if (params.length > 0) {
			if (finalPath.includes('?')) {
				finalPath += '&' + params.join('&')
			} else {
				finalPath += '?' + params.join('&')
			}
		}
		
		console.log('[ShareMixin] 最终分享路径:', finalPath)
		
		return {
			title: title,
			path: finalPath
		}
	},
	
	// 分享到朋友圈
	async onShareTimeline(res) {
		let inviterId = null
		try {
			const userInfo = uni.getStorageSync('userInfo')
			if (userInfo && userInfo.id) {
				inviterId = userInfo.id
			}
		} catch (error) {
			console.error('[ShareMixin] Error accessing storage:', error)
		}
		
		const title = this.shareTitle || '发现好物，一起分享'
		
		let basePath = ''
		if (this.sharePath) {
			basePath = this.sharePath
		} else {
			const pages = getCurrentPages()
			if (pages.length > 0) {
				basePath = '/' + pages[pages.length - 1].route
			}
		}
		
		let finalPath = basePath
		const params = []
		
		if (inviterId) {
			params.push(`inviterId=${inviterId}`)
		}
		
		if (params.length > 0) {
			if (finalPath.includes('?')) {
				finalPath += '&' + params.join('&')
			} else {
				finalPath += '?' + params.join('&')
			}
		}
		
		return {
			title: title,
			path: finalPath
		}
	},
	// #endif
	
	methods: {
		// 处理分享来源，保存邀请者ID
		handleShareInviter() {
			const pages = getCurrentPages()
			if (pages.length > 0) {
				const currentPage = pages[pages.length - 1]
				const options = currentPage.options
				
				if (options.inviterId) {
					// 保存邀请者ID，注册时使用
					this.saveInviterId(options.inviterId)
				}
			}
		},
		
		// 保存邀请者ID
		saveInviterId(inviterId) {
			try {
				// 保存到本地存储，注册时读取
				uni.setStorageSync('inviterId', inviterId)
				console.log('邀请者ID已保存:', inviterId)
				
				// 可选：设置过期时间（7天）
				const expireTime = Date.now() + 7 * 24 * 60 * 60 * 1000
				uni.setStorageSync('inviterIdExpire', expireTime)
			} catch (error) {
				console.error('保存邀请者ID失败:', error)
			}
		},
		
		// 获取有效的邀请者ID（注册时调用）
		getValidInviterId() {
			try {
				const inviterId = uni.getStorageSync('inviterId')
				const expireTime = uni.getStorageSync('inviterIdExpire')
				
				// 检查是否过期
				if (expireTime && Date.now() > expireTime) {
					// 已过期，清除数据
					uni.removeStorageSync('inviterId')
					uni.removeStorageSync('inviterIdExpire')
					return null
				}
				
				return inviterId
			} catch (error) {
				console.error('获取邀请者ID失败:', error)
				return null
			}
		},
		
		// 清除邀请者ID（注册成功后调用）
		clearInviterId() {
			try {
				uni.removeStorageSync('inviterId')
				uni.removeStorageSync('inviterIdExpire')
				console.log('邀请者ID已清除')
			} catch (error) {
				console.error('清除邀请者ID失败:', error)
			}
		}
	},
	
	// 页面显示时处理分享来源
	onLoad(options) {
		if (options && options.inviterId) {
			this.handleShareInviter()
		}
	}
} 