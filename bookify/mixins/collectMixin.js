import { collectItem, getCollectStatus, likeItem, getLikeStatus } from '@/api/collect'
import AuthUtils from '@/utils/auth'

export default {
	methods: {
		// 切换收藏状态
		async toggleCollect(targetId, targetType, currentStatus = false) {
			if (!AuthUtils.checkLoginStatus()) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				})
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/login/login'
					})
				}, 1500)
				return false
			}

			try {
				const action = currentStatus ? 0 : 1  // 0: 取消收藏, 1: 收藏
				await collectItem({
					targetId: targetId,
					targetType: targetType,
					action: action
				})

				const message = action === 1 ? '收藏成功' : '取消收藏成功'
				uni.showToast({
					title: message,
					icon: 'success'
				})

				return !currentStatus  // 返回新的状态
			} catch (error) {
				console.error('收藏操作失败:', error)
				uni.showToast({
					title: error.message || '操作失败',
					icon: 'none'
				})
				return currentStatus  // 保持原状态
			}
		},

		// 切换点赞状态
		async toggleLike(targetId, targetType, currentStatus = false) {
			if (!AuthUtils.checkLoginStatus()) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				})
				setTimeout(() => {
					uni.navigateTo({
						url: '/pages/login/login'
					})
				}, 1500)
				return { isLiked: false, count: 0 }
			}

			try {
				const action = currentStatus ? 0 : 1  // 0: 取消点赞, 1: 点赞
				await likeItem({
					targetId: targetId,
					targetType: targetType,
					action: action
				})

				const message = action === 1 ? '点赞成功' : '取消点赞成功'
				uni.showToast({
					title: message,
					icon: 'success',
					duration: 1000
				})

				// 重新获取点赞状态
				const statusRes = await getLikeStatus({
					targetId: targetId,
					targetType: targetType
				})
				
				if (statusRes.code === 200) {
					return statusRes.data
				} else {
					return { isLiked: !currentStatus, count: 0 }
				}
			} catch (error) {
				console.error('点赞操作失败:', error)
				uni.showToast({
					title: error.message || '操作失败',
					icon: 'none'
				})
				return { isLiked: currentStatus, count: 0 }  // 保持原状态
			}
		},

		// 获取收藏状态
		async getItemCollectStatus(targetId, targetType) {
			try {
				const res = await getCollectStatus({
					targetId: targetId,
					targetType: targetType
				})
				if (res.code === 200) {
					return res.data
				}
			} catch (error) {
				console.error('获取收藏状态失败:', error)
			}
			return { isCollected: false, count: 0 }
		},

		// 获取点赞状态
		async getItemLikeStatus(targetId, targetType) {
			try {
				const res = await getLikeStatus({
					targetId: targetId,
					targetType: targetType
				})
				if (res.code === 200) {
					return res.data
				}
			} catch (error) {
				console.error('获取点赞状态失败:', error)
			}
			return { isLiked: false, count: 0 }
		},

		// 批量获取商品的点赞和收藏状态（用于列表页面）
		async batchLoadGoodsStatus(goodsList) {
			if (!goodsList || goodsList.length === 0) return goodsList

			// 为了优化性能，我们可以考虑批量查询接口，但目前先保持单个查询
			const promises = goodsList.map(async (item) => {
				try {
					// 并行获取点赞和收藏状态
					const [likeStatus, collectStatus] = await Promise.all([
						this.getItemLikeStatus(item.id, 'goods'),
						this.getItemCollectStatus(item.id, 'goods')
					])

					return {
						...item,
						isLiked: likeStatus.isLiked,
						likes: likeStatus.count,
						isCollected: collectStatus.isCollected,
						collectCount: collectStatus.count
					}
				} catch (error) {
					console.error(`获取商品 ${item.id} 状态失败:`, error)
					return {
						...item,
						isLiked: false,
						likes: item.likes || Math.floor(Math.random() * 100) + 20,
						isCollected: false,
						collectCount: 0
					}
				}
			})

			return Promise.all(promises)
		},

		// 批量获取状态的优化版本（可选，需要后端支持）
		async batchGetStatus(targetIds, targetType) {
			// 这里可以实现批量查询接口，减少网络请求
			// 暂时保留单个查询的实现
			return null
		}
	}
} 