# 支付功能测试指南

## 测试目标
验证统一支付页面重构后，所有支付场景都能正常工作。

## 测试场景

### 1. 美容预约支付测试
**测试路径**: 
1. 进入美容首页 → 选择服务 → 选择技师 → 选择时间 → 确认预约
2. 在预约详情页面点击"去支付"按钮
3. 验证跳转到统一支付页面 `/pages/common/payment?type=booking&bookingId=xxx`

**预期结果**:
- 页面标题显示"预约支付"
- 正确显示预约信息（服务项目、技师、预约时间）
- 支付方式选择正常
- 支付流程正常

### 2. 套餐卡支付测试
**测试路径**:
1. 进入套餐卡页面 → 选择套餐卡 → 点击购买
2. 在确认页面选择数量后点击"立即购买"
3. 验证跳转到统一支付页面 `/pages/common/payment?type=card&cardId=xxx`

**预期结果**:
- 页面标题显示"套餐卡支付"
- 正确显示套餐卡信息（名称、数量）
- 支付方式选择正常
- 支付流程正常

### 3. 商品订单支付测试
**测试路径**:
1. 商品列表 → 商品详情 → 立即购买 → 订单确认 → 提交订单
2. 验证跳转到统一支付页面 `/pages/common/payment?type=order&orderId=xxx`

**测试路径2**:
1. 我的订单 → 选择未支付订单 → 点击"立即支付"
2. 验证跳转到统一支付页面

**预期结果**:
- 页面标题显示"订单支付"
- 正确显示订单信息（商品名称、数量）
- 支付方式选择正常
- 支付流程正常

## 支付方式测试

### 微信支付测试
1. 选择微信支付
2. 点击"立即支付"按钮
3. 验证调用微信支付接口
4. 验证支付成功后的跳转

### 支付宝支付测试
1. 选择支付宝支付
2. 点击"立即支付"按钮
3. 验证调用支付宝支付接口
4. 验证支付成功后的跳转

### 余额支付测试
1. 选择余额支付
2. 验证余额不足时的提示
3. 余额充足时验证支付流程
4. 验证支付成功后的跳转

## 错误处理测试

### 参数错误测试
1. 直接访问 `/pages/common/payment` (无参数)
2. 访问 `/pages/common/payment?type=invalid` (无效类型)
3. 访问 `/pages/common/payment?type=booking` (缺少ID)

**预期结果**: 显示相应的错误提示

### 网络错误测试
1. 断网情况下进行支付
2. 服务器返回错误时的处理

**预期结果**: 显示友好的错误提示

## 页面跳转测试

### 支付成功跳转
- 美容预约支付成功 → `/pages/beauty/user/booking-history`
- 套餐卡支付成功 → `/pages/beauty/card/my`
- 商品订单支付成功 → `/pages/order/list`

### 返回按钮测试
验证各个页面的返回按钮功能正常

## 兼容性测试

### 不同支付金额测试
- 小额支付 (0.01元)
- 大额支付 (999.99元)
- 整数金额 (100元)
- 小数金额 (99.99元)

### 不同设备测试
- 微信小程序
- H5页面
- App端

## 性能测试

### 页面加载速度
- 统一支付页面首次加载时间
- 切换支付方式的响应时间
- 支付接口调用时间

### 内存使用
- 页面内存占用情况
- 是否存在内存泄漏

## 回归测试

### 原有功能验证
确保重构后原有的支付功能没有受到影响：
- 支付接口调用正常
- 支付回调处理正常
- 订单状态更新正常
- 用户余额更新正常

## 测试工具

### 调试工具
- 浏览器开发者工具
- 微信开发者工具
- 网络抓包工具

### 测试数据
- 测试用户账号
- 测试商品数据
- 测试套餐卡数据
- 测试技师和服务数据

## 测试报告模板

```
测试场景: [场景名称]
测试时间: [YYYY-MM-DD HH:mm:ss]
测试环境: [微信小程序/H5/App]
测试结果: [通过/失败]
问题描述: [如果失败，描述具体问题]
修复建议: [如果有问题，提供修复建议]
```

## 注意事项

1. **测试环境**: 确保在测试环境进行测试，避免产生真实订单
2. **数据备份**: 测试前备份重要数据
3. **权限检查**: 确保测试账号有足够的权限
4. **日志记录**: 测试过程中注意查看控制台日志
5. **异常处理**: 重点测试各种异常情况的处理

## 测试完成标准

- [ ] 所有支付场景测试通过
- [ ] 所有支付方式测试通过
- [ ] 错误处理测试通过
- [ ] 页面跳转测试通过
- [ ] 兼容性测试通过
- [ ] 性能测试达标
- [ ] 回归测试通过
- [ ] 无严重Bug
- [ ] 用户体验良好
