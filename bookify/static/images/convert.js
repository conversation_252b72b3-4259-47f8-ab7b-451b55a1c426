const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

const svgFiles = ['wechat-pay.svg', 'alipay.svg', 'balance.svg'];

async function convertSvgToPng() {
    for (const svgFile of svgFiles) {
        const inputPath = path.join(__dirname, svgFile);
        const outputPath = path.join(__dirname, svgFile.replace('.svg', '.png'));
        
        try {
            await sharp(inputPath)
                .resize(128, 128)
                .png()
                .toFile(outputPath);
            console.log(`Converted ${svgFile} to PNG`);
        } catch (error) {
            console.error(`Error converting ${svgFile}:`, error);
        }
    }
}

convertSvgToPng(); 