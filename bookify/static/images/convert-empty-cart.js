const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

async function convertEmptyCartSvg() {
    try {
        await sharp('empty-cart.svg')
            .resize(400, 400) // 增大尺寸以获得更好的清晰度
            .png()
            .toFile('empty-cart.png');
        console.log('Successfully converted empty-cart.svg to PNG');
    } catch (error) {
        console.error('Error converting empty-cart.svg:', error);
    }
}

convertEmptyCartSvg(); 