const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

async function convertEmptyOrderSvg() {
    try {
        await sharp('empty-order.svg')
            .resize(400, 400) // 增大尺寸以获得更好的清晰度
            .png()
            .toFile('../empty-order.png');
        console.log('Successfully converted empty-order.svg to PNG');
    } catch (error) {
        console.error('Error converting empty-order.svg:', error);
    }
}

convertEmptyOrderSvg(); 