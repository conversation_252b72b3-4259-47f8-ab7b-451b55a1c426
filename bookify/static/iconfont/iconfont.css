@font-face {
  font-family: "iconfont"; /* Project id 4815948 */
  src: url('iconfont.woff2?t=1739546600530') format('woff2'),
       url('iconfont.woff?t=1739546600530') format('woff'),
       url('iconfont.ttf?t=1739546600530') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-wallet:before {
  content: "\e6b5";
}

.icon-box:before {
  content: "\e7f5";
}

.icon-check-circle:before {
  content: "\e824";
}

.icon-close-circle:before {
  content: "\e66e";
}

.icon-truck:before {
  content: "\e6b6";
}

.icon-close-circle-copy:before {
  content: "\e825";
}

.icon-back-light:before {
  content: "\e7e0";
}

.icon-password:before {
  content: "\e702";
}

.icon-eye:before {
  content: "\e694";
}

.icon-eye-close:before {
  content: "\e64b";
}

.icon-setting:before {
  content: "\e63f";
}

.icon-cart-active:before {
  content: "\e6b0";
}

.icon-user-active:before {
  content: "\e6b1";
}

.icon-sousuo-active:before {
  content: "\e6b2";
}

.icon-shangcheng-active:before {
  content: "\e6b3";
}

.icon-home-active-copy:before {
  content: "\e6b4";
}

.icon-cart:before {
  content: "\e6af";
}

.icon-user:before {
  content: "\e632";
}

.icon-sousuo:before {
  content: "\e610";
}

.icon-shangcheng:before {
  content: "\e644";
}

.icon-home:before {
  content: "\e677";
}

