const axios = require('axios');

// 配置
const BASE_URL = 'http://127.0.0.1:8081';
const USERNAME = '15001764308';
const PASSWORD = '123456'; // 请替换为真实密码
const TECHNICIAN_NAME = '李美娜';
const CONTACT_PHONE = '15001764308'; // 可替换

// 工具函数
function logStep(title, data) {
  console.log(`\n=== ${title} ===`);
  if (data) console.dir(data, { depth: 5 });
}

(async () => {
  try {
    // 1. 登录
    let resp = await axios.post(`${BASE_URL}/api/user/login`, {
      username: USERNA<PERSON>,
      password: PASSWORD
    });
    logStep('登录', resp.data);
    const token = resp.data.data.token;

    // 2. 查询技师列表，找到李美娜
    resp = await axios.get(`${BASE_URL}/api/beauty/technician/list?page=1&page_size=10`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    logStep('技师列表', resp.data);
    const technician = resp.data.data.list.find(t => t.name === TECHNICIAN_NAME);
    if (!technician) throw new Error('未找到技师：' + TECHNICIAN_NAME);

    // 3. 查询服务列表，任选一个服务
    resp = await axios.get(`${BASE_URL}/api/beauty/service/list?page=1&page_size=10`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    logStep('服务列表', resp.data);
    const service = resp.data.data.list[0];
    if (!service) throw new Error('未找到服务');

    // 4. 查询技师可用时间（取明天）
    const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000);
    const dateStr = tomorrow.toISOString().slice(0, 10);
    resp = await axios.get(`${BASE_URL}/api/beauty/technician/${technician.id}/available-time?date=${dateStr}&service_id=${service.id}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    logStep('技师可用时间', resp.data);
    const slot = resp.data.data.time_slots ? resp.data.data.time_slots.find(s => s.available) : null;
    if (!slot) throw new Error('未找到可用时间段');

    // 5. 创建预约
    resp = await axios.post(`${BASE_URL}/api/beauty/booking`, {
      service_id: service.id,
      technician_id: technician.id,
      booking_date: dateStr,
      start_time: slot.start_time, // 修正为下划线
      contact_name: USERNAME,
      contact_phone: CONTACT_PHONE,
      customer_gender: 1,
      customer_age: 28,
      skin_type: "",
      skin_concerns: [],
      allergies: "",
      special_requests: "",
      coupon_id: 0
    }, {
      headers: { Authorization: `Bearer ${token}` }
    });
    logStep('预约结果', resp.data);

    if (resp.data.code === 200) {
      console.log('\n✅ 预约成功！预约信息：');
      console.dir(resp.data.data, { depth: 5 });
    } else {
      console.error('\n❌ 预约失败：', resp.data.message);
    }
  } catch (err) {
    console.error('\n❌ 测试流程出错：', err.message);
    if (err.response) {
      console.dir(err.response.data, { depth: 5 });
    }
  }
})(); 