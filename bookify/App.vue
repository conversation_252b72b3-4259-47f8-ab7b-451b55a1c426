<script>
import request from './api/request'
import RouterGuard from '@/utils/routerGuard.js'

function setupRouterGuard() {
	// 保存原始的导航方法
	const originalNavigateTo = uni.navigateTo
	const originalRedirectTo = uni.redirectTo
	const originalReLaunch = uni.reLaunch
	const originalSwitchTab = uni.switchTab

	// 重写navigateTo方法
	uni.navigateTo = async function(options) {
		const canNavigate = await RouterGuard.beforeRoute(options.url, options)
		if (canNavigate) {
			originalNavigateTo.call(this, options)
		}
	}

	// 重写redirectTo方法
	uni.redirectTo = async function(options) {
		const canNavigate = await RouterGuard.beforeRoute(options.url, options)
		if (canNavigate) {
			originalRedirectTo.call(this, options)
		}
	}

	// 重写reLaunch方法
	uni.reLaunch = async function(options) {
		const canNavigate = await RouterGuard.beforeRoute(options.url, options)
		if (canNavigate) {
			originalReLaunch.call(this, options)
		}
	}

	// 重写switchTab方法
	uni.switchTab = async function(options) {
		const canNavigate = await RouterGuard.beforeRoute(options.url, options)
		if (canNavigate) {
			originalSwitchTab.call(this, options)
		}
	}

	console.log('路由守卫设置完成')
}

export default {
	onLaunch() {
		console.log('App Launch')
		// 挂载到全局
		this.$http = request
		// 挂载路由守卫到全局
		this.$routerGuard = RouterGuard
		// 安全地初始化主题
		try {
			const savedTheme = uni.getStorageSync('app_theme') || 'default'
			uni.setStorageSync('app_theme', savedTheme)
		} catch (error) {
			console.warn('存储操作失败:', error)
			// 如果存储失败，使用默认主题
		}
		// 重写uni的导航方法，添加路由守卫
		setupRouterGuard()
	},
	onShow() {
		console.log('App Show')
	},
	onHide() {
		console.log('App Hide')
	}
}
</script>

<style lang="scss">
	// 引入全局通用样式
	@import "@/styles/common.scss";

	// 全局基础样式重置
	view, text, button, input, textarea, image, navigator {
		box-sizing: border-box;
	}

	page {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
		line-height: 1.6;
		color: #333;
		background-color: #f5f5f5;
	}

	// 全局按钮样式重置
	button {
		border: none;
		outline: none;
		background: none;
		padding: 0;
		margin: 0;
		font-size: inherit;
		font-family: inherit;
		line-height: inherit;
		
		&::after {
			border: none;
		}
	}

	// 全局输入框样式
	input, textarea {
		font-family: inherit;
		font-size: inherit;
		line-height: inherit;
		color: inherit;
		background: transparent;
		border: none;
		outline: none;
		
		&::placeholder {
			color: #999;
		}
	}

	// 全局图片样式
	image {
		display: block;
		max-width: 100%;
		height: auto;
	}

	// 全局链接样式
	navigator {
		text-decoration: none;
		color: inherit;
	}

	// 滚动条样式（仅在支持的平台生效）
	::-webkit-scrollbar {
		width: 6rpx;
		height: 6rpx;
	}

	::-webkit-scrollbar-track {
		background: rgba(0, 0, 0, 0.1);
		border-radius: 3rpx;
	}

	::-webkit-scrollbar-thumb {
		background: rgba(0, 0, 0, 0.3);
		border-radius: 3rpx;
		
		&:hover {
			background: rgba(0, 0, 0, 0.5);
		}
	}

	// 全局动画
	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(20rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	@keyframes slideInUp {
		from {
			opacity: 0;
			transform: translateY(100%);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	@keyframes slideInDown {
		from {
			opacity: 0;
			transform: translateY(-100%);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	@keyframes slideInLeft {
		from {
			opacity: 0;
			transform: translateX(-100%);
		}
		to {
			opacity: 1;
			transform: translateX(0);
		}
	}

	@keyframes slideInRight {
		from {
			opacity: 0;
			transform: translateX(100%);
		}
		to {
			opacity: 1;
			transform: translateX(0);
		}
	}

	@keyframes pulse {
		0%, 100% {
			transform: scale(1);
		}
		50% {
			transform: scale(1.05);
		}
	}

	@keyframes bounce {
		0%, 20%, 53%, 80%, 100% {
			transform: translate3d(0, 0, 0);
		}
		40%, 43% {
			transform: translate3d(0, -30rpx, 0);
		}
		70% {
			transform: translate3d(0, -15rpx, 0);
		}
		90% {
			transform: translate3d(0, -4rpx, 0);
		}
	}

	// 动画类
	.fade-in {
		animation: fadeIn 0.3s ease-out;
	}

	.slide-in-up {
		animation: slideInUp 0.3s ease-out;
	}

	.slide-in-down {
		animation: slideInDown 0.3s ease-out;
	}

	.slide-in-left {
		animation: slideInLeft 0.3s ease-out;
	}

	.slide-in-right {
		animation: slideInRight 0.3s ease-out;
	}

	.pulse {
		animation: pulse 2s infinite;
	}

	.bounce {
		animation: bounce 1s;
	}

	// 全局工具类
	.clearfix::after {
		content: '';
		display: table;
		clear: both;
	}

	.sr-only {
		position: absolute;
		width: 1rpx;
		height: 1rpx;
		padding: 0;
		margin: -1rpx;
		overflow: hidden;
		clip: rect(0, 0, 0, 0);
		white-space: nowrap;
		border: 0;
	}

	// 全局状态类
	.loading {
		pointer-events: none;
		opacity: 0.6;
	}

	.disabled {
		pointer-events: none;
		opacity: 0.5;
		cursor: not-allowed;
	}

	// 全局响应式断点
	@media (max-width: 750rpx) {
		.container {
			padding: 0 20rpx;
		}
	}

	@media (min-width: 751rpx) and (max-width: 1200rpx) {
		.container {
			padding: 0 40rpx;
		}
	}

	@media (min-width: 1201rpx) {
		.container {
			max-width: 1200rpx;
			margin: 0 auto;
			padding: 0 60rpx;
		}
	}
</style>
