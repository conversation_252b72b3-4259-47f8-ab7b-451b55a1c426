-- 美容预约系统测试数据
-- 数据库: wnsys
-- 创建时间: 2025-01-20

USE wnsys;

-- 获取分类ID
SET @face_category_id = (SELECT id FROM category WHERE module = 'beauty' AND name = '面部护理');
SET @body_category_id = (SELECT id FROM category WHERE module = 'beauty' AND name = '身体护理');
SET @nail_category_id = (SELECT id FROM category WHERE module = 'beauty' AND name = '美甲美睫');
SET @hair_category_id = (SELECT id FROM category WHERE module = 'beauty' AND name = '美发造型');

-- 1. 插入美容服务数据
INSERT INTO beauty_service (
    category_id, name, subtitle, description, images, price, original_price, duration,
    need_technician, max_advance_days, min_advance_hours, allow_cancel_hours,
    gender_limit, age_min, age_max, tags, contraindications, preparation_notes,
    booking_count, rating_avg, rating_count, status, sort, is_hot, is_new
) VALUES
-- 面部护理服务
(@face_category_id, '深层清洁面部护理', '深度清洁毛孔，改善肌肤质感', '采用专业清洁产品，深层清洁面部毛孔，去除黑头白头，改善肌肤质感，让肌肤重现光彩。', 
 JSON_ARRAY('https://example.com/face1.jpg', 'https://example.com/face2.jpg'), 299.00, 399.00, 90, 
 1, 30, 2, 24, 0, 16, 65, 
 JSON_ARRAY('深层清洁', '毛孔护理', '去黑头', '改善肌理'), 
 '孕妇、哺乳期妇女、皮肤破损者不宜', 
 '请提前卸妆，避免使用刺激性护肤品', 
 156, 4.8, 89, 1, 1, 1, 0),

(@face_category_id, '水光针面部护理', '补水保湿，提亮肌肤', '采用玻尿酸精华，深层补水保湿，改善肌肤干燥问题，让肌肤水润有光泽。', 
 JSON_ARRAY('https://example.com/hydra1.jpg', 'https://example.com/hydra2.jpg'), 399.00, 499.00, 120, 
 1, 30, 2, 24, 0, 18, 60, 
 JSON_ARRAY('补水保湿', '水光针', '提亮肌肤', '玻尿酸'), 
 '孕妇、哺乳期妇女、过敏体质者慎用', 
 '请提前告知过敏史，避免化妆', 
 234, 4.9, 156, 1, 2, 1, 1),

(@face_category_id, '抗衰老面部护理', '紧致提拉，延缓衰老', '采用高端抗衰老产品，配合专业手法，紧致提拉面部肌肤，淡化细纹，延缓衰老。', 
 JSON_ARRAY('https://example.com/anti-aging1.jpg', 'https://example.com/anti-aging2.jpg'), 599.00, 799.00, 150, 
 1, 30, 2, 24, 0, 25, 70, 
 JSON_ARRAY('抗衰老', '紧致提拉', '淡化细纹', '高端护理'), 
 '孕妇、哺乳期妇女、皮肤敏感者慎用', 
 '请提前进行皮肤测试，避免日晒', 
 89, 4.7, 67, 1, 3, 0, 0),

-- 身体护理服务
(@body_category_id, '全身精油按摩', '舒缓疲劳，放松身心', '采用天然精油，全身深度按摩，舒缓肌肉疲劳，促进血液循环，让身心得到完全放松。', 
 JSON_ARRAY('https://example.com/massage1.jpg', 'https://example.com/massage2.jpg'), 399.00, 499.00, 90, 
 1, 30, 2, 24, 0, 16, 70, 
 JSON_ARRAY('精油按摩', '舒缓疲劳', '放松身心', '促进循环'), 
 '孕妇、心脏病患者、皮肤破损者不宜', 
 '请穿着宽松衣物，避免饱餐后进行', 
 345, 4.8, 234, 1, 4, 1, 0),

(@body_category_id, '淋巴排毒护理', '排毒养颜，改善体质', '专业淋巴排毒手法，促进淋巴循环，排出体内毒素，改善体质，提升免疫力。', 
 JSON_ARRAY('https://example.com/lymph1.jpg', 'https://example.com/lymph2.jpg'), 499.00, 599.00, 120, 
 1, 30, 2, 24, 2, 18, 65, 
 JSON_ARRAY('淋巴排毒', '排毒养颜', '改善体质', '提升免疫'), 
 '孕妇、哺乳期妇女、严重疾病患者不宜', 
 '请多喝水，避免剧烈运动', 
 123, 4.6, 89, 1, 5, 0, 1),

-- 美甲美睫服务
(@nail_category_id, '日式美甲', '精致美甲，持久亮丽', '采用日式美甲技法，精致造型设计，持久亮丽，让双手更加优雅迷人。', 
 JSON_ARRAY('https://example.com/nail1.jpg', 'https://example.com/nail2.jpg'), 199.00, 299.00, 90, 
 1, 15, 1, 12, 2, 16, 65, 
 JSON_ARRAY('日式美甲', '精致造型', '持久亮丽', '优雅迷人'), 
 '甲周炎、灰指甲患者不宜', 
 '请修剪指甲，避免涂抹护手霜', 
 567, 4.9, 389, 1, 6, 1, 0),

(@nail_category_id, '睫毛嫁接', '自然浓密，魅力双眸', '采用优质睫毛材料，专业嫁接技术，打造自然浓密睫毛，让双眸更加迷人。', 
 JSON_ARRAY('https://example.com/lash1.jpg', 'https://example.com/lash2.jpg'), 299.00, 399.00, 120, 
 1, 15, 1, 12, 2, 16, 60, 
 JSON_ARRAY('睫毛嫁接', '自然浓密', '魅力双眸', '专业技术'), 
 '眼部疾病、过敏体质者慎用', 
 '请卸除眼妆，避免揉眼', 
 234, 4.7, 167, 1, 7, 0, 1),

-- 美发造型服务
(@hair_category_id, '洗剪吹造型', '专业造型，时尚发型', '专业发型师设计，根据脸型和气质打造最适合的时尚发型，让你焕然一新。', 
 JSON_ARRAY('https://example.com/hair1.jpg', 'https://example.com/hair2.jpg'), 299.00, 399.00, 120, 
 1, 15, 1, 12, 0, 16, 70, 
 JSON_ARRAY('专业造型', '时尚发型', '个性设计', '焕然一新'), 
 '头皮破损、严重脱发者慎用', 
 '请清洗头发，避免使用造型产品', 
 445, 4.8, 298, 1, 8, 1, 0),

(@hair_category_id, '头皮护理', '深层护理，健康头皮', '专业头皮检测，深层清洁护理，改善头皮环境，促进头发健康生长。', 
 JSON_ARRAY('https://example.com/scalp1.jpg', 'https://example.com/scalp2.jpg'), 399.00, 499.00, 90, 
 1, 15, 1, 12, 0, 16, 70, 
 JSON_ARRAY('头皮护理', '深层清洁', '健康头皮', '促进生长'), 
 '头皮严重疾病患者不宜', 
 '请避免使用刺激性洗发产品', 
 167, 4.6, 123, 1, 9, 0, 1);

-- 2. 插入技师数据
INSERT INTO beauty_technician (
    name, avatar, gender, age, phone, level, experience, specialties, certificates, introduction,
    service_ids, work_hours, total_bookings, completed_bookings, rating_avg, rating_count,
    extra_fee, status, is_featured, sort
) VALUES
('李美娜', 'https://example.com/avatar1.jpg', 2, 28, '13800138001', '高级技师', 5, 
 JSON_ARRAY('面部护理', '深层清洁', '抗衰老护理'), 
 JSON_ARRAY('国际美容师资格证', '高级面部护理师证'), 
 '拥有5年丰富经验，擅长各种面部护理项目，深受客户喜爱。手法专业，服务贴心。',
 JSON_ARRAY(1, 2, 3), 
 JSON_OBJECT('monday', JSON_OBJECT('start', '09:00', 'end', '18:00'), 'tuesday', JSON_OBJECT('start', '09:00', 'end', '18:00'), 'wednesday', JSON_OBJECT('start', '09:00', 'end', '18:00'), 'thursday', JSON_OBJECT('start', '09:00', 'end', '18:00'), 'friday', JSON_OBJECT('start', '09:00', 'end', '18:00'), 'saturday', JSON_OBJECT('start', '10:00', 'end', '17:00'), 'sunday', 'off'),
 234, 228, 4.9, 156, 50.00, 1, 1, 1),

('王雅琪', 'https://example.com/avatar2.jpg', 2, 32, '13800138002', '资深技师', 8, 
 JSON_ARRAY('身体护理', '精油按摩', '淋巴排毒'), 
 JSON_ARRAY('国际芳疗师证书', '淋巴排毒师证'), 
 '8年身体护理经验，精通各种按摩手法，特别擅长淋巴排毒和精油按摩。',
 JSON_ARRAY(4, 5), 
 JSON_OBJECT('monday', JSON_OBJECT('start', '10:00', 'end', '19:00'), 'tuesday', JSON_OBJECT('start', '10:00', 'end', '19:00'), 'wednesday', JSON_OBJECT('start', '10:00', 'end', '19:00'), 'thursday', JSON_OBJECT('start', '10:00', 'end', '19:00'), 'friday', JSON_OBJECT('start', '10:00', 'end', '19:00'), 'saturday', JSON_OBJECT('start', '09:00', 'end', '18:00'), 'sunday', 'off'),
 345, 338, 4.8, 234, 80.00, 1, 1, 2),

('张小美', 'https://example.com/avatar3.jpg', 2, 25, '13800138003', '中级技师', 3, 
 JSON_ARRAY('美甲美睫', '日式美甲', '睫毛嫁接'), 
 JSON_ARRAY('美甲师资格证', '睫毛嫁接技师证'), 
 '专业美甲美睫技师，手法精细，造型时尚，深受年轻客户喜爱。',
 JSON_ARRAY(6, 7), 
 JSON_OBJECT('monday', JSON_OBJECT('start', '09:30', 'end', '18:30'), 'tuesday', JSON_OBJECT('start', '09:30', 'end', '18:30'), 'wednesday', JSON_OBJECT('start', '09:30', 'end', '18:30'), 'thursday', JSON_OBJECT('start', '09:30', 'end', '18:30'), 'friday', JSON_OBJECT('start', '09:30', 'end', '18:30'), 'saturday', JSON_OBJECT('start', '09:00', 'end', '19:00'), 'sunday', JSON_OBJECT('start', '10:00', 'end', '17:00')),
 456, 445, 4.9, 298, 30.00, 1, 0, 3),

('陈发型', 'https://example.com/avatar4.jpg', 1, 35, '13800138004', '首席技师', 12, 
 JSON_ARRAY('美发造型', '头皮护理', '时尚设计'), 
 JSON_ARRAY('高级美发师证', '造型设计师证'), 
 '12年美发经验，首席设计师，擅长各种时尚造型设计和头皮护理。',
 JSON_ARRAY(8, 9), 
 JSON_OBJECT('monday', JSON_OBJECT('start', '10:00', 'end', '20:00'), 'tuesday', JSON_OBJECT('start', '10:00', 'end', '20:00'), 'wednesday', 'off', 'thursday', JSON_OBJECT('start', '10:00', 'end', '20:00'), 'friday', JSON_OBJECT('start', '10:00', 'end', '20:00'), 'saturday', JSON_OBJECT('start', '09:00', 'end', '19:00'), 'sunday', JSON_OBJECT('start', '10:00', 'end', '18:00')),
 567, 556, 4.7, 389, 100.00, 1, 1, 4),

('刘护肤', 'https://example.com/avatar5.jpg', 2, 30, '13800138005', '高级技师', 6, 
 JSON_ARRAY('面部护理', '水光针', '抗衰老'), 
 JSON_ARRAY('高级美容师证', '皮肤管理师证'), 
 '专业皮肤管理师，擅长各种面部护理项目，特别是水光针和抗衰老护理。',
 JSON_ARRAY(1, 2, 3), 
 JSON_OBJECT('monday', JSON_OBJECT('start', '09:00', 'end', '17:00'), 'tuesday', JSON_OBJECT('start', '09:00', 'end', '17:00'), 'wednesday', JSON_OBJECT('start', '09:00', 'end', '17:00'), 'thursday', JSON_OBJECT('start', '09:00', 'end', '17:00'), 'friday', JSON_OBJECT('start', '09:00', 'end', '17:00'), 'saturday', 'off', 'sunday', 'off'),
 298, 289, 4.8, 198, 60.00, 1, 0, 5);

-- 3. 插入技师排班数据（未来7天）
INSERT INTO beauty_schedule (technician_id, work_date, start_time, end_time, break_start, break_end, schedule_type, status) VALUES
-- 李美娜 (技师ID: 1)
(1, CURDATE(), '09:00:00', '18:00:00', '12:00:00', '13:00:00', 'normal', 1),
(1, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '09:00:00', '18:00:00', '12:00:00', '13:00:00', 'normal', 1),
(1, DATE_ADD(CURDATE(), INTERVAL 2 DAY), '09:00:00', '18:00:00', '12:00:00', '13:00:00', 'normal', 1),
(1, DATE_ADD(CURDATE(), INTERVAL 3 DAY), '09:00:00', '18:00:00', '12:00:00', '13:00:00', 'normal', 1),
(1, DATE_ADD(CURDATE(), INTERVAL 4 DAY), '09:00:00', '18:00:00', '12:00:00', '13:00:00', 'normal', 1),
(1, DATE_ADD(CURDATE(), INTERVAL 5 DAY), '10:00:00', '17:00:00', '12:30:00', '13:30:00', 'normal', 1),

-- 王雅琪 (技师ID: 2)
(2, CURDATE(), '10:00:00', '19:00:00', '12:30:00', '13:30:00', 'normal', 1),
(2, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '10:00:00', '19:00:00', '12:30:00', '13:30:00', 'normal', 1),
(2, DATE_ADD(CURDATE(), INTERVAL 2 DAY), '10:00:00', '19:00:00', '12:30:00', '13:30:00', 'normal', 1),
(2, DATE_ADD(CURDATE(), INTERVAL 3 DAY), '10:00:00', '19:00:00', '12:30:00', '13:30:00', 'normal', 1),
(2, DATE_ADD(CURDATE(), INTERVAL 4 DAY), '10:00:00', '19:00:00', '12:30:00', '13:30:00', 'normal', 1),
(2, DATE_ADD(CURDATE(), INTERVAL 5 DAY), '09:00:00', '18:00:00', '12:00:00', '13:00:00', 'normal', 1),

-- 张小美 (技师ID: 3)
(3, CURDATE(), '09:30:00', '18:30:00', '12:00:00', '13:00:00', 'normal', 1),
(3, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '09:30:00', '18:30:00', '12:00:00', '13:00:00', 'normal', 1),
(3, DATE_ADD(CURDATE(), INTERVAL 2 DAY), '09:30:00', '18:30:00', '12:00:00', '13:00:00', 'normal', 1),
(3, DATE_ADD(CURDATE(), INTERVAL 3 DAY), '09:30:00', '18:30:00', '12:00:00', '13:00:00', 'normal', 1),
(3, DATE_ADD(CURDATE(), INTERVAL 4 DAY), '09:30:00', '18:30:00', '12:00:00', '13:00:00', 'normal', 1),
(3, DATE_ADD(CURDATE(), INTERVAL 5 DAY), '09:00:00', '19:00:00', '12:30:00', '13:30:00', 'normal', 1),
(3, DATE_ADD(CURDATE(), INTERVAL 6 DAY), '10:00:00', '17:00:00', '12:00:00', '13:00:00', 'normal', 1),

-- 陈发型 (技师ID: 4)
(4, CURDATE(), '10:00:00', '20:00:00', '13:00:00', '14:00:00', 'normal', 1),
(4, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '10:00:00', '20:00:00', '13:00:00', '14:00:00', 'normal', 1),
(4, DATE_ADD(CURDATE(), INTERVAL 3 DAY), '10:00:00', '20:00:00', '13:00:00', '14:00:00', 'normal', 1),
(4, DATE_ADD(CURDATE(), INTERVAL 4 DAY), '10:00:00', '20:00:00', '13:00:00', '14:00:00', 'normal', 1),
(4, DATE_ADD(CURDATE(), INTERVAL 5 DAY), '09:00:00', '19:00:00', '12:30:00', '13:30:00', 'normal', 1),
(4, DATE_ADD(CURDATE(), INTERVAL 6 DAY), '10:00:00', '18:00:00', '12:00:00', '13:00:00', 'normal', 1),

-- 刘护肤 (技师ID: 5)
(5, CURDATE(), '09:00:00', '17:00:00', '12:00:00', '13:00:00', 'normal', 1),
(5, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '09:00:00', '17:00:00', '12:00:00', '13:00:00', 'normal', 1),
(5, DATE_ADD(CURDATE(), INTERVAL 2 DAY), '09:00:00', '17:00:00', '12:00:00', '13:00:00', 'normal', 1),
(5, DATE_ADD(CURDATE(), INTERVAL 3 DAY), '09:00:00', '17:00:00', '12:00:00', '13:00:00', 'normal', 1),
(5, DATE_ADD(CURDATE(), INTERVAL 4 DAY), '09:00:00', '17:00:00', '12:00:00', '13:00:00', 'normal', 1);

-- 4. 插入优惠券数据
INSERT INTO beauty_coupon (
    name, description, type, value, min_amount, max_discount, applicable_services, applicable_technicians,
    total_quantity, used_quantity, user_limit, start_time, end_time, validity_days, status
) VALUES
('新用户专享券', '新用户首次消费立减50元', 'amount', 50.00, 200.00, NULL, NULL, NULL, 
 1000, 45, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59', 30, 1),

('满300减80', '单笔消费满300元可使用', 'amount', 80.00, 300.00, NULL, NULL, NULL, 
 500, 123, 2, '2025-01-15 00:00:00', '2025-03-31 23:59:59', 60, 1),

('面部护理8折券', '面部护理项目享8折优惠', 'percent', 0.20, 100.00, 100.00, JSON_ARRAY(1, 2, 3), NULL, 
 200, 67, 1, '2025-01-01 00:00:00', '2025-02-28 23:59:59', 45, 1),

('明星技师专享券', '指定明星技师服务减免技师费', 'amount', 100.00, 500.00, NULL, NULL, JSON_ARRAY(1, 2, 4), 
 100, 23, 1, '2025-01-20 00:00:00', '2025-04-30 23:59:59', 90, 1),

('身体护理9折券', '身体护理项目享9折优惠', 'percent', 0.10, 200.00, 80.00, JSON_ARRAY(4, 5), NULL, 
 300, 89, 1, '2025-01-10 00:00:00', '2025-03-15 23:59:59', 60, 1);

-- 5. 插入测试用户数据（如果user表不存在相关数据）
INSERT IGNORE INTO user (username, phone, nickname, avatar, gender, birthday, balance, points, level, create_time) VALUES
('testuser1', '13800138888', '小美', 'https://example.com/user1.jpg', 2, '1995-06-15', 1000.00, 500, 1, NOW()),
('testuser2', '13800138889', '小丽', 'https://example.com/user2.jpg', 2, '1992-03-22', 800.00, 300, 2, NOW()),
('testuser3', '13800138890', '小王', 'https://example.com/user3.jpg', 1, '1988-11-08', 1200.00, 800, 3, NOW()),
('testuser4', '13800138891', '小张', 'https://example.com/user4.jpg', 2, '1990-09-12', 600.00, 200, 1, NOW()),
('testuser5', '13800138892', '小李', 'https://example.com/user5.jpg', 1, '1985-07-30', 1500.00, 1000, 2, NOW());

-- 6. 插入用户优惠券数据
INSERT INTO beauty_user_coupon (user_id, coupon_id, status, expire_time) VALUES
(1, 1, 1, DATE_ADD(NOW(), INTERVAL 30 DAY)),
(1, 2, 1, DATE_ADD(NOW(), INTERVAL 60 DAY)),
(1, 3, 1, DATE_ADD(NOW(), INTERVAL 45 DAY)),
(2, 1, 1, DATE_ADD(NOW(), INTERVAL 30 DAY)),
(2, 4, 1, DATE_ADD(NOW(), INTERVAL 90 DAY)),
(3, 2, 1, DATE_ADD(NOW(), INTERVAL 60 DAY)),
(3, 5, 1, DATE_ADD(NOW(), INTERVAL 60 DAY)),
(4, 1, 1, DATE_ADD(NOW(), INTERVAL 30 DAY)),
(4, 3, 1, DATE_ADD(NOW(), INTERVAL 45 DAY)),
(5, 2, 1, DATE_ADD(NOW(), INTERVAL 60 DAY)),
(5, 4, 1, DATE_ADD(NOW(), INTERVAL 90 DAY)),
(5, 5, 1, DATE_ADD(NOW(), INTERVAL 60 DAY));

-- 7. 插入预约订单测试数据
INSERT INTO beauty_booking (
    booking_no, user_id, service_id, technician_id, booking_date, start_time, end_time, duration,
    contact_name, contact_phone, customer_gender, customer_age, skin_type, skin_concerns, allergies, special_requests,
    service_price, technician_fee, discount_amount, final_price, coupon_id, coupon_amount,
    booking_status, payment_status, service_status, confirmed_time
) VALUES
('BK202501200001', 1, 1, 1, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '10:00:00', '11:30:00', 90,
 '小美', '13800138888', 2, 28, '混合性', JSON_ARRAY('毛孔粗大', '黑头'), '无', '希望重点清洁T区',
 299.00, 50.00, 50.00, 299.00, 1, 50.00,
 'confirmed', 'paid', 'waiting', NOW()),

('BK202501200002', 2, 4, 2, DATE_ADD(CURDATE(), INTERVAL 2 DAY), '14:00:00', '15:30:00', 90,
 '小丽', '13800138889', 2, 31, NULL, NULL, '花粉过敏', '喜欢薰衣草精油',
 399.00, 80.00, 0.00, 479.00, NULL, 0.00,
 'confirmed', 'paid', 'waiting', NOW()),

('BK202501200003', 3, 6, 3, CURDATE(), '15:00:00', '16:30:00', 90,
 '小王', '13800138890', 1, 35, NULL, NULL, '无', '要求简约风格',
 199.00, 30.00, 0.00, 229.00, NULL, 0.00,
 'confirmed', 'paid', 'waiting', NOW()),

('BK202501200004', 4, 8, 4, DATE_ADD(CURDATE(), INTERVAL 3 DAY), '16:00:00', '18:00:00', 120,
 '小张', '13800138891', 2, 33, NULL, NULL, '无', '想要时尚一点的发型',
 299.00, 100.00, 0.00, 399.00, NULL, 0.00,
 'pending', 'unpaid', 'waiting', NULL),

('BK202501200005', 5, 2, 5, DATE_ADD(CURDATE(), INTERVAL 4 DAY), '11:00:00', '13:00:00', 120,
 '小李', '13800138892', 1, 38, '干性', JSON_ARRAY('细纹', '暗沉'), '无', '希望改善肌肤暗沉问题',
 399.00, 60.00, 80.00, 379.00, 2, 80.00,
 'confirmed', 'paid', 'waiting', NOW());

-- 8. 插入服务评价数据
INSERT INTO beauty_review (
    booking_id, user_id, service_id, technician_id, service_rating, technician_rating, environment_rating, overall_rating,
    review_content, review_images, review_tags, status, is_anonymous
) VALUES
(1, 1, 1, 1, 5, 5, 4, 5, 
 '李技师手法很专业，清洁效果很好，皮肤变得很干净。环境也很舒适，下次还会来的。', 
 JSON_ARRAY('https://example.com/review1.jpg'), 
 JSON_ARRAY('手法专业', '效果好', '环境舒适'), 1, 0),

(2, 2, 4, 2, 4, 5, 5, 5, 
 '王技师的按摩手法很棒，全身都放松了。精油的味道很好闻，整个过程很享受。', 
 JSON_ARRAY('https://example.com/review2.jpg'), 
 JSON_ARRAY('手法棒', '很放松', '精油好闻'), 1, 0),

(3, 3, 6, 3, 5, 4, 4, 4, 
 '美甲做得很精致，颜色搭配也很好看。张技师很细心，但是等待时间稍长。', 
 JSON_ARRAY('https://example.com/review3.jpg'), 
 JSON_ARRAY('精致', '颜色好看', '技师细心'), 1, 0); 