-- 美容预约系统数据库验证脚本
-- 数据库: wnsys
-- 创建时间: 2025-01-20

USE wnsys;

-- 1. 验证表结构
SHOW TABLES LIKE 'beauty_%';

-- 2. 验证分类数据
SELECT '=== 美容分类数据 ===' as info;
SELECT id, name, module, level FROM category WHERE module = 'beauty';

-- 3. 验证服务数据
SELECT '=== 美容服务数据 ===' as info;
SELECT 
    bs.id, 
    bs.name, 
    c.name as category_name,
    bs.price, 
    bs.duration, 
    bs.rating_avg,
    bs.booking_count,
    bs.is_hot,
    bs.is_new
FROM beauty_service bs 
LEFT JOIN category c ON bs.category_id = c.id 
ORDER BY bs.id;

-- 4. 验证技师数据
SELECT '=== 技师数据 ===' as info;
SELECT 
    id, 
    name, 
    level, 
    experience, 
    rating_avg,
    total_bookings,
    is_featured,
    status
FROM beauty_technician 
ORDER BY id;

-- 5. 验证排班数据
SELECT '=== 技师排班数据 ===' as info;
SELECT 
    bs.id,
    bt.name as technician_name,
    bs.work_date,
    bs.start_time,
    bs.end_time,
    bs.schedule_type
FROM beauty_schedule bs
LEFT JOIN beauty_technician bt ON bs.technician_id = bt.id
WHERE bs.work_date >= CURDATE()
ORDER BY bs.work_date, bs.technician_id;

-- 6. 验证预约订单数据
SELECT '=== 预约订单数据 ===' as info;
SELECT 
    bb.id,
    bb.booking_no,
    u.nickname as user_name,
    bs.name as service_name,
    bt.name as technician_name,
    bb.booking_date,
    bb.start_time,
    bb.final_price,
    bb.booking_status,
    bb.payment_status
FROM beauty_booking bb
LEFT JOIN user u ON bb.user_id = u.id
LEFT JOIN beauty_service bs ON bb.service_id = bs.id
LEFT JOIN beauty_technician bt ON bb.technician_id = bt.id
ORDER BY bb.id;

-- 7. 验证优惠券数据
SELECT '=== 优惠券数据 ===' as info;
SELECT 
    id,
    name,
    type,
    value,
    min_amount,
    total_quantity,
    used_quantity,
    start_time,
    end_time,
    status
FROM beauty_coupon
ORDER BY id;

-- 8. 验证用户优惠券数据
SELECT '=== 用户优惠券数据 ===' as info;
SELECT 
    buc.id,
    u.nickname as user_name,
    bc.name as coupon_name,
    buc.status,
    buc.expire_time
FROM beauty_user_coupon buc
LEFT JOIN user u ON buc.user_id = u.id
LEFT JOIN beauty_coupon bc ON buc.coupon_id = bc.id
ORDER BY buc.id;

-- 9. 验证评价数据
SELECT '=== 服务评价数据 ===' as info;
SELECT 
    br.id,
    u.nickname as user_name,
    bs.name as service_name,
    bt.name as technician_name,
    br.overall_rating,
    br.review_content,
    br.create_time
FROM beauty_review br
LEFT JOIN user u ON br.user_id = u.id
LEFT JOIN beauty_service bs ON br.service_id = bs.id
LEFT JOIN beauty_technician bt ON br.technician_id = bt.id
ORDER BY br.id;

-- 10. 数据统计
SELECT '=== 数据统计 ===' as info;
SELECT 
    'beauty_service' as table_name, 
    COUNT(*) as record_count 
FROM beauty_service
UNION ALL
SELECT 
    'beauty_technician', 
    COUNT(*) 
FROM beauty_technician
UNION ALL
SELECT 
    'beauty_schedule', 
    COUNT(*) 
FROM beauty_schedule
UNION ALL
SELECT 
    'beauty_booking', 
    COUNT(*) 
FROM beauty_booking
UNION ALL
SELECT 
    'beauty_review', 
    COUNT(*) 
FROM beauty_review
UNION ALL
SELECT 
    'beauty_coupon', 
    COUNT(*) 
FROM beauty_coupon
UNION ALL
SELECT 
    'beauty_user_coupon', 
    COUNT(*) 
FROM beauty_user_coupon
UNION ALL
SELECT 
    'category(beauty)', 
    COUNT(*) 
FROM category 
WHERE module = 'beauty';

-- 11. 验证JSON字段
SELECT '=== JSON字段验证 ===' as info;
SELECT 
    id,
    name,
    JSON_EXTRACT(tags, '$[0]') as first_tag,
    JSON_EXTRACT(images, '$[0]') as first_image
FROM beauty_service 
WHERE id <= 3;

SELECT 
    id,
    name,
    JSON_EXTRACT(specialties, '$[0]') as first_specialty,
    JSON_EXTRACT(service_ids, '$[0]') as first_service_id
FROM beauty_technician 
WHERE id <= 3; 