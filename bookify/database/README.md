# 美容预约系统数据库说明

## 📋 概述

本文档描述了美容预约系统的数据库设计和实现。数据库包含7个核心业务表和4个分类数据。

## 🗄️ 数据库信息

- **数据库名称**: `wnsys`
- **字符集**: `utf8mb4`
- **排序规则**: `utf8mb4_unicode_ci`
- **创建时间**: 2025-01-20

## 📊 表结构

### 1. 核心业务表

| 表名 | 说明 | 记录数 |
|------|------|--------|
| `beauty_service` | 美容服务表 | 9条 |
| `beauty_technician` | 技师表 | 5条 |
| `beauty_schedule` | 技师排班表 | 30条 |
| `beauty_booking` | 预约订单表 | 5条 |
| `beauty_review` | 服务评价表 | 3条 |
| `beauty_coupon` | 优惠券表 | 5条 |
| `beauty_user_coupon` | 用户优惠券表 | 12条 |

### 2. 复用系统表

| 表名 | 说明 | 相关记录 |
|------|------|----------|
| `category` | 分类表 | 4条美容分类 |
| `user` | 用户表 | 5个测试用户 |

## 🔧 使用方法

### 1. 创建表结构

```bash
mysql -h 127.0.0.1 -P 3306 -u root -p123456 wnsys < database/beauty_tables.sql
```

### 2. 插入测试数据

```bash
mysql -h 127.0.0.1 -P 3306 -u root -p123456 wnsys < database/beauty_test_data.sql
```

### 3. 验证数据

```bash
mysql -h 127.0.0.1 -P 3306 -u root -p123456 wnsys < database/verify_data.sql
```

## 📈 数据概览

### 服务分类
- 面部护理：3个服务
- 身体护理：2个服务
- 美甲美睫：2个服务
- 美发造型：2个服务

### 技师团队
- 李美娜：高级技师，面部护理专家
- 王雅琪：资深技师，身体护理专家
- 张小美：中级技师，美甲美睫专家
- 陈发型：首席技师，美发造型专家
- 刘护肤：高级技师，皮肤管理专家

### 价格范围
- 最低价格：199元（日式美甲）
- 最高价格：599元（抗衰老面部护理）
- 平均价格：约350元

### 服务时长
- 最短时长：90分钟
- 最长时长：150分钟
- 平均时长：约105分钟

## 🎯 特色功能

### 1. JSON字段支持
- 服务标签、图片数组
- 技师专长、证书信息
- 工作时间配置
- 肌肤问题记录

### 2. 灵活的预约规则
- 最大提前预约天数
- 最小提前预约小时数
- 允许取消的提前小时数
- 性别和年龄限制

### 3. 完整的状态管理
- 预约状态：pending, confirmed, completed等
- 支付状态：unpaid, paid, refunded等
- 服务状态：waiting, serving, completed等

### 4. 多样化优惠券
- 满减券：满300减80
- 折扣券：面部护理8折
- 专享券：明星技师专享
- 新用户券：首次消费立减

## 🔍 查询示例

### 查看热门服务
```sql
SELECT name, price, rating_avg, booking_count 
FROM beauty_service 
WHERE is_hot = 1 
ORDER BY rating_avg DESC;
```

### 查看明星技师
```sql
SELECT name, level, rating_avg, total_bookings 
FROM beauty_technician 
WHERE is_featured = 1 
ORDER BY rating_avg DESC;
```

### 查看今日预约
```sql
SELECT bb.booking_no, u.nickname, bs.name, bt.name, bb.start_time
FROM beauty_booking bb
LEFT JOIN user u ON bb.user_id = u.id
LEFT JOIN beauty_service bs ON bb.service_id = bs.id
LEFT JOIN beauty_technician bt ON bb.technician_id = bt.id
WHERE bb.booking_date = CURDATE()
AND bb.booking_status = 'confirmed';
```

## 🛡️ 数据安全

### 索引优化
- 所有外键都建立了索引
- 查询频繁的字段建立了复合索引
- 时间字段建立了范围索引

### 数据完整性
- 外键约束确保数据关联正确
- 唯一键约束防止重复数据
- 默认值设置保证数据完整性

## 📝 后续扩展

### 1. 可能的新增表
- 会员等级表
- 积分记录表
- 营销活动表
- 财务对账表

### 2. 可能的字段扩展
- 服务项目的详细步骤
- 技师的专业等级认证
- 更详细的客户画像
- 设备和产品管理

## 🔄 维护建议

### 1. 定期清理
- 清理过期的优惠券
- 清理历史预约记录
- 清理无效的排班数据

### 2. 性能优化
- 定期分析慢查询
- 优化索引策略
- 考虑数据分区

### 3. 备份策略
- 每日增量备份
- 每周全量备份
- 重要操作前手动备份

---

**创建时间**: 2025-01-20  
**版本**: v1.0  
**维护人**: 系统管理员 