import { defineStore } from 'pinia'
import { popupApi } from '@/api/popup'

export const usePopupStore = defineStore('popup', {
  state: () => ({
    // 弹窗队列
    popupQueue: [],
    // 当前显示的弹窗
    currentPopup: null,
    // 弹窗是否可见
    isVisible: false,
    // 是否正在处理弹窗
    isProcessing: false
  }),

  getters: {
    // 获取队列长度
    queueLength: (state) => state.popupQueue.length,
    
    // 是否有待显示的弹窗
    hasWaitingPopup: (state) => state.popupQueue.length > 0,
    
    // 获取当前弹窗信息
    getCurrentPopup: (state) => state.currentPopup
  },

  actions: {
    /**
     * 添加弹窗到队列
     * @param {Object} popupConfig 弹窗配置
     * @param {string} popupConfig.id 弹窗唯一标识
     * @param {string} popupConfig.title 弹窗标题
     * @param {string} popupConfig.content 弹窗内容
     * @param {Object} popupConfig.titleStyle 标题样式配置
     * @param {string} popupConfig.titleStyle.color 标题文字颜色
     * @param {string} popupConfig.titleStyle.background 标题背景色
     * @param {string} popupConfig.titleStyle.backgroundImage 标题背景图片
     * @param {Array} popupConfig.buttons 底部按钮配置
     * @param {number} popupConfig.priority 优先级 (数字越大优先级越高)
     * @param {number} popupConfig.autoClose 自动关闭时间(毫秒)，0表示不自动关闭
     * @param {number} popupConfig.delay 延迟显示时间(毫秒)
     * @param {boolean} popupConfig.maskClosable 点击遮罩是否关闭
     * @param {Function} popupConfig.onShow 显示时回调
     * @param {Function} popupConfig.onClose 关闭时回调
     */
    addPopup(popupConfig) {
      const defaultConfig = {
        id: Date.now().toString(),
        title: '',
        content: '',
        titleStyle: {
          color: '#333',
          background: '#fff',
          backgroundImage: ''
        },
        buttons: [
          {
            text: '确定',
            type: 'primary',
            style: {},
            onClick: () => this.closePopup()
          }
        ],
        priority: 0,
        autoClose: 0,
        delay: 0,
        maskClosable: true,
        onShow: null,
        onClose: null
      }

      const popup = { ...defaultConfig, ...popupConfig }
      
      // 防止重复添加相同ID的弹窗
      const existingIndex = this.popupQueue.findIndex(p => p.id === popup.id)
      if (existingIndex !== -1) {
        this.popupQueue[existingIndex] = popup
      } else {
        this.popupQueue.push(popup)
      }

      // 按优先级排序
      this.popupQueue.sort((a, b) => b.priority - a.priority)

      // 如果当前没有显示弹窗，立即显示
      !this.isVisible && this.displayNextPopup()
    },

    /**
     * 显示下一个弹窗
     */
    displayNextPopup() {
      if (this.isProcessing || this.popupQueue.length === 0) return

      this.isProcessing = true
      const nextPopup = this.popupQueue.shift()

      const showPopup = () => {
        this.currentPopup = nextPopup
        this.isVisible = true
        this.isProcessing = false

        // 执行显示回调
        nextPopup.onShow?.()

        // 设置自动关闭
        if (nextPopup.autoClose > 0) {
          setTimeout(() => {
            this.closePopup()
          }, nextPopup.autoClose)
        }
      }

      // 延迟显示
      nextPopup.delay > 0 ? setTimeout(showPopup, nextPopup.delay) : showPopup()
    },

    /**
     * 关闭当前弹窗
     * @param {*} result 关闭结果
     */
    closePopup(result = null) {
      if (!this.isVisible) return

      const currentPopup = this.currentPopup
      
      this.isVisible = false
      this.currentPopup = null

      // 执行关闭回调
      currentPopup?.onClose?.(result)

      // 显示下一个弹窗
      setTimeout(() => {
        this.displayNextPopup()
      }, 300) // 等待动画完成
    },

    /**
     * 清空弹窗队列
     */
    clearQueue() {
      this.popupQueue = []
    },

    /**
     * 移除指定弹窗
     * @param {string} id 弹窗ID
     */
    removePopup(id) {
      const index = this.popupQueue.findIndex(p => p.id === id)
      index !== -1 && this.popupQueue.splice(index, 1)
    },

    /**
     * 从后端加载弹窗
     * @param {Object} params 请求参数
     */
    async loadPopupsFromServer(params = {}) {
      try {
        const res = await popupApi.getActivePopups(params)
        if (res.code === 200 && res.data && res.data.length > 0) {
          // 将后端弹窗添加到队列
          res.data.forEach(popup => {
            // 转换后端数据格式
            const popupConfig = {
              id: popup.id.toString(),
              title: popup.title || '',
              content: popup.content || '',
              titleStyle: popup.titleStyle || {
                color: '#333',
                background: '#fff'
              },
              buttons: popup.buttons || [{
                text: '确定',
                type: 'primary',
                onClick: () => this.closePopup()
              }],
              priority: popup.priority || 0,
              autoClose: popup.autoClose || 0,
              delay: popup.delay || 0,
              maskClosable: popup.maskClosable !== false,
              onShow: () => this.recordLog(popup.id, params.userId || 0, 'show'),
              onClose: (result) => {
                // 如果是按钮点击关闭，记录按钮信息
                if (result && result.buttonText) {
                  this.recordLog(popup.id, params.userId || 0, 'click', result.buttonText)
                } else {
                  this.recordLog(popup.id, params.userId || 0, 'close')
                }
              }
            }
            
            // 为按钮添加点击事件
            if (popupConfig.buttons && popupConfig.buttons.length > 0) {
              popupConfig.buttons.forEach(button => {
                const originalOnClick = button.onClick || (() => {})
                button.onClick = () => {
                  originalOnClick()
                  this.closePopup({ buttonText: button.text })
                }
              })
            }
            
            this.addPopup(popupConfig)
          })
        }
      } catch (error) {
        console.error('加载弹窗失败:', error)
      }
    },

    /**
     * 记录弹窗操作日志
     * @param {string} popupId 弹窗ID
     * @param {number} userId 用户ID
     * @param {string} action 操作类型
     * @param {string} buttonText 按钮文字
     */
    async recordLog(popupId, userId, action, buttonText = '') {
      try {
        await popupApi.recordLog({
          popupId: parseInt(popupId),
          userId: userId,
          action: action,
          buttonText: buttonText
        })
      } catch (error) {
        console.error('记录弹窗日志失败:', error)
      }
    }
  }
}) 