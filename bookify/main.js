import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'

Vue.config.productionTip = false


// 添加全局配置
if (typeof wx !== 'undefined') {
  wx.getABTestConfig = () => {
    return Promise.resolve({})
  }
}

App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import { createPinia } from 'pinia'

export function createApp() {
  const app = createSSRApp(App)
  
  // 创建并使用 Pinia
  const pinia = createPinia()
  app.use(pinia)
  
  return {
    app,
    Pinia: pinia
  }
}
// #endif