/* 美容院预约系统全局样式 */

// 颜色变量
$primary-color: #FFB6C1;
$primary-light: #FFE5E5;
$primary-dark: #FF91A4;
$success-color: #52C41A;
$warning-color: #FA8C16;
$error-color: #F5222D;
$info-color: #1890FF;

$text-color: #333;
$text-color-light: #666;
$text-color-lighter: #999;
$border-color: #f0f0f0;
$background-color: #f8f9fa;

// 字体大小
$font-size-xs: 20rpx;
$font-size-sm: 24rpx;
$font-size-base: 28rpx;
$font-size-lg: 32rpx;
$font-size-xl: 36rpx;
$font-size-xxl: 40rpx;

// 间距
$spacing-xs: 8rpx;
$spacing-sm: 12rpx;
$spacing-base: 16rpx;
$spacing-lg: 20rpx;
$spacing-xl: 24rpx;
$spacing-xxl: 30rpx;

// 圆角
$border-radius-sm: 8rpx;
$border-radius-base: 12rpx;
$border-radius-lg: 16rpx;
$border-radius-xl: 20rpx;
$border-radius-circle: 50%;

// 阴影
$box-shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
$box-shadow-base: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
$box-shadow-lg: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);

// 通用样式类
.beauty-container {
  background: $background-color;
  min-height: 100vh;
}

.beauty-card {
  background: #fff;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-base;
  margin-bottom: $spacing-lg;
  overflow: hidden;
}

.beauty-section {
  background: #fff;
  margin-bottom: $spacing-lg;
  padding: $spacing-xxl;
  
  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-xxl;
    
    .title-text {
      font-size: $font-size-xl;
      font-weight: 600;
      color: $text-color;
    }
    
    .more-text {
      font-size: $font-size-base;
      color: $primary-color;
    }
  }
}

.beauty-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-xxl;
  border-bottom: 1px solid $border-color;
  
  .header-title {
    font-size: $font-size-lg;
    font-weight: 600;
    color: $text-color;
  }
}

.beauty-content {
  padding: $spacing-lg $spacing-xxl $spacing-xxl;
}

// 状态样式
.status-pending {
  color: $warning-color;
  background: #FFF7E6;
}

.status-confirmed {
  color: $info-color;
  background: #E6F7FF;
}

.status-in-service {
  color: $success-color;
  background: #F6FFED;
}

.status-completed {
  color: #722ED1;
  background: #F9F0FF;
}

.status-cancelled {
  color: $error-color;
  background: #FFF2F0;
}

// 价格样式
.price-text {
  font-weight: 600;
  color: $primary-color;
}

.price-original {
  color: $text-color-lighter;
  text-decoration: line-through;
}

.price-member {
  color: #FF6B6B;
  background: $primary-light;
  padding: 4rpx 8rpx;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
}

// 标签样式
.beauty-tag {
  display: inline-block;
  background: $background-color;
  color: $text-color-light;
  padding: $spacing-xs $spacing-base;
  border-radius: $border-radius-xl;
  font-size: $font-size-sm;
  margin-right: $spacing-sm;
  margin-bottom: $spacing-sm;
}

.beauty-tag-primary {
  background: $primary-light;
  color: $primary-color;
}

// 按钮样式
.beauty-button {
  border-radius: $border-radius-base;
  font-size: $font-size-base;
  
  &.beauty-button-primary {
    background: $primary-color;
    color: #fff;
    border: none;
  }
  
  &.beauty-button-default {
    background: #fff;
    color: $text-color;
    border: 1px solid $border-color;
  }
  
  &.beauty-button-small {
    padding: $spacing-sm $spacing-base;
    font-size: $font-size-sm;
  }
  
  &.beauty-button-large {
    padding: $spacing-base $spacing-xl;
    font-size: $font-size-base;
  }
}

// 输入框样式
.beauty-input {
  border: 1px solid $border-color;
  border-radius: $border-radius-base;
  padding: $spacing-base;
  font-size: $font-size-base;
  background: #fff;
  
  &:focus {
    border-color: $primary-color;
    outline: none;
  }
}

// 加载状态
.beauty-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  
  .loading-text {
    font-size: $font-size-base;
    color: $text-color-light;
    margin-top: $spacing-lg;
  }
}

// 空状态
.beauty-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  
  .empty-icon {
    margin-bottom: $spacing-lg;
  }
  
  .empty-text {
    font-size: $font-size-base;
    color: $text-color-lighter;
    margin-bottom: $spacing-xxl;
  }
}

// 分割线
.beauty-divider {
  height: 1px;
  background: $border-color;
  margin: $spacing-lg 0;
}

// 网格布局
.beauty-grid {
  display: grid;
  gap: $spacing-lg;
  
  &.beauty-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  &.beauty-grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  &.beauty-grid-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

// 弹性布局
.beauty-flex {
  display: flex;
  
  &.beauty-flex-center {
    align-items: center;
    justify-content: center;
  }
  
  &.beauty-flex-between {
    justify-content: space-between;
  }
  
  &.beauty-flex-around {
    justify-content: space-around;
  }
  
  &.beauty-flex-column {
    flex-direction: column;
  }
}

// 文本样式
.beauty-text {
  &.beauty-text-primary {
    color: $primary-color;
  }
  
  &.beauty-text-success {
    color: $success-color;
  }
  
  &.beauty-text-warning {
    color: $warning-color;
  }
  
  &.beauty-text-error {
    color: $error-color;
  }
  
  &.beauty-text-light {
    color: $text-color-light;
  }
  
  &.beauty-text-lighter {
    color: $text-color-lighter;
  }
  
  &.beauty-text-center {
    text-align: center;
  }
  
  &.beauty-text-right {
    text-align: right;
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .beauty-grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (max-width: 500rpx) {
  .beauty-grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .beauty-grid-2 {
    grid-template-columns: 1fr;
  }
}

// 动画效果
.beauty-fade-in {
  animation: beautyFadeIn 0.3s ease-in-out;
}

.beauty-slide-up {
  animation: beautySlideUp 0.3s ease-in-out;
}

@keyframes beautyFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes beautySlideUp {
  from {
    transform: translateY(30rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// 固定底部按钮
.beauty-fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: $spacing-lg $spacing-xxl;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
}

// 为固定底部按钮预留空间
.beauty-page-with-fixed-bottom {
  padding-bottom: 120rpx;
}

// 安全区域适配
.beauty-safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.beauty-safe-area-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
