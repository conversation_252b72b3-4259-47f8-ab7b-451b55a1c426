// 全局通用样式文件

// 颜色变量
$primary-color: #667eea;
$secondary-color: #764ba2;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff6b6b;
$info-color: #1890ff;

// 间距变量
$spacing-xs: 8rpx;
$spacing-sm: 16rpx;
$spacing-md: 24rpx;
$spacing-lg: 32rpx;
$spacing-xl: 40rpx;

// 圆角变量
$radius-sm: 8rpx;
$radius-md: 16rpx;
$radius-lg: 24rpx;
$radius-xl: 32rpx;
$radius-round: 50%;

// 阴影变量
$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
$shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
$shadow-lg: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
$shadow-xl: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);

// 布局类
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

// 文本类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-xs {
  font-size: 20rpx;
}

.text-sm {
  font-size: 24rpx;
}

.text-md {
  font-size: 28rpx;
}

.text-lg {
  font-size: 32rpx;
}

.text-xl {
  font-size: 36rpx;
}

.text-bold {
  font-weight: 600;
}

.text-normal {
  font-weight: 400;
}

// 颜色类
.text-primary {
  color: $primary-color;
}

.text-success {
  color: $success-color;
}

.text-warning {
  color: $warning-color;
}

.text-error {
  color: $error-color;
}

.text-info {
  color: $info-color;
}

.text-gray {
  color: #666;
}

.text-light {
  color: #999;
}

.text-white {
  color: #fff;
}

// 背景颜色类
.bg-primary {
  background-color: $primary-color;
}

.bg-success {
  background-color: $success-color;
}

.bg-warning {
  background-color: $warning-color;
}

.bg-error {
  background-color: $error-color;
}

.bg-info {
  background-color: $info-color;
}

.bg-white {
  background-color: #fff;
}

.bg-gray {
  background-color: #f5f5f5;
}

// 间距类
.p-xs { padding: $spacing-xs; }
.p-sm { padding: $spacing-sm; }
.p-md { padding: $spacing-md; }
.p-lg { padding: $spacing-lg; }
.p-xl { padding: $spacing-xl; }

.pt-xs { padding-top: $spacing-xs; }
.pt-sm { padding-top: $spacing-sm; }
.pt-md { padding-top: $spacing-md; }
.pt-lg { padding-top: $spacing-lg; }
.pt-xl { padding-top: $spacing-xl; }

.pb-xs { padding-bottom: $spacing-xs; }
.pb-sm { padding-bottom: $spacing-sm; }
.pb-md { padding-bottom: $spacing-md; }
.pb-lg { padding-bottom: $spacing-lg; }
.pb-xl { padding-bottom: $spacing-xl; }

.pl-xs { padding-left: $spacing-xs; }
.pl-sm { padding-left: $spacing-sm; }
.pl-md { padding-left: $spacing-md; }
.pl-lg { padding-left: $spacing-lg; }
.pl-xl { padding-left: $spacing-xl; }

.pr-xs { padding-right: $spacing-xs; }
.pr-sm { padding-right: $spacing-sm; }
.pr-md { padding-right: $spacing-md; }
.pr-lg { padding-right: $spacing-lg; }
.pr-xl { padding-right: $spacing-xl; }

.m-xs { margin: $spacing-xs; }
.m-sm { margin: $spacing-sm; }
.m-md { margin: $spacing-md; }
.m-lg { margin: $spacing-lg; }
.m-xl { margin: $spacing-xl; }

.mt-xs { margin-top: $spacing-xs; }
.mt-sm { margin-top: $spacing-sm; }
.mt-md { margin-top: $spacing-md; }
.mt-lg { margin-top: $spacing-lg; }
.mt-xl { margin-top: $spacing-xl; }

.mb-xs { margin-bottom: $spacing-xs; }
.mb-sm { margin-bottom: $spacing-sm; }
.mb-md { margin-bottom: $spacing-md; }
.mb-lg { margin-bottom: $spacing-lg; }
.mb-xl { margin-bottom: $spacing-xl; }

.ml-xs { margin-left: $spacing-xs; }
.ml-sm { margin-left: $spacing-sm; }
.ml-md { margin-left: $spacing-md; }
.ml-lg { margin-left: $spacing-lg; }
.ml-xl { margin-left: $spacing-xl; }

.mr-xs { margin-right: $spacing-xs; }
.mr-sm { margin-right: $spacing-sm; }
.mr-md { margin-right: $spacing-md; }
.mr-lg { margin-right: $spacing-lg; }
.mr-xl { margin-right: $spacing-xl; }

// 圆角类
.rounded-sm {
  border-radius: $radius-sm;
}

.rounded-md {
  border-radius: $radius-md;
}

.rounded-lg {
  border-radius: $radius-lg;
}

.rounded-xl {
  border-radius: $radius-xl;
}

.rounded-full {
  border-radius: $radius-round;
}

// 阴影类
.shadow-sm {
  box-shadow: $shadow-sm;
}

.shadow-md {
  box-shadow: $shadow-md;
}

.shadow-lg {
  box-shadow: $shadow-lg;
}

.shadow-xl {
  box-shadow: $shadow-xl;
}

// 边框类
.border {
  border: 1rpx solid #e5e5e5;
}

.border-primary {
  border: 1rpx solid $primary-color;
}

.border-success {
  border: 1rpx solid $success-color;
}

.border-warning {
  border: 1rpx solid $warning-color;
}

.border-error {
  border: 1rpx solid $error-color;
}

// 位置类
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

// 显示隐藏类
.hidden {
  display: none;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

// 溢出类
.overflow-hidden {
  overflow: hidden;
}

.overflow-scroll {
  overflow: scroll;
}

.overflow-auto {
  overflow: auto;
}

// 文本溢出类
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 宽高类
.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.w-screen {
  width: 100vw;
}

.h-screen {
  height: 100vh;
}

// 特殊效果类
.blur {
  filter: blur(4rpx);
}

.grayscale {
  filter: grayscale(100%);
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-75 {
  opacity: 0.75;
}

// 动画类
.transition {
  transition: all 0.3s ease;
}

.transition-fast {
  transition: all 0.15s ease;
}

.transition-slow {
  transition: all 0.5s ease;
}

.scale-95 {
  transform: scale(0.95);
}

.scale-105 {
  transform: scale(1.05);
}

.rotate-90 {
  transform: rotate(90deg);
}

.rotate-180 {
  transform: rotate(180deg);
}

// 交互状态类
.active-scale {
  &:active {
    transform: scale(0.95);
  }
}

.hover-shadow {
  &:hover {
    box-shadow: $shadow-lg;
  }
}

// 网格布局类
.grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-md;
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: $spacing-md;
}

.grid-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: $spacing-md;
}

.grid-5 {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: $spacing-md;
}

// 按钮样式类
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  border: none;
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
  }
  
  &.btn-primary {
    background: linear-gradient(135deg, #FF6B81 0%, #FF8E9E 100%);
    color: #fff;
    box-shadow: 0 4rpx 12rpx rgba(255, 107, 129, 0.3);
  }
  
  &.btn-default {
    background: #ffffff;
    color: #333;
    border: 2rpx solid #e0e0e0;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    
    &:active {
      background: #f5f5f5;
      border-color: #d0d0d0;
    }
  }
  
  &.btn-success {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    color: #fff;
    box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
  }
  
  &.btn-warning {
    background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
    color: #fff;
    box-shadow: 0 4rpx 12rpx rgba(250, 173, 20, 0.3);
  }
  
  &.btn-error {
    background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
    color: #fff;
    box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
  }
  
  &.btn-info {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    color: #fff;
    box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
  }
  
  &.btn-outline {
    background: transparent;
    border: 1rpx solid currentColor;
    
    &.btn-outline-primary {
      color: $primary-color;
    }
    
    &.btn-outline-success {
      color: $success-color;
    }
    
    &.btn-outline-warning {
      color: $warning-color;
    }
    
    &.btn-outline-error {
      color: $error-color;
    }
  }
  
  &.btn-sm {
    height: 60rpx;
    font-size: 24rpx;
    padding: 0 24rpx;
    border-radius: 30rpx;
  }
  
  &.btn-md {
    height: 80rpx;
    font-size: 28rpx;
    padding: 0 32rpx;
    border-radius: 40rpx;
  }
  
  &.btn-lg {
    height: 100rpx;
    font-size: 32rpx;
    padding: 0 40rpx;
    border-radius: 50rpx;
  }
  
  &.btn-round {
    border-radius: 50%;
    width: 80rpx;
    height: 80rpx;
    padding: 0;
  }
  
  &.btn-block {
    width: 100%;
  }
  
  &.btn-disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}

// 响应式类
@media (max-width: 750rpx) {
  .sm-hidden {
    display: none;
  }
  
  .sm-block {
    display: block;
  }
  
  .sm-flex {
    display: flex;
  }
  
  .sm-grid-1 {
    grid-template-columns: 1fr;
  }
  
  .sm-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
} 