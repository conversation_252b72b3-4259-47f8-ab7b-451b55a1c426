<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务列表数据测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .service-item {
            background: white;
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .service-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .service-desc {
            color: #666;
            margin-bottom: 10px;
        }
        .service-price {
            font-size: 18px;
            font-weight: bold;
            color: #ff6b9d;
            margin-right: 10px;
        }
        .service-original-price {
            text-decoration: line-through;
            color: #999;
            margin-right: 10px;
        }
        .service-stats {
            color: #999;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .service-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        .service-tag {
            background: #fff5f8;
            color: #ff6b9d;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
        .category-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin: 20px 0 10px 0;
            padding-bottom: 5px;
            border-bottom: 2px solid #ff6b9d;
        }
        .total-count {
            background: #ff6b9d;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>美容服务列表测试数据</h1>
    <div class="total-count">
        <strong>总计：<span id="totalCount">0</span> 个服务</strong>
    </div>
    <div id="serviceList"></div>

    <script>
        // 模拟API数据
        const serviceCategories = [
            { id: 1, name: '面部护理', icon: '🧴', code: 'facial' },
            { id: 2, name: '美甲服务', icon: '💅', code: 'nail' },
            { id: 3, name: '美发造型', icon: '💇', code: 'hair' },
            { id: 4, name: '美睫服务', icon: '👁️', code: 'eyelash' },
            { id: 5, name: '身体护理', icon: '💆', code: 'body' },
            { id: 6, name: '美容仪器', icon: '🔬', code: 'device' },
            { id: 7, name: '纹绣服务', icon: '🎨', code: 'tattoo' }
        ];

        const services = [
            {
                id: 1,
                name: '深层清洁面部护理',
                categoryId: 1,
                price: 198,
                originalPrice: 298,
                rating: 4.8,
                sold: 1289,
                description: '专业深层清洁，去除黑头粉刺，收缩毛孔，改善肤质',
                tags: ['深层清洁', '去黑头', '收缩毛孔', '补水保湿'],
                isHot: true
            },
            {
                id: 2,
                name: '日式美甲套餐',
                categoryId: 2,
                price: 88,
                originalPrice: 128,
                rating: 4.9,
                sold: 876,
                description: '精致日式美甲，持久不脱落，多种款式可选',
                tags: ['美甲', '日式', '持久', '多款式'],
                isHot: false
            },
            {
                id: 3,
                name: '韩式半永久眉毛',
                categoryId: 7,
                price: 688,
                originalPrice: 888,
                rating: 4.7,
                sold: 432,
                description: '自然韩式眉型，专业纹绣师操作，持久自然',
                tags: ['纹绣', '韩式', '半永久', '眉毛'],
                isHot: true
            }
        ];

        // 渲染服务列表
        function renderServices() {
            const container = document.getElementById('serviceList');
            const totalCountEl = document.getElementById('totalCount');
            
            // 按分类分组
            const groupedServices = {};
            services.forEach(service => {
                const category = serviceCategories.find(cat => cat.id === service.categoryId);
                const categoryName = category ? category.name : '未分类';
                
                if (!groupedServices[categoryName]) {
                    groupedServices[categoryName] = [];
                }
                groupedServices[categoryName].push(service);
            });

            // 渲染
            let html = '';
            Object.keys(groupedServices).forEach(categoryName => {
                html += `<div class="category-title">${categoryName} (${groupedServices[categoryName].length}个)</div>`;
                
                groupedServices[categoryName].forEach(service => {
                    html += `
                        <div class="service-item">
                            <div class="service-name">
                                ${service.name}
                                ${service.isHot ? '🔥' : ''}
                            </div>
                            <div class="service-desc">${service.description}</div>
                            <div>
                                <span class="service-price">¥${service.price}</span>
                                ${service.originalPrice ? `<span class="service-original-price">¥${service.originalPrice}</span>` : ''}
                            </div>
                            <div class="service-stats">
                                已售${service.sold}+ | 评分${service.rating}分
                            </div>
                            <div class="service-tags">
                                ${service.tags.map(tag => `<span class="service-tag">${tag}</span>`).join('')}
                            </div>
                        </div>
                    `;
                });
            });

            container.innerHTML = html;
            totalCountEl.textContent = services.length;
        }

        // 页面加载时渲染
        renderServices();
    </script>
</body>
</html> 