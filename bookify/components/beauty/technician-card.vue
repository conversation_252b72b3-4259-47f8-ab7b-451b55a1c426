<template>
  <view class="technician-card" @click="onClick">
    <view class="technician-avatar-wrapper">
      <image 
        :src="technician.avatar || 'https://picsum.photos/200/200?random=70'" 
        class="technician-avatar" 
        mode="aspectFill"
      ></image>
      <view class="technician-badge" v-if="technician.is_featured">
        <text>推荐</text>
      </view>
    </view>
    <view class="technician-info">
      <view class="technician-header">
        <text class="technician-name">{{ technician.name }}</text>
        <text class="technician-level">{{ technician.level }}</text>
      </view>
      <view class="technician-rating">
        <view class="rating-stars">
          <text class="star-icon" v-for="i in 5" :key="i">
            {{ i <= Math.floor(technician.rating_avg || 0) ? '★' : '☆' }}
          </text>
        </view>
        <text class="rating-value">{{ technician.rating_avg || 0 }}</text>
        <text class="review-count">({{ technician.rating_count || 0 }}条评价)</text>
      </view>
      <view class="technician-meta">
        <text class="experience">{{ technician.experience || 0 }}年经验</text>
        <text class="price" v-if="technician.extra_fee">¥{{ technician.extra_fee }}/次</text>
      </view>
      
      <!-- 工作时间信息 -->
      <view class="work-time-info" v-if="technician.work_hours">
        <text class="work-time-label">工作时间：</text>
        <text class="work-time">{{ technician.work_hours }}</text>
      </view>
      
      <!-- 专业技能标签 -->
      <view class="technician-tags" v-if="technician.specialties && technician.specialties.length">
        <text class="tag" v-for="(tag, index) in displayTags" :key="index">{{ tag }}</text>
      </view>
      

      
      <!-- 简介 -->
      <view class="technician-intro" v-if="technician.introduction">
        <text class="intro-text">{{ truncatedIntro }}</text>
        <text class="intro-more" v-if="technician.introduction.length > 50" @click.stop="showFullIntro">
          {{ showFullIntroText ? '收起' : '展开' }}
        </text>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <view class="collect-btn" @click.stop="handleCollect">
        <text class="collect-icon">{{ technician.is_collected ? '♥' : '♡' }}</text>
        <text class="collect-text">{{ technician.is_collected ? '已收藏' : '收藏' }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TechnicianCard',
  
  props: {
    technician: {
      type: Object,
      required: true
    },
    showDetail: {
      type: Boolean,
      default: true
    }
  },
  
  data() {
    return {
      showFullIntroText: false
    }
  },
  
  computed: {
    displayTags() {
      return (this.technician.specialties || []).slice(0, 3)
    },
    

    
    truncatedIntro() {
      if (!this.technician.introduction) return ''
      if (this.showFullIntroText || this.technician.introduction.length <= 50) {
        return this.technician.introduction
      }
      return this.technician.introduction.substring(0, 50) + '...'
    }
  },
  
  methods: {
    onClick() {
      this.$emit('click', this.technician)
    },
    
    handleCollect() {
      this.$emit('collect', this.technician)
    },
    
    showFullIntro() {
      this.showFullIntroText = !this.showFullIntroText
    },
    
    previewImage(images, current) {
      uni.previewImage({
        urls: images,
        current: images[current]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.technician-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  
  .technician-avatar-wrapper {
    position: relative;
    margin-right: 20rpx;
    align-self: flex-start;
    
    .technician-avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
    }
    
    .technician-badge {
      position: absolute;
      top: 0;
      right: 0;
      background-color: #FFB6C1;
      color: #ffffff;
      font-size: 20rpx;
      padding: 4rpx 12rpx;
      border-radius: 20rpx;
    }
  }
  
  .technician-info {
    flex: 1;
    margin-top: 10rpx;
    
    .technician-header {
      display: flex;
      align-items: center;
      margin-bottom: 10rpx;
      
      .technician-name {
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
        margin-right: 10rpx;
      }
      
      .technician-level {
        font-size: 24rpx;
        color: #666666;
        background: #f0f0f0;
        padding: 4rpx 8rpx;
        border-radius: 4rpx;
      }
    }
    
    .technician-rating {
      display: flex;
      align-items: center;
      margin-bottom: 10rpx;
      
      .rating-stars {
        display: flex;
        margin-right: 10rpx;
        
        .star-icon {
          color: #FFB6C1;
          font-size: 24rpx;
        }
      }
      
      .rating-value {
        font-size: 24rpx;
        color: #666666;
        margin-right: 8rpx;
      }
      
      .review-count {
        font-size: 22rpx;
        color: #999999;
      }
    }
    
    .technician-meta {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12rpx;
      
      .experience {
        font-size: 24rpx;
        color: #666666;
      }
      
      .price {
        font-size: 24rpx;
        color: #FF6B81;
        font-weight: bold;
      }
    }
    
    .work-time-info {
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;
      flex-wrap: wrap;
      
      .work-time-label {
        font-size: 22rpx;
        color: #666666;
        margin-right: 8rpx;
      }
      
      .work-time {
        font-size: 22rpx;
        color: #333333;
        margin-right: 8rpx;
      }
      
      .rest-day {
        font-size: 20rpx;
        color: #999999;
      }
    }
    
    .technician-tags {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 12rpx;
      
      .tag {
        font-size: 20rpx;
        color: #666666;
        background-color: #F8F8F8;
        padding: 4rpx 10rpx;
        border-radius: 6rpx;
        margin-right: 8rpx;
        margin-bottom: 6rpx;
      }
    }
    
    .certificate-info {
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;
      flex-wrap: wrap;
      
      .certificate-label {
        font-size: 22rpx;
        color: #666666;
        margin-right: 8rpx;
      }
      
      .certificate-item {
        font-size: 20rpx;
        color: #FF6B81;
      }
    }
    
    .gallery-preview {
      margin-bottom: 12rpx;
      
      .gallery-label {
        font-size: 22rpx;
        color: #666666;
        margin-bottom: 8rpx;
        display: block;
      }
      
      .gallery-images {
        display: flex;
        gap: 8rpx;
        
        .gallery-image {
          width: 60rpx;
          height: 60rpx;
          border-radius: 6rpx;
        }
      }
    }
    
    .technician-intro {
      margin-bottom: 12rpx;
      
      .intro-text {
        font-size: 22rpx;
        color: #666666;
        line-height: 1.4;
      }
      
      .intro-more {
        font-size: 20rpx;
        color: #FF6B81;
        margin-left: 8rpx;
      }
    }
  }
  
  .action-buttons {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    display: flex;
    gap: 12rpx;
  }
  
  .collect-btn {
    background: rgba(255, 255, 255, 0.9);
    color: #FF69B4;
    padding: 6rpx 12rpx;
    border-radius: 20rpx;
    font-size: 20rpx;
    display: flex;
    align-items: center;
    gap: 4rpx;
    border: 1rpx solid #FFB6C1;
    
    .collect-icon {
      font-size: 18rpx;
      color: #FF69B4;
    }
    
    .collect-text {
      font-size: 18rpx;
    }
  }
  

}
</style>