<template>
  <view class="booking-card" @click="handleClick">
    <view class="booking-header">
      <view class="booking-status">
        <text class="status-text" :class="getStatusClass()">{{ getStatusText() }}</text>
      </view>
      <view class="booking-time">
        <text>{{ booking.bookingDate }} {{ booking.bookingTime }}</text>
      </view>
    </view>
    <view class="booking-content">
      <view class="service-info">
        <text class="service-name">{{ booking.serviceName }}</text>
        <text class="technician-name">技师：{{ booking.technicianName }}</text>
      </view>
      <view class="booking-meta">
        <text class="duration">{{ booking.duration }}分钟</text>
        <text class="price">¥{{ booking.totalPrice }}</text>
      </view>
    </view>
    <view class="booking-footer" v-if="showActions">
      <view class="booking-actions">
        <button class="action-btn btn-default" @click.stop="handleCancel" v-if="booking.status === 'pending'">取消预约</button>
        <button class="action-btn btn-primary" @click.stop="handleRebook" v-if="booking.status === 'cancelled'">重新预约</button>
        <button class="action-btn btn-primary" @click.stop="handleReview" v-if="booking.status === 'completed' && !booking.reviewed">评价</button>
        <!-- 新增：服务操作按钮 -->
        <button class="action-btn btn-primary" @click.stop="handleStartService" v-if="booking.status === 'confirmed'">开始服务</button>
        <button class="action-btn btn-success" @click.stop="handleCompleteService" v-if="booking.status === 'in_service'">完成服务</button>

      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'BookingCard',
  props: {
    booking: {
      type: Object,
      required: true
    },
    showActions: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    handleClick() {
      this.$emit('click', this.booking)
    },
    
    handleCancel() {
      this.$emit('cancel', this.booking)
    },
    
    handleRebook() {
      this.$emit('rebook', this.booking)
    },
    
    handleReview() {
      this.$emit('review', this.booking)
    },
    
    handleStartService() {
      this.$emit('start-service', this.booking)
    },
    handleCompleteService() {
      this.$emit('complete-service', this.booking)
    },

    
    getStatusText() {
      const statusMap = {
        'pending': '待确认',
        'confirmed': '已确认',
        'in_service': '服务中',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusMap[this.booking.status] || '未知'
    },
    
    getStatusClass() {
      return `status-${this.booking.status}`
    }
  }
}
</script>

<style lang="scss" scoped>
.booking-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  
  .booking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    
    .booking-status {
      .status-text {
        font-size: 24rpx;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        
        &.status-pending {
          background: #fff7e6;
          color: #fa8c16;
        }
        
        &.status-confirmed {
          background: #f6ffed;
          color: #52c41a;
        }
        
        &.status-in_service {
          background: #e6f7ff;
          color: #1890ff;
        }
        
        &.status-completed {
          background: #f6ffed;
          color: #52c41a;
        }
        
        &.status-cancelled {
          background: #fff1f0;
          color: #ff4d4f;
        }
      }
    }
    
    .booking-time {
      text {
        font-size: 24rpx;
        color: #999;
      }
    }
  }
  
  .booking-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20rpx;
    
    .service-info {
      flex: 1;
      
      .service-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        display: block;
        margin-bottom: 8rpx;
      }
      
      .technician-name {
        font-size: 26rpx;
        color: #666;
        display: block;
      }
    }
    
    .booking-meta {
      text-align: right;
      
      .duration {
        font-size: 24rpx;
        color: #999;
        display: block;
        margin-bottom: 8rpx;
      }
      
      .price {
        font-size: 28rpx;
        font-weight: 600;
        color: #FFB6C1;
        display: block;
      }
    }
  }
  
  .booking-footer {
    border-top: 1rpx solid #f0f0f0;
    padding-top: 20rpx;
    
    .booking-actions {
      display: flex;
      justify-content: flex-end;
      gap: 20rpx;
      
      .action-btn {
        padding: 12rpx 24rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        border: none;
        
        &.btn-default {
          background: #f5f5f5;
          color: #666;
        }
        
        &.btn-primary {
          background: #FFB6C1;
          color: #fff;
        }
        
        &.btn-success {
          background: #52c41a;
          color: #fff;
        }
      }
    }
  }
}
</style>
