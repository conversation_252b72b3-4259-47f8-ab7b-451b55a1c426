<template>
    <view class="beauty-navbar">
      <view class="navbar-status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="navbar-content">
        <view class="navbar-left">
          <view v-if="showBack" class="back-btn" @click="goBack">
            <text class="back-icon">‹</text>
          </view>
        </view>
        
        <view class="navbar-center">
          <text class="navbar-title">{{ title }}</text>
        </view>
        
        <view class="navbar-right">
          <slot name="right"></slot>
          <view v-if="showSearch" class="nav-icon" @click="onSearch">
            <text>🔍</text>
          </view>
          <view v-if="showNotification" class="nav-icon" @click="onNotification">
            <text>💬</text>
          </view>
          <view v-if="showMore" class="nav-icon" @click="onMore">
            <text>⋯</text>
          </view>
        </view>
      </view>
    </view>
  </template>
  
  <script>
  export default {
    name: 'BeautyNavbar',
    props: {
      title: {
        type: String,
        default: '美丽时光'
      },
      showBack: {
        type: Boolean,
        default: true
      },
      showSearch: {
        type: Boolean,
        default: false
      },
      showNotification: {
        type: Boolean,
        default: false
      },
      showMore: {
        type: Boolean,
        default: false
      }
    },
    
    data() {
      return {
        statusBarHeight: 0
      }
    },
    
    created() {
      // 安全地获取状态栏高度
      try {
        const systemInfo = uni.getSystemInfoSync()
        this.statusBarHeight = systemInfo.statusBarHeight || 0
      } catch (error) {
        console.warn('获取系统信息失败:', error)
        this.statusBarHeight = 20 // 默认状态栏高度
      }
    },
    
    methods: {
      goBack() {
        // 检查当前页面栈
        const pages = getCurrentPages()
        if (pages.length > 1) {
          // 如果有上一页，则返回上一页
          uni.navigateBack({
            delta: 1
          })
        } else {
          // 如果没有上一页（比如tabbar页面），则跳转到首页
          uni.switchTab({
            url: '/pages/beauty/index/index'
          })
        }
      },
      
      onSearch() {
        this.$emit('search')
      },
      
      onNotification() {
        this.$emit('notification')
      },
      
      onMore() {
        this.$emit('more')
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .beauty-navbar {
    background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 100%);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    
    .navbar-status-bar {
      width: 100%;
    }
    
    .navbar-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 44px;
      padding: 0 15px;
      position: relative;
      
      .navbar-left {
        width: 60px;
        display: flex;
        align-items: center;
        
        .back-btn {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 16px;
          
          .back-icon {
            font-size: 20px;
            color: #fff;
            font-weight: bold;
          }
        }
      }
      
      .navbar-center {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .navbar-title {
          font-size: 18px;
          font-weight: 600;
          color: #fff;
          text-align: center;
        }
      }
      
      .navbar-right {
        width: 60px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 10px;
        
        .nav-icon {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 16px;
          font-size: 16px;
        }
      }
    }
  }
  </style>