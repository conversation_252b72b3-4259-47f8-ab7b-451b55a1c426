<template>
  <view class="payment-handler">
    <!-- 支付方式选择 -->
    <view class="payment-methods">
      <view class="section-title">支付方式</view>
      <view class="method-list">
        <view 
          class="method-item" 
          :class="{ active: selectedPayType === 1 }"
          @click="selectPayType(1)"
        >
          <view class="method-info">
            <image src="/static/images/wechat-pay.png" class="method-icon" />
            <text class="method-name">微信支付</text>
          </view>
          <text class="check-icon" v-if="selectedPayType === 1">✓</text>
        </view>
        
        <view 
          class="method-item" 
          :class="{ active: selectedPayType === 2 }"
          @click="selectPayType(2)"
        >
          <view class="method-info">
            <image src="/static/images/alipay.png" class="method-icon" />
            <text class="method-name">支付宝支付</text>
          </view>
          <text class="check-icon" v-if="selectedPayType === 2">✓</text>
        </view>
        
        <view 
          class="method-item" 
          :class="{ active: selectedPayType === 3 }"
          @click="selectPayType(3)"
        >
          <view class="method-info">
            <image src="/static/images/balance.png" class="method-icon" />
            <text class="method-name">余额支付</text>
            <text class="balance-info" v-if="userBalance !== null">
              (余额：¥{{ userBalance }})
            </text>
          </view>
          <text class="check-icon" v-if="selectedPayType === 3">✓</text>
        </view>
      </view>
    </view>

    <!-- 支付按钮 -->
    <view class="payment-footer">
      <button 
        class="pay-btn"
        :class="{ disabled: !canPay, loading: paying }"
        @click="handlePay"
        :disabled="!canPay"
      >
        {{ payButtonText }}
      </button>
    </view>
  </view>
</template>

<script>
import { getUserInfo } from '@/api/user'

export default {
  name: 'PaymentHandler',
  
  props: {
    // 支付金额
    amount: {
      type: Number,
      required: true
    },
    
    // 支付API函数
    payApi: {
      type: Function,
      required: true
    },
    
    // 支付参数
    payParams: {
      type: Object,
      default: () => ({})
    },
    
    // 支付按钮文本
    payButtonText: {
      type: String,
      default: '立即支付'
    },
    
    // 是否可以支付
    canPay: {
      type: Boolean,
      default: true
    },
    
    // 默认支付方式
    defaultPayType: {
      type: Number,
      default: 1
    }
  },

  data() {
    return {
      selectedPayType: this.defaultPayType,
      userBalance: null,
      paying: false
    }
  },

  mounted() {
    this.loadUserBalance()
  },

  methods: {
    // 加载用户余额
    async loadUserBalance() {
      try {
        const response = await getUserInfo()
        if (response.code === 200) {
          this.userBalance = response.data.balance
        }
      } catch (error) {
        console.error('加载用户余额失败:', error)
      }
    },

    // 选择支付方式
    selectPayType(type) {
      // 余额支付时检查余额
      if (type === 3 && this.userBalance < this.amount) {
        uni.showModal({
          title: '余额不足',
          content: `当前余额：¥${this.userBalance}\n支付金额：¥${this.amount}`,
          showCancel: false
        })
        return
      }
      
      this.selectedPayType = type
      this.$emit('payTypeChange', type)
    },

    // 处理支付
    async handlePay() {
      if (!this.canPay || this.paying) return
      
      this.paying = true
      this.$emit('payStart')
      
      try {
        const payParams = {
          ...this.payParams,
          pay_type: this.selectedPayType
        }
        
        const response = await this.payApi(payParams)
        
        if (response.code === 200) {
          await this.processPayment(response.data)
        } else {
          throw new Error(response.message || '支付失败')
        }
      } catch (error) {
        console.error('支付失败:', error)
        this.$emit('payError', error)
        uni.showToast({
          title: error.message || '支付失败',
          icon: 'none'
        })
      } finally {
        this.paying = false
        this.$emit('payEnd')
      }
    },

    // 处理支付流程
    async processPayment(payData) {
      if (this.selectedPayType === 1) {
        // 微信支付
        await this.handleWechatPayment(payData)
      } else if (this.selectedPayType === 2) {
        // 支付宝支付
        await this.handleAlipayPayment(payData)
      } else if (this.selectedPayType === 3) {
        // 余额支付
        this.paySuccess()
      }
    },

    // 微信支付处理
    async handleWechatPayment(payData) {
      const wechatData = payData.data || payData
      
      uni.requestPayment({
        provider: 'wxpay',
        appId: wechatData.appId,
        timeStamp: wechatData.timeStamp,
        nonceStr: wechatData.nonceStr,
        package: wechatData.package,
        signType: wechatData.signType,
        paySign: wechatData.paySign,
        success: () => this.paySuccess(),
        fail: (err) => {
          console.error('微信支付失败:', err)
          this.$emit('payError', err)
          uni.showToast({ title: '支付失败', icon: 'none' })
        }
      })
    },

    // 支付宝支付处理
    async handleAlipayPayment(payData) {
      uni.requestPayment({
        provider: 'alipay',
        orderInfo: payData.orderInfo,
        success: () => this.paySuccess(),
        fail: (err) => {
          console.error('支付宝支付失败:', err)
          this.$emit('payError', err)
          uni.showToast({ title: '支付失败', icon: 'none' })
        }
      })
    },

    // 支付成功
    paySuccess() {
      uni.showToast({
        title: '支付成功',
        icon: 'success'
      })
      
      // 更新余额（如果是余额支付）
      if (this.selectedPayType === 3) {
        this.userBalance -= this.amount
      }
      
      this.$emit('paySuccess', {
        payType: this.selectedPayType,
        amount: this.amount
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.payment-handler {
  .payment-methods {
    background: #fff;
    margin: 15px;
    border-radius: 12px;
    padding: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 15px;
    }
    
    .method-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      &.active {
        .method-name {
          color: #FFB6C1;
        }
        .check-icon {
          color: #FFB6C1;
        }
      }
      
      .method-info {
        display: flex;
        align-items: center;
        
        .method-icon {
          width: 24px;
          height: 24px;
          margin-right: 12px;
        }
        
        .method-name {
          font-size: 16px;
          color: #333;
          margin-right: 8px;
        }
        
        .balance-info {
          font-size: 12px;
          color: #666;
        }
      }
      
      .check-icon {
        font-size: 18px;
        font-weight: 600;
      }
    }
  }
  
  .payment-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 15px;
    border-top: 1px solid #f0f0f0;
    
    .pay-btn {
      width: 100%;
      height: 50px;
      background: linear-gradient(135deg, #FFB6C1, #FF91A4);
      color: #fff;
      border: none;
      border-radius: 25px;
      font-size: 16px;
      font-weight: 600;
      
      &.disabled {
        background: #ccc;
        color: #999;
      }
      
      &.loading {
        background: #ddd;
      }
    }
  }
}
</style>
