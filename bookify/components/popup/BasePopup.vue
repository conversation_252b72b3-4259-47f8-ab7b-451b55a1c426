<template>
  <view class="popup-mask" v-if="isVisible" @click="handleMaskClick">
    <view class="popup-container" @click.stop>
      <view class="popup-content">
        <!-- 标题区域 -->
        <view 
          v-if="currentPopup.title" 
          class="popup-title"
          :style="titleStyle"
        >
          {{ currentPopup.title }}
        </view>
        
        <!-- 内容区域 -->
        <view v-if="currentPopup.content" class="popup-body">
          <text class="popup-text">{{ currentPopup.content }}</text>
        </view>
        
        <!-- 自动关闭倒计时 -->
        <view v-if="autoCloseSeconds > 0" class="popup-countdown">
          {{ autoCloseSeconds }}秒后自动关闭
        </view>
        
        <!-- 底部按钮区域 -->
        <view v-if="currentPopup.buttons && currentPopup.buttons.length > 0" class="popup-footer">
          <button 
            v-for="(button, index) in currentPopup.buttons" 
            :key="index"
            :class="['popup-button', `popup-button-${button.type || 'default'}`]"
            :style="button.style || {}"
            @click="handleButtonClick(button)"
          >
            {{ button.text }}
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { usePopupStore } from '@/stores/popup'

export default {
  name: 'BasePopup',
  data() {
    return {
      autoCloseTimer: null,
      autoCloseSeconds: 0
    }
  },
  computed: {
    popupStore() {
      return usePopupStore()
    },
    isVisible() {
      return this.popupStore.isVisible
    },
    currentPopup() {
      return this.popupStore.currentPopup || {}
    },
    titleStyle() {
      if (!this.currentPopup.titleStyle) return {}
      
      const style = {
        color: this.currentPopup.titleStyle.color || '#333',
        background: this.currentPopup.titleStyle.backgroundColor || '#fff'
      }
      
      if (this.currentPopup.titleStyle.backgroundImage) {
        style.backgroundImage = `url(${this.currentPopup.titleStyle.backgroundImage})`
        style.backgroundSize = 'cover'
        style.backgroundPosition = 'center'
        style.backgroundRepeat = 'no-repeat'
      }
      
      return style
    }
  },
  watch: {
    isVisible(newVal) {
      if (newVal && this.currentPopup.autoClose > 0) {
        this.startAutoCloseTimer()
      } else {
        this.stopAutoCloseTimer()
      }
    }
  },
  methods: {
    handleMaskClick() {
      if (this.currentPopup.maskClosable !== false) {
        this.popupStore.closePopup()
      }
    },
    
    handleButtonClick(button) {
      if (button.onClick) {
        button.onClick()
      } else {
        this.popupStore.closePopup()
      }
    },
    
    startAutoCloseTimer() {
      this.stopAutoCloseTimer()
      this.autoCloseSeconds = Math.ceil(this.currentPopup.autoClose / 1000)
      
      this.autoCloseTimer = setInterval(() => {
        if (this.autoCloseSeconds > 0) {
          this.autoCloseSeconds--
        } else {
          this.popupStore.closePopup()
        }
      }, 1000)
    },
    
    stopAutoCloseTimer() {
      if (this.autoCloseTimer) {
        clearInterval(this.autoCloseTimer)
        this.autoCloseTimer = null
      }
      this.autoCloseSeconds = 0
    }
  },
  
  beforeDestroy() {
    this.stopAutoCloseTimer()
  }
}
</script>

<style scoped>
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

.popup-container {
  background: white;
  border-radius: 12px;
  max-width: 80%;
  min-width: 280px;
  max-height: 80%;
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.popup-content {
  display: flex;
  flex-direction: column;
}

.popup-title {
  padding: 20px 20px 15px;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  min-height: 20px;
}

.popup-body {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
}

.popup-text {
  font-size: 16px;
  line-height: 1.5;
  color: #666;
  text-align: center;
}

.popup-countdown {
  padding: 10px 20px;
  text-align: center;
  font-size: 14px;
  color: #999;
  background: #f8f8f8;
  border-top: 1px solid #f0f0f0;
}

.popup-footer {
  padding: 15px 20px 20px;
  display: flex;
  gap: 12px;
  justify-content: center;
  border-top: 1px solid #f0f0f0;
}

.popup-button {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 80px;
}

.popup-button-default {
  background: #f5f5f5;
  color: #666;
}

.popup-button-default:hover {
  background: #e8e8e8;
}

.popup-button-primary {
  background: #007aff;
  color: white;
}

.popup-button-primary:hover {
  background: #0056cc;
}

.popup-button-success {
  background: #28a745;
  color: white;
}

.popup-button-success:hover {
  background: #218838;
}

.popup-button-warning {
  background: #ffc107;
  color: #212529;
}

.popup-button-warning:hover {
  background: #e0a800;
}

.popup-button-danger {
  background: #dc3545;
  color: white;
}

.popup-button-danger:hover {
  background: #c82333;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    transform: scale(0.7) translateY(-50px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}
</style> 