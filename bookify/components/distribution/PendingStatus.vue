<template>
	<view class="pending-status">
		<view class="status-card">
			<view class="status-content">
				<view class="status-icon">
					<image src="/static/images/pending.png" mode="aspectFit" v-if="false" />
					<text class="icon-text">⏳</text>
				</view>
				<view class="status-text">
					<text class="status-title">申请审核中</text>
					<text class="status-desc">您的分销申请正在审核中，请耐心等待</text>
				</view>
			</view>
			
			<!-- 申请信息展示 -->
			<view class="apply-info" v-if="applicationData">
				<view class="info-title">申请信息</view>
				<view class="info-list">
					<view class="info-item">
						<text class="info-label">真实姓名：</text>
						<text class="info-value">{{ applicationData.realName || '-' }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">手机号码：</text>
						<text class="info-value">{{ formatPhone(applicationData.phone) || '-' }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">身份证号：</text>
						<text class="info-value">{{ formatIdCard(applicationData.idCard) || '-' }}</text>
					</view>
					<view class="info-item" v-if="applicationData.remark">
						<text class="info-label">申请理由：</text>
						<text class="info-value">{{ applicationData.remark }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">申请时间：</text>
						<text class="info-value">{{ formatTime(applicationData.applyTime) || '-' }}</text>
					</view>
				</view>
			</view>
			
			<!-- 审核进度 -->
			<view class="progress-section">
				<view class="progress-title">审核进度</view>
				<view class="progress-steps">
					<view class="step-item active">
						<view class="step-icon">✓</view>
						<view class="step-text">
							<text class="step-title">提交申请</text>
							<text class="step-time">{{ formatTime(applicationData?.applyTime) }}</text>
						</view>
					</view>
					<view class="step-line active"></view>
					<view class="step-item current">
						<view class="step-icon">⏳</view>
						<view class="step-text">
							<text class="step-title">审核中</text>
							<text class="step-time">预计1-3个工作日</text>
						</view>
					</view>
					<view class="step-line"></view>
					<view class="step-item">
						<view class="step-icon">○</view>
						<view class="step-text">
							<text class="step-title">审核完成</text>
							<text class="step-time">等待中</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 温馨提示 -->
			<view class="tips-section">
				<view class="tips-title">温馨提示</view>
				<view class="tips-list">
					<view class="tip-item">• 审核时间通常为1-3个工作日</view>
					<view class="tip-item">• 请确保提交的信息真实有效</view>
					<view class="tip-item">• 如有疑问可联系客服咨询</view>
					<view class="tip-item">• 审核结果将通过站内消息通知</view>
				</view>
			</view>
			
			<!-- 操作按钮 -->
			<view class="action-buttons">
				<button class="btn-secondary" @click="goBack">返回首页</button>
				<button class="btn-primary" @click="contactService">联系客服</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'PendingStatus',
	props: {
		// 申请数据
		applicationData: {
			type: Object,
			default: () => ({})
		}
	},
	
	methods: {
		// 格式化手机号（中间4位用*代替）
		formatPhone(phone) {
			if (!phone) return ''
			return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
		},
		
		// 格式化身份证号（中间部分用*代替）
		formatIdCard(idCard) {
			if (!idCard) return ''
			if (idCard.length === 18) {
				return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
			} else if (idCard.length === 15) {
				return idCard.replace(/(\d{6})\d{6}(\d{3})/, '$1******$2')
			}
			return idCard
		},
		
		// 格式化时间
		formatTime(time) {
			if (!time) return ''
			const date = new Date(time)
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			const hour = String(date.getHours()).padStart(2, '0')
			const minute = String(date.getMinutes()).padStart(2, '0')
			return `${year}-${month}-${day} ${hour}:${minute}`
		},
		
		// 返回首页
		goBack() {
			uni.switchTab({
				url: '/pages/index/index'
			})
		},
		
		// 联系客服
		contactService() {
			// 这里可以根据实际情况实现客服联系方式
			uni.showModal({
				title: '联系客服',
				content: '客服电话：400-123-4567\n工作时间：9:00-18:00',
				showCancel: false,
				confirmText: '知道了'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.pending-status {
	padding: 20rpx;
}

.status-card {
	background: #fff;
	border-radius: 16rpx;
	padding: 32rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.status-content {
	display: flex;
	align-items: center;
	margin-bottom: 40rpx;
	
	.status-icon {
		width: 80rpx;
		height: 80rpx;
		background: linear-gradient(135deg, #FFA726 0%, #FF9800 100%);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 24rpx;
		
		.icon-text {
			font-size: 36rpx;
			color: #fff;
		}
		
		image {
			width: 48rpx;
			height: 48rpx;
		}
	}
	
	.status-text {
		flex: 1;
		
		.status-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #333;
			display: block;
			margin-bottom: 8rpx;
		}
		
		.status-desc {
			font-size: 28rpx;
			color: #666;
			display: block;
		}
	}
}

.apply-info {
	margin-bottom: 40rpx;
	
	.info-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 20rpx;
	}
	
	.info-list {
		background: #f8f9fa;
		border-radius: 12rpx;
		padding: 24rpx;
		
		.info-item {
			display: flex;
			margin-bottom: 16rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.info-label {
				font-size: 28rpx;
				color: #666;
				width: 160rpx;
				flex-shrink: 0;
			}
			
			.info-value {
				font-size: 28rpx;
				color: #333;
				flex: 1;
				word-break: break-all;
			}
		}
	}
}

.progress-section {
	margin-bottom: 40rpx;
	
	.progress-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 24rpx;
	}
	
	.progress-steps {
		position: relative;
		
		.step-item {
			display: flex;
			align-items: flex-start;
			position: relative;
			z-index: 2;
			
			&.active .step-icon {
				background: #4CAF50;
				color: #fff;
			}
			
			&.current .step-icon {
				background: #FF9800;
				color: #fff;
			}
			
			.step-icon {
				width: 48rpx;
				height: 48rpx;
				border-radius: 50%;
				background: #e0e0e0;
				color: #999;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 24rpx;
				margin-right: 16rpx;
				flex-shrink: 0;
			}
			
			.step-text {
				flex: 1;
				padding-bottom: 32rpx;
				
				.step-title {
					font-size: 28rpx;
					color: #333;
					font-weight: 500;
					display: block;
					margin-bottom: 4rpx;
				}
				
				.step-time {
					font-size: 24rpx;
					color: #999;
					display: block;
				}
			}
		}
		
		.step-line {
			position: absolute;
			left: 24rpx;
			top: 48rpx;
			width: 2rpx;
			height: 32rpx;
			background: #e0e0e0;
			z-index: 1;
			
			&.active {
				background: #4CAF50;
			}
		}
	}
}

.tips-section {
	margin-bottom: 40rpx;
	
	.tips-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 20rpx;
	}
	
	.tips-list {
		background: #fff3e0;
		border-radius: 12rpx;
		padding: 24rpx;
		border-left: 4rpx solid #FF9800;
		
		.tip-item {
			font-size: 28rpx;
			color: #666;
			line-height: 1.6;
			margin-bottom: 8rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
		}
	}
}

.action-buttons {
	display: flex;
	gap: 20rpx;
	
	button {
		flex: 1;
		height: 80rpx;
		border: none;
		border-radius: 40rpx;
		font-size: 30rpx;
		font-weight: 500;
	}
	
	.btn-secondary {
		background: #f5f5f5;
		color: #666;
	}
	
	.btn-primary {
		background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
		color: #fff;
	}
}
</style> 