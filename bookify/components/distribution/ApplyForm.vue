<template>
	<view class="apply-form">
		<view class="form-card">
			<view class="form-header">
				<text class="form-title">申请信息</text>
				<text class="form-subtitle">请如实填写以下信息</text>
			</view>

			<form @submit.prevent="handleSubmit">
				<view class="form-group">
					<text class="form-label">真实姓名 *</text>
					<view class="input-wrapper">
						<input 
							class="form-input" 
							:class="{ 'error': errors.realName }"
							type="text" 
							v-model="formData.realName"
							placeholder="请输入真实姓名"
							maxlength="20"
							@blur="validateRealName"
						/>
					</view>
					<text class="error-text" v-if="errors.realName">{{ errors.realName }}</text>
				</view>

				<view class="form-group">
					<text class="form-label">手机号码 *</text>
					<view class="input-wrapper">
						<input 
							class="form-input" 
							:class="{ 'error': errors.phone }"
							type="tel" 
							v-model="formData.phone"
							placeholder="请输入手机号码"
							maxlength="11"
							@blur="validatePhone"
						/>
					</view>
					<text class="error-text" v-if="errors.phone">{{ errors.phone }}</text>
				</view>

				<view class="form-group">
					<text class="form-label">身份证号 *</text>
					<view class="input-wrapper">
						<input 
							class="form-input" 
							:class="{ 'error': errors.idCard }"
							type="text" 
							v-model="formData.idCard"
							placeholder="请输入身份证号码"
							maxlength="18"
							@blur="validateIdCard"
						/>
					</view>
					<text class="error-text" v-if="errors.idCard">{{ errors.idCard }}</text>
				</view>

				<view class="form-group">
					<text class="form-label">申请理由</text>
					<view class="textarea-wrapper">
						<textarea 
							class="form-textarea" 
							v-model="formData.remark"
							placeholder="请简单介绍您的推广渠道或申请原因（选填）"
							maxlength="200"
							auto-height
						></textarea>
					</view>
					<text class="char-count">{{ formData.remark.length }}/200</text>
				</view>

				<view class="form-agreement">
					<label class="agreement-item" @click="toggleAgreement">
						<checkbox 
							:checked="agreed" 
							color="#FF6B35"
						/>
						<text class="agreement-text">我已阅读并同意</text>
						<text class="agreement-link" @click.stop="showAgreement">《分销协议》</text>
					</label>
				</view>

				<button 
					class="submit-btn" 
					:class="{ disabled: !canSubmit, loading: submitting }"
					:disabled="!canSubmit || submitting"
					@click="handleSubmit"
				>
					<text v-if="submitting">提交中...</text>
					<text v-else>{{ isReapply ? '重新提交申请' : '提交申请' }}</text>
				</button>
			</form>
		</view>

		<!-- 分销规则弹窗 -->
		<uni-popup ref="rulesPopup" type="center" :is-mask-click="false">
			<view class="rules-popup">
				<view class="rules-header">
					<view class="rules-title">分销协议</view>
					<view class="close-btn" @click="closeRulesPopup">✕</view>
				</view>
				<scroll-view class="rules-content" scroll-y>
					<view class="rules-section">
						<view class="section-title">一、分销员权益</view>
						<view class="section-content">
							<view class="content-item">1. 享受推广商品{{ config.level1_rate }}%的佣金收益</view>
							<view class="content-item">2. 发展下级分销员可获得{{ config.level2_rate }}%的二级佣金</view>
							<view class="content-item" v-if="config.register_enabled">3. 新用户注册奖励{{ config.register_amount }}元（如开启）</view>
							<view class="content-item">4. 专业的数据统计和管理工具</view>
						</view>
					</view>
					
					<view class="rules-section">
						<view class="section-title">二、提现规则</view>
						<view class="section-content">
							<view class="content-item">1. 佣金冻结期：{{ config.freeze_days }}天</view>
							<view class="content-item">2. 最低提现金额：{{ config.min_withdraw }}元</view>
							<view class="content-item">3. 最高单次提现：{{ config.max_withdraw }}元</view>
							<view class="content-item">4. 提现手续费：{{ config.withdraw_fee_rate }}%</view>
							<view class="content-item">5. 支持提现方式：{{ withdrawMethods }}</view>
						</view>
					</view>
					
					<view class="rules-section">
						<view class="section-title">三、申请条件</view>
						<view class="section-content">
							<view class="content-item">1. 需提供真实有效的个人信息</view>
							<view class="content-item">2. 手机号码需完成实名认证</view>
							<view class="content-item">3. 身份证信息需真实有效</view>
							<view class="content-item">4. 遵守平台相关规定和法律法规</view>
						</view>
					</view>
					
					<view class="rules-section">
						<view class="section-title">四、分销规范</view>
						<view class="section-content">
							<view class="content-item">1. 不得进行虚假宣传或误导消费者</view>
							<view class="content-item">2. 不得恶意刷单或进行欺诈行为</view>
							<view class="content-item">3. 不得在违法违规平台进行推广</view>
							<view class="content-item">4. 违规行为将导致分销资格取消</view>
						</view>
					</view>
					
					<view class="rules-section">
						<view class="section-title">五、其他条款</view>
						<view class="section-content">
							<view class="content-item">1. 平台保留最终解释权</view>
							<view class="content-item">2. 如有争议，以平台最新规则为准</view>
							<view class="content-item">3. 分销员需配合平台的审核和管理</view>
							<view class="content-item">4. 平台有权随时调整分销政策</view>
						</view>
					</view>
				</scroll-view>
				<view class="rules-footer">
					<button class="agree-btn" @click="agreeRules">同意并关闭</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import { applyDistributor } from '@/api/distribution'

export default {
	name: 'ApplyForm',
	props: {
		// 分销配置
		config: {
			type: Object,
			default: () => ({
				enabled: true,
				register_enabled: true,
				register_amount: 5,
				purchase_enabled: true,
				level1_rate: 10,
				level2_rate: 5,
				freeze_days: 7,
				min_withdraw: 10,
				max_withdraw: 5000,
				withdraw_fee_rate: 0,
				wechat_withdraw: true,
				alipay_withdraw: false
			})
		},
		// 是否为重新申请
		isReapply: {
			type: Boolean,
			default: false
		},
		// 初始表单数据（用于重新申请时回填）
		initialData: {
			type: Object,
			default: () => ({
				realName: '',
				phone: '',
				idCard: '',
				remark: ''
			})
		}
	},
	
	data() {
		return {
			// 表单数据
			formData: {
				realName: '',
				phone: '',
				idCard: '',
				remark: ''
			},
			
			// 表单验证错误
			errors: {
				realName: '',
				phone: '',
				idCard: ''
			},
			
			// 协议同意状态
			agreed: false,
			
			// 提交状态
			submitting: false
		}
	},
	
	computed: {
		canSubmit() {
			return this.formData.realName.trim() && 
				   this.formData.phone.trim() && 
				   this.formData.idCard.trim() && 
				   this.agreed &&
				   !this.errors.realName &&
				   !this.errors.phone &&
				   !this.errors.idCard
		},
		
		withdrawMethods() {
			const methods = []
			if (this.config.wechat_withdraw) methods.push('微信')
			if (this.config.alipay_withdraw) methods.push('支付宝')
			return methods.join('、') || '暂无'
		}
	},
	
	watch: {
		initialData: {
			handler(newData) {
				if (newData) {
					this.formData = { ...this.formData, ...newData }
				}
			},
			immediate: true,
			deep: true
		}
	},
	
	methods: {
		// 验证真实姓名
		validateRealName() {
			if (!this.formData.realName.trim()) {
				this.errors.realName = '请输入真实姓名'
			} else if (this.formData.realName.length < 2) {
				this.errors.realName = '姓名至少2个字符'
			} else {
				this.errors.realName = ''
			}
		},
		
		// 验证手机号
		validatePhone() {
			if (!this.formData.phone.trim()) {
				this.errors.phone = '请输入手机号码'
			} else if (!this.isValidPhone(this.formData.phone)) {
				this.errors.phone = '请输入正确的手机号码'
			} else {
				this.errors.phone = ''
			}
		},
		
		// 验证身份证号
		validateIdCard() {
			if (!this.formData.idCard.trim()) {
				this.errors.idCard = '请输入身份证号码'
			} else if (!this.isValidIdCard(this.formData.idCard)) {
				this.errors.idCard = '请输入正确的身份证号码'
			} else {
				this.errors.idCard = ''
			}
		},
		
		// 手机号验证
		isValidPhone(phone) {
			return /^1[3-9]\d{9}$/.test(phone)
		},
		
		// 身份证号验证
		isValidIdCard(idCard) {
			return /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(idCard)
		},
		
		// 切换协议同意状态
		toggleAgreement() {
			this.agreed = !this.agreed
		},
		
		// 显示协议
		showAgreement() {
			this.$refs.rulesPopup.open()
		},
		
		// 同意协议
		agreeRules() {
			this.agreed = true
			this.closeRulesPopup()
		},
		
		// 关闭协议弹窗
		closeRulesPopup() {
			this.$refs.rulesPopup.close()
		},
		
		// 提交申请
		async handleSubmit() {
			if (!this.canSubmit || this.submitting) return
			
			// 重新验证所有字段
			this.validateRealName()
			this.validatePhone()
			this.validateIdCard()
			
			if (!this.canSubmit) return
			
			try {
				this.submitting = true
				
				const { code, message } = await applyDistributor(this.formData)
				
				if (code === 200) {
					uni.showToast({
						title: '申请提交成功',
						icon: 'success'
					})
					
					// 通知父组件申请成功
					this.$emit('apply-success')
				} else {
					uni.showToast({
						title: message || '申请失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('申请失败:', error)
				uni.showToast({
					title: '申请失败，请重试',
					icon: 'none'
				})
			} finally {
				this.submitting = false
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.apply-form {
	padding: 20rpx;
}

.form-card {
	background: #fff;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.form-header {
	text-align: center;
	margin-bottom: 40rpx;
	
	.form-title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
		display: block;
		margin-bottom: 12rpx;
	}
	
	.form-subtitle {
		font-size: 28rpx;
		color: #666;
		display: block;
	}
}

.form-group {
	margin-bottom: 32rpx;
	
	.form-label {
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
		display: block;
		margin-bottom: 16rpx;
	}
	
	.input-wrapper, .textarea-wrapper {
		position: relative;
	}
	
	.form-input {
		width: 100%;
		height: 88rpx;
		background: #f8f9fa;
		border: 2rpx solid #e9ecef;
		border-radius: 12rpx;
		padding: 0 24rpx;
		font-size: 30rpx;
		color: #333;
		box-sizing: border-box;
		
		&:focus {
			border-color: #FF6B35;
			background: #fff;
		}
		
		&.error {
			border-color: #ff4757;
			background: #fff5f5;
		}
	}
	
	.form-textarea {
		width: 100%;
		min-height: 120rpx;
		background: #f8f9fa;
		border: 2rpx solid #e9ecef;
		border-radius: 12rpx;
		padding: 20rpx 24rpx;
		font-size: 30rpx;
		color: #333;
		box-sizing: border-box;
		
		&:focus {
			border-color: #FF6B35;
			background: #fff;
		}
	}
	
	.char-count {
		font-size: 24rpx;
		color: #999;
		text-align: right;
		margin-top: 8rpx;
		display: block;
	}
	
	.error-text {
		font-size: 24rpx;
		color: #ff4757;
		margin-top: 8rpx;
		display: block;
	}
}

.form-agreement {
	margin-bottom: 40rpx;
	
	.agreement-item {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		
		checkbox {
			margin-right: 16rpx;
		}
		
		.agreement-text {
			color: #666;
		}
		
		.agreement-link {
			color: #FF6B35;
			text-decoration: underline;
		}
	}
}

.submit-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
	border: none;
	border-radius: 44rpx;
	color: #fff;
	font-size: 32rpx;
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: center;
	
	&.disabled {
		background: #ccc;
		color: #999;
	}
	
	&.loading {
		background: #ccc;
	}
}

// 规则弹窗样式
.rules-popup {
	width: 680rpx;
	max-height: 80vh;
	background: #fff;
	border-radius: 16rpx;
	overflow: hidden;
}

.rules-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx;
	border-bottom: 1rpx solid #eee;
	
	.rules-title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
	}
	
	.close-btn {
		width: 48rpx;
		height: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		color: #999;
	}
}

.rules-content {
	max-height: 60vh;
	padding: 32rpx;
}

.rules-section {
	margin-bottom: 32rpx;
	
	.section-title {
		font-size: 30rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 16rpx;
	}
	
	.section-content {
		.content-item {
			font-size: 28rpx;
			color: #666;
			line-height: 1.6;
			margin-bottom: 12rpx;
		}
	}
}

.rules-footer {
	padding: 32rpx;
	border-top: 1rpx solid #eee;
	
	.agree-btn {
		width: 100%;
		height: 80rpx;
		background: #FF6B35;
		border: none;
		border-radius: 40rpx;
		color: #fff;
		font-size: 30rpx;
		font-weight: 600;
	}
}
</style> 