<template>
	<view class="page-layout" :class="themeClass">
		<!-- 自定义导航栏 -->
		<CustomNavBar 
			v-if="showNavBar"
			ref="navBar"
			:title="navTitle"
			:show-left="showLeft"
			:show-center="showCenter"
			:show-right="showRight"
			:text-color="navTextColor"
			:transparent="navTransparent"
			:fixed="navFixed"
			@leftClick="handleNavLeftClick"
			@rightClick="handleNavRightClick"
		/>
		
		<!-- 页面内容 -->
		<view class="page-content" :style="computedContentStyle">
			<slot></slot>
		</view>
		
		<!-- 弹窗组件 -->
		<BasePopup />
	</view>
</template>

<script>
import CustomNavBar from './CustomNavBar.vue'
import BasePopup from '@/components/popup/BasePopup.vue'
import { usePopupStore } from '@/stores/popup'

export default {
	name: 'PageLayout',
	components: {
		CustomNavBar,
		BasePopup
	},
	props: {
		// 主题类型
		theme: {
			type: String,
			default: 'default', // default, dark, light
			validator: value => ['default', 'dark', 'light'].includes(value)
		},
		// 内容区域样式
		contentStyle: {
			type: Object,
			default: () => ({})
		},
		// 是否全屏
		fullscreen: {
			type: Boolean,
			default: false
		},
		// 是否显示导航栏
		showNavBar: {
			type: Boolean,
			default: true
		},
		// 导航栏标题
		navTitle: {
			type: String,
			default: ''
		},
		// 导航栏相关属性
		showLeft: {
			type: Boolean,
			default: true
		},
		showCenter: {
			type: Boolean,
			default: true
		},
		showRight: {
			type: Boolean,
			default: false
		},
		navTextColor: {
			type: String,
			default: '#333'
		},
		navTransparent: {
			type: Boolean,
			default: true
		},
		// 导航栏是否固定定位
		navFixed: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			popupStore: null
		}
	},
	mounted() {
		// 初始化弹窗Store
		this.popupStore = usePopupStore()
	},
	methods: {
		
		// 导航栏左侧点击事件
		handleNavLeftClick() {
			this.$emit('navLeftClick')
		},
		// 导航栏右侧点击事件
		handleNavRightClick() {
			this.$emit('navRightClick')
		}
	},
	computed: {
		themeClass() {
			return `theme-${this.theme}`
		},
		computedContentStyle() {
			const style = { ...this.contentStyle }
			
			// 如果显示导航栏且是固定定位，需要添加顶部间距避免被遮挡
			if (this.showNavBar && this.navFixed) {
				const systemInfo = uni.getSystemInfoSync()
				const statusBarHeight = systemInfo.statusBarHeight || 20
				const navBarHeight = 44 // 导航栏高度
				const totalHeight = statusBarHeight + navBarHeight
				
				// 透明导航栏和非透明导航栏需要区别处理
				// 透明导航栏：内容可以延伸到导航栏下方，不需要padding
				// 非透明导航栏：内容应该在导航栏下方开始，需要预留空间
				if (!style.paddingTop && !this.navTransparent) {
					// 只有非透明导航栏才需要预留空间
					style.paddingTop = totalHeight + 'px'
				}
			}
			
			return style
		}
	}
}
</script>

<style lang="scss" scoped>
.page-layout {
	min-height: 100vh;
	
	&.theme-default {
		// 默认主题样式
	}
	
	&.theme-dark {
		// 深色主题样式
		background-color: #1a1a1a;
		color: #fff;
	}
	
	&.theme-light {
		// 浅色主题样式
		background-color: #fff;
		color: #333;
	}
}

.page-content {
	position: relative;
	z-index: 1;
	min-height: 100vh;
}


</style> 