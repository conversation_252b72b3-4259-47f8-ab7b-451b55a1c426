<template>
	<view class="custom-nav-bar" :class="themeClass" :style="navBarStyle">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>
		
		<!-- 导航栏内容 -->
		<view class="nav-content" :style="{height: navBarHeight + 'px'}">
			<!-- 左侧内容 -->
			<view class="nav-left" v-if="showLeft">
				<view class="nav-btn back-btn" @click="handleLeftClick">
					<uni-icons type="left" :size="20" :color="textColor"></uni-icons>
					</view>
			</view>
			
			<!-- 中间标题 -->
			<view class="nav-center" v-if="showCenter">
				<text class="nav-title" v-if="title" :style="{color: textColor}">{{title}}</text>
			</view>
			
			<!-- 右侧内容 -->
			<view class="nav-right" v-if="showRight">
				<view class="nav-btn more-btn" @click="handleRightClick">
					<uni-icons type="more" :size="20" :color="textColor"></uni-icons>
					</view>
			</view>
		</view>
	</view>
</template>

<script>
import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue'

export default {
	name: 'CustomNavBar',
	components: {
		'uni-icons': uniIcons
	},
	props: {
		// 标题
		title: {
			type: String,
			default: ''
		},
		// 是否显示左侧区域
		showLeft: {
			type: Boolean,
			default: true
		},
		// 是否显示中间区域
		showCenter: {
			type: Boolean,
			default: true
		},
		// 是否显示右侧区域
		showRight: {
			type: Boolean,
			default: false
		},
		// 文字颜色
		textColor: {
			type: String,
			default: '#333'
		},
		// 是否透明
		transparent: {
			type: Boolean,
			default: false
		},
		// 是否固定定位（不占用文档流高度）
		fixed: {
			type: Boolean,
			default: true
		}
	},
	data() {
		return {
			statusBarHeight: 0,
			navBarHeight: 44
		}
	},
	computed: {
		themeClass() {
			let classes = []
			if (this.transparent) {
				classes.push('nav-transparent')
			}
			return classes.join(' ')
		},
		navBarStyle() {
			const style = {
				top: '0',
				left: '0',
				right: '0',
				zIndex: '9999'
			}
			
			// 根据fixed属性决定定位方式
			if (this.fixed) {
				style.position = 'fixed'
			} else {
				style.position = 'relative'
			}
			
			if (this.transparent) {
				style.background = 'transparent'
			} else {
				style.background = '#fff'
			}
			return style
		},
		
		// 计算导航栏总高度（状态栏 + 导航栏）
		totalNavHeight() {
			return this.statusBarHeight + this.navBarHeight
		}
	},
	mounted() {
		this.getSystemInfo()
	},
	methods: {
		// 获取系统信息
		getSystemInfo() {
			const systemInfo = uni.getSystemInfoSync()
			this.statusBarHeight = systemInfo.statusBarHeight || 20
			
			// 不同平台的导航栏高度
			// #ifdef MP-WEIXIN
			this.navBarHeight = 44
			// #endif
			// #ifdef MP-ALIPAY
			this.navBarHeight = 48
			// #endif
			// #ifdef H5
			this.navBarHeight = 44
			// #endif
			// #ifdef APP-PLUS
			this.navBarHeight = 44
			// #endif
		},
		// 左侧点击事件
		handleLeftClick() {
			this.$emit('leftClick')
			// 默认返回上一页
			const pages = getCurrentPages()
			if (pages.length > 1) {
				uni.navigateBack()
			} else {
			uni.switchTab({
				url: '/pages/beauty/index/index'
			})
			}
		},
		// 右侧点击事件
		handleRightClick() {
			this.$emit('rightClick')
		},
		
		// 获取导航栏总高度
		getNavBarHeight() {
			return this.totalNavHeight
		}
	}
}
</script>

<style lang="scss" scoped>
.custom-nav-bar {
	background: #fff;
	transition: all 0.3s ease;
	border-bottom: 1rpx solid #f0f0f0;
	/* 调试样式 - 让导航栏更明显 */
	// 透明导航栏样式
	&.nav-transparent {
		border-bottom: none;
		background: transparent !important;
		
		.nav-content {
			background: transparent;
		}
		
		.status-bar {
			background: transparent;
		}
		
		.nav-btn {
			background: transparent;
			border: none;
			
			&:active {
				background: rgba(255, 255, 255, 0.2);
				transform: scale(0.95);
			}
		}
	}
	
	// 非透明模式样式
	&:not(.nav-transparent) {
		.nav-btn {
			background: rgba(190, 190, 190, 0.1);
			backdrop-filter: blur(10px);
			border: 1rpx solid rgba(156, 156, 156, 0.3);
			
			&:active {
				background: rgba(255, 255, 255, 0.9);
				transform: scale(0.95);
			}
		}
		
		.nav-title {
		color: #333;
			text-shadow: none;
			font-weight: 600;
		}
	}
}

.nav-content {
	display: flex;
	align-items: center;
	padding: 0 20rpx;
	position: relative;
	height: 100%;
}

.nav-left {
	display: flex;
	align-items: center;
	width: 120rpx;
	height: 100%;
	justify-content: flex-start;
}

.nav-right {
	display: flex;
	align-items: center;
	width: 120rpx;
	height: 100%;
	justify-content: flex-end;
}

.nav-center {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	display: flex;
	align-items: center;
	justify-content: center;
	max-width: calc(100% - 280rpx);
}

.nav-title {
	font-size: 32rpx;
	font-weight: 600;
	text-align: center;
	max-width: 400rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.nav-btn {
	width: 64rpx;
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	margin-right: 16rpx;
	transition: all 0.3s ease;
	position: relative;
	
	&:last-child {
		margin-right: 0;
	}
}


</style> 