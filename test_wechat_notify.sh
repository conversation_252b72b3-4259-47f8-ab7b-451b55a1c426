#!/bin/bash

# 微信支付回调测试脚本
# 使用方法: ./test_wechat_notify.sh <order_no> <transaction_id>

ORDER_NO=${1:-"***************"}
TRANSACTION_ID=${2:-"****************"}

echo "测试微信支付回调..."
echo "订单号: $ORDER_NO"
echo "交易号: $TRANSACTION_ID"

# 生成模拟的微信支付回调XML数据
XML_DATA=$(cat <<EOF
<xml>
<appid><![CDATA[wx58459973f280f050]]></appid>
<bank_type><![CDATA[OTHERS]]></bank_type>
<cash_fee><![CDATA[34900]]></cash_fee>
<fee_type><![CDATA[CNY]]></fee_type>
<is_subscribe><![CDATA[N]]></is_subscribe>
<mch_id><![CDATA[**********]]></mch_id>
<nonce_str><![CDATA[$(openssl rand -hex 16)]]></nonce_str>
<openid><![CDATA[oUpF8uMuAJO_M2pxb1Q9zNjWeS6o]]></openid>
<out_trade_no><![CDATA[$ORDER_NO]]></out_trade_no>
<result_code><![CDATA[SUCCESS]]></result_code>
<return_code><![CDATA[SUCCESS]]></return_code>
<time_end><![CDATA[$(date +%s)]]></time_end>
<total_fee><![CDATA[34900]]></total_fee>
<trade_type><![CDATA[JSAPI]]></trade_type>
<transaction_id><![CDATA[$TRANSACTION_ID]]></transaction_id>
<sign><![CDATA[A40FA0C0606ECA96910E6531BF4E5716]]></sign>
</xml>
EOF
)

echo "发送回调数据到: http://localhost:8081/api/payment/wx/notify"
echo "回调数据:"
echo "$XML_DATA"
echo ""

# 发送POST请求
curl -X POST \
  -H "Content-Type: application/xml" \
  -d "$XML_DATA" \
  "http://localhost:8081/api/payment/wx/notify"

echo ""
echo "测试完成！" 