# 弹窗功能测试说明

## 1. 数据库准备

### 1.1 创建表结构和示例数据
执行迁移文件创建弹窗表和示例数据：

```bash
# 执行迁移文件
mysql -h 127.0.0.1 -u root -p123456 < migrations/20250113_create_popup_tables.sql

# 或者直接在数据库管理工具中执行migrations/20250113_create_popup_tables.sql文件内容
```

### 1.2 更新现有数据（如果已有数据）
如果数据库中已有弹窗数据但没有设置时间，执行以下脚本：

```bash
# 更新现有弹窗的时间设置
mysql -h 127.0.0.1 -u root -p123456 < update_popup_data.sql
```

**重要说明**: 从接口获取弹窗时，系统只会返回设置了开始时间的弹窗。没有设置开始时间的弹窗将不会显示。

## 2. 启动服务器

```bash
cd server
go run main.go
```

服务器将在 http://localhost:8080 启动

## 3. API测试

### 3.1 获取弹窗列表
```bash
curl "http://localhost:8080/popup/active?userId=1&page=/pages/index/index&userType=new"
```

预期响应：
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "title": "欢迎使用wn商城",
      "content": "感谢您使用我们的应用，祝您购物愉快！",
      "titleStyle": {
        "color": "#fff",
        "background": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
      },
      "buttons": [
        {
          "text": "开始购物",
          "type": "primary"
        }
      ],
      "priority": 10,
      "autoClose": 0,
      "delay": 1000,
      "maskClosable": true
    }
  ]
}
```

### 3.2 记录弹窗日志
```bash
curl -X POST "http://localhost:8080/popup/log" \
  -H "Content-Type: application/json" \
  -d '{
    "popupId": 1,
    "userId": 1,
    "action": "show",
    "buttonText": ""
  }'
```

### 3.3 获取统计信息
```bash
curl "http://localhost:8080/popup/stats"
```

## 4. 前端测试

### 4.1 首页弹窗测试
1. 启动前端项目
2. 打开首页 `/pages/index/index`
3. 应该能看到从后端加载的弹窗自动显示
4. 点击弹窗按钮会自动记录日志

### 4.2 弹窗功能演示
1. 进入用户中心页面
2. 点击"弹窗功能演示"按钮
3. 可以测试各种弹窗配置选项

**注意**: 
- 首页的弹窗完全从后端接口获取，不包含任何硬编码的测试数据
- 只有设置了开始时间的弹窗才会从接口返回
- 演示页面的弹窗是前端硬编码的，用于功能测试

## 5. 验证数据

查看数据库中的数据变化：

```sql
-- 查看弹窗数据
SELECT * FROM popup;

-- 查看用户操作日志
SELECT * FROM popup_user_log;

-- 查看统计数据
SELECT id, title, show_count, click_count FROM popup;
```

## 6. 故障排除

如果遇到问题：

1. 检查数据库连接配置
2. 确认表已正确创建
3. 检查服务器日志输出
4. 验证路由是否正确注册
5. 检查前端API请求地址是否正确 