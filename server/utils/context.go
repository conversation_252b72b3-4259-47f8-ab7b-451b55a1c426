package utils

import (
	"github.com/gin-gonic/gin"
)

// GetUserIdFromContext 从上下文中获取用户ID
func GetUserIdFromContext(ctx *gin.Context) int64 {
	value, exists := ctx.Get("userId")
	if !exists {
		return 0
	}
	userId, ok := value.(int64)
	if !ok {
		return 0
	}
	return userId
}

// Success 返回成功响应
func Success(ctx *gin.Context, data interface{}) {
	ctx.JSON(200, gin.H{
		"code": 200,
		"msg":  "success",
		"data": data,
	})
}

// Failed 返回失败响应
func Failed(ctx *gin.Context, msg string) {
	ctx.JSON(200, gin.H{
		"code": 500,
		"msg":  msg,
	})
}
