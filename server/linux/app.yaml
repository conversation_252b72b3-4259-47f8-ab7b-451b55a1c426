Name: wnadmin
Host: 0.0.0.0
Port: 8081
ENV: dev


# 阿里云配置
aliyun:
  # OSS配置
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com
    accessKeyId: LTAI5tPdEbKwLWf1dRm47HJn
    accessKeySecret: ******************************
    bucketName: dlsj
    domain: https://dlsj.oss-cn-beijing.aliyuncs.com
  # 短信配置
  sms:
    accessKeyId: LTAI5tPdEbKwLWf1dRm47HJn
    accessKeySecret: ******************************
    signName: 赤县门生实习平台
    templateCode:
      register: SMS_266885365
      login: SMS_266885365
      resetPassword: SMS_266885365 

# 文件上传路径 (请使用绝对路径或相对于项目根目录的已知路径)
UploadPath: "./uploads" # <<< 请修改为实际路径，建议绝对路径

# 微信支付配置
wx:
  miniapp:
    appid: wx58459973f280f050  # 小程序appid
    secret: 2e71fdd89f674172c7f5eb3f385e0917  # 小程序secret 
  # 微信支付配置
  pay:
    version: v2  # 支付版本选择 (v2|v3)
    mchid: 1636299931  # 商户号
    apikey: jlNH9PW28B4CRkPoWMhwFfvzQa5VNE0G  # 支付密钥
    appsecret: 2e71fdd89f674172c7f5eb3f385e0917  # 支付秘钥
    notify-url: https://44799531.r18.cpolar.top/api/payment/wx/notify  # 支付回调通知地址
    cert-path: /app/cert/apiclient_cert.pem  # 证书路径
    private-key-path: /app/cert/apiclient_key.pem  # 私钥路径
    serial-no: 5157F09EFDC096DE15EBE81A47057A7232F1B8E1  # 证书序列号
