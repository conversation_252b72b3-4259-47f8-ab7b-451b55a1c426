package main

import (
	"fmt"
	"runtime"
	"wnsys/shop/app/config"
	"wnsys/shop/app/middleware"
	"wnsys/shop/app/provider"
	"wnsys/shop/app/provider/mylog"
	"wnsys/shop/app/route"
	"wnsys/shop/app/server"

	"github.com/gin-gonic/gin"
)

func main() {
	runtime.GOMAXPROCS(runtime.NumCPU())
	provider.Register()

	switch config.App.ENV {
	case "dev", "debug", "test":
		gin.SetMode(gin.DebugMode)
	case "prod", "release":
		gin.SetMode(gin.ReleaseMode)
	default:
		gin.SetMode(gin.ReleaseMode)
	}

	r := gin.Default()
	r.Use(middleware.CORSMiddleware())
	r.Use(middleware.Logs())

	server.SetupStaticFiles(r, config.App.UploadPath)

	route.RegisterRoutes(r)

	serverAddr := fmt.Sprintf(":%d", config.App.Port)
	if err := server.Run(r, serverAddr); err != nil {
		mylog.GetDefaultLogger().Fatal(fmt.Sprintf("服务器启动失败: %v", err))
	}

	mylog.GetDefaultLogger().Info("主程序退出")
}
