package home

import (
	"wnsys/shop/app/common/utils"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"
)

type HomeService struct{}

func NewHomeService() *HomeService {
	return &HomeService{}
}

// GetBannerList 获取轮播图列表
func (s *HomeService) GetBannerList(query *dto.BannerQuery) ([]dto.BannerResp, error) {
	var list []ShopDB.BannerDO
	bannerDO := &ShopDB.BannerDO{}

	db := bannerDO.Query().Where("status = ?", 1)
	if query.Position > 0 {
		db = db.Where("position = ?", query.Position)
	}

	err := db.Order("sort DESC").Find(&list).Error
	if err != nil {
		return nil, err
	}

	result := make([]dto.BannerResp, 0, len(list))
	for _, v := range list {
		result = append(result, dto.BannerResp{
			ID:       v.ID,
			Image:    utils.BuildImageURL(v.Image),
			URL:      v.URL,
			Type:     v.Type,
			TargetID: v.TargetID,
			Title:    v.Title,
			Sort:     v.Sort,
		})
	}

	return result, nil
}

// GetCategoryList 获取分类列表
func (s *HomeService) GetCategoryList(query *dto.CategoryQuery) ([]dto.CategoryResp, error) {
	var list []ShopDB.CategoryDO
	categoryDO := &ShopDB.CategoryDO{}

	db := categoryDO.Query().Where("status = ?", 1).Where("module = ?", "goods")
	if query.Level > 0 {
		db = db.Where("level = ?", query.Level)
	}
	if query.ParentID >= 0 {
		db = db.Where("parent_id = ?", query.ParentID)
	}

	err := db.Order("sort DESC").Find(&list).Error
	if err != nil {
		return nil, err
	}

	result := make([]dto.CategoryResp, 0, len(list))
	for _, v := range list {
		result = append(result, dto.CategoryResp{
			ID:   v.ID,
			Name: v.Name,
			Icon: utils.BuildImageURL(v.Icon),
			Sort: v.Sort,
		})
	}

	return result, nil
}

// GetActivityList 获取活动列表
func (s *HomeService) GetActivityList(query *dto.ActivityQuery) ([]dto.ActivityResp, error) {
	var list []ShopDB.ActivityDO
	activityDO := &ShopDB.ActivityDO{}

	db := activityDO.Query()
	if query.Type > 0 {
		db = db.Where("type = ?", query.Type)
	}
	if query.Status >= 0 {
		db = db.Where("status = ?", query.Status)
	}

	err := db.Order("sort DESC").Find(&list).Error
	if err != nil {
		return nil, err
	}

	result := make([]dto.ActivityResp, 0, len(list))
	for _, v := range list {
		result = append(result, dto.ActivityResp{
			ID:        v.ID,
			Name:      v.Name,
			Desc:      v.Desc,
			Image:     utils.BuildImageURL(v.Image),
			StartTime: v.StartTime.Format("2006-01-02 15:04:05"),
			EndTime:   v.EndTime.Format("2006-01-02 15:04:05"),
			Type:      v.Type,
			Status:    v.Status,
		})
	}

	return result, nil
}

// GetTagList 获取标签列表
func (s *HomeService) GetTagList(query *dto.TagQuery) ([]dto.TagResp, error) {
	var list []ShopDB.GoodsTagDO
	tagDO := &ShopDB.GoodsTagDO{}

	err := tagDO.Query().
		Order("id ASC").
		Find(&list).Error
	if err != nil {
		return nil, err
	}

	result := make([]dto.TagResp, 0, len(list))
	for _, v := range list {
		result = append(result, dto.TagResp{
			ID:   v.ID,
			Name: v.Name,
		})
	}

	return result, nil
}

// GetBanners 获取轮播图列表
func (s *HomeService) GetBanners() ([]dto.BannerResp, error) {
	var banners []ShopDB.BannerDO
	if err := (&ShopDB.BannerDO{}).Query().Where("status = ?", 1).Order("sort DESC").Find(&banners).Error; err != nil {
		return nil, err
	}

	resp := make([]dto.BannerResp, 0, len(banners))
	for _, banner := range banners {
		resp = append(resp, dto.BannerResp{
			ID:       banner.ID,
			Title:    banner.Title,
			Image:    utils.BuildImageURL(banner.Image),
			URL:      banner.URL,
			Type:     banner.Type,
			TargetID: banner.TargetID,
		})
	}

	return resp, nil
}
