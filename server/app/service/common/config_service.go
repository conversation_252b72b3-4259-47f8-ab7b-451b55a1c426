package common

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// ConfigService 系统配置服务
type ConfigService struct {
	db    *gorm.DB
	cache map[string]*ConfigItem
	mutex sync.RWMutex
}

// ConfigItem 配置项缓存结构
type ConfigItem struct {
	Value      string
	Type       string
	UpdateTime time.Time
}

// ConfigRequest 配置请求结构
type ConfigRequest struct {
	ModuleKey string   `json:"moduleKey" binding:"required"` // 模块标识
	Keys      []string `json:"keys"`                         // 配置键列表，为空则获取模块下所有配置
}

// ConfigResponse 配置响应结构
type ConfigResponse struct {
	ModuleKey string                 `json:"moduleKey"`
	Configs   map[string]interface{} `json:"configs"`
}

// NewConfigService 创建配置服务实例
func NewConfigService() *ConfigService {
	return &ConfigService{
		db:    db.DB.Shop,
		cache: make(map[string]*ConfigItem),
	}
}

// GetConfigs 获取配置信息
func (s *ConfigService) GetConfigs(req *ConfigRequest) (*ConfigResponse, error) {
	var configs []ShopDB.SysConfigDO
	query := (&ShopDB.SysConfigDO{}).Query().
		Where("module_key = ? AND status = 1 AND del_flag = '0'", req.ModuleKey)

	// 如果指定了具体的配置键，则只查询这些配置
	if len(req.Keys) > 0 {
		query = query.Where("config_key IN ?", req.Keys)
	}

	err := query.Order("sort ASC, id ASC").Find(&configs).Error
	if err != nil {
		return nil, werror.New(500, "获取配置失败", map[string]interface{}{
			"moduleKey": req.ModuleKey,
			"keys":      req.Keys,
			"error":     err.Error(),
		})
	}

	// 构建响应数据
	result := &ConfigResponse{
		ModuleKey: req.ModuleKey,
		Configs:   make(map[string]interface{}),
	}

	for _, config := range configs {
		// 根据配置类型转换值
		value, err := s.convertConfigValue(config.ConfigValue, config.ConfigType)
		if err != nil {
			// 转换失败时使用原始字符串值
			value = config.ConfigValue
		}
		result.Configs[config.ConfigKey] = value

		// 更新缓存
		s.updateCache(s.buildCacheKey(req.ModuleKey, config.ConfigKey), &ConfigItem{
			Value:      config.ConfigValue,
			Type:       config.ConfigType,
			UpdateTime: config.UpdateTime,
		})
	}

	return result, nil
}

// GetConfig 获取单个配置值
func (s *ConfigService) GetConfig(moduleKey, configKey string) (interface{}, error) {
	// 先尝试从缓存获取
	cacheKey := s.buildCacheKey(moduleKey, configKey)
	if item := s.getFromCache(cacheKey); item != nil {
		value, err := s.convertConfigValue(item.Value, item.Type)
		if err != nil {
			return item.Value, nil
		}
		return value, nil
	}

	// 缓存未命中，从数据库查询
	var config ShopDB.SysConfigDO
	err := (&ShopDB.SysConfigDO{}).Query().
		Where("module_key = ? AND config_key = ? AND status = 1 AND del_flag = '0'", moduleKey, configKey).
		First(&config).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, werror.New(404, "配置不存在", map[string]interface{}{
				"moduleKey": moduleKey,
				"configKey": configKey,
			})
		}
		return nil, werror.New(500, "获取配置失败", map[string]interface{}{
			"moduleKey": moduleKey,
			"configKey": configKey,
			"error":     err.Error(),
		})
	}

	// 转换配置值
	value, err := s.convertConfigValue(config.ConfigValue, config.ConfigType)
	if err != nil {
		value = config.ConfigValue
	}
	// 更新缓存
	s.updateCache(cacheKey, &ConfigItem{
		Value:      config.ConfigValue,
		Type:       config.ConfigType,
		UpdateTime: config.UpdateTime,
	})

	return value, nil
}

// GetConfigString 获取字符串类型配置
func (s *ConfigService) GetConfigString(moduleKey, configKey string, defaultValue ...string) string {
	value, err := s.GetConfig(moduleKey, configKey)
	if err != nil {
		if len(defaultValue) > 0 {
			return defaultValue[0]
		}
		return ""
	}

	if str, ok := value.(string); ok {
		return str
	}

	if len(defaultValue) > 0 {
		return defaultValue[0]
	}
	return ""
}

// GetConfigInt 获取整数类型配置
func (s *ConfigService) GetConfigInt(moduleKey, configKey string, defaultValue ...int) int {
	value, err := s.GetConfig(moduleKey, configKey)
	if err != nil {
		if len(defaultValue) > 0 {
			return defaultValue[0]
		}
		return 0
	}

	if intVal, ok := value.(int); ok {
		return intVal
	}

	if len(defaultValue) > 0 {
		return defaultValue[0]
	}
	return 0
}

// GetConfigFloat 获取浮点数类型配置
func (s *ConfigService) GetConfigFloat(moduleKey, configKey string, defaultValue ...float64) float64 {
	value, err := s.GetConfig(moduleKey, configKey)
	if err != nil {
		if len(defaultValue) > 0 {
			return defaultValue[0]
		}
		return 0.0
	}
	fmt.Println("value", value)
	fmt.Println("value type", fmt.Sprintf("%T", value))

	// 尝试转换为float64
	if floatVal, ok := value.(float64); ok {
		fmt.Println("floatVal", floatVal)
		return floatVal
	}

	// 如果是int类型，转换为float64
	if intVal, ok := value.(int); ok {
		floatVal := float64(intVal)
		fmt.Println("intVal converted to floatVal", floatVal)
		return floatVal
	}

	// 如果是字符串，尝试解析为float64
	if strVal, ok := value.(string); ok {
		if floatVal, err := strconv.ParseFloat(strVal, 64); err == nil {
			fmt.Println("strVal converted to floatVal", floatVal)
			return floatVal
		}
	}

	fmt.Println("defaultValue", defaultValue)
	if len(defaultValue) > 0 {
		return defaultValue[0]
	}
	return 0.0
}

// GetConfigBool 获取布尔类型配置
func (s *ConfigService) GetConfigBool(moduleKey, configKey string, defaultValue ...bool) bool {
	value, err := s.GetConfig(moduleKey, configKey)
	if err != nil {
		if len(defaultValue) > 0 {
			return defaultValue[0]
		}
		return false
	}

	if boolVal, ok := value.(bool); ok {
		return boolVal
	}

	if len(defaultValue) > 0 {
		return defaultValue[0]
	}
	return false
}

// GetShopConfigs 获取商城配置的便捷方法
func (s *ConfigService) GetShopConfigs(keys ...string) (*ConfigResponse, error) {
	req := &ConfigRequest{
		ModuleKey: "shopConfig",
		Keys:      keys,
	}
	return s.GetConfigs(req)
}

// GetFreightConfig 获取运费配置的便捷方法
func (s *ConfigService) GetFreightConfig() (float64, error) {
	// 这里可以根据实际的配置键名调整
	// 从SQL文件看，运费配置的键名是 "shop.yunfei.shunfeng"
	value := s.GetConfigFloat("shopConfig", "shop.yunfei.shunfeng", 0.0)
	return value, nil
}

// ClearCache 清除缓存
func (s *ConfigService) ClearCache() {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.cache = make(map[string]*ConfigItem)
}

// ClearModuleCache 清除指定模块的缓存
func (s *ConfigService) ClearModuleCache(moduleKey string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	prefix := moduleKey + ":"
	for key := range s.cache {
		if strings.HasPrefix(key, prefix) {
			delete(s.cache, key)
		}
	}
}

// convertConfigValue 根据配置类型转换配置值
func (s *ConfigService) convertConfigValue(value, configType string) (interface{}, error) {
	switch strings.ToLower(configType) {
	case "string", "text":
		return value, nil
	case "number":
		// number类型统一返回float64，支持整数和小数
		return strconv.ParseFloat(value, 64)
	case "int", "integer":
		return strconv.Atoi(value)
	case "float", "double", "decimal":
		return strconv.ParseFloat(value, 64)
	case "bool", "boolean":
		return strconv.ParseBool(value)
	case "json":
		var result interface{}
		err := json.Unmarshal([]byte(value), &result)
		return result, err
	default:
		return value, nil
	}
}

// buildCacheKey 构建缓存键
func (s *ConfigService) buildCacheKey(moduleKey, configKey string) string {
	return fmt.Sprintf("%s:%s", moduleKey, configKey)
}

// getFromCache 从缓存获取配置
func (s *ConfigService) getFromCache(key string) *ConfigItem {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if item, exists := s.cache[key]; exists {
		// 简单的缓存过期检查（5分钟）
		if time.Since(item.UpdateTime) < 5*time.Minute {
			return item
		}
		// 缓存过期，删除
		delete(s.cache, key)
	}
	return nil
}

// updateCache 更新缓存
func (s *ConfigService) updateCache(key string, item *ConfigItem) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.cache[key] = item
}

// ShareConfig 分享配置结构体
type ShareConfig struct {
	// 基础设置
	Enabled bool `json:"enabled"`

	// 注册佣金
	RegisterEnabled bool    `json:"register_enabled"`
	RegisterAmount  float64 `json:"register_amount"`

	// 购买佣金
	PurchaseEnabled bool    `json:"purchase_enabled"`
	Level1Rate      float64 `json:"level1_rate"`
	Level2Rate      float64 `json:"level2_rate"`

	// 佣金管理
	FreezeDays int `json:"freeze_days"`

	// 提现设置
	MinWithdraw     float64 `json:"min_withdraw"`
	MaxWithdraw     float64 `json:"max_withdraw"`
	WithdrawFeeRate float64 `json:"withdraw_fee_rate"`
	WechatWithdraw  bool    `json:"wechat_withdraw"`
	AlipayWithdraw  bool    `json:"alipay_withdraw"`
}

// GetShareConfig 获取分享配置
func (s *ConfigService) GetShareConfig() (*ShareConfig, error) {
	config := &ShareConfig{}

	// 获取所有分享相关配置
	var configs []ShopDB.SysConfigDO
	err := s.db.Table("sys_config").
		Where("module_key = ? AND status = 1", "shareConfig").
		Find(&configs).Error
	if err != nil {
		return nil, werror.Wrap(err, 500, "获取分享配置失败")
	}

	// 解析配置
	configMap := make(map[string]string)
	for _, cfg := range configs {
		if cfg.ConfigKey != "" {
			configMap[cfg.ConfigKey] = cfg.ConfigValue
		}
	}

	// 填充配置结构体
	config.Enabled = s.getBoolConfig(configMap, "share.enabled", true)
	config.RegisterEnabled = s.getBoolConfig(configMap, "share.register.enabled", true)
	config.RegisterAmount = s.getFloatConfig(configMap, "share.register.amount", 5.0)
	config.PurchaseEnabled = s.getBoolConfig(configMap, "share.purchase.enabled", true)
	config.Level1Rate = s.getFloatConfig(configMap, "share.purchase.level1_rate", 10.0)
	config.Level2Rate = s.getFloatConfig(configMap, "share.purchase.level2_rate", 5.0)
	config.FreezeDays = s.getIntConfig(configMap, "share.commission.freeze_days", 7)
	config.MinWithdraw = s.getFloatConfig(configMap, "share.withdraw.min_amount", 10.0)
	config.MaxWithdraw = s.getFloatConfig(configMap, "share.withdraw.max_amount", 5000.0)
	config.WithdrawFeeRate = s.getFloatConfig(configMap, "share.withdraw.fee_rate", 0.0)
	config.WechatWithdraw = s.getBoolConfig(configMap, "share.withdraw.wechat_enabled", true)
	config.AlipayWithdraw = s.getBoolConfig(configMap, "share.withdraw.alipay_enabled", false)

	return config, nil
}

// UpdateShareConfig 更新分享配置
func (s *ConfigService) UpdateShareConfig(config *ShareConfig) error {
	// 配置映射
	configUpdates := map[string]string{
		"share.enabled":                 s.boolToString(config.Enabled),
		"share.register.enabled":        s.boolToString(config.RegisterEnabled),
		"share.register.amount":         s.floatToString(config.RegisterAmount),
		"share.purchase.enabled":        s.boolToString(config.PurchaseEnabled),
		"share.purchase.level1_rate":    s.floatToString(config.Level1Rate),
		"share.purchase.level2_rate":    s.floatToString(config.Level2Rate),
		"share.commission.freeze_days":  s.intToString(config.FreezeDays),
		"share.withdraw.min_amount":     s.floatToString(config.MinWithdraw),
		"share.withdraw.max_amount":     s.floatToString(config.MaxWithdraw),
		"share.withdraw.fee_rate":       s.floatToString(config.WithdrawFeeRate),
		"share.withdraw.wechat_enabled": s.boolToString(config.WechatWithdraw),
		"share.withdraw.alipay_enabled": s.boolToString(config.AlipayWithdraw),
	}

	// 批量更新配置
	for key, value := range configUpdates {
		err := s.db.Table("sys_config").
			Where("config_key = ? AND module_key = ?", key, "shareConfig").
			Update("config_value", value).Error
		if err != nil {
			return werror.Wrap(err, 500, "更新配置失败").
				WithContext("config_key", key).
				WithContext("config_value", value)
		}
	}

	return nil
}

// 辅助方法
func (s *ConfigService) getBoolConfig(configMap map[string]string, key string, defaultValue bool) bool {
	if value, exists := configMap[key]; exists {
		return value == "1" || value == "true"
	}
	return defaultValue
}

func (s *ConfigService) getFloatConfig(configMap map[string]string, key string, defaultValue float64) float64 {
	if value, exists := configMap[key]; exists {
		if f, err := strconv.ParseFloat(value, 64); err == nil {
			return f
		}
	}
	return defaultValue
}

func (s *ConfigService) getIntConfig(configMap map[string]string, key string, defaultValue int) int {
	if value, exists := configMap[key]; exists {
		if i, err := strconv.Atoi(value); err == nil {
			return i
		}
	}
	return defaultValue
}

func (s *ConfigService) boolToString(b bool) string {
	if b {
		return "1"
	}
	return "0"
}

func (s *ConfigService) floatToString(f float64) string {
	return strconv.FormatFloat(f, 'f', 2, 64)
}

func (s *ConfigService) intToString(i int) string {
	return strconv.Itoa(i)
}
