package common

import (
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/provider/db"
)

type FeedbackService struct{}

// NewFeedbackService 创建反馈服务
func NewFeedbackService() *FeedbackService {
	return &FeedbackService{}
}

// SubmitFeedback 提交反馈
func (s *FeedbackService) SubmitFeedback(userID int64, req *dto.SubmitFeedbackRequest) error {
	feedback := &ShopDB.FeedbackDO{
		UserID:  userID,
		Content: req.Content,
		Contact: req.Contact,
		Status:  0, // 待处理
	}

	if err := db.DB.Shop.Create(feedback).Error; err != nil {
		return werror.New(500, "提交反馈失败")
	}

	return nil
}

// GetUserFeedbacks 获取用户反馈列表
func (s *FeedbackService) GetUserFeedbacks(userID int64, req *dto.FeedbackListRequest) ([]*dto.FeedbackResponse, int64, error) {
	var feedbacks []ShopDB.FeedbackDO
	var total int64

	query := db.DB.Shop.Model(&ShopDB.FeedbackDO{}).Where("user_id = ?", userID)

	// 状态筛选
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, werror.New(500, "获取反馈列表失败")
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("create_time DESC").Offset(offset).Limit(req.PageSize).Find(&feedbacks).Error; err != nil {
		return nil, 0, werror.New(500, "获取反馈列表失败")
	}

	// 转换为响应格式
	var responses []*dto.FeedbackResponse
	for _, feedback := range feedbacks {
		responses = append(responses, s.convertToResponse(&feedback))
	}

	return responses, total, nil
}

// GetFeedbackByID 根据ID获取反馈详情
func (s *FeedbackService) GetFeedbackByID(userID, feedbackID int64) (*dto.FeedbackResponse, error) {
	var feedback ShopDB.FeedbackDO
	if err := db.DB.Shop.Where("id = ? AND user_id = ?", feedbackID, userID).First(&feedback).Error; err != nil {
		return nil, werror.New(404, "反馈不存在")
	}

	return s.convertToResponse(&feedback), nil
}

// convertToResponse 转换为响应格式
func (s *FeedbackService) convertToResponse(feedback *ShopDB.FeedbackDO) *dto.FeedbackResponse {
	statusText := s.getStatusText(feedback.Status)

	return &dto.FeedbackResponse{
		ID:         feedback.ID,
		Content:    feedback.Content,
		Contact:    feedback.Contact,
		Status:     feedback.Status,
		StatusText: statusText,
		Reply:      feedback.Reply,
		ReplyTime:  feedback.ReplyTime,
		CreateTime: feedback.CreateTime,
	}
}

// getStatusText 获取状态文本
func (s *FeedbackService) getStatusText(status int8) string {
	switch status {
	case 0:
		return "待处理"
	case 1:
		return "已处理"
	case 2:
		return "已关闭"
	default:
		return "未知状态"
	}
}
