package popup

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"

	"gorm.io/gorm"
)

type PopupService struct{}

// GetActivePopups 获取用户的生效弹窗列表
func (s *PopupService) GetActivePopups(req *dto.PopupActiveListReq) ([]dto.PopupActiveResp, error) {
	var popups []ShopDB.PopupDO
	now := time.Now()

	// 查询生效的弹窗 - 必须设置了开始时间
	query := (&ShopDB.PopupDO{}).Query().
		Where("status = ?", 1). // 启用状态
		Where("start_time IS NOT NULL AND start_time <= ?", now).
		Where("end_time IS NULL OR end_time >= ?", now).
		Order("priority DESC, create_time ASC")

	if err := query.Find(&popups).Error; err != nil {
		return nil, werror.Wrap(err, 500, "查询弹窗失败")
	}

	var result []dto.PopupActiveResp
	for _, popup := range popups {
		// 检查用户是否符合触发条件
		if s.checkTriggerRule(&popup, req.UserID) {
			activeResp, err := s.convertToActiveResp(&popup)
			if err != nil {
				continue // 跳过转换失败的弹窗
			}
			result = append(result, *activeResp)
		}
	}

	return result, nil
}

// RecordPopupLog 记录弹窗日志
func (s *PopupService) RecordPopupLog(req *dto.PopupLogReq) error {
	// 记录用户操作日志
	logDO := &ShopDB.PopupUserLogDO{
		PopupID:    req.PopupID,
		UserID:     req.UserID,
		Action:     req.Action,
		ButtonText: req.ButtonText,
	}

	if err := logDO.Query().Create(logDO).Error; err != nil {
		return werror.Wrap(err, 500, "记录弹窗日志失败")
	}

	// 更新弹窗统计数据
	if req.Action == "show" {
		// 增加显示次数
		if err := (&ShopDB.PopupDO{}).Query().
			Where("id = ?", req.PopupID).
			UpdateColumn("show_count", gorm.Expr("show_count + 1")).Error; err != nil {
			// 日志记录成功但统计更新失败，不影响主流程
			fmt.Printf("更新弹窗显示次数失败: %v\n", err)
		}
	} else if req.Action == "click" {
		// 增加点击次数
		if err := (&ShopDB.PopupDO{}).Query().
			Where("id = ?", req.PopupID).
			UpdateColumn("click_count", gorm.Expr("click_count + 1")).Error; err != nil {
			// 日志记录成功但统计更新失败，不影响主流程
			fmt.Printf("更新弹窗点击次数失败: %v\n", err)
		}
	}

	return nil
}

// GetPopupStats 获取弹窗统计信息
func (s *PopupService) GetPopupStats() (*dto.PopupStatsResp, error) {
	var stats dto.PopupStatsResp

	// 查询总弹窗数
	if err := (&ShopDB.PopupDO{}).Query().Count(&stats.TotalPopups).Error; err != nil {
		return nil, werror.Wrap(err, 500, "查询总弹窗数失败")
	}

	// 查询生效弹窗数
	now := time.Now()
	if err := (&ShopDB.PopupDO{}).Query().
		Where("status = ?", 1).
		Where("(start_time IS NULL OR start_time <= ?)", now).
		Where("(end_time IS NULL OR end_time >= ?)", now).
		Count(&stats.ActivePopups).Error; err != nil {
		return nil, werror.Wrap(err, 500, "查询生效弹窗数失败")
	}

	// 查询总显示次数和点击次数
	var result struct {
		TotalShows  int64 `gorm:"column:total_shows"`
		TotalClicks int64 `gorm:"column:total_clicks"`
	}

	if err := (&ShopDB.PopupDO{}).Query().
		Select("SUM(show_count) as total_shows, SUM(click_count) as total_clicks").
		Scan(&result).Error; err != nil {
		return nil, werror.Wrap(err, 500, "查询统计数据失败")
	}

	stats.TotalShows = result.TotalShows
	stats.TotalClicks = result.TotalClicks

	// 计算点击率
	if stats.TotalShows > 0 {
		stats.ClickRate = float64(stats.TotalClicks) / float64(stats.TotalShows) * 100
	}

	return &stats, nil
}

// checkTriggerRule 检查用户是否符合触发条件
func (s *PopupService) checkTriggerRule(popup *ShopDB.PopupDO, userID int64) bool {
	// 手动触发类型，直接返回true
	if popup.TriggerType == "manual" {
		return true
	}

	// 解析触发规则
	var triggerRule dto.PopupTriggerRule
	if popup.TriggerRule != "" {
		if err := json.Unmarshal([]byte(popup.TriggerRule), &triggerRule); err != nil {
			return false // 规则解析失败，不触发
		}
	}

	// 检查频次控制
	if !s.checkShowFrequency(popup.ID, userID, &triggerRule) {
		return false
	}

	// 自动触发类型的条件检查
	if popup.TriggerType == "auto" {
		return s.checkAutoTriggerRule(&triggerRule, userID)
	}

	// 定时触发类型的条件检查
	if popup.TriggerType == "schedule" {
		return s.checkScheduleTriggerRule(&triggerRule)
	}

	return false
}

// checkShowFrequency 检查显示频次
func (s *PopupService) checkShowFrequency(popupID, userID int64, rule *dto.PopupTriggerRule) bool {
	// 检查最大显示次数
	if rule.MaxShowTimes != nil && *rule.MaxShowTimes > 0 {
		var showCount int64
		if err := (&ShopDB.PopupUserLogDO{}).Query().
			Where("popup_id = ? AND user_id = ? AND action = ?", popupID, userID, "show").
			Count(&showCount).Error; err != nil {
			return false
		}

		if showCount >= int64(*rule.MaxShowTimes) {
			return false // 已达到最大显示次数
		}
	}

	// 检查显示间隔
	if rule.ShowInterval != nil && *rule.ShowInterval > 0 {
		var lastLog ShopDB.PopupUserLogDO
		if err := (&ShopDB.PopupUserLogDO{}).Query().
			Where("popup_id = ? AND user_id = ? AND action = ?", popupID, userID, "show").
			Order("create_time DESC").
			First(&lastLog).Error; err == nil {
			// 找到了上次显示记录，检查时间间隔
			intervalHours := time.Since(lastLog.CreateTime).Hours()
			if intervalHours < float64(*rule.ShowInterval) {
				return false // 未达到显示间隔
			}
		}
	}

	return true
}

// checkAutoTriggerRule 检查自动触发规则
func (s *PopupService) checkAutoTriggerRule(rule *dto.PopupTriggerRule, userID int64) bool {
	// 这里可以根据实际业务需求实现更复杂的用户条件检查
	// 比如用户类型、访问次数、注册天数等
	// 目前简化处理，直接返回true
	return true
}

// checkScheduleTriggerRule 检查定时触发规则
func (s *PopupService) checkScheduleTriggerRule(rule *dto.PopupTriggerRule) bool {
	now := time.Now()

	switch rule.ScheduleType {
	case "once":
		// 一次性触发，需要检查具体时间
		return true
	case "daily":
		// 每日触发，检查当前时间是否匹配
		return true
	case "weekly":
		// 每周触发，检查星期几
		weekday := int(now.Weekday())
		if weekday == 0 {
			weekday = 7 // 将Sunday从0改为7
		}
		for _, day := range rule.ScheduleDays {
			if day == weekday {
				return true
			}
		}
		return false
	case "monthly":
		// 每月触发，检查日期
		day := now.Day()
		for _, d := range rule.ScheduleDays {
			if d == day {
				return true
			}
		}
		return false
	}

	return false
}

// convertToActiveResp 转换为前端响应格式
func (s *PopupService) convertToActiveResp(popup *ShopDB.PopupDO) (*dto.PopupActiveResp, error) {
	resp := &dto.PopupActiveResp{
		ID:           strconv.FormatInt(popup.ID, 10),
		Title:        popup.Title,
		Content:      popup.Content,
		Priority:     popup.Priority,
		AutoClose:    popup.AutoClose,
		Delay:        popup.Delay,
		MaskClosable: popup.MaskClosable,
	}

	// 解析标题样式
	if popup.TitleStyle != "" {
		var titleStyle dto.PopupTitleStyle
		if err := json.Unmarshal([]byte(popup.TitleStyle), &titleStyle); err == nil {
			resp.TitleStyle = &titleStyle
		}
	}

	// 解析按钮配置
	if popup.Buttons != "" {
		var buttons []dto.PopupButton
		if err := json.Unmarshal([]byte(popup.Buttons), &buttons); err != nil {
			return nil, fmt.Errorf("解析按钮配置失败: %v", err)
		}
		resp.Buttons = buttons
	}

	return resp, nil
}
