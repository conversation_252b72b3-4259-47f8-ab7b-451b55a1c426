package relation

import (
	"encoding/json"
	"time"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

type RelationService struct{}

// CreateRelation 创建用户关系
func (s *RelationService) CreateRelation(userId, relatedUserId int64, relationType, direction, source string, metadata map[string]interface{}) error {
	// 检查是否已存在关系
	var existingRelation ShopDB.UserRelationDO
	err := existingRelation.Query().Where("user_id = ? AND related_user_id = ? AND relation_type = ?",
		userId, relatedUserId, relationType).First(&existingRelation).Error

	if err == nil {
		// 关系已存在，更新状态
		return s.UpdateRelationStatus(existingRelation.ID, ShopDB.StatusActive)
	}

	if err != gorm.ErrRecordNotFound {
		return werror.Wrap(err, 500, "查询用户关系失败")
	}

	// 序列化元数据
	metadataJSON := ""
	if metadata != nil {
		if jsonBytes, err := json.Marshal(metadata); err == nil {
			metadataJSON = string(jsonBytes)
		}
	}

	// 创建新关系
	relation := &ShopDB.UserRelationDO{
		UserId:        userId,
		RelatedUserId: relatedUserId,
		RelationType:  relationType,
		Direction:     direction,
		Status:        ShopDB.StatusActive,
		Source:        source,
		Metadata:      metadataJSON,
		CreateTime:    time.Now(),
		UpdateTime:    time.Now(),
	}

	err = relation.Query().Create(relation).Error
	if err != nil {
		return werror.Wrap(err, 500, "创建用户关系失败")
	}

	// 如果是双向关系，创建反向关系
	if direction == ShopDB.DirectionTwoWay {
		reverseRelation := &ShopDB.UserRelationDO{
			UserId:        relatedUserId,
			RelatedUserId: userId,
			RelationType:  relationType,
			Direction:     direction,
			Status:        ShopDB.StatusActive,
			Source:        source,
			Metadata:      metadataJSON,
			CreateTime:    time.Now(),
			UpdateTime:    time.Now(),
		}

		err = reverseRelation.Query().Create(reverseRelation).Error
		if err != nil {
			return werror.Wrap(err, 500, "创建反向用户关系失败")
		}
	}

	return nil
}

// GetRelations 获取用户关系列表
func (s *RelationService) GetRelations(userId int64, relationType string, page, pageSize int) ([]ShopDB.UserRelationDO, int64, error) {
	var relations []ShopDB.UserRelationDO
	var total int64

	query := (&ShopDB.UserRelationDO{}).Query().Where("user_id = ? AND status = ?", userId, ShopDB.StatusActive)

	if relationType != "" {
		query = query.Where("relation_type = ?", relationType)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, werror.Wrap(err, 500, "查询用户关系总数失败")
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.Offset(offset).Limit(pageSize).Order("create_time DESC").Find(&relations).Error
	if err != nil {
		return nil, 0, werror.Wrap(err, 500, "查询用户关系列表失败")
	}

	return relations, total, nil
}

// GetRelationsByType 根据关系类型获取关系
func (s *RelationService) GetRelationsByType(userId int64, relationType string) ([]ShopDB.UserRelationDO, error) {
	var relations []ShopDB.UserRelationDO

	err := (&ShopDB.UserRelationDO{}).Query().
		Where("user_id = ? AND relation_type = ? AND status = ?", userId, relationType, ShopDB.StatusActive).
		Order("create_time DESC").
		Find(&relations).Error

	if err != nil {
		return nil, werror.Wrap(err, 500, "查询用户关系失败")
	}

	return relations, nil
}

// GetInviteTree 获取邀请树（递归获取下级）
func (s *RelationService) GetInviteTree(userId int64, maxLevel int) ([]map[string]interface{}, error) {
	return s.getInviteTreeRecursive(userId, 1, maxLevel)
}

// getInviteTreeRecursive 递归获取邀请树
func (s *RelationService) getInviteTreeRecursive(userId int64, currentLevel, maxLevel int) ([]map[string]interface{}, error) {
	if currentLevel > maxLevel {
		return nil, nil
	}

	// 获取直接下级
	var relations []ShopDB.UserRelationDO
	err := (&ShopDB.UserRelationDO{}).Query().
		Where("user_id = ? AND relation_type = ? AND status = ?", userId, ShopDB.RelationTypeInvite, ShopDB.StatusActive).
		Find(&relations).Error

	if err != nil {
		return nil, werror.Wrap(err, 500, "查询邀请关系失败")
	}

	var result []map[string]interface{}
	for _, relation := range relations {
		item := map[string]interface{}{
			"user_id":     relation.RelatedUserId,
			"level":       currentLevel,
			"create_time": relation.CreateTime,
			"source":      relation.Source,
		}

		// 递归获取下级
		if currentLevel < maxLevel {
			children, err := s.getInviteTreeRecursive(relation.RelatedUserId, currentLevel+1, maxLevel)
			if err != nil {
				return nil, err
			}
			item["children"] = children
		}

		result = append(result, item)
	}

	return result, nil
}

// GetTeamCount 获取团队人数（递归统计）
func (s *RelationService) GetTeamCount(userId int64, relationType string) (int64, error) {
	return s.getTeamCountRecursive(userId, relationType, make(map[int64]bool))
}

// getTeamCountRecursive 递归统计团队人数
func (s *RelationService) getTeamCountRecursive(userId int64, relationType string, visited map[int64]bool) (int64, error) {
	// 防止循环引用
	if visited[userId] {
		return 0, nil
	}
	visited[userId] = true

	// 获取直接下级
	var relations []ShopDB.UserRelationDO
	err := (&ShopDB.UserRelationDO{}).Query().
		Where("user_id = ? AND relation_type = ? AND status = ?", userId, relationType, ShopDB.StatusActive).
		Find(&relations).Error

	if err != nil {
		return 0, werror.Wrap(err, 500, "查询用户关系失败")
	}

	count := int64(len(relations))

	// 递归统计下级的下级
	for _, relation := range relations {
		subCount, err := s.getTeamCountRecursive(relation.RelatedUserId, relationType, visited)
		if err != nil {
			return 0, err
		}
		count += subCount
	}

	return count, nil
}

// UpdateRelationStatus 更新关系状态
func (s *RelationService) UpdateRelationStatus(relationId int64, status int) error {
	err := (&ShopDB.UserRelationDO{}).Query().
		Where("id = ?", relationId).
		Update("status", status).Error

	if err != nil {
		return werror.Wrap(err, 500, "更新关系状态失败")
	}

	return nil
}

// DeleteRelation 删除关系
func (s *RelationService) DeleteRelation(userId, relatedUserId int64, relationType string) error {
	err := (&ShopDB.UserRelationDO{}).Query().
		Where("user_id = ? AND related_user_id = ? AND relation_type = ?", userId, relatedUserId, relationType).
		Update("status", 0).Error

	if err != nil {
		return werror.Wrap(err, 500, "删除用户关系失败")
	}

	return nil
}

// CheckRelation 检查关系是否存在
func (s *RelationService) CheckRelation(userId, relatedUserId int64, relationType string) (bool, error) {
	var count int64
	err := (&ShopDB.UserRelationDO{}).Query().
		Where("user_id = ? AND related_user_id = ? AND relation_type = ? AND status = ?",
			userId, relatedUserId, relationType, ShopDB.StatusActive).
		Count(&count).Error

	if err != nil {
		return false, werror.Wrap(err, 500, "检查用户关系失败")
	}

	return count > 0, nil
}

// GetAncestors 获取用户的上级链（递归向上查找）
func (s *RelationService) GetAncestors(userId int64, relationType string) ([]int64, error) {
	return s.getAncestorsRecursive(userId, relationType, make(map[int64]bool))
}

// getAncestorsRecursive 递归获取上级关系链
func (s *RelationService) getAncestorsRecursive(userId int64, relationType string, visited map[int64]bool) ([]int64, error) {
	// 防止循环引用
	if visited[userId] {
		return nil, nil
	}
	visited[userId] = true

	// 查找直接上级
	var relation ShopDB.UserRelationDO
	err := (&ShopDB.UserRelationDO{}).Query().
		Where("related_user_id = ? AND relation_type = ? AND status = ?", userId, relationType, ShopDB.StatusActive).
		First(&relation).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // 没有上级
		}
		return nil, werror.Wrap(err, 500, "查询上级关系失败")
	}

	// 递归获取上级的上级
	ancestors, err := s.getAncestorsRecursive(relation.UserId, relationType, visited)
	if err != nil {
		return nil, err
	}

	// 将当前上级添加到结果中
	result := []int64{relation.UserId}
	result = append(result, ancestors...)

	return result, nil
}

// BatchCreateRelations 批量创建关系
func (s *RelationService) BatchCreateRelations(relations []map[string]interface{}) error {
	return db.DB.Shop.Transaction(func(tx *gorm.DB) error {
		for _, rel := range relations {
			userId := rel["user_id"].(int64)
			relatedUserId := rel["related_user_id"].(int64)
			relationType := rel["relation_type"].(string)
			direction := rel["direction"].(string)
			source := rel["source"].(string)

			var metadata map[string]interface{}
			if rel["metadata"] != nil {
				metadata = rel["metadata"].(map[string]interface{})
			}

			err := s.CreateRelation(userId, relatedUserId, relationType, direction, source, metadata)
			if err != nil {
				return err
			}
		}
		return nil
	})
}
