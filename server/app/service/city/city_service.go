package city

import (
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/provider/db"
)

type Service struct{}

func NewService() *Service {
	return &Service{}
}

// GetCityList 获取城市列表
func (s *Service) GetCityList(parentId string) ([]ShopDB.DataCity, error) {
	var list []ShopDB.DataCity
	query := db.DB.Shop.Model(&ShopDB.DataCity{}).Where("is_show = ?", 1)

	if parentId != "" {
		query = query.Where("parent_id = ?", parentId)
	} else {
		query = query.Where("level = ?", 1) // 获取省份
	}

	err := query.Order("id asc").Find(&list).Error
	return list, err
}
