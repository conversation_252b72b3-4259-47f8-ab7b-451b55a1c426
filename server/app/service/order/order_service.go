package order

import (
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"wnsys/shop/app/common/utils"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/config"
	"wnsys/shop/app/model/do"
	"wnsys/shop/app/model/do/BeautyDB"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/model/vo"
	"wnsys/shop/app/provider/db"
	"wnsys/shop/app/service/common"
	"wnsys/shop/app/service/dict"
	"wnsys/shop/app/service/dining"
	"wnsys/shop/app/service/goods"
	"wnsys/shop/app/service/payment"
	"wnsys/shop/app/service/share"

	"gorm.io/gorm"
)

type OrderService struct {
	goodsService  *goods.Service
	diningService *dining.Service
	db            *gorm.DB
	shareService  *share.ShareService
	configService *common.ConfigService
	dictService   *dict.DictService
}

// NewService 创建订单服务
func NewService() *OrderService {
	return &OrderService{
		goodsService:  goods.NewService(),
		diningService: dining.NewService(),
		db:            db.DB.Shop,
		shareService:  share.NewShareService(),
		configService: common.NewConfigService(),
		dictService:   dict.NewDictService(db.DB.Shop),
	}
}

// CalculateOrder 计算订单金额
func (s *OrderService) CalculateOrder(req *dto.OrderCalculateReq) (*dto.OrderCalculateResp, error) {
	// 1. 计算商品总金额
	var totalAmount float64
	for _, item := range req.Goods {
		// 获取商品信息
		goodsInfo, err := s.goodsService.GetDetail(item.GoodsId, 0)
		if err != nil {
			return nil, fmt.Errorf("获取商品信息失败: %v", err)
		}

		// 根据商品规格类型处理价格
		var price float64
		if goodsInfo.SpecType == 2 { // 多规格商品
			// 获取SKU信息
			skuInfo, err := s.goodsService.GetGoodsSkuByCode(item.SkuCode)
			if err != nil {
				return nil, fmt.Errorf("获取商品SKU信息失败: %v", err)
			}
			price = skuInfo.Price
		} else { // 单规格商品
			// 对于单规格商品，查找对应的SKU记录
			skuInfo, err := s.goodsService.GetGoodsSkuByCode(item.SkuCode)
			if err != nil {
				return nil, fmt.Errorf("获取商品SKU信息失败: %v", err)
			}
			price = skuInfo.Price
		}

		// 计算商品金额
		totalAmount += float64(item.Num) * price
	}

	// 2. 从配置表获取运费
	freightAmount, err := s.configService.GetFreightConfig()
	if err != nil {
		// 如果获取配置失败，使用默认运费
		freightAmount = 0.00
	}

	// 3. 计算优惠金额(这里简单处理，实际应该根据优惠活动计算)
	discountAmount := 0.00 // 修改为0，避免实付金额过小

	// 4. 计算实付金额
	payAmount := totalAmount + freightAmount - discountAmount

	return &dto.OrderCalculateResp{
		TotalAmount:    totalAmount,
		FreightAmount:  freightAmount,
		DiscountAmount: discountAmount,
		PayAmount:      payAmount,
	}, nil
}

// 获取订单状态文字
func (s *OrderService) getOrderStatusInfo(status string) (string, string) {
	// 使用字典服务获取状态标签
	statusText := s.dictService.GetOrderStatusLabel(status)

	// 状态描述映射
	statusDescMap := map[string]string{
		"unpaid":    "请尽快完成支付",
		"unshipped": "商家正在处理订单",
		"delivered": "商品已发出，请注意查收",
		"completed": "交易已完成",
		"cancelled": "订单已取消",
	}

	desc := statusDescMap[status]
	return statusText, desc
}

// CreateOrder 创建订单
func (s *OrderService) CreateOrder(userId int64, req *dto.CreateOrderReq) (*dto.CreateOrderResp, error) {
	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 生成订单号
	orderNo := utils.GenerateOrderNo()

	// 2. 计算订单金额
	calculateReq := &dto.OrderCalculateReq{
		Goods: req.Goods,
	}
	calculateResp, err := s.CalculateOrder(calculateReq)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	// 3. 创建订单
	// 设置默认用餐类型
	diningType := req.DiningType
	if diningType == "" {
		diningType = "takeout" // 默认为外卖
	}

	order := &ShopDB.OrderDO{
		OrderNo:        orderNo,
		UserID:         userId,
		TotalAmount:    calculateResp.TotalAmount,
		FreightAmount:  calculateResp.FreightAmount,
		DiscountAmount: calculateResp.DiscountAmount,
		PayAmount:      calculateResp.PayAmount,
		PayType:        int8(req.PayType),
		Status:         "unpaid",
		DiningType:     diningType,
		TableID:        req.TableID,
		TableNo:        req.TableNo,
		Remark:         req.Remark,
	}

	if err := tx.Table("shop_order").Create(order).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建订单失败: %v", err)
	}

	// 4. 创建订单收货地址
	receiver := &ShopDB.OrderReceiverDO{
		OrderID:  order.ID,
		Receiver: req.ReceiverName,
		Phone:    req.ReceiverPhone,
		Province: req.ReceiverProvince,
		City:     req.ReceiverCity,
		District: req.ReceiverDistrict,
		Detail:   req.ReceiverAddress,
	}
	if err := tx.Table("shop_order_receiver").Create(receiver).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建订单收货地址失败: %v", err)
	}

	// 5. 创建订单商品
	for _, item := range req.Goods {
		// 获取商品信息
		goodsInfo, err := s.goodsService.GetDetail(item.GoodsId, 0)
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("获取商品信息失败: %v", err)
		}

		var price float64
		var specs string
		if goodsInfo.SpecType == 2 { // 多规格商品
			// 获取SKU信息
			skuInfo, err := s.goodsService.GetGoodsSkuByCode(item.SkuCode)
			if err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("获取商品SKU信息失败: %v", err)
			}
			price = skuInfo.Price

			// 查询SKU规格信息
			var skuSpecInfos []struct {
				SkuID         int64  `gorm:"column:sku_id"`
				SpecGroupName string `gorm:"column:spec_group_name"`
				SpecValue     string `gorm:"column:spec_value"`
			}

			err = db.DB.Shop.Table("shop_goods_sku_spec ss").
				Select("ss.sku_id, sg.name as spec_group_name, sv.value as spec_value").
				Joins("LEFT JOIN shop_goods_spec_group sg ON ss.spec_group_id = sg.id").
				Joins("LEFT JOIN shop_goods_spec_value sv ON ss.spec_value_id = sv.id").
				Where("ss.sku_id = ?", skuInfo.ID).
				Find(&skuSpecInfos).Error
			if err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("查询SKU规格信息失败: %v", err)
			}

			// 构建规格JSON字符串
			if len(skuSpecInfos) > 0 {
				specMap := make(map[string]string)
				for _, spec := range skuSpecInfos {
					specMap[spec.SpecGroupName] = spec.SpecValue
				}
				specBytes, _ := json.Marshal(specMap)
				specs = string(specBytes)
			} else {
				specs = "{}"
			}
		} else { // 单规格商品
			// 对于单规格商品，查找对应的SKU记录
			skuInfo, err := s.goodsService.GetGoodsSkuByCode(item.SkuCode)
			if err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("获取商品SKU信息失败: %v", err)
			}
			price = skuInfo.Price
			specs = "{}" // 单规格商品，规格信息为空JSON对象
		}

		// 创建订单商品
		orderGoods := &ShopDB.OrderGoodsDO{
			OrderID:     order.ID,
			GoodsID:     item.GoodsId,
			GoodsName:   goodsInfo.Name,
			GoodsImage:  goodsInfo.Images[0],
			SkuCode:     item.SkuCode,
			Specs:       specs,
			Price:       price,
			Quantity:    item.Num,
			TotalAmount: float64(item.Num) * price,
		}
		if err := tx.Table("shop_order_goods").Create(orderGoods).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("创建订单商品失败: %v", err)
		}

		// 扣减库存
		if goodsInfo.SpecType == 2 { // 多规格商品才需要扣减SKU库存
			if err := s.goodsService.DeductStock(item.GoodsId, item.SkuCode, item.Num); err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("扣减库存失败: %v", err)
			}
		} else { // 单规格商品直接扣减商品总库存
			if err := s.goodsService.DeductGoodsStock(item.GoodsId, item.Num); err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("扣减库存失败: %v", err)
			}
		}
	}

	// 如果是从购物车创建的订单，清除已选中的商品
	if req.Type == "cart" {
		err := tx.Where("user_id = ? AND selected = 1", userId).Delete(&ShopDB.CartDO{}).Error
		if err != nil {
			tx.Rollback()
			return nil, werror.New(500, "清除购物车失败", map[string]interface{}{
				"userId": userId,
				"error":  err.Error(),
			})
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	return &dto.CreateOrderResp{
		OrderId:   order.ID,
		OrderNo:   order.OrderNo,
		PayAmount: order.PayAmount,
	}, nil
}

// GetOrderDetail 获取订单详情
func (s *OrderService) GetOrderDetail(orderId int64, userId int64) (*vo.OrderDetailVO, error) {
	// 查询订单信息
	var order ShopDB.OrderDO
	err := order.Query().Where("id = ? AND user_id = ?", orderId, userId).First(&order).Error
	if err != nil {
		return nil, fmt.Errorf("订单不存在")
	}

	// 查询收货地址（只有商品订单才需要收货地址）
	var receiver ShopDB.OrderReceiverDO
	var address vo.OrderAddressVO
	if order.BusinessType == "goods" {
		err = (&ShopDB.OrderReceiverDO{}).Query().Where("order_id = ?", orderId).First(&receiver).Error
		if err != nil {
			return nil, fmt.Errorf("订单收货地址不存在")
		}
		address = vo.OrderAddressVO{
			ReceiverName:   receiver.Receiver,
			ReceiverMobile: receiver.Phone,
			Province:       receiver.Province,
			City:           receiver.City,
			District:       receiver.District,
			Detail:         receiver.Detail,
		}
	}

	// 查询订单商品（只有商品订单才有商品信息）
	var goods []ShopDB.OrderGoodsDO
	if order.BusinessType == "goods" {
		err = (&ShopDB.OrderGoodsDO{}).Query().Where("order_id = ?", orderId).Find(&goods).Error
		if err != nil {
			return nil, fmt.Errorf("订单商品不存在")
		}
	}

	// 获取状态文字
	statusText, statusDesc := s.getOrderStatusInfo(order.Status)

	// 获取支付方式文字
	payTypeText := s.dictService.GetDictLabel("order_pay_type", fmt.Sprintf("%d", order.PayType))

	// 获取业务类型文字
	businessTypeText := s.dictService.GetDictLabel("order_business_type", order.BusinessType)

	// 组装数据
	detail := &vo.OrderDetailVO{
		ID:               order.ID,
		OrderNo:          order.OrderNo,
		Status:           order.Status,
		StatusText:       statusText,
		StatusDesc:       statusDesc,
		TotalAmount:      order.TotalAmount,
		FreightAmount:    order.FreightAmount,
		PayAmount:        order.PayAmount,
		PayType:          order.PayType,
		PayTypeText:      payTypeText,
		Remark:           order.Remark,
		CreateTime:       order.CreateTime.Format("2006-01-02 15:04:05"),
		BusinessType:     order.BusinessType,
		BusinessTypeText: businessTypeText,
		Address:          address,
		Goods:            make([]vo.OrderGoodsVO, 0, len(goods)),
	}

	// 填充商品信息
	for _, g := range goods {
		detail.Goods = append(detail.Goods, vo.OrderGoodsVO{
			ID:        g.ID,
			GoodsID:   g.GoodsID,
			GoodsName: g.GoodsName,
			Image:     g.GoodsImage,
			Price:     g.Price,
			Quantity:  g.Quantity,
			Specs:     g.Specs,
		})
	}

	// 填充预约信息
	if order.BusinessType == "booking" {
		var booking BeautyDB.BeautyBookingDO
		err := s.db.Table("beauty_booking").Where("order_id = ? AND is_delete = 0", order.ID).First(&booking).Error
		if err == nil {
			var serviceName string
			s.db.Table("beauty_service").Select("name").Where("id = ?", booking.ServiceID).Scan(&serviceName)
			var technicianName string
			s.db.Table("beauty_technician").Select("name").Where("user_id = ?", booking.TechnicianUserID).Scan(&technicianName)

			// 使用字典服务获取预约状态标签
			statusText := s.dictService.GetBookingStatusLabel(booking.BookingStatus)

			detail.BookingInfo = &vo.BookingInfoVO{
				ServiceName: serviceName,
				Technician:  technicianName,
				BookingDate: booking.BookingDate.Format("2006-01-02"),
				StartTime:   booking.StartTime,
				EndTime:     booking.EndTime,
				Status:      statusText,
			}
		}
	}

	// 填充套餐卡信息
	if order.BusinessType == "card" {
		var userCard do.UserCardDO
		err := s.db.Table("beauty_user_card").Where("order_id = ? AND is_delete = 0", order.ID).First(&userCard).Error
		if err == nil {
			remainCount := 0
			if userCard.RemainingTimes != nil {
				remainCount = *userCard.RemainingTimes
			}
			validFrom := ""
			validTo := ""
			if !userCard.StartTime.IsZero() {
				validFrom = userCard.StartTime.Format("2006-01-02")
			}
			if userCard.EndTime != nil {
				validTo = userCard.EndTime.Format("2006-01-02")
			}

			// 使用字典服务获取套餐卡状态标签
			statusText := s.dictService.GetCardStatusLabel(fmt.Sprintf("%d", userCard.Status))

			detail.CardInfo = &vo.CardInfoVO{
				CardName:    userCard.CardName,
				ValidFrom:   validFrom,
				ValidTo:     validTo,
				RemainCount: remainCount,
				Status:      statusText,
			}
		}
	}

	return detail, nil
}

// PayOrder 支付订单
func (s *OrderService) PayOrder(userId int64, req *dto.PayOrderReq) (map[string]interface{}, error) {
	// 查询订单
	var order ShopDB.OrderDO
	err := order.Query().Where("id = ? AND user_id = ?", req.OrderId, userId).First(&order).Error
	if err != nil {
		return nil, werror.New(404, "订单不存在", map[string]interface{}{
			"orderId": req.OrderId,
			"userId":  userId,
			"error":   err.Error(),
		})
	}

	// 检查订单状态
	if order.Status != "unpaid" {
		return nil, werror.New(400, "订单状态不正确", map[string]interface{}{
			"orderNo":     order.OrderNo,
			"orderStatus": order.Status,
			"payType":     order.PayType,
		})
	}

	// 如果是微信支付，先处理微信登录获取openid
	if req.PayType == 1 && req.WechatCode != "" {
		err = s.updateUserOpenID(userId, req.WechatCode)
		if err != nil {
			return nil, werror.New(500, "获取微信授权失败", map[string]interface{}{
				"userId": userId,
				"error":  err.Error(),
			})
		}
	}

	// 更新支付方式（如果与原支付方式不同）
	if req.PayType != int(order.PayType) {
		err = order.Query().Where("id = ?", req.OrderId).
			Update("pay_type", req.PayType).Error
		if err != nil {
			return nil, werror.New(500, "更新支付方式失败", map[string]interface{}{
				"orderId":    req.OrderId,
				"oldPayType": order.PayType,
				"newPayType": req.PayType,
				"error":      err.Error(),
			})
		}
		order.PayType = int8(req.PayType)
	}

	// 调用支付服务
	paymentFactory := payment.NewPaymentFactory()
	paymentService := paymentFactory.GetPaymentService(req.PayType)
	if paymentService == nil {
		return nil, werror.New(400, "不支持的支付方式", map[string]interface{}{
			"payType": req.PayType,
		})
	}

	// 创建支付订单
	return paymentService.Pay(order.ID, order.OrderNo, order.PayAmount, "order")
}

// GetOrderList 获取订单列表
func (s *OrderService) GetOrderList(userId int64, status string, page, size int, businessType string) (*vo.OrderListVO, error) {
	// 打印查询参数
	fmt.Printf("GetOrderList params: userId=%d, status=%s, businessType=%s, page=%d, size=%d\n", userId, status, businessType, page, size)

	// 构建查询条件
	query := (&ShopDB.OrderDO{}).Query().Where("user_id = ? AND is_delete = 0", userId)

	// 根据状态筛选
	if status != "" {
		query = query.Where("status = ?", status)
		// 打印带状态的查询条件
		fmt.Printf("GetOrderList query with status: user_id = %d AND is_delete = 0 AND status = '%s'\n", userId, status)
	} else {
		// 打印不带状态的查询条件
		fmt.Printf("GetOrderList query without status: user_id = %d AND is_delete = 0\n", userId)
	}
	// 根据业务类型筛选
	if businessType != "" {
		query = query.Where("business_type = ?", businessType)
	}

	// 查询总数
	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return nil, fmt.Errorf("查询订单总数失败")
	}

	// 查询列表
	var orders []ShopDB.OrderDO
	err = query.Order("create_time DESC").
		Offset((page - 1) * size).
		Limit(size).
		Find(&orders).Error
	if err != nil {
		return nil, fmt.Errorf("查询订单列表失败")
	}

	// 组装数据
	list := &vo.OrderListVO{
		Total: total,
		List:  make([]vo.OrderVO, 0, len(orders)),
	}

	// 查询订单商品
	for _, order := range orders {
		var goods []ShopDB.OrderGoodsDO
		err = (&ShopDB.OrderGoodsDO{}).Query().
			Where("order_id = ?", order.ID).
			Find(&goods).Error
		if err != nil {
			return nil, fmt.Errorf("查询订单商品失败")
		}

		// 获取状态文字
		statusText, statusDesc := s.getOrderStatusInfo(order.Status)

		orderVO := vo.OrderVO{
			ID:            order.ID,
			OrderNo:       order.OrderNo,
			Status:        order.Status,
			StatusText:    statusText,
			StatusDesc:    statusDesc,
			TotalAmount:   order.TotalAmount,
			FreightAmount: order.FreightAmount,
			PayAmount:     order.PayAmount,
			PayType:       order.PayType,
			CreateTime:    order.CreateTime.Format("2006-01-02 15:04:05"),
			Goods:         make([]vo.OrderGoodsVO, 0, len(goods)),
			BusinessType:  order.BusinessType,
		}

		for _, g := range goods {
			orderVO.Goods = append(orderVO.Goods, vo.OrderGoodsVO{
				ID:        g.ID,
				GoodsID:   g.GoodsID,
				GoodsName: g.GoodsName,
				Image:     g.GoodsImage,
				Price:     g.Price,
				Quantity:  g.Quantity,
				Specs:     g.Specs,
			})
		}

		// 填充预约信息
		if order.BusinessType == "booking" {
			var booking BeautyDB.BeautyBookingDO
			err := s.db.Table("beauty_booking").Where("order_id = ? AND is_delete = 0", order.ID).First(&booking).Error
			if err == nil {
				// 查服务名
				var serviceName string
				s.db.Table("beauty_service").Select("name").Where("id = ?", booking.ServiceID).Scan(&serviceName)
				// 查技师名
				var technicianName string
				s.db.Table("beauty_technician").Select("name").Where("user_id = ?", booking.TechnicianUserID).Scan(&technicianName)
				// 使用字典服务获取预约状态标签
				statusText := s.dictService.GetBookingStatusLabel(booking.BookingStatus)
				orderVO.BookingInfo = &vo.BookingInfoVO{
					ServiceName: serviceName,
					Technician:  technicianName,
					BookingDate: booking.BookingDate.Format("2006-01-02"),
					StartTime:   booking.StartTime,
					EndTime:     booking.EndTime,
					Status:      statusText,
				}
			}
		}
		// 填充套餐卡信息
		if order.BusinessType == "card" {
			var userCard do.UserCardDO
			err := s.db.Table("beauty_user_card").Where("order_id = ? AND is_delete = 0", order.ID).First(&userCard).Error
			if err == nil {
				remainCount := 0
				if userCard.RemainingTimes != nil {
					remainCount = *userCard.RemainingTimes
				}
				validFrom := ""
				validTo := ""
				if !userCard.StartTime.IsZero() {
					validFrom = userCard.StartTime.Format("2006-01-02")
				}
				if userCard.EndTime != nil {
					validTo = userCard.EndTime.Format("2006-01-02")
				}
				// 使用字典服务获取套餐卡状态标签
				statusText := s.dictService.GetCardStatusLabel(fmt.Sprintf("%d", userCard.Status))

				orderVO.CardInfo = &vo.CardInfoVO{
					CardName:    userCard.CardName,
					ValidFrom:   validFrom,
					ValidTo:     validTo,
					RemainCount: remainCount,
					Status:      statusText,
				}
			}
		}

		list.List = append(list.List, orderVO)
	}

	return list, nil
}

// CancelOrder 取消订单
func (s *OrderService) CancelOrder(userId, orderId int64) error {
	// 查询订单
	order := &ShopDB.OrderDO{}
	err := order.Query().Where("id = ? AND user_id = ?", orderId, userId).First(order).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return werror.New(404, "订单不存在").
				WithContext("orderId", orderId).
				WithContext("userId", userId).
				WithLevel(werror.LevelWarning)
		}
		return werror.Wrap(err, 500, "查询订单失败").
			WithContext("orderId", orderId).
			WithContext("userId", userId)
	}

	// 检查订单状态
	if order.Status != "unpaid" {
		return werror.New(400, "只能取消未支付的订单").
			WithContext("orderId", orderId).
			WithContext("userId", userId).
			WithContext("status", order.Status).
			WithLevel(werror.LevelWarning)
	}

	// 开启事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新订单状态
	err = tx.Model(order).Updates(map[string]interface{}{
		"status": "cancelled",
	}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	// 恢复商品库存
	var orderGoods []ShopDB.OrderGoodsDO
	err = tx.Where("order_id = ?", orderId).Find(&orderGoods).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	for _, goods := range orderGoods {
		// 恢复SKU库存
		err = s.goodsService.DeductStock(goods.GoodsID, goods.SkuCode, -goods.Quantity)
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// ConfirmReceive 确认收货
func (s *OrderService) ConfirmReceive(userId, orderId int64) error {
	// 开启事务
	tx := s.db.Begin()
	if tx.Error != nil {
		return tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询订单
	order := &ShopDB.OrderDO{}
	err := tx.Where("id = ? AND user_id = ?", orderId, userId).First(order).Error
	if err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return werror.New(404, "订单不存在").
				WithContext("orderId", orderId).
				WithContext("userId", userId).
				WithLevel(werror.LevelWarning)
		}
		return werror.Wrap(err, 500, "查询订单失败").
			WithContext("orderId", orderId).
			WithContext("userId", userId)
	}

	// 检查订单状态
	if order.Status != "delivered" {
		tx.Rollback()
		return werror.New(400, "只能确认已发货的订单").
			WithContext("orderId", orderId).
			WithContext("userId", userId).
			WithContext("status", order.Status).
			WithLevel(werror.LevelWarning)
	}

	// 更新订单状态
	now := time.Now()
	err = tx.Model(order).Updates(map[string]interface{}{
		"status":       "completed",
		"receive_time": now,
		"finish_time":  now,
		"update_time":  now,
	}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	// 创建分销收益记录
	err = s.shareService.CreateDistributionIncome(tx.Statement.Context, order)
	if err != nil {
		tx.Rollback()
		return err
	}

	// 提交事务
	return tx.Commit().Error
}

// DeleteOrder 删除订单
func (s *OrderService) DeleteOrder(userId, orderId int64) error {
	// 查询订单
	order := &ShopDB.OrderDO{}
	err := order.Query().Where("id = ? AND user_id = ?", orderId, userId).First(order).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return werror.New(404, "订单不存在").
				WithContext("orderId", orderId).
				WithContext("userId", userId).
				WithLevel(werror.LevelWarning)
		}
		return werror.Wrap(err, 500, "查询订单失败").
			WithContext("orderId", orderId).
			WithContext("userId", userId)
	}

	// 检查订单状态
	if order.Status != "completed" && order.Status != "cancelled" {
		return werror.New(400, "只能删除已完成或已取消的订单").
			WithContext("orderId", orderId).
			WithContext("userId", userId).
			WithContext("status", order.Status).
			WithLevel(werror.LevelWarning)
	}

	// 标记订单为删除状态
	return s.db.Model(order).Updates(map[string]interface{}{
		"is_delete": 1,
	}).Error
}

// updateUserOpenID 更新用户openid
func (s *OrderService) updateUserOpenID(userId int64, code string) error {
	// 调用微信API获取openid
	openid, err := s.getWechatOpenID(code)
	if err != nil {
		return fmt.Errorf("获取微信openid失败: %v", err)
	}

	// 更新用户的openid
	err = s.db.Model(&ShopDB.UserDO{}).
		Where("id = ?", userId).
		Update("openid", openid).Error
	if err != nil {
		return fmt.Errorf("更新用户openid失败: %v", err)
	}

	return nil
}

// getWechatOpenID 调用微信API获取openid
func (s *OrderService) getWechatOpenID(code string) (string, error) {
	appID := config.App.Wx.MiniApp.AppID
	appSecret := config.App.Wx.MiniApp.Secret

	if appID == "" || appSecret == "" {
		return "", fmt.Errorf("微信小程序配置不完整")
	}

	url := fmt.Sprintf("https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
		appID, appSecret, code)

	resp, err := http.Get(url)
	if err != nil {
		return "", fmt.Errorf("调用微信API失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取微信API响应失败: %v", err)
	}

	var result struct {
		OpenID     string `json:"openid"`
		SessionKey string `json:"session_key"`
		ErrCode    int    `json:"errcode"`
		ErrMsg     string `json:"errmsg"`
	}

	if err := json.Unmarshal(body, &result); err != nil {
		return "", fmt.Errorf("解析微信API响应失败: %v", err)
	}

	if result.ErrCode != 0 {
		return "", fmt.Errorf("微信API错误: %s (错误码: %d)", result.ErrMsg, result.ErrCode)
	}

	if result.OpenID == "" {
		return "", fmt.Errorf("微信API返回的openid为空")
	}

	return result.OpenID, nil
}
