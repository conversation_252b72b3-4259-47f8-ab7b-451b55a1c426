package address

import (
	"errors"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/model/vo"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

type Service struct{}

func NewService() *Service {
	return &Service{}
}

// GetDefaultAddress 获取默认收货地址
func (s *Service) GetDefaultAddress(userID int64) (*vo.AddressVO, error) {
	var addr ShopDB.AddressDO
	err := addr.Query().Where("user_id = ? AND is_default = 1 AND is_delete = 0", userID).First(&addr).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return &vo.AddressVO{
		ID:             addr.ID,
		ReceiverName:   addr.ReceiverName,
		ReceiverMobile: addr.ReceiverMobile,
		Province:       addr.Province,
		City:           addr.City,
		District:       addr.District,
		Detail:         addr.Detail,
		IsDefault:      addr.IsDefault == 1,
	}, nil
}

// GetAddressList 获取收货地址列表
func (s *Service) GetAddressList(userID int64) ([]*vo.AddressVO, error) {
	var addrs []ShopDB.AddressDO
	err := (&ShopDB.AddressDO{}).Query().
		Where("user_id = ? AND is_delete = 0", userID).
		Order("is_default DESC, create_time DESC").
		Find(&addrs).Error
	if err != nil {
		return nil, err
	}

	list := make([]*vo.AddressVO, 0, len(addrs))
	for _, addr := range addrs {
		list = append(list, &vo.AddressVO{
			ID:             addr.ID,
			ReceiverName:   addr.ReceiverName,
			ReceiverMobile: addr.ReceiverMobile,
			Province:       addr.Province,
			City:           addr.City,
			District:       addr.District,
			Detail:         addr.Detail,
			IsDefault:      addr.IsDefault == 1,
		})
	}

	return list, nil
}

// AddAddress 添加收货地址
func (s *Service) AddAddress(userID int64, req *dto.AddressReq) error {
	// 如果是默认地址，先将其他地址设为非默认
	if req.IsDefault {
		err := (&ShopDB.AddressDO{}).Query().
			Where("user_id = ? AND is_delete = 0", userID).
			Update("is_default", 0).Error
		if err != nil {
			return err
		}
	}

	// 创建新地址
	addr := &ShopDB.AddressDO{
		UserID:         userID,
		ReceiverName:   req.ReceiverName,
		ReceiverMobile: req.ReceiverMobile,
		Province:       req.Province,
		City:           req.City,
		District:       req.District,
		Detail:         req.Detail,
		IsDefault:      0,
	}
	if req.IsDefault {
		addr.IsDefault = 1
	}

	return db.DB.Shop.Create(addr).Error
}

// UpdateAddress 修改收货地址
func (s *Service) UpdateAddress(userID int64, req *dto.AddressReq) error {
	// 查询地址是否存在
	addr := &ShopDB.AddressDO{}
	err := addr.Query().Where("id = ? AND user_id = ? AND is_delete = 0", req.ID, userID).First(addr).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return werror.New(404, "地址不存在").
				WithContext("addressId", req.ID).
				WithContext("userId", userID).
				WithLevel(werror.LevelWarning)
		}
		return werror.Wrap(err, 500, "查询地址失败").
			WithContext("addressId", req.ID).
			WithContext("userId", userID)
	}

	// 如果设为默认地址，先将其他地址设为非默认
	if req.IsDefault && addr.IsDefault == 0 {
		err := (&ShopDB.AddressDO{}).Query().
			Where("user_id = ? AND is_delete = 0", userID).
			Update("is_default", 0).Error
		if err != nil {
			return werror.Wrap(err, 500, "更新默认地址失败").
				WithContext("userId", userID)
		}
	}

	// 更新地址信息
	addr.ReceiverName = req.ReceiverName
	addr.ReceiverMobile = req.ReceiverMobile
	addr.Province = req.Province
	addr.City = req.City
	addr.District = req.District
	addr.Detail = req.Detail
	if req.IsDefault {
		addr.IsDefault = 1
	}

	return db.DB.Shop.Save(addr).Error
}

// DeleteAddress 删除收货地址
func (s *Service) DeleteAddress(userID int64, id int64) error {
	// 查询地址是否存在
	addr := &ShopDB.AddressDO{}
	err := addr.Query().Where("id = ? AND user_id = ? AND is_delete = 0", id, userID).First(addr).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return werror.New(404, "地址不存在").
				WithContext("addressId", id).
				WithContext("userId", userID).
				WithLevel(werror.LevelWarning)
		}
		return werror.Wrap(err, 500, "查询地址失败").
			WithContext("addressId", id).
			WithContext("userId", userID)
	}

	// 软删除地址
	return db.DB.Shop.Model(addr).Update("is_delete", 1).Error
}

// SetDefaultAddress 设置默认地址
func (s *Service) SetDefaultAddress(userID int64, id int64) error {
	// 查询地址是否存在
	addr := &ShopDB.AddressDO{}
	err := addr.Query().Where("id = ? AND user_id = ? AND is_delete = 0", id, userID).First(addr).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return werror.New(404, "地址不存在").
				WithContext("addressId", id).
				WithContext("userId", userID).
				WithLevel(werror.LevelWarning)
		}
		return werror.Wrap(err, 500, "查询地址失败").
			WithContext("addressId", id).
			WithContext("userId", userID)
	}

	// 开启事务
	return db.DB.Shop.Transaction(func(tx *gorm.DB) error {
		// 将其他地址设为非默认
		if err := tx.Model(&ShopDB.AddressDO{}).
			Where("user_id = ? AND is_delete = 0", userID).
			Update("is_default", 0).Error; err != nil {
			return err
		}

		// 设置当前地址为默认
		return tx.Model(addr).Update("is_default", 1).Error
	})
}

// GetAddressDetail 获取单个地址详情
func (s *Service) GetAddressDetail(userID int64, id int64) (*vo.AddressVO, error) {
	var addr ShopDB.AddressDO
	err := addr.Query().Where("id = ? AND user_id = ? AND is_delete = 0", id, userID).First(&addr).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, werror.New(404, "地址不存在").
				WithContext("addressId", id).
				WithContext("userId", userID).
				WithLevel(werror.LevelWarning)
		}
		return nil, werror.Wrap(err, 500, "查询地址失败").
			WithContext("addressId", id).
			WithContext("userId", userID)
	}

	return &vo.AddressVO{
		ID:             addr.ID,
		ReceiverName:   addr.ReceiverName,
		ReceiverMobile: addr.ReceiverMobile,
		Province:       addr.Province,
		City:           addr.City,
		District:       addr.District,
		Detail:         addr.Detail,
		IsDefault:      addr.IsDefault == 1,
	}, nil
}
