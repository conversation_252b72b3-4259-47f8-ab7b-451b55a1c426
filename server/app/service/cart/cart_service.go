package cart

import (
	"encoding/json"
	"errors"
	"strings"
	"wnsys/shop/app/common/utils"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/model/vo"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// Service 购物车服务
type Service struct{}

// NewService 创建购物车服务
func NewService() *Service {
	return &Service{}
}

// AddCart 添加商品到购物车
func (s *Service) AddCart(userID int64, req *dto.AddCartReq) (int64, error) {
	// 查询商品信息
	goods := &ShopDB.GoodsDO{}
	err := goods.Query().Where("id = ?", req.GoodsID).First(goods).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, werror.New(400, "商品不存在").
				WithContext("goodsId", req.GoodsID).
				WithLevel(werror.LevelWarning)
		}
		return 0, werror.Wrap(err, 500, "查询商品失败").
			WithContext("goodsId", req.GoodsID)
	}

	var finalPrice float64
	var currentStock int

	// 根据商品规格类型处理
	if goods.SpecType == 3 && (req.SkuCode == "COMBO_DEFAULT" || strings.HasPrefix(req.SkuCode, "COMBO_")) {
		// 套餐规格商品处理
		// 查询套餐规格的SKU（套餐商品通常只有一个SKU）
		comboSku := &ShopDB.GoodsSkuDO{}
		err = comboSku.Query().Where("goods_id = ? AND status = 1", req.GoodsID).First(comboSku).Error
		if err != nil {
			return 0, werror.Wrap(err, 500, "查询套餐商品SKU失败").
				WithContext("goodsId", req.GoodsID)
		}

		// 使用正确的SKU编码
		if req.SkuCode == "COMBO_DEFAULT" {
			req.SkuCode = comboSku.SkuCode
		}

		finalPrice = comboSku.Price   // 基础价格
		currentStock = comboSku.Stock // 使用套餐SKU库存

		// 解析套餐规格，生成详细的规格JSON并计算总价
		var comboSpecs map[string]string
		if err := json.Unmarshal([]byte(req.SpecsJSON), &comboSpecs); err == nil {
			detailedSpecs := make(map[string]interface{})

			// 查询套餐规格组和选项，计算加价并生成详细规格
			for groupName, optionName := range comboSpecs {
				// 查询规格组
				var specGroup ShopDB.GoodsSpecGroupDO
				err := (&ShopDB.GoodsSpecGroupDO{}).Query().
					Where("goods_id = ? AND name = ? AND spec_type = 3", req.GoodsID, groupName).
					First(&specGroup).Error
				if err != nil {
					detailedSpecs[groupName] = map[string]interface{}{
						"name":  optionName,
						"error": "规格组未找到",
					}
					continue
				}

				// 查询规格值（套餐选项）
				var specValue ShopDB.GoodsSpecValueDO
				err = (&ShopDB.GoodsSpecValueDO{}).Query().
					Where("spec_group_id = ? AND name = ?", specGroup.ID, optionName).
					First(&specValue).Error
				if err != nil {
					detailedSpecs[groupName] = map[string]interface{}{
						"name":  optionName,
						"error": "规格值未找到",
					}
					continue
				}

				// 添加选项加价
				finalPrice += specValue.Config.AddPrice

				// 生成详细规格信息
				detailedSpecs[groupName] = map[string]interface{}{
					"name":       optionName,
					"value":      specValue.Value,
					"add_price":  specValue.Config.AddPrice,
					"image":      specValue.Config.Image,
					"quantity":   specValue.Config.Quantity,
					"is_default": specValue.Config.IsDefault,
				}
			}

			// 更新规格JSON为详细格式
			if detailedSpecsJSON, err := json.Marshal(detailedSpecs); err == nil {
				req.SpecsJSON = string(detailedSpecsJSON)
			}
		}
	} else {
		// 普通商品（单规格或多规格）处理
		sku := &ShopDB.GoodsSkuDO{}
		err = sku.Query().Where("goods_id = ? AND sku_code = ? AND status = 1",
			req.GoodsID, req.SkuCode).First(sku).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return 0, werror.New(400, "商品规格不存在").
					WithContext("goodsId", req.GoodsID).
					WithContext("skuCode", req.SkuCode).
					WithLevel(werror.LevelWarning)
			}
			return 0, werror.Wrap(err, 500, "查询商品规格失败").
				WithContext("goodsId", req.GoodsID).
				WithContext("skuCode", req.SkuCode)
		}

		finalPrice = sku.Price
		currentStock = sku.Stock
	}

	// 检查库存
	if currentStock < req.Quantity {
		return 0, werror.New(400, "商品库存不足").
			WithContext("goodsId", req.GoodsID).
			WithContext("skuCode", req.SkuCode).
			WithContext("stock", currentStock).
			WithContext("quantity", req.Quantity).
			WithLevel(werror.LevelWarning)
	}

	// 查询是否已存在
	cart := &ShopDB.CartDO{}
	err = cart.Query().Where("user_id = ? AND goods_id = ? AND sku_code = ?",
		userID, req.GoodsID, req.SkuCode).First(cart).Error
	if err == nil {
		// 已存在，更新数量
		cart.Quantity += req.Quantity
		cart.Price = finalPrice // 更新价格（套餐商品可能价格会变化）
		err = db.DB.Shop.Save(cart).Error
		return cart.ID, err
	}

	// 不存在，创建新记录
	cart = &ShopDB.CartDO{
		UserID:    userID,
		GoodsID:   req.GoodsID,
		SkuCode:   req.SkuCode,
		SpecsJSON: req.SpecsJSON,
		Quantity:  req.Quantity,
		Price:     finalPrice, // 使用计算后的最终价格
		Selected:  1,          // 默认选中
	}
	err = db.DB.Shop.Create(cart).Error
	return cart.ID, err
}

// GetCartCount 获取购物车数量
func (s *Service) GetCartCount(userID int64) (int64, error) {
	var count int64
	err := (&ShopDB.CartDO{}).Query().Where("user_id = ?", userID).Count(&count).Error
	return count, err
}

// GetCartList 获取购物车列表
func (s *Service) GetCartList(userID int64, selected bool) (*vo.CartListVO, error) {
	// 查询购物车列表
	var cartList []ShopDB.CartDO
	query := (&ShopDB.CartDO{}).Query().Where("user_id = ?", userID)

	// 如果需要查询已选中的商品
	if selected {
		query = query.Where("selected = 1")
	}

	err := query.Order("create_time DESC").Find(&cartList).Error
	if err != nil {
		return nil, err
	}

	if len(cartList) == 0 {
		return &vo.CartListVO{
			Total:      0,
			TotalPrice: 0,
			List:       make([]vo.CartItemVO, 0),
		}, nil
	}

	// 获取商品ID列表
	var goodsIDs []int64
	for _, cart := range cartList {
		goodsIDs = append(goodsIDs, cart.GoodsID)
	}

	// 查询商品信息
	var goodsList []ShopDB.GoodsDO
	err = (&ShopDB.GoodsDO{}).Query().
		Where("id IN ?", goodsIDs).
		Find(&goodsList).Error
	if err != nil {
		return nil, err
	}

	// 查询商品主图
	var images []struct {
		GoodsID int64  `gorm:"column:goods_id"`
		URL     string `gorm:"column:url"`
	}
	err = (&ShopDB.GoodsImageDO{}).Query().
		Select("goods_id, url").
		Where("goods_id IN ? AND position = ?", goodsIDs, 1).
		Find(&images).Error
	if err != nil {
		return nil, err
	}

	// 构建商品ID到图片的映射
	imageMap := make(map[int64]string)
	for _, img := range images {
		imageMap[img.GoodsID] = img.URL
	}

	// 查询SKU库存
	var skuList []ShopDB.GoodsSkuDO
	err = (&ShopDB.GoodsSkuDO{}).Query().
		Where("goods_id IN ?", goodsIDs).
		Find(&skuList).Error
	if err != nil {
		return nil, err
	}

	// 构建SKU编码到SKU信息的映射
	skuMap := make(map[string]*ShopDB.GoodsSkuDO)
	for i := range skuList {
		skuMap[skuList[i].SkuCode] = &skuList[i]
	}

	// 构建商品ID到商品信息的映射
	goodsMap := make(map[int64]*ShopDB.GoodsDO)
	for i := range goodsList {
		goodsMap[goodsList[i].ID] = &goodsList[i]
	}

	// 组装数据
	result := &vo.CartListVO{
		Total:      int64(len(cartList)),
		TotalPrice: 0,
		List:       make([]vo.CartItemVO, 0, len(cartList)),
	}

	for _, cart := range cartList {
		goods, ok := goodsMap[cart.GoodsID]
		if !ok {
			continue
		}

		sku, ok := skuMap[cart.SkuCode]
		if !ok {
			continue
		}

		// 解析规格JSON
		var specs []vo.SpecItem

		// 尝试解析新格式（详细规格JSON）
		var detailedSpecs map[string]interface{}
		if err := json.Unmarshal([]byte(cart.SpecsJSON), &detailedSpecs); err == nil {
			// 检查是否是详细格式（包含对象而不是简单字符串）
			isDetailedFormat := false
			for _, value := range detailedSpecs {
				if _, ok := value.(map[string]interface{}); ok {
					isDetailedFormat = true
					break
				}
			}

			if isDetailedFormat {
				// 详细格式：{"主食": {"name": "test-复制", "value": "goods_4", ...}}
				// 查询商品规格组定义，确保顺序一致
				var goodsSpecGroups []ShopDB.GoodsSpecGroupDO
				if err := (&ShopDB.GoodsSpecGroupDO{}).Query().
					Where("goods_id = ?", cart.GoodsID).
					Order("sort ASC").
					Find(&goodsSpecGroups).Error; err != nil {
					return nil, err
				}

				// 按规格组定义的顺序构建规格列表
				for _, specGroup := range goodsSpecGroups {
					if specData, ok := detailedSpecs[specGroup.Name]; ok {
						if specObj, ok := specData.(map[string]interface{}); ok {
							if name, nameOk := specObj["name"].(string); nameOk {
								specs = append(specs, vo.SpecItem{
									Name:  specGroup.Name,
									Value: name,
								})
							}
						}
					}
				}
			} else {
				// 简单格式：{"颜色": "红色", "尺寸": "M"}
				var specsMap map[string]string
				if err := json.Unmarshal([]byte(cart.SpecsJSON), &specsMap); err != nil {
					return nil, err
				}

				// 简单格式规格处理：直接从specsMap构建规格列表
				// 为了保持一致的顺序，按规格名称排序
				var specNames []string
				for name := range specsMap {
					specNames = append(specNames, name)
				}
				// 对规格名称进行排序，确保顺序一致
				for i := 0; i < len(specNames); i++ {
					for j := i + 1; j < len(specNames); j++ {
						if specNames[i] > specNames[j] {
							specNames[i], specNames[j] = specNames[j], specNames[i]
						}
					}
				}
				// 按排序后的名称构建规格列表
				for _, name := range specNames {
					specs = append(specs, vo.SpecItem{
						Name:  name,
						Value: specsMap[name],
					})
				}
			}
		} else {
			// 解析失败，返回错误
			return nil, err
		}

		item := vo.CartItemVO{
			ID:       cart.ID,
			GoodsID:  cart.GoodsID,
			Name:     goods.Name,
			Image:    utils.BuildImageURL(imageMap[cart.GoodsID]),
			SkuCode:  cart.SkuCode,
			Specs:    specs,
			Price:    cart.Price,
			Quantity: cart.Quantity,
			Stock:    sku.Stock,
			Status:   goods.Status,
			Selected: cart.Selected == 1,
			SubTotal: cart.Price * float64(cart.Quantity),
		}

		if item.Selected {
			result.TotalPrice += item.SubTotal
		}
		result.List = append(result.List, item)
	}

	return result, nil
}

// UpdateCartQuantity 修改购物车商品数量
func (s *Service) UpdateCartQuantity(userID int64, req *dto.UpdateCartReq) error {
	// 查询购物车记录
	cart := &ShopDB.CartDO{}
	err := cart.Query().Where("id = ? AND user_id = ?", req.ID, userID).First(cart).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return werror.New(404, "购物车记录不存在").
				WithContext("cartId", req.ID).
				WithContext("userId", userID).
				WithLevel(werror.LevelWarning)
		}
		return werror.Wrap(err, 500, "查询购物车失败").
			WithContext("cartId", req.ID).
			WithContext("userId", userID)
	}

	// 查询SKU库存
	sku := &ShopDB.GoodsSkuDO{}
	err = sku.Query().Where("goods_id = ? AND sku_code = ? AND status = 1",
		cart.GoodsID, cart.SkuCode).First(sku).Error
	if err != nil {
		return werror.Wrap(err, 500, "查询商品规格失败").
			WithContext("goodsId", cart.GoodsID).
			WithContext("skuCode", cart.SkuCode)
	}

	// 检查库存
	if sku.Stock < req.Quantity {
		return werror.New(400, "商品库存不足").
			WithContext("goodsId", cart.GoodsID).
			WithContext("skuCode", cart.SkuCode).
			WithContext("stock", sku.Stock).
			WithContext("quantity", req.Quantity).
			WithLevel(werror.LevelWarning)
	}

	// 更新数量
	cart.Quantity = req.Quantity
	return db.DB.Shop.Save(cart).Error
}

// UpdateCartSelected 修改购物车商品选中状态
func (s *Service) UpdateCartSelected(userID int64, req *dto.UpdateCartSelectedReq) error {
	selected := 0
	if req.Selected {
		selected = 1
	}

	return (&ShopDB.CartDO{}).Query().
		Where("id IN ? AND user_id = ?", req.IDs, userID).
		Updates(map[string]interface{}{
			"selected": selected,
		}).Error
}

// DeleteCart 删除购物车商品
func (s *Service) DeleteCart(userID int64, req *dto.DeleteCartReq) error {
	return (&ShopDB.CartDO{}).Query().
		Where("id IN ? AND user_id = ?", req.IDs, userID).
		Updates(map[string]interface{}{
			"is_delete": 1,
		}).Error
}

// ClearCart 清空购物车
func (s *Service) ClearCart(userID int64) error {
	return (&ShopDB.CartDO{}).Query().
		Where("user_id = ?", userID).
		Updates(map[string]interface{}{
			"is_delete": 1,
		}).Error
}

// ClearSelectedCart 清除已选中的购物车商品
func (s *Service) ClearSelectedCart(userID int64) error {
	return db.DB.Shop.Where("user_id = ? AND selected = 1", userID).Delete(&ShopDB.CartDO{}).Error
}
