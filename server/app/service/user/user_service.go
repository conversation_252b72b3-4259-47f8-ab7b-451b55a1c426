package user

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"wnsys/shop/app/common/utils"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/config"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/model/vo"
	"wnsys/shop/app/provider/db"
	"wnsys/shop/app/service/common"
	"wnsys/shop/app/service/sms"

	"wnsys/shop/app/model/do/BeautyDB"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis"
	"github.com/silenceper/wechat/v2"
	"github.com/silenceper/wechat/v2/cache"
	"github.com/silenceper/wechat/v2/miniprogram"
	miniconfig "github.com/silenceper/wechat/v2/miniprogram/config"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// --- 微信数据结构体 ---
type WechatPhoneInfo struct {
	PhoneNumber     string `json:"phoneNumber"`
	PurePhoneNumber string `json:"purePhoneNumber"`
	CountryCode     string `json:"countryCode"`
	Watermark       struct {
		Timestamp int64  `json:"timestamp"`
		AppID     string `json:"appid"`
	} `json:"watermark"`
}

type Service struct {
	db          *gorm.DB
	redis       redis.UniversalClient
	wechat      *wechat.Wechat
	miniProgram *miniprogram.MiniProgram
}

func NewService() *Service {
	// 初始化微信SDK
	wc := wechat.NewWechat()

	// 使用Redis缓存替代内存缓存，解决缓存过期问题
	redisCache := cache.NewRedis(context.Background(), &cache.RedisOpts{
		Host:        "127.0.0.1:6379",
		Database:    0,
		MaxActive:   10,
		MaxIdle:     10,
		IdleTimeout: 240,
	})

	cfg := &miniconfig.Config{
		AppID:     config.App.Wx.MiniApp.AppID,
		AppSecret: config.App.Wx.MiniApp.Secret,
		Cache:     redisCache,
	}
	miniProgram := wc.GetMiniProgram(cfg)

	return &Service{
		db:          db.DB.Shop,
		redis:       db.DB.Redis,
		wechat:      wc,
		miniProgram: miniProgram,
	}
}

// Login 用户登录
func (s *Service) Login(req *dto.LoginReq) (*dto.LoginResp, error) {
	// 1. 查询用户
	var user ShopDB.UserDO
	err := (&ShopDB.UserDO{}).Query().Where("username = ?", req.Username).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, werror.New(404, "用户不存在").
				WithContext("username", req.Username).
				WithLevel(werror.LevelWarning)
		}
		return nil, werror.Wrap(err, 500, "查询用户失败").
			WithContext("username", req.Username)
	}

	// 2. 验证密码
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password))
	if err != nil {
		return nil, werror.New(403, "密码错误").
			WithContext("userId", user.ID).
			WithContext("username", req.Username).
			WithLevel(werror.LevelWarning)
	}

	// 3. 检查用户状态
	if user.IsDelete != 0 {
		return nil, werror.New(403, "账号已被禁用").
			WithContext("userId", user.ID).
			WithContext("username", req.Username).
			WithLevel(werror.LevelWarning)
	}

	// 4. 生成token
	token, err := utils.GenerateToken(user.ID, user.Username)
	if err != nil {
		return nil, werror.Wrap(err, 500, "生成token失败").
			WithContext("userId", user.ID).
			WithContext("username", req.Username)
	}

	// 5. 返回用户信息
	return &dto.LoginResp{
		Token:    token,
		ID:       user.ID,
		Username: user.Username,
		Nickname: user.Nickname,
		Avatar:   user.Avatar,
		Balance:  user.Balance,
		Phone:    user.Phone,
		Gender:   user.Gender,
	}, nil
}

// GetUserInfo 获取用户信息
func (s *Service) GetUserInfo(userId int64) (*dto.LoginResp, error) {
	var user ShopDB.UserDO
	err := (&ShopDB.UserDO{}).Query().Where("id = ?", userId).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, werror.New(404, "用户不存在").
				WithContext("userId", userId).
				WithLevel(werror.LevelWarning)
		}
		return nil, werror.Wrap(err, 500, "查询用户失败").
			WithContext("userId", userId)
	}

	// 处理生日格式
	var birthday string
	if user.Birthday != nil && *user.Birthday != "" {
		if t, err := time.Parse("2006-01-02T15:04:05+08:00", *user.Birthday); err == nil {
			birthday = t.Format("2006-01-02")
		} else {
			birthday = *user.Birthday
		}
	}

	// 查询技师信息
	var technicianInfo map[string]interface{} = nil
	isTechnician := false
	var technician BeautyDB.BeautyTechnicianDO
	err = (&BeautyDB.BeautyTechnicianDO{}).Query().Where("user_id = ?", userId).First(&technician).Error
	if err == nil {
		isTechnician = true
		technicianInfo = map[string]interface{}{
			"id":     technician.ID,
			"name":   technician.Name,
			"level":  technician.Level,
			"status": technician.Status,
			// 可按需补充更多字段
		}
	}

	resp := &dto.LoginResp{
		ID:             user.ID,
		Username:       user.Username,
		Nickname:       user.Nickname,
		Avatar:         user.Avatar,
		Balance:        user.Balance,
		Gender:         user.Gender,
		Phone:          user.Phone,
		Birthday:       birthday,
		IsTechnician:   isTechnician,
		TechnicianInfo: technicianInfo,
	}
	return resp, nil
}

// Logout 处理用户退出登录
func (s *Service) Logout(token string) error {
	// 将token加入黑名单
	err := s.redis.Set("token_blacklist:"+token, "1", 24*time.Hour).Err()
	if err != nil {
		return err
	}
	return nil
}

// GetCollectList 获取收藏列表
func (s *Service) GetCollectList(userId int64, page, size int) (*vo.PageVO, error) {
	var total int64
	var list []struct {
		ID         int64     `json:"id"`
		GoodsID    int64     `json:"goodsId"`
		Name       string    `json:"name"`
		CreateTime time.Time `json:"collectTime"`
	}

	// 查询总数
	err := s.db.Model(&ShopDB.CollectDO{}).
		Select("collect.id, collect.target_id as goods_id, shop_goods.name, collect.create_time").
		Joins("LEFT JOIN shop_goods ON shop_goods.id = collect.target_id").
		Where("collect.user_id = ? AND collect.target_type = ? AND collect.action_type = ? AND collect.is_delete = 0",
			userId, "goods", ShopDB.ActionTypeCollect).
		Count(&total).Error
	if err != nil {
		return nil, err
	}

	// 查询列表
	err = s.db.Model(&ShopDB.CollectDO{}).
		Select("collect.id, collect.target_id as goods_id, shop_goods.name, collect.create_time").
		Joins("LEFT JOIN shop_goods ON shop_goods.id = collect.target_id").
		Where("collect.user_id = ? AND collect.target_type = ? AND collect.action_type = ? AND collect.is_delete = 0",
			userId, "goods", ShopDB.ActionTypeCollect).
		Order("collect.create_time DESC").
		Offset((page - 1) * size).
		Limit(size).
		Scan(&list).Error
	if err != nil {
		return nil, err
	}

	// 查询商品主图
	var goodsIDs []int64
	for _, item := range list {
		goodsIDs = append(goodsIDs, item.GoodsID)
	}

	var images []struct {
		GoodsID int64  `gorm:"column:goods_id"`
		URL     string `gorm:"column:url"`
	}
	if len(goodsIDs) > 0 {
		err = (&ShopDB.GoodsImageDO{}).Query().
			Select("goods_id, url").
			Where("goods_id IN (?) AND position = ?", goodsIDs, 1).
			Find(&images).Error
		if err != nil {
			return nil, err
		}
	}

	// 构建商品ID到图片URL的映射
	imageMap := make(map[int64]string)
	for _, img := range images {
		imageMap[img.GoodsID] = img.URL
	}

	// 查询商品SKU信息获取最低价格
	var skuList []struct {
		GoodsID  int64   `gorm:"column:goods_id"`
		MinPrice float64 `gorm:"column:min_price"`
	}
	if len(goodsIDs) > 0 {
		err = (&ShopDB.GoodsSkuDO{}).Query().
			Select("goods_id, MIN(price) as min_price").
			Where("goods_id IN (?) AND status = ?", goodsIDs, 1).
			Group("goods_id").
			Find(&skuList).Error
		if err != nil {
			return nil, err
		}
	}

	// 构建商品ID到价格的映射
	priceMap := make(map[int64]float64)
	for _, sku := range skuList {
		priceMap[sku.GoodsID] = sku.MinPrice
	}

	// 构建返回结果
	result := make([]map[string]interface{}, 0, len(list))
	for _, item := range list {
		result = append(result, map[string]interface{}{
			"id":          item.ID,
			"goodsId":     item.GoodsID,
			"name":        item.Name,
			"price":       priceMap[item.GoodsID],
			"image":       utils.BuildImageURL(imageMap[item.GoodsID]),
			"collectTime": item.CreateTime.Format("2006-01-02 15:04:05"),
		})
	}

	return &vo.PageVO{
		Total: total,
		List:  result,
	}, nil
}

// DeleteCollect 删除收藏
func (s *Service) DeleteCollect(userId, collectId int64) error {
	return (&ShopDB.CollectDO{}).Query().
		Where("id = ? AND user_id = ?", collectId, userId).
		Update("is_delete", 1).Error
}

// GetFootprintList 获取足迹列表
func (s *Service) GetFootprintList(userId int64, page, size int) (*vo.PageVO, error) {
	var total int64
	var list []struct {
		ID         int64     `json:"id"`
		GoodsID    int64     `json:"goodsId"`
		Name       string    `json:"name"`
		CreateTime time.Time `json:"viewTime"`
	}

	// 查询总数
	err := s.db.Model(&ShopDB.GoodsFootprintDO{}).
		Select("shop_goods_footprint.id, shop_goods_footprint.goods_id, shop_goods.name, shop_goods_footprint.create_time").
		Joins("LEFT JOIN shop_goods ON shop_goods.id = shop_goods_footprint.goods_id").
		Where("shop_goods_footprint.user_id = ? AND shop_goods_footprint.is_delete = 0", userId).
		Count(&total).Error
	if err != nil {
		return nil, err
	}

	// 查询列表
	err = s.db.Model(&ShopDB.GoodsFootprintDO{}).
		Select("shop_goods_footprint.id, shop_goods_footprint.goods_id, shop_goods.name, shop_goods_footprint.create_time").
		Joins("LEFT JOIN shop_goods ON shop_goods.id = shop_goods_footprint.goods_id").
		Where("shop_goods_footprint.user_id = ? AND shop_goods_footprint.is_delete = 0", userId).
		Order("shop_goods_footprint.create_time DESC").
		Offset((page - 1) * size).
		Limit(size).
		Scan(&list).Error
	if err != nil {
		return nil, err
	}

	// 查询商品主图
	var goodsIDs []int64
	for _, item := range list {
		goodsIDs = append(goodsIDs, item.GoodsID)
	}

	var images []struct {
		GoodsID int64  `gorm:"column:goods_id"`
		URL     string `gorm:"column:url"`
	}
	if len(goodsIDs) > 0 {
		err = (&ShopDB.GoodsImageDO{}).Query().
			Select("goods_id, url").
			Where("goods_id IN (?) AND position = ?", goodsIDs, 1).
			Find(&images).Error
		if err != nil {
			return nil, err
		}
	}

	// 构建商品ID到图片URL的映射
	imageMap := make(map[int64]string)
	for _, img := range images {
		imageMap[img.GoodsID] = img.URL
	}

	// 查询商品SKU信息获取最低价格
	var skuList []struct {
		GoodsID  int64   `gorm:"column:goods_id"`
		MinPrice float64 `gorm:"column:min_price"`
	}
	if len(goodsIDs) > 0 {
		err = (&ShopDB.GoodsSkuDO{}).Query().
			Select("goods_id, MIN(price) as min_price").
			Where("goods_id IN (?) AND status = ?", goodsIDs, 1).
			Group("goods_id").
			Find(&skuList).Error
		if err != nil {
			return nil, err
		}
	}

	// 构建商品ID到价格的映射
	priceMap := make(map[int64]float64)
	for _, sku := range skuList {
		priceMap[sku.GoodsID] = sku.MinPrice
	}

	// 构建返回结果
	result := make([]map[string]interface{}, 0, len(list))
	for _, item := range list {
		result = append(result, map[string]interface{}{
			"id":       item.ID,
			"goodsId":  item.GoodsID,
			"name":     item.Name,
			"price":    priceMap[item.GoodsID],
			"image":    utils.BuildImageURL(imageMap[item.GoodsID]),
			"viewTime": item.CreateTime.Format("2006-01-02 15:04:05"),
		})
	}

	return &vo.PageVO{
		Total: total,
		List:  result,
	}, nil
}

// DeleteFootprint 删除足迹
func (s *Service) DeleteFootprint(userId, footprintId int64) error {
	return (&ShopDB.GoodsFootprintDO{}).Query().
		Where("id = ? AND user_id = ?", footprintId, userId).
		Update("is_delete", 1).Error
}

// ClearFootprint 清空足迹
func (s *Service) ClearFootprint(userId int64) error {
	return (&ShopDB.GoodsFootprintDO{}).Query().
		Where("user_id = ?", userId).
		Update("is_delete", 1).Error
}

// GetStats 获取用户数据统计
func (s *Service) GetStats(userId int64) (gin.H, error) {
	var collectCount, footprintCount int64

	// 查询收藏数量
	err := (&ShopDB.GoodsCollectDO{}).Query().
		Where("user_id = ?", userId).
		Count(&collectCount).Error
	if err != nil {
		return nil, werror.Wrap(err, 500, "获取收藏数量失败").
			WithContext("userId", userId)
	}

	// 查询足迹数量
	err = (&ShopDB.GoodsFootprintDO{}).Query().
		Where("user_id = ?", userId).
		Count(&footprintCount).Error
	if err != nil {
		return nil, werror.Wrap(err, 500, "获取足迹数量失败").
			WithContext("userId", userId)
	}

	return gin.H{
		"collectCount":   collectCount,
		"footprintCount": footprintCount,
	}, nil
}

// GetOrderStats 获取订单统计
func (s *Service) GetOrderStats(userId int64) (gin.H, error) {
	var (
		unpaidCount      int64 // 待付款
		unshippedCount   int64 // 待发货
		unreceivedCount  int64 // 待收货
		unevaluatedCount int64 // 待评价
		afterSaleCount   int64 // 售后
	)

	// 分别查询各状态订单数量
	if err := s.db.Model(&ShopDB.OrderDO{}).Where("user_id = ? AND status = 'unpaid' AND is_delete = 0", userId).Count(&unpaidCount).Error; err != nil {
		return nil, werror.Wrap(err, 500, "获取订单统计失败").
			WithContext("userId", userId).
			WithContext("status", "unpaid")
	}

	if err := s.db.Model(&ShopDB.OrderDO{}).Where("user_id = ? AND status = 'unshipped' AND is_delete = 0", userId).Count(&unshippedCount).Error; err != nil {
		return nil, werror.Wrap(err, 500, "获取订单统计失败").
			WithContext("userId", userId).
			WithContext("status", "unshipped")
	}

	if err := s.db.Model(&ShopDB.OrderDO{}).Where("user_id = ? AND status = 'unreceived' AND is_delete = 0", userId).Count(&unreceivedCount).Error; err != nil {
		return nil, werror.Wrap(err, 500, "获取订单统计失败").
			WithContext("userId", userId).
			WithContext("status", "unreceived")
	}

	if err := s.db.Model(&ShopDB.OrderDO{}).Where("user_id = ? AND status = 'completed' AND is_delete = 0", userId).Count(&unevaluatedCount).Error; err != nil {
		return nil, werror.Wrap(err, 500, "获取订单统计失败").
			WithContext("userId", userId).
			WithContext("status", "completed")
	}

	if err := s.db.Model(&ShopDB.OrderDO{}).Where("user_id = ? AND status = 'cancelled' AND is_delete = 0", userId).Count(&afterSaleCount).Error; err != nil {
		return nil, werror.Wrap(err, 500, "获取订单统计失败").
			WithContext("userId", userId).
			WithContext("status", "cancelled")
	}

	return gin.H{
		"unpaidCount":      unpaidCount,
		"unshippedCount":   unshippedCount,
		"unreceivedCount":  unreceivedCount,
		"unevaluatedCount": unevaluatedCount,
		"afterSaleCount":   afterSaleCount,
	}, nil
}

// UpdateUserInfo 更新用户信息
func (s *Service) UpdateUserInfo(userId int64, userInfo *ShopDB.UserDO) error {
	// 查询用户是否存在
	user := &ShopDB.UserDO{}
	err := user.Query().Where("id = ?", userId).First(user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return werror.New(404, "用户不存在").
				WithContext("userId", userId).
				WithLevel(werror.LevelWarning)
		}
		return werror.Wrap(err, 500, "查询用户失败").
			WithContext("userId", userId)
	}

	// 更新用户信息
	updates := map[string]interface{}{
		"nickname":    userInfo.Nickname,
		"avatar":      userInfo.Avatar,
		"phone":       userInfo.Phone,
		"gender":      userInfo.Gender,
		"update_time": time.Now(),
	}

	err = user.Query().Where("id = ?", userId).Updates(updates).Error
	if err != nil {
		return werror.Wrap(err, 500, "更新用户信息失败").
			WithContext("userId", userId)
	}

	return nil
}

// UpdateAvatar 更新用户头像
func (s *Service) UpdateAvatar(userId int64, avatar string) error {
	return s.db.Model(&ShopDB.UserDO{}).Where("id = ?", userId).Update("avatar", avatar).Error
}

// WechatPhoneLogin 方法实现
func (s *Service) WechatPhoneLogin(req *dto.WechatPhoneLoginReq) (*dto.LoginResp, error) {
	// 1. 使用微信SDK获取session信息
	auth := s.miniProgram.GetAuth()
	session, err := auth.Code2Session(req.Code)
	if err != nil {
		fmt.Printf("[Error] Wechat Code2Session failed: %v\n", err)
		return nil, werror.Wrap(err, 500, "获取微信会话失败").
			WithContext("code", req.Code)
	}

	fmt.Printf("[Debug] Wechat session success: openid=%s, sessionKey length=%d\n", session.OpenID, len(session.SessionKey))

	// 2. 使用微信SDK解密手机号
	encryptor := s.miniProgram.GetEncryptor()
	plainData, err := encryptor.Decrypt(session.SessionKey, req.EncryptedData, req.Iv)
	if err != nil {
		fmt.Printf("[Error] Wechat DecryptPhoneNumber failed: %v\n", err)
		fmt.Printf("[Debug] SessionKey: %s\n", session.SessionKey)
		fmt.Printf("[Debug] EncryptedData length: %d\n", len(req.EncryptedData))
		fmt.Printf("[Debug] IV length: %d\n", len(req.Iv))
		return nil, werror.Wrap(err, 500, "解析手机号失败").
			WithContext("openid", session.OpenID).
			WithContext("sessionKeyLength", len(session.SessionKey)).
			WithContext("encryptedDataLength", len(req.EncryptedData)).
			WithContext("ivLength", len(req.Iv))
	}

	// 3. 解析手机号信息 - PlainData本身就包含解密后的数据
	var phoneInfo WechatPhoneInfo
	// 将PlainData转换为JSON字节数组
	plainDataBytes, err := json.Marshal(plainData)
	if err != nil {
		fmt.Printf("[Error] Marshal plainData failed: %v\n", err)
		return nil, werror.Wrap(err, 500, "处理解密数据失败")
	}

	err = json.Unmarshal(plainDataBytes, &phoneInfo)
	if err != nil {
		fmt.Printf("[Error] Unmarshal phone info failed: %v\n", err)
		fmt.Printf("[Debug] PlainData: %+v\n", plainData)
		fmt.Printf("[Debug] PlainData JSON: %s\n", string(plainDataBytes))
		return nil, werror.Wrap(err, 500, "解析手机号数据失败")
	}

	fmt.Printf("[Debug] Phone info decrypted: phone=%s, appid=%s\n", phoneInfo.PurePhoneNumber, phoneInfo.Watermark.AppID)

	// 4. 验证 AppID 是否匹配
	if phoneInfo.Watermark.AppID != config.App.Wx.MiniApp.AppID {
		fmt.Printf("[Error] Wechat AppID mismatch: expected %s, got %s\n", config.App.Wx.MiniApp.AppID, phoneInfo.Watermark.AppID)
		return nil, werror.New(500, "微信 AppID 不匹配").
			WithContext("expected", config.App.Wx.MiniApp.AppID).
			WithContext("actual", phoneInfo.Watermark.AppID)
	}

	phoneNumber := phoneInfo.PurePhoneNumber // 不带国家码的手机号
	openid := session.OpenID                 // OpenID

	// 3. 根据手机号查询用户
	var user ShopDB.UserDO
	err = (&ShopDB.UserDO{}).Query().Where("phone = ?", phoneNumber).First(&user).Error

	// 4. 处理用户存在与否
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		// 用户不存在，自动注册
		fmt.Printf("[Info] User with phone %s not found, creating new user.\n", phoneNumber) // 临时日志

		// 密码处理：微信登录用户通常不直接设置密码，或生成随机密码
		// 这里暂时生成一个不可登录的 bcrypt hash
		hashedPassword, _ := bcrypt.GenerateFromPassword([]byte(utils.GenerateUUID()), bcrypt.DefaultCost) // 使用随机UUID生成密码hash

		newUser := ShopDB.UserDO{
			Username: phoneNumber,                                             // 默认用户名 wx_手机尾号
			Password: string(hashedPassword),                                  // 存储hash后的密码
			Nickname: fmt.Sprintf("微信用户%s", phoneNumber[len(phoneNumber)-4:]), // 默认昵称
			Avatar:   "",                                                      // 默认头像，或后续引导用户设置
			Phone:    phoneNumber,
			OpenID:   &openid, // 存储 OpenID
			Status:   1,       // 默认启用
			// CreateTime, UpdateTime 由 GORM 自动处理或手动设置
			CreateTime: time.Now(),
			UpdateTime: time.Now(),
		}

		// 创建用户
		createErr := (&ShopDB.UserDO{}).Query().Create(&newUser).Error
		if createErr != nil {
			fmt.Printf("[Error] Create user failed: %v\n", createErr) // 临时日志
			return nil, werror.Wrap(createErr, 500, "创建用户失败").
				WithContext("phone", phoneNumber).
				WithContext("openid", openid)
		}
		user = newUser                                               // 将新创建的用户赋值给 user
		fmt.Printf("[Info] New user created with ID: %d\n", user.ID) // 临时日志

		// 如果有邀请者ID，建立分销关系
		if req.InviterId != nil && *req.InviterId > 0 && *req.InviterId != user.ID {
			err = s.createDistributionRelation(user.ID, *req.InviterId)
			if err != nil {
				// 分销关系创建失败不影响登录流程，只记录日志
				fmt.Printf("创建分销关系失败 userId=%d, inviterId=%d, error=%v\n", user.ID, *req.InviterId, err)
			}
		}

	} else if err != nil {
		// 数据库查询出错
		fmt.Printf("[Error] Query user failed: %v\n", err) // 临时日志
		return nil, werror.Wrap(err, 500, "查询用户失败").
			WithContext("phone", phoneNumber).
			WithContext("openid", openid)
	} else {
		// 用户存在，更新 OpenID（如果为空）
		if user.OpenID == nil || *user.OpenID == "" {
			user.OpenID = &openid
			updateErr := (&ShopDB.UserDO{}).Query().Where("id = ?", user.ID).Update("open_id", openid).Error
			if updateErr != nil {
				fmt.Printf("[Warning] Update user OpenID failed: %v\n", updateErr) // 临时日志
				// 不阻止登录流程，只是记录警告
			} else {
				fmt.Printf("[Info] Updated user %d OpenID: %s\n", user.ID, openid) // 临时日志
			}
		}
		fmt.Printf("[Info] Existing user login: ID=%d, phone=%s\n", user.ID, user.Phone) // 临时日志
	}

	// 5. 检查用户状态
	if user.Status != 1 {
		return nil, werror.New(403, "用户已被禁用").
			WithContext("userId", user.ID).
			WithContext("status", user.Status).
			WithLevel(werror.LevelWarning)
	}

	// 6. 生成 JWT Token
	token, err := utils.GenerateToken(user.ID, user.Username)
	if err != nil {
		return nil, werror.Wrap(err, 500, "生成token失败").
			WithContext("userId", user.ID).
			WithContext("username", user.Username)
	}

	// 7. 构造登录响应
	loginResp := &dto.LoginResp{
		Token:    token,
		ID:       user.ID,
		Username: user.Username,
		Nickname: user.Nickname,
		Avatar:   user.Avatar,
		Balance:  user.Balance,
		Gender:   user.Gender,
		Phone:    user.Phone,
	}

	return loginResp, nil
}

// Register 用户注册
func (s *Service) Register(req *dto.RegisterReq) (*dto.RegisterResp, error) {
	// 1. 验证短信验证码
	err := s.verifySmsCodeInternal(req.Phone, req.Code, "register")
	if err != nil {
		return nil, err
	}

	// 2. 检查手机号是否已注册
	var existUser ShopDB.UserDO
	err = (&ShopDB.UserDO{}).Query().Where("phone = ?", req.Phone).First(&existUser).Error
	if err == nil {
		return nil, werror.New(400, "手机号已注册").
			WithContext("phone", req.Phone).
			WithLevel(werror.LevelWarning)
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, werror.Wrap(err, 500, "查询用户失败").
			WithContext("phone", req.Phone)
	}

	// 3. 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, werror.Wrap(err, 500, "密码加密失败").
			WithContext("phone", req.Phone)
	}

	// 4. 创建用户
	user := ShopDB.UserDO{
		Username:   req.Phone, // 用户名使用手机号
		Password:   string(hashedPassword),
		Nickname:   fmt.Sprintf("用户%s", req.Phone[len(req.Phone)-4:]), // 默认昵称：用户手机尾号
		Phone:      req.Phone,
		Status:     1, // 启用状态
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
		// Birthday 字段为指针类型，不设置时自动为 NULL
	}

	err = (&ShopDB.UserDO{}).Query().Create(&user).Error
	if err != nil {
		return nil, werror.Wrap(err, 500, "创建用户失败").
			WithContext("phone", req.Phone)
	}

	// 5. 删除验证码缓存
	s.redis.Del(fmt.Sprintf("sms_code:%s:register", req.Phone))

	// 6. 如果有邀请者ID，建立分销关系
	if req.InviterId != nil && *req.InviterId > 0 && *req.InviterId != user.ID {
		err = s.createDistributionRelation(user.ID, *req.InviterId)
		if err != nil {
			// 分销关系创建失败不影响注册流程，只记录日志
			fmt.Printf("创建分销关系失败 userId=%d, inviterId=%d, error=%v\n", user.ID, *req.InviterId, err)
		}
	}

	// 7. 返回注册结果
	return &dto.RegisterResp{
		ID:       user.ID,
		Phone:    user.Phone,
		Nickname: user.Nickname,
	}, nil
}

// SendSms 发送短信验证码
func (s *Service) SendSms(req *dto.SendSmsReq) error {
	// 1. 验证手机号格式
	if !isValidPhone(req.Phone) {
		return werror.New(400, "手机号格式不正确").
			WithContext("phone", req.Phone)
	}

	// 2. 检查发送频率限制（60秒内只能发送一次）
	key := fmt.Sprintf("sms_limit:%s", req.Phone)
	exists, err := s.redis.Exists(key).Result()
	if err != nil {
		return werror.Wrap(err, 500, "检查发送频率失败").
			WithContext("phone", req.Phone)
	}
	if exists > 0 {
		return werror.New(400, "发送过于频繁，请稍后再试").
			WithData(map[string]interface{}{
				"retryAfter": 60,
			}).
			WithContext("phone", req.Phone).
			WithLevel(werror.LevelWarning)
	}

	// 3. 根据类型进行业务验证
	switch req.Type {
	case "register":
		// 注册时检查手机号是否已存在
		var user ShopDB.UserDO
		err := (&ShopDB.UserDO{}).Query().Where("phone = ?", req.Phone).First(&user).Error
		if err == nil {
			return werror.New(400, "手机号已注册").
				WithContext("phone", req.Phone).
				WithLevel(werror.LevelWarning)
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return werror.Wrap(err, 500, "查询用户失败").
				WithContext("phone", req.Phone)
		}
	case "login":
		// 登录时检查手机号是否存在
		var user ShopDB.UserDO
		err := (&ShopDB.UserDO{}).Query().Where("phone = ?", req.Phone).First(&user).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return werror.New(400, "手机号未注册").
				WithContext("phone", req.Phone).
				WithLevel(werror.LevelWarning)
		} else if err != nil {
			return werror.Wrap(err, 500, "查询用户失败").
				WithContext("phone", req.Phone)
		}
	case "reset":
		// 重置密码时检查手机号是否存在
		var user ShopDB.UserDO
		err := (&ShopDB.UserDO{}).Query().Where("phone = ?", req.Phone).First(&user).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return werror.New(400, "手机号未注册").
				WithContext("phone", req.Phone).
				WithLevel(werror.LevelWarning)
		} else if err != nil {
			return werror.Wrap(err, 500, "查询用户失败").
				WithContext("phone", req.Phone)
		}
	default:
		return werror.New(400, "验证码类型不正确").
			WithContext("type", req.Type)
	}

	// 4. 生成6位数字验证码
	code := generateSmsCode()

	// 5. 调用阿里云短信服务发送短信
	smsService := sms.NewAliyunSmsService()
	err = smsService.SendSmsCode(req.Phone, code, req.Type)
	if err != nil {
		// 如果短信发送失败，记录日志但不阻止流程（开发环境可以继续）
		fmt.Printf("短信发送失败: %v\n", err)
		if config.App.ENV == "prod" {
			// 生产环境下短信发送失败应该返回错误
			return werror.Wrap(err, 500, "短信发送失败").
				WithContext("phone", req.Phone).
				WithContext("provider", "aliyun")
		}
		// 开发环境下打印验证码到控制台
		fmt.Printf("开发环境 - 发送短信验证码到 %s: %s\n", req.Phone, code)
	}

	// 6. 将验证码存储到Redis（5分钟有效期）
	codeKey := fmt.Sprintf("sms_code:%s:%s", req.Phone, req.Type)
	err = s.redis.Set(codeKey, code, 5*time.Minute).Err()
	if err != nil {
		return werror.Wrap(err, 500, "存储验证码失败").
			WithContext("phone", req.Phone).
			WithContext("codeKey", codeKey)
	}

	// 7. 设置发送频率限制（60秒）
	err = s.redis.Set(key, "1", 60*time.Second).Err()
	if err != nil {
		return werror.Wrap(err, 500, "设置发送限制失败").
			WithContext("phone", req.Phone).
			WithContext("limitKey", key)
	}

	return nil
}

// VerifySms 验证短信验证码
func (s *Service) VerifySms(req *dto.VerifySmsReq) error {
	return s.verifySmsCodeInternal(req.Phone, req.Code, req.Type)
}

// verifySmsCodeInternal 内部验证短信验证码方法
func (s *Service) verifySmsCodeInternal(phone, code, codeType string) error {
	// 1. 从Redis获取验证码
	key := fmt.Sprintf("sms_code:%s:%s", phone, codeType)
	storedCode, err := s.redis.Get(key).Result()
	if err == redis.Nil {
		return werror.New(400, "验证码已过期或不存在").
			WithContext("phone", phone).
			WithContext("type", codeType).
			WithLevel(werror.LevelWarning)
	} else if err != nil {
		return werror.Wrap(err, 500, "获取验证码失败").
			WithContext("phone", phone).
			WithContext("type", codeType).
			WithContext("key", key)
	}

	// 2. 验证验证码
	if storedCode != code {
		return werror.New(400, "验证码错误").
			WithContext("phone", phone).
			WithContext("type", codeType).
			WithLevel(werror.LevelWarning)
	}

	return nil
}

// isValidPhone 验证手机号格式
func isValidPhone(phone string) bool {
	if len(phone) != 11 {
		return false
	}
	// 简单的手机号验证：以1开头，第二位是3-9
	return phone[0] == '1' && phone[1] >= '3' && phone[1] <= '9'
}

// generateSmsCode 生成6位数字验证码
func generateSmsCode() string {
	return fmt.Sprintf("%06d", time.Now().UnixNano()%1000000)
}

// ChangePassword 修改密码
func (s *Service) ChangePassword(userId int64, oldPassword, newPassword string) error {
	// 1. 查询用户
	var user ShopDB.UserDO
	err := (&ShopDB.UserDO{}).Query().Where("id = ?", userId).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return werror.New(404, "用户不存在").
				WithContext("userId", userId).
				WithLevel(werror.LevelWarning)
		}
		return werror.Wrap(err, 500, "查询用户失败").
			WithContext("userId", userId)
	}

	// 2. 验证旧密码
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(oldPassword))
	if err != nil {
		return werror.New(400, "当前密码错误").
			WithContext("userId", userId).
			WithLevel(werror.LevelWarning)
	}

	// 3. 检查新密码是否与旧密码相同
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(newPassword))
	if err == nil {
		return werror.New(400, "新密码不能与当前密码相同").
			WithContext("userId", userId).
			WithLevel(werror.LevelWarning)
	}

	// 4. 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return werror.Wrap(err, 500, "密码加密失败").
			WithContext("userId", userId)
	}

	// 5. 更新密码
	err = (&ShopDB.UserDO{}).Query().Where("id = ?", userId).Update("password", string(hashedPassword)).Error
	if err != nil {
		return werror.Wrap(err, 500, "更新密码失败").
			WithContext("userId", userId)
	}

	return nil
}

// ResetPassword 重置密码（不需要验证当前密码）
func (s *Service) ResetPassword(userId int64, newPassword string) error {
	// 1. 查询用户是否存在
	var user ShopDB.UserDO
	err := (&ShopDB.UserDO{}).Query().Where("id = ?", userId).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return werror.New(404, "用户不存在").
				WithContext("userId", userId).
				WithLevel(werror.LevelWarning)
		}
		return werror.Wrap(err, 500, "查询用户失败").
			WithContext("userId", userId)
	}

	// 2. 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return werror.Wrap(err, 500, "密码加密失败").
			WithContext("userId", userId)
	}

	// 3. 更新密码
	err = (&ShopDB.UserDO{}).Query().Where("id = ?", userId).Update("password", string(hashedPassword)).Error
	if err != nil {
		return werror.Wrap(err, 500, "更新密码失败").
			WithContext("userId", userId)
	}

	return nil
}

// createDistributionRelation 创建邀请关系（关系建立和分销分开）
func (s *Service) createDistributionRelation(userId, inviterId int64) error {
	// 1. 先创建邀请关系（无论是否为分销员都记录关系）
	err := s.createInviteRelation(userId, inviterId)
	if err != nil {
		return err
	}

	// 2. 检查邀请者是否是分销员，如果是则处理分销相关逻辑
	var inviter ShopDB.ShareUserDO
	err = (&ShopDB.ShareUserDO{}).Query().
		Where("user_id = ? AND status = 1", inviterId).
		First(&inviter).Error

	if err == nil {
		// 邀请者是分销员，更新团队人数
		err = s.updateTeamCount(inviterId)
		if err != nil {
			// 团队人数更新失败不影响主流程，只记录日志
			fmt.Printf("更新分销员团队人数失败: inviterId=%d, error=%v\n", inviterId, err)
		}

		// 3. 处理注册佣金
		err = s.createRegisterCommission(userId, inviterId)
		if err != nil {
			// 注册佣金创建失败不影响主流程，只记录日志
			fmt.Printf("创建注册佣金失败: userId=%d, inviterId=%d, error=%v\n", userId, inviterId, err)
		}
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		// 查询出错（非记录不存在），记录日志但不影响主流程
		fmt.Printf("查询邀请者分销员信息失败: inviterId=%d, error=%v\n", inviterId, err)
	}
	// 如果邀请者不是分销员，只记录关系，不处理分销逻辑

	return nil
}

// createInviteRelation 创建邀请关系（纯关系记录，不涉及分销）
func (s *Service) createInviteRelation(userId, inviterId int64) error {
	// 1. 检查是否已存在邀请关系
	var existRelation ShopDB.UserRelationDO
	err := (&ShopDB.UserRelationDO{}).Query().
		Where("related_user_id = ? AND user_id = ? AND relation_type = ? AND status = ?",
			userId, inviterId, ShopDB.RelationTypeInvite, ShopDB.StatusActive).
		First(&existRelation).Error

	if err == nil {
		// 关系已存在，不重复创建
		return nil
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return werror.Wrap(err, 500, "查询邀请关系失败").
			WithContext("userId", userId).
			WithContext("inviterId", inviterId)
	}

	// 2. 创建邀请关系（使用通用关系表）
	now := time.Now()
	relation := &ShopDB.UserRelationDO{
		UserId:        inviterId,                           // 邀请者ID
		RelatedUserId: userId,                              // 被邀请者ID
		RelationType:  ShopDB.RelationTypeInvite,           // 邀请关系
		Direction:     ShopDB.DirectionOneWay,              // 单向关系
		Status:        ShopDB.StatusActive,                 // 有效状态
		Source:        ShopDB.SourceRegister,               // 注册时建立
		Metadata:      `{"created_at_registration": true}`, // 扩展数据
		CreateTime:    now,
		UpdateTime:    now,
	}

	err = relation.Query().Create(relation).Error
	if err != nil {
		return werror.Wrap(err, 500, "创建邀请关系失败").
			WithContext("userId", userId).
			WithContext("inviterId", inviterId)
	}

	return nil
}

// createDistributor 创建分销员
func (s *Service) createDistributor(userId int64) error {
	now := time.Now()
	distributor := &ShopDB.ShareUserDO{
		UserId:           userId,
		Level:            1, // 默认一级分销员
		Status:           1, // 自动审核通过
		TotalIncome:      0,
		AvailableBalance: 0,
		FrozenBalance:    0,
		TeamCount:        0,
		CreateTime:       now,
		UpdateTime:       now,
	}

	err := (&ShopDB.ShareUserDO{}).Query().Create(distributor).Error
	if err != nil {
		return werror.Wrap(err, 500, "创建分销员失败").
			WithContext("userId", userId)
	}
	return nil
}

// ApplyDistributor 用户申请成为分销员
func (s *Service) ApplyDistributor(userId int64) error {
	// 1. 检查用户是否已经是分销员
	var existDistributor ShopDB.ShareUserDO
	err := (&ShopDB.ShareUserDO{}).Query().
		Where("user_id = ?", userId).
		First(&existDistributor).Error

	if err == nil {
		// 已经是分销员
		if existDistributor.Status == 1 {
			return werror.New(400, "您已经是分销员了").
				WithContext("userId", userId).
				WithLevel(werror.LevelWarning)
		} else {
			return werror.New(400, "您的分销员申请正在审核中").
				WithContext("userId", userId).
				WithLevel(werror.LevelWarning)
		}
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return werror.Wrap(err, 500, "查询分销员信息失败").
			WithContext("userId", userId)
	}

	// 2. 创建分销员申请
	err = s.createDistributor(userId)
	if err != nil {
		return err
	}

	// 3. 如果用户有邀请关系，更新上级的团队人数
	var inviteRelation ShopDB.UserRelationDO
	err = (&ShopDB.UserRelationDO{}).Query().
		Where("related_user_id = ? AND relation_type = ? AND status = ?",
			userId, ShopDB.RelationTypeInvite, ShopDB.StatusActive).
		First(&inviteRelation).Error

	if err == nil {
		// 有上级，更新上级团队人数
		err = s.updateTeamCount(inviteRelation.UserId)
		if err != nil {
			// 更新失败不影响主流程，只记录日志
			fmt.Printf("更新上级团队人数失败: inviterId=%d, error=%v\n", inviteRelation.UserId, err)
		}
	}

	return nil
}

// GetInviteRelations 获取用户的邀请关系（不限于分销员）
func (s *Service) GetInviteRelations(userId int64) ([]map[string]interface{}, error) {
	// 获取用户邀请的所有人（直接下级）
	var relations []ShopDB.UserRelationDO
	err := (&ShopDB.UserRelationDO{}).Query().
		Where("user_id = ? AND relation_type = ? AND status = ?",
			userId, ShopDB.RelationTypeInvite, ShopDB.StatusActive).
		Order("create_time DESC").
		Find(&relations).Error

	if err != nil {
		return nil, werror.Wrap(err, 500, "查询邀请关系失败").
			WithContext("userId", userId)
	}

	var result []map[string]interface{}
	for _, relation := range relations {
		// 获取被邀请用户的基本信息
		var user ShopDB.UserDO
		err = (&ShopDB.UserDO{}).Query().
			Where("id = ?", relation.RelatedUserId).
			First(&user).Error

		if err != nil {
			continue // 跳过查询失败的用户
		}

		// 检查被邀请用户是否是分销员
		var distributor ShopDB.ShareUserDO
		isDistributor := false
		err = (&ShopDB.ShareUserDO{}).Query().
			Where("user_id = ? AND status = 1", relation.RelatedUserId).
			First(&distributor).Error
		if err == nil {
			isDistributor = true
		}

		item := map[string]interface{}{
			"user_id":        relation.RelatedUserId,
			"nickname":       user.Nickname,
			"avatar":         user.Avatar,
			"phone":          user.Phone,
			"is_distributor": isDistributor,
			"invite_time":    relation.CreateTime,
			"source":         relation.Source,
		}

		result = append(result, item)
	}

	return result, nil
}

// GetInviteStats 获取邀请统计（包含非分销员）
func (s *Service) GetInviteStats(userId int64) (map[string]interface{}, error) {
	// 1. 获取总邀请人数
	var totalInvites int64
	err := (&ShopDB.UserRelationDO{}).Query().
		Where("user_id = ? AND relation_type = ? AND status = ?",
			userId, ShopDB.RelationTypeInvite, ShopDB.StatusActive).
		Count(&totalInvites).Error

	if err != nil {
		return nil, werror.Wrap(err, 500, "查询邀请总数失败").
			WithContext("userId", userId)
	}

	// 2. 获取成为分销员的人数
	var distributorCount int64
	err = s.db.Table("user_relation ur").
		Joins("INNER JOIN share_user su ON ur.related_user_id = su.user_id").
		Where("ur.user_id = ? AND ur.relation_type = ? AND ur.status = ? AND su.status = 1",
			userId, ShopDB.RelationTypeInvite, ShopDB.StatusActive).
		Count(&distributorCount).Error

	if err != nil {
		return nil, werror.Wrap(err, 500, "查询分销员人数失败").
			WithContext("userId", userId)
	}

	// 3. 检查当前用户是否是分销员
	var currentUserDistributor ShopDB.ShareUserDO
	isCurrentUserDistributor := false
	err = (&ShopDB.ShareUserDO{}).Query().
		Where("user_id = ? AND status = 1", userId).
		First(&currentUserDistributor).Error
	if err == nil {
		isCurrentUserDistributor = true
	}

	return map[string]interface{}{
		"total_invites":               totalInvites,
		"distributor_count":           distributorCount,
		"non_distributor_count":       totalInvites - distributorCount,
		"is_current_user_distributor": isCurrentUserDistributor,
	}, nil
}

// updateTeamCount 更新团队人数（递归计算所有下级）
func (s *Service) updateTeamCount(userId int64) error {
	count, err := s.getTeamCountRecursive(userId)
	if err != nil {
		return err
	}

	// 更新团队人数
	err = (&ShopDB.ShareUserDO{}).Query().
		Where("user_id = ?", userId).
		Update("team_count", count).Error
	if err != nil {
		return werror.Wrap(err, 500, "更新团队人数失败").
			WithContext("userId", userId).
			WithContext("teamCount", count)
	}

	return nil
}

// getTeamCountRecursive 递归计算团队人数（使用通用关系表）
func (s *Service) getTeamCountRecursive(userId int64) (int64, error) {
	// 查询直接下级（邀请关系）
	var directMembers []ShopDB.UserRelationDO
	err := (&ShopDB.UserRelationDO{}).Query().
		Where("user_id = ? AND relation_type = ? AND status = ?",
			userId, ShopDB.RelationTypeInvite, ShopDB.StatusActive).
		Find(&directMembers).Error
	if err != nil {
		return 0, werror.Wrap(err, 500, "查询直接下级失败").
			WithContext("userId", userId)
	}

	totalCount := int64(len(directMembers)) // 直接下级数量

	// 递归计算每个直接下级的团队人数
	for _, member := range directMembers {
		subCount, err := s.getTeamCountRecursive(member.RelatedUserId)
		if err != nil {
			// 递归失败不影响主流程，只记录日志
			fmt.Printf("递归计算团队人数失败: userId=%d, error=%v\n", member.RelatedUserId, err)
			continue
		}
		totalCount += subCount
	}

	return totalCount, nil
}

// createRegisterCommission 创建注册佣金
func (s *Service) createRegisterCommission(newUserId, inviterId int64) error {
	fmt.Printf("开始创建注册佣金: 新用户=%d, 邀请者=%d\n", newUserId, inviterId)

	// 1. 获取分销设置（从配置服务获取）
	configService := common.NewConfigService()
	setting, err := configService.GetShareConfig()
	if err != nil {
		fmt.Printf("获取分销设置失败: %v\n", err)
		// 获取配置失败，不处理佣金
		return nil
	}

	// 2. 检查分销功能是否启用
	if !setting.Enabled {
		fmt.Printf("分销功能未启用\n")
		return nil // 分销功能未启用
	}

	// 3. 检查注册佣金是否启用
	if !setting.RegisterEnabled || setting.RegisterAmount <= 0 {
		fmt.Printf("注册佣金未启用或金额为0: RegisterEnabled=%t, RegisterAmount=%.2f\n",
			setting.RegisterEnabled, setting.RegisterAmount)
		return nil // 注册佣金未启用或金额为0
	}

	fmt.Printf("分销设置检查通过: 注册佣金=%.2f, 冻结天数=%d\n", setting.RegisterAmount, setting.FreezeDays)

	// 4. 获取邀请关系链（支持多级分销）
	ancestors, err := s.getInviteAncestors(newUserId)
	if err != nil {
		fmt.Printf("获取邀请关系链失败: %v\n", err)
		return werror.Wrap(err, 500, "获取邀请关系链失败")
	}

	if len(ancestors) == 0 {
		fmt.Printf("没有找到邀请关系链\n")
		return nil // 没有上级
	}

	fmt.Printf("找到邀请关系链: %v\n", ancestors)

	// 5. 计算解冻时间
	var unfreezeTime *time.Time
	if setting.FreezeDays > 0 {
		t := time.Now().AddDate(0, 0, setting.FreezeDays)
		unfreezeTime = &t
	}

	now := time.Now()

	// 6. 为每个层级的分销员创建注册佣金（最多2级）
	for level, ancestorId := range ancestors {
		if level >= 2 {
			break // 超过2级分销层级
		}

		// 检查该层级的分销员是否存在且有效
		var distributor ShopDB.ShareUserDO
		err = (&ShopDB.ShareUserDO{}).Query().
			Where("user_id = ? AND status = 1", ancestorId).
			First(&distributor).Error
		if err != nil {
			fmt.Printf("层级%d用户%d不是有效分销员: %v\n", level+1, ancestorId, err)
			continue // 该层级用户不是有效分销员，跳过
		}

		fmt.Printf("层级%d用户%d是有效分销员，ID=%d\n", level+1, ancestorId, distributor.ID)

		// 计算该层级的佣金金额（注册佣金统一金额）
		commissionAmount := setting.RegisterAmount

		// 获取新用户信息（用于记录姓名）
		var newUser ShopDB.UserDO
		err = (&ShopDB.UserDO{}).Query().
			Where("id = ?", newUserId).
			First(&newUser).Error
		if err != nil {
			continue // 获取用户信息失败，跳过
		}

		// 创建佣金记录
		income := &ShopDB.ShareIncomeDO{
			ReferrerId:       &ancestorId, // 推荐官ID
			UserId:           ancestorId,
			InviteeId:        &newUserId,
			InviteeName:      newUser.Nickname, // 使用昵称作为被邀请人姓名
			CommissionAmount: commissionAmount,
			CommissionType:   ShopDB.CommissionTypeRegister,
			CommissionStatus: ShopDB.CommissionStatusPending, // 默认待结算状态
			SourceOrderNo:    "",                             // 注册佣金没有订单号
			OrderAmount:      0,                              // 注册佣金订单金额为0
			CommissionRate:   0,                              // 注册佣金不按比例计算
			Level:            level + 1,                      // 层级从1开始
			UnfreezeTime:     unfreezeTime,
			Remark:           fmt.Sprintf("用户注册奖励，邀请用户：%s", newUser.Nickname),
			CreateTime:       now,
			UpdateTime:       now,
		}

		// 如果没有冻结期，直接设为已结算状态
		if setting.FreezeDays == 0 {
			income.CommissionStatus = ShopDB.CommissionStatusSettled
		}

		err = income.Query().Create(income).Error
		if err != nil {
			fmt.Printf("创建注册佣金记录失败: userId=%d, level=%d, error=%v\n", ancestorId, level+1, err)
			continue // 创建失败不影响其他层级
		}

		// 更新分销员余额
		err = s.updateDistributorBalance(ancestorId, commissionAmount, income.CommissionStatus)
		if err != nil {
			fmt.Printf("更新分销员余额失败: userId=%d, amount=%f, error=%v\n", ancestorId, commissionAmount, err)
		}

		fmt.Printf("创建注册佣金成功: 新用户=%d, 分销员=%d, 层级=%d, 金额=%f\n", newUserId, ancestorId, level+1, commissionAmount)
	}

	return nil
}

// getInviteAncestors 获取邀请关系链（上级列表）
func (s *Service) getInviteAncestors(userId int64) ([]int64, error) {
	var ancestors []int64
	currentUserId := userId
	visited := make(map[int64]bool) // 防止循环引用

	for len(ancestors) < 10 { // 最多查询10层，防止无限循环
		if visited[currentUserId] {
			break // 检测到循环，停止查询
		}
		visited[currentUserId] = true

		// 查找直接上级
		var relation ShopDB.UserRelationDO
		err := (&ShopDB.UserRelationDO{}).Query().
			Where("related_user_id = ? AND relation_type = ? AND status = ?",
				currentUserId, ShopDB.RelationTypeInvite, ShopDB.StatusActive).
			First(&relation).Error

		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				break // 没有上级了
			}
			return nil, werror.Wrap(err, 500, "查询邀请关系失败")
		}

		ancestors = append(ancestors, relation.UserId)
		currentUserId = relation.UserId
	}

	return ancestors, nil
}

// updateDistributorBalance 更新分销员余额
func (s *Service) updateDistributorBalance(userId int64, amount float64, status int) error {
	updates := map[string]interface{}{
		"total_income": gorm.Expr("total_income + ?", amount),
		"update_time":  time.Now(),
	}

	// 根据状态更新不同的余额字段
	if status == ShopDB.CommissionStatusSettled {
		updates["available_balance"] = gorm.Expr("available_balance + ?", amount)
	} else {
		updates["frozen_balance"] = gorm.Expr("frozen_balance + ?", amount)
	}

	err := (&ShopDB.ShareUserDO{}).Query().
		Where("user_id = ?", userId).
		Updates(updates).Error

	if err != nil {
		return werror.Wrap(err, 500, "更新分销员余额失败").
			WithContext("userId", userId).
			WithContext("amount", amount)
	}

	return nil
}
