package dict

import (
	"sync"
	"wnsys/shop/app/model/do/ShopDB"

	"gorm.io/gorm"
)

type DictService struct {
	db *gorm.DB
	// 缓存字典数据，避免频繁查询数据库
	cache map[string]map[string]string
	mutex sync.RWMutex
}

func NewDictService(db *gorm.DB) *DictService {
	return &DictService{
		db:    db,
		cache: make(map[string]map[string]string),
	}
}

// GetDictLabel 根据字典类型和值获取标签
func (s *DictService) GetDictLabel(dictType, dictValue string) string {
	s.mutex.RLock()
	if cache, exists := s.cache[dictType]; exists {
		if label, ok := cache[dictValue]; ok {
			s.mutex.RUnlock()
			return label
		}
	}
	s.mutex.RUnlock()

	// 缓存未命中，从数据库查询
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 双重检查，避免并发问题
	if cache, exists := s.cache[dictType]; exists {
		if label, ok := cache[dictValue]; ok {
			return label
		}
	}

	// 查询数据库
	var dictData ShopDB.SysDictData
	err := s.db.Where("dict_type = ? AND dict_value = ? AND status = '0'", dictType, dictValue).First(&dictData).Error
	if err != nil {
		// 查询失败，返回原值
		return dictValue
	}

	// 更新缓存
	if s.cache[dictType] == nil {
		s.cache[dictType] = make(map[string]string)
	}
	s.cache[dictType][dictValue] = dictData.DictLabel

	return dictData.DictLabel
}

// LoadDictType 预加载指定类型的字典数据到缓存
func (s *DictService) LoadDictType(dictType string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	var dictDataList []ShopDB.SysDictData
	err := s.db.Where("dict_type = ? AND status = '0'", dictType).Find(&dictDataList).Error
	if err != nil {
		return err
	}

	// 更新缓存
	s.cache[dictType] = make(map[string]string)
	for _, data := range dictDataList {
		s.cache[dictType][data.DictValue] = data.DictLabel
	}

	return nil
}

// ClearCache 清除缓存
func (s *DictService) ClearCache() {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.cache = make(map[string]map[string]string)
}

// GetOrderStatusLabel 获取订单状态标签
func (s *DictService) GetOrderStatusLabel(status string) string {
	return s.GetDictLabel("order_status", status)
}

// GetBookingStatusLabel 获取预约状态标签
func (s *DictService) GetBookingStatusLabel(status string) string {
	return s.GetDictLabel("booking_status", status)
}

// GetCardStatusLabel 获取套餐卡状态标签
func (s *DictService) GetCardStatusLabel(status string) string {
	return s.GetDictLabel("card_status", status)
}
