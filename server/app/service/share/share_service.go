package share

import (
	"context"
	"errors"
	"fmt"
	"time"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/model/vo"
	"wnsys/shop/app/provider/db"
	"wnsys/shop/app/service/common"

	"gorm.io/gorm"
)

type ShareService struct {
	db            *gorm.DB
	configService *common.ConfigService
}

func NewShareService() *ShareService {
	return &ShareService{
		db:            db.DB.Shop,
		configService: common.NewConfigService(),
	}
}

// GetShareSetting 获取分销设置（从配置服务获取）
func (s *ShareService) GetShareSetting() (*common.ShareConfig, error) {
	return s.configService.GetShareConfig()
}

// GetDistributorInfo 获取分销员信息
func (s *ShareService) GetDistributorInfo(userId int64) (*ShopDB.ShareUserDO, error) {
	var distributor ShopDB.ShareUserDO
	err := (&ShopDB.ShareUserDO{}).Query().Where("user_id = ?", userId).First(&distributor).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 用户没有分销员记录，返回404错误
			return nil, werror.New(404, "用户暂未申请分销", map[string]interface{}{
				"userId": userId,
			})
		}
		return nil, werror.New(200, "获取分销员信息失败", map[string]interface{}{
			"error": err.Error(),
		})
	}

	// 计算总收益（所有已结算的分销订单）
	var totalIncome float64
	err = (&ShopDB.ShareIncomeDO{}).Query().
		Where("user_id = ? AND commission_status = ?", userId, ShopDB.CommissionStatusSettled).
		Select("COALESCE(SUM(commission_amount), 0)").
		Scan(&totalIncome).Error
	if err != nil {
		return nil, werror.New(500, "计算总收益失败", map[string]interface{}{
			"error": err.Error(),
		})
	}

	// 计算可提现金额（已解冻的分销订单）
	var availableBalance float64
	err = (&ShopDB.ShareIncomeDO{}).Query().
		Where("user_id = ? AND commission_status = ? AND (unfreeze_time IS NULL OR unfreeze_time <= ?)", userId, ShopDB.CommissionStatusSettled, time.Now()).
		Select("COALESCE(SUM(commission_amount), 0)").
		Scan(&availableBalance).Error
	if err != nil {
		return nil, werror.New(500, "计算可提现金额失败", map[string]interface{}{
			"error": err.Error(),
		})
	}

	// 计算冻结金额（未解冻的分销订单）
	var frozenBalance float64
	err = (&ShopDB.ShareIncomeDO{}).Query().
		Where("user_id = ? AND commission_status = ? AND unfreeze_time > ?", userId, ShopDB.CommissionStatusPending, time.Now()).
		Select("COALESCE(SUM(commission_amount), 0)").
		Scan(&frozenBalance).Error
	if err != nil {
		return nil, werror.New(500, "计算冻结金额失败", map[string]interface{}{
			"error": err.Error(),
		})
	}

	// 更新分销员信息
	distributor.TotalIncome = totalIncome
	distributor.AvailableBalance = availableBalance
	distributor.FrozenBalance = frozenBalance

	return &distributor, nil
}

// ApplyDistributor 申请成为分销员
func (s *ShareService) ApplyDistributor(req *dto.ApplyDistributorReq) error {
	// 获取分销配置
	setting, err := s.GetShareSetting()
	if err != nil {
		return err
	}

	if !setting.Enabled {
		return werror.New(500, "分销功能未开启", map[string]interface{}{
			"enabled": setting.Enabled,
		})
	}

	// 检查是否已经有申请记录
	distributor, err := s.GetDistributorInfo(req.UserId)
	if err != nil {
		// 如果是404错误，表示用户没有申请记录，可以继续申请
		if werr, ok := err.(*werror.WError); ok && werr.Code == 404 {
			// 创建新的申请记录（待审核状态）
			return s.createDistributorApplication(req)
		}
		// 其他错误直接返回
		return err
	}

	// 如果获取到了分销员信息，检查状态
	if distributor.Status == 1 {
		return werror.New(400, "您已经是分销员", map[string]interface{}{
			"userId":        req.UserId,
			"distributorId": distributor.ID,
		})
	} else if distributor.Status == 0 {
		return werror.New(400, "您的申请正在审核中，请耐心等待", map[string]interface{}{
			"userId":        req.UserId,
			"distributorId": distributor.ID,
		})
	}

	// 如果状态是2（已拒绝），允许重新申请，更新记录
	return s.updateDistributorApplication(req)
}

// CheckQualification 检查分销资格
func (s *ShareService) CheckQualification(userId int64) (bool, error) {
	// 获取分销配置
	_, err := s.GetShareSetting()
	if err != nil {
		return false, err
	}

	// 获取用户消费总额
	var totalAmount float64
	err = (&ShopDB.OrderDO{}).Query().
		Where("user_id = ? AND status = ?", userId, "completed").
		Select("COALESCE(SUM(pay_amount), 0)").
		Row().Scan(&totalAmount)
	if err != nil {
		return false, werror.New(500, "检查资格失败", map[string]interface{}{
			"error": err.Error(),
		})
	}

	// TODO: 根据实际业务需求添加其他条件检查
	return true, nil
}

// createDistributor 创建分销员
func (s *ShareService) createDistributor(userId int64, status ...int) error {
	defaultStatus := 1 // 默认审核通过
	if len(status) > 0 {
		defaultStatus = status[0]
	}

	now := time.Now()
	distributor := &ShopDB.ShareUserDO{
		UserId:           userId,
		Level:            1, // 默认一级分销员
		Status:           defaultStatus,
		TotalIncome:      0,
		AvailableBalance: 0,
		FrozenBalance:    0,
		TeamCount:        0,
		CreateTime:       now,
		UpdateTime:       now,
	}

	err := (&ShopDB.ShareUserDO{}).Query().Create(distributor).Error
	if err != nil {
		return werror.New(500, "创建分销员失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}
	return nil
}

// GetTeamStats 获取团队统计数据
func (s *ShareService) GetTeamStats(userId int64) (map[string]interface{}, error) {
	// 获取直接下级数量（一级成员）- 通过user_relation表查询邀请关系
	var directCount int64
	err := s.db.Table("user_relation ur").
		Joins("JOIN share_user su ON ur.user_id = su.user_id").
		Where("ur.related_user_id = ? AND ur.relation_type = 'invite' AND ur.status = 1 AND su.status = 1", userId).
		Count(&directCount).Error
	if err != nil {
		return nil, werror.New(500, "获取直接下级数量失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 获取二级成员数量（直接下级的下级）
	var indirectCount int64
	err = s.db.Table("user_relation ur1").
		Joins("JOIN user_relation ur2 ON ur1.related_user_id = ur2.user_id").
		Joins("JOIN share_user su1 ON ur1.user_id = su1.user_id").
		Joins("JOIN share_user su2 ON ur2.user_id = su2.user_id").
		Where("ur2.related_user_id = ? AND ur1.relation_type = 'invite' AND ur2.relation_type = 'invite' AND ur1.status = 1 AND ur2.status = 1 AND su1.status = 1 AND su2.status = 1", userId).
		Count(&indirectCount).Error
	if err != nil {
		return nil, werror.New(500, "获取二级下级数量失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 计算团队总收益（所有下级的收益总和）
	var totalIncome float64
	err = s.db.Table("user_relation ur").
		Select("COALESCE(SUM(su.total_income), 0)").
		Joins("JOIN share_user su ON ur.user_id = su.user_id").
		Where("ur.related_user_id = ? AND ur.relation_type = 'invite' AND ur.status = 1 AND su.status = 1", userId).
		Row().Scan(&totalIncome)
	if err != nil {
		totalIncome = 0 // 如果查询失败，设为0
	}

	// 返回统计数据
	return map[string]interface{}{
		"teamCount":     directCount + indirectCount, // 团队总人数
		"directCount":   directCount,                 // 直接下级（一级成员）
		"indirectCount": indirectCount,               // 间接下级（二级成员）
		"totalIncome":   totalIncome,                 // 团队总收益
		// 为了兼容前端，保留原有字段
		"level1Count": directCount,
		"level2Count": indirectCount,
	}, nil
}

// getTeamCountRecursive 递归计算团队人数
func (s *ShareService) getTeamCountRecursive(userId int64) (int64, error) {
	// 查询直接下级
	var directMembers []ShopDB.ShareRelationDO
	err := (&ShopDB.ShareRelationDO{}).Query().
		Where("inviter_id = ? AND status = 1", userId).
		Find(&directMembers).Error
	if err != nil {
		return 0, werror.New(500, "查询直接下级失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	totalCount := int64(len(directMembers)) // 直接下级数量

	// 递归计算每个直接下级的团队人数
	for _, member := range directMembers {
		subCount, err := s.getTeamCountRecursive(member.UserId)
		if err != nil {
			// 递归失败不影响主流程，只记录日志
			fmt.Printf("递归计算团队人数失败: userId=%d, error=%v\n", member.UserId, err)
			continue
		}
		totalCount += subCount
	}

	return totalCount, nil
}

// getTeamMembersRecursive 递归获取团队成员列表
func (s *ShareService) getTeamMembersRecursive(userId int64, level int, maxLevel int) ([]map[string]interface{}, error) {
	if level > maxLevel {
		return nil, nil
	}

	var members []map[string]interface{}

	// 查询直接下级
	var directMembers []struct {
		UserId     int64     `gorm:"column:user_id"`
		CreateTime time.Time `gorm:"column:create_time"`
	}

	err := s.db.Table("share_relation").
		Select("user_id, create_time").
		Where("inviter_id = ? AND status = 1", userId).
		Find(&directMembers).Error
	if err != nil {
		return nil, err
	}

	// 获取用户详细信息
	for _, member := range directMembers {
		var userInfo struct {
			ID          int64   `gorm:"column:id"`
			Nickname    string  `gorm:"column:nickname"`
			Avatar      string  `gorm:"column:avatar"`
			TotalIncome float64 `gorm:"column:total_income"`
		}

		err := s.db.Table("user u").
			Select("u.id, u.nickname, u.avatar, COALESCE(du.total_income, 0) as total_income").
			Joins("LEFT JOIN share_user du ON u.id = du.user_id").
			Where("u.id = ?", member.UserId).
			First(&userInfo).Error
		if err != nil {
			continue // 跳过查询失败的用户
		}

		memberInfo := map[string]interface{}{
			"ID":          userInfo.ID,
			"Nickname":    userInfo.Nickname,
			"Avatar":      userInfo.Avatar,
			"Level":       level,
			"TotalIncome": userInfo.TotalIncome,
			"CreateTime":  member.CreateTime,
		}
		members = append(members, memberInfo)

		// 递归获取下级成员
		if level < maxLevel {
			subMembers, err := s.getTeamMembersRecursive(member.UserId, level+1, maxLevel)
			if err == nil {
				members = append(members, subMembers...)
			}
		}
	}

	return members, nil
}

// GetTeamMembers 获取团队成员列表
func (s *ShareService) GetTeamMembers(userId int64, page, size int) (map[string]interface{}, error) {
	offset := (page - 1) * size

	// 查询团队成员（通过parent_id字段）
	var members []struct {
		ID          int64     `gorm:"column:id"`
		UserId      int64     `gorm:"column:user_id"`
		Level       int       `gorm:"column:level"`
		TotalIncome float64   `gorm:"column:total_income"`
		CreateTime  time.Time `gorm:"column:create_time"`
		Nickname    string    `gorm:"column:nickname"`
		Avatar      string    `gorm:"column:avatar"`
	}

	err := s.db.Table("user_relation ur").
		Select("su.id, su.user_id, su.level, su.total_income, ur.create_time, u.nickname, u.avatar").
		Joins("JOIN share_user su ON ur.user_id = su.user_id").
		Joins("LEFT JOIN user u ON su.user_id = u.id").
		Where("ur.related_user_id = ? AND ur.relation_type = 'invite' AND ur.status = 1 AND su.status = 1", userId).
		Order("ur.create_time DESC").
		Offset(offset).
		Limit(size).
		Find(&members).Error

	if err != nil {
		return nil, werror.New(500, "获取团队成员失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 查询总数
	var total int64
	err = s.db.Table("user_relation ur").
		Joins("JOIN share_user su ON ur.user_id = su.user_id").
		Where("ur.related_user_id = ? AND ur.relation_type = 'invite' AND ur.status = 1 AND su.status = 1", userId).
		Count(&total).Error
	if err != nil {
		return nil, werror.New(500, "获取团队成员总数失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 转换为前端需要的格式
	var memberList []map[string]interface{}
	for _, member := range members {
		memberInfo := map[string]interface{}{
			"id":          member.ID,
			"userId":      member.UserId,
			"nickname":    member.Nickname,
			"avatar":      member.Avatar,
			"level":       member.Level,
			"totalIncome": member.TotalIncome,
			"createTime":  member.CreateTime,
		}
		memberList = append(memberList, memberInfo)
	}

	return map[string]interface{}{
		"list":  memberList,
		"total": total,
	}, nil
}

// GetDistributionOrders 获取分销订单列表
func (s *ShareService) GetDistributionOrders(userId int64, page, size int) (*vo.DistributionOrderListVO, error) {
	offset := (page - 1) * size

	// 查询分销订单
	var orders []vo.DistributionOrderVO

	err := s.db.Table("share_income si").
		Select(`si.id, si.order_amount, si.commission_rate, si.commission_amount, 
			si.level, si.commission_status as status, si.unfreeze_time, si.create_time,
			si.source_order_no as order_no, '' as goods_name, '' as goods_image`).
		Where("si.user_id = ?", userId).
		Offset(offset).
		Limit(size).
		Order("si.create_time DESC").
		Scan(&orders).Error

	if err != nil {
		return nil, werror.New(500, "获取分销订单失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 查询总数
	var total int64
	err = s.db.Table("share_income").
		Where("user_id = ?", userId).
		Count(&total).Error
	if err != nil {
		return nil, werror.New(500, "获取分销订单总数失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	return &vo.DistributionOrderListVO{
		List:  orders,
		Total: total,
	}, nil
}

// GetDistributionOrderStats 获取分销订单统计数据
func (s *ShareService) GetDistributionOrderStats(userId int64) (*vo.DistributionOrderStatsVO, error) {
	// 查询订单总数
	var orderCount int64
	err := s.db.Table("share_income").
		Where("user_id = ? AND commission_status = ?", userId, 1). // 只统计已结算的订单
		Count(&orderCount).Error
	if err != nil {
		return nil, werror.New(500, "获取订单总数失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 查询总收益（所有已结算的订单）
	var totalIncome float64
	err = s.db.Table("share_income").
		Where("user_id = ? AND commission_status = ?", userId, 1).
		Select("COALESCE(SUM(commission_amount), 0)").
		Row().Scan(&totalIncome)
	if err != nil {
		return nil, werror.New(500, "获取总收益失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 查询本月收益
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	var monthIncome float64
	err = s.db.Table("share_income").
		Where("user_id = ? AND commission_status = ? AND create_time >= ?", userId, 1, monthStart).
		Select("COALESCE(SUM(commission_amount), 0)").
		Row().Scan(&monthIncome)
	if err != nil {
		return nil, werror.New(500, "获取本月收益失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 查询可提现金额（已结算且已解冻的订单）
	var availableIncome float64
	err = s.db.Table("share_income").
		Where("user_id = ? AND commission_status = ? AND (unfreeze_time IS NULL OR unfreeze_time <= ?)", userId, 1, time.Now()).
		Select("COALESCE(SUM(commission_amount), 0)").
		Row().Scan(&availableIncome)
	if err != nil {
		return nil, werror.New(500, "获取可提现金额失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 查询冻结金额（待结算的订单）
	var frozenIncome float64
	err = s.db.Table("share_income").
		Where("user_id = ? AND commission_status = ?", userId, 0).
		Select("COALESCE(SUM(commission_amount), 0)").
		Row().Scan(&frozenIncome)
	if err != nil {
		return nil, werror.New(500, "获取冻结金额失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 查询今日收益（今日已结算的订单）
	today := time.Now().Format("2006-01-02")
	var todayIncome float64
	err = s.db.Table("share_income").
		Where("user_id = ? AND commission_status = ? AND DATE(create_time) = ?", userId, 1, today).
		Select("COALESCE(SUM(commission_amount), 0)").
		Row().Scan(&todayIncome)
	if err != nil {
		return nil, werror.New(500, "获取今日收益失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	return &vo.DistributionOrderStatsVO{
		TotalCount:      orderCount,
		TotalIncome:     totalIncome,
		MonthIncome:     monthIncome,
		AvailableIncome: availableIncome,
		FrozenIncome:    frozenIncome,
		TodayIncome:     todayIncome,
	}, nil
}

// Withdraw 提现
func (s *ShareService) Withdraw(ctx context.Context, req *dto.WithdrawReq) error {
	// 1. 检查用户可提现金额
	var availableAmount float64
	err := (&ShopDB.ShareIncomeDO{}).Query().
		Where("user_id = ? AND status = ? AND unfreeze_time <= ?", req.UserId, 1, time.Now()).
		Select("COALESCE(SUM(commission_amount), 0)").
		Scan(&availableAmount).Error
	if err != nil {
		return werror.New(500, "查询可提现金额失败", map[string]interface{}{
			"error": err.Error(),
		})
	}

	// 2. 检查提现金额是否超过可提现金额
	if req.Amount > availableAmount {
		return werror.New(500, "提现金额超过可提现金额", map[string]interface{}{
			"availableAmount": availableAmount,
			"amount":          req.Amount,
		})
	}

	// 3. 创建提现记录
	now := time.Now()
	withdraw := &ShopDB.ShareWithdrawDO{
		UserId:       req.UserId,
		Amount:       req.Amount,
		Fee:          req.Amount * 0.01, // 1% 手续费
		ActualAmount: req.Amount * 0.99,
		Method:       req.Method,
		Account:      req.Account,
		RealName:     req.RealName,
		Status:       ShopDB.WithdrawStatusPending,
		CreateTime:   now,
		UpdateTime:   now,
	}

	err = withdraw.Query().Create(withdraw).Error
	if err != nil {
		return werror.New(500, "创建提现记录失败", map[string]interface{}{
			"error": err.Error(),
		})
	}

	return nil
}

// CreateDistributionIncome 创建分销收益记录（订单确认收货时调用）
func (s *ShareService) CreateDistributionIncome(ctx context.Context, order *ShopDB.OrderDO) error {
	// 1. 获取分销配置
	setting, err := s.GetShareSetting()
	if err != nil {
		return err
	}

	// 2. 递归查找订单购买者的上级关系（最多2级）
	ancestors, err := s.getAncestorsRecursive(order.UserID, 2)
	if err != nil {
		return werror.New(500, "查询分销关系失败", map[string]interface{}{
			"error": err.Error(),
		})
	}

	// 如果没有推荐关系,直接返回
	if len(ancestors) == 0 {
		return nil
	}

	// 3. 计算解冻时间
	unfreezeTime := time.Now().AddDate(0, 0, setting.FreezeDays)

	// 4. 为每个分销层级创建分销收益记录
	for i, ancestor := range ancestors {
		level := i + 1 // 层级从1开始

		// 根据层级获取佣金比例
		var commissionRate float64
		if level == 1 {
			commissionRate = setting.Level1Rate
		} else if level == 2 {
			commissionRate = setting.Level2Rate
		} else {
			// 超过配置的层级，跳过
			continue
		}

		// 计算佣金金额（简化：统一按支付金额计算）
		commissionAmount := order.PayAmount * (commissionRate / 100)

		// 获取购买用户信息
		var buyerUser ShopDB.UserDO
		err = (&ShopDB.UserDO{}).Query().
			Where("id = ?", order.UserID).
			First(&buyerUser).Error
		if err != nil {
			continue // 获取用户信息失败，跳过
		}

		// 创建分销收益记录
		income := &ShopDB.ShareIncomeDO{
			ReferrerId:       &ancestor,                                 // 推荐官ID
			UserId:           ancestor,                                  // 推荐官用户ID
			InviteeId:        &order.UserID,                             // 被邀请人ID
			InviteeName:      buyerUser.Nickname,                        // 被邀请人姓名
			CommissionAmount: commissionAmount,                          // 佣金金额
			CommissionType:   ShopDB.CommissionTypePurchase,             // 购买佣金
			CommissionStatus: ShopDB.CommissionStatusPending,            // 待结算状态
			SourceOrderNo:    order.OrderNo,                             // 来源订单号
			OrderAmount:      order.PayAmount,                           // 订单金额
			CommissionRate:   commissionRate,                            // 佣金比例
			Level:            level,                                     // 分销层级
			UnfreezeTime:     &unfreezeTime,                             // 解冻时间
			Remark:           fmt.Sprintf("订单佣金，订单号：%s", order.OrderNo), // 备注
			CreateTime:       time.Now(),
			UpdateTime:       time.Now(),
		}

		err = income.Query().Create(income).Error
		if err != nil {
			return werror.New(500, "创建分销收益记录失败", map[string]interface{}{
				"error": err.Error(),
			})
		}
	}

	return nil
}

// getAncestorsRecursive 递归获取用户的上级关系
func (s *ShareService) getAncestorsRecursive(userId int64, maxLevel int) ([]int64, error) {
	var ancestors []int64
	currentUserId := userId

	for level := 1; level <= maxLevel; level++ {
		// 查找当前用户的直接上级
		var relation ShopDB.ShareRelationDO
		err := (&ShopDB.ShareRelationDO{}).Query().
			Where("user_id = ? AND status = 1", currentUserId).
			First(&relation).Error

		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 没有上级了，结束递归
			break
		} else if err != nil {
			return nil, werror.New(500, "查询上级关系失败", map[string]interface{}{
				"userId": currentUserId,
				"level":  level,
				"error":  err.Error(),
			})
		}

		// 添加上级到结果中
		ancestors = append(ancestors, relation.InviterId)
		// 继续查找上级的上级
		currentUserId = relation.InviterId
	}

	return ancestors, nil
}

// CancelDistributionIncome 取消分销收益（订单退款时调用）
func (s *ShareService) CancelDistributionIncome(ctx context.Context, orderNo string) error {
	// 将分销收益状态改为已取消
	err := (&ShopDB.ShareIncomeDO{}).Query().
		Where("source_order_no = ?", orderNo).
		Updates(map[string]interface{}{
			"commission_status": ShopDB.CommissionStatusCancelled, // 已取消
			"update_time":       time.Now(),
		}).Error

	if err != nil {
		return werror.New(500, "取消分销收益失败", map[string]interface{}{
			"orderNo": orderNo,
			"error":   err.Error(),
		})
	}

	return nil
}

// UpdateIncomeUnfreezeTime 更新收益解冻时间
func (s *ShareService) UpdateIncomeUnfreezeTime(ctx context.Context, orderNo string) error {
	// 1. 获取分销配置
	setting, err := s.GetShareSetting()
	if err != nil {
		return err
	}

	// 2. 计算新的解冻时间
	unfreezeTime := time.Now().AddDate(0, 0, setting.FreezeDays)

	// 3. 更新解冻时间
	err = (&ShopDB.ShareIncomeDO{}).Query().
		Where("source_order_no = ? AND commission_status = ?", orderNo, ShopDB.CommissionStatusPending). // 只更新待结算的记录
		Updates(map[string]interface{}{
			"unfreeze_time": unfreezeTime,
			"update_time":   time.Now(),
		}).Error

	if err != nil {
		return werror.New(500, "更新解冻时间失败", map[string]interface{}{
			"orderNo": orderNo,
			"error":   err.Error(),
		})
	}

	return nil
}

// GetWithdrawList 获取提现列表
func (s *ShareService) GetWithdrawList(userId int64, page, size int) (map[string]interface{}, error) {
	offset := (page - 1) * size

	// 查询提现记录
	var withdraws []ShopDB.ShareWithdrawDO
	err := (&ShopDB.ShareWithdrawDO{}).Query().
		Where("user_id = ?", userId).
		Order("create_time DESC").
		Offset(offset).
		Limit(size).
		Find(&withdraws).Error

	if err != nil {
		return nil, werror.New(500, "获取提现记录失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 查询总数
	var total int64
	err = (&ShopDB.ShareWithdrawDO{}).Query().
		Where("user_id = ?", userId).
		Count(&total).Error
	if err != nil {
		return nil, werror.New(500, "获取提现记录总数失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	return map[string]interface{}{
		"list":  withdraws,
		"total": total,
	}, nil
}

// GetIncomeList 获取收益记录列表
func (s *ShareService) GetIncomeList(userId int64, req *dto.ShareIncomeListReq) (map[string]interface{}, error) {
	offset := (req.Page - 1) * req.Size

	// 构建查询条件
	query := (&ShopDB.ShareIncomeDO{}).Query().Where("user_id = ?", userId)

	// 添加筛选条件
	if req.CommissionType != "" {
		query = query.Where("commission_type = ?", req.CommissionType)
	}
	if req.CommissionStatus != nil {
		query = query.Where("commission_status = ?", *req.CommissionStatus)
	}
	if req.StartTime != "" {
		query = query.Where("create_time >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		query = query.Where("create_time <= ?", req.EndTime)
	}

	// 查询列表
	var incomes []ShopDB.ShareIncomeDO
	err := query.Order("create_time DESC").
		Offset(offset).
		Limit(req.Size).
		Find(&incomes).Error
	if err != nil {
		return nil, werror.New(500, "获取收益记录失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 查询总数
	var total int64
	err = (&ShopDB.ShareIncomeDO{}).Query().
		Where("user_id = ?", userId).
		Count(&total).Error
	if err != nil {
		return nil, werror.New(500, "获取收益记录总数失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 转换为DTO
	var incomeList []dto.ShareIncomeResp
	for _, income := range incomes {
		incomeList = append(incomeList, dto.ShareIncomeResp{
			ID:               income.ID,
			ReferrerId:       income.ReferrerId,
			UserId:           income.UserId,
			InviteeId:        income.InviteeId,
			InviteeName:      income.InviteeName,
			CommissionAmount: income.CommissionAmount,
			CommissionType:   income.CommissionType,
			CommissionStatus: income.CommissionStatus,
			SourceOrderNo:    income.SourceOrderNo,
			OrderAmount:      income.OrderAmount,
			CommissionRate:   income.CommissionRate,
			Level:            income.Level,
			UnfreezeTime:     income.UnfreezeTime,
			ProcessTime:      income.ProcessTime,
			Remark:           income.Remark,
			CreateTime:       income.CreateTime,
			UpdateTime:       income.UpdateTime,
		})
	}

	return map[string]interface{}{
		"list":  incomeList,
		"total": total,
	}, nil
}

// GetIncomeStats 获取收益统计
func (s *ShareService) GetIncomeStats(userId int64) (*dto.ShareIncomeStatsResp, error) {
	now := time.Now()
	today := now.Format("2006-01-02")
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	stats := &dto.ShareIncomeStatsResp{}

	// 累计收益（所有已结算的）
	err := (&ShopDB.ShareIncomeDO{}).Query().
		Where("user_id = ? AND commission_status = ?", userId, ShopDB.CommissionStatusSettled).
		Select("COALESCE(SUM(commission_amount), 0)").
		Scan(&stats.TotalIncome).Error
	if err != nil {
		return nil, werror.New(500, "计算累计收益失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 可用余额（已结算且已解冻的）
	err = (&ShopDB.ShareIncomeDO{}).Query().
		Where("user_id = ? AND commission_status = ? AND (unfreeze_time IS NULL OR unfreeze_time <= ?)",
			userId, ShopDB.CommissionStatusSettled, now).
		Select("COALESCE(SUM(commission_amount), 0)").
		Scan(&stats.AvailableBalance).Error
	if err != nil {
		return nil, werror.New(500, "计算可用余额失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 冻结余额（待结算的）
	err = (&ShopDB.ShareIncomeDO{}).Query().
		Where("user_id = ? AND commission_status = ?", userId, ShopDB.CommissionStatusPending).
		Select("COALESCE(SUM(commission_amount), 0)").
		Scan(&stats.FrozenBalance).Error
	if err != nil {
		return nil, werror.New(500, "计算冻结余额失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 今日收益
	err = (&ShopDB.ShareIncomeDO{}).Query().
		Where("user_id = ? AND commission_status = ? AND DATE(create_time) = ?",
			userId, ShopDB.CommissionStatusSettled, today).
		Select("COALESCE(SUM(commission_amount), 0)").
		Scan(&stats.TodayIncome).Error
	if err != nil {
		return nil, werror.New(500, "计算今日收益失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 本月收益
	err = (&ShopDB.ShareIncomeDO{}).Query().
		Where("user_id = ? AND commission_status = ? AND create_time >= ?",
			userId, ShopDB.CommissionStatusSettled, monthStart).
		Select("COALESCE(SUM(commission_amount), 0)").
		Scan(&stats.MonthIncome).Error
	if err != nil {
		return nil, werror.New(500, "计算本月收益失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 待结算数量
	err = (&ShopDB.ShareIncomeDO{}).Query().
		Where("user_id = ? AND commission_status = ?", userId, ShopDB.CommissionStatusPending).
		Count(&stats.PendingCount).Error
	if err != nil {
		return nil, werror.New(500, "计算待结算数量失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	// 已结算数量
	err = (&ShopDB.ShareIncomeDO{}).Query().
		Where("user_id = ? AND commission_status = ?", userId, ShopDB.CommissionStatusSettled).
		Count(&stats.SettledCount).Error
	if err != nil {
		return nil, werror.New(500, "计算已结算数量失败", map[string]interface{}{
			"userId": userId,
			"error":  err.Error(),
		})
	}

	return stats, nil
}

// CreateIncome 创建收益记录（手动创建）
func (s *ShareService) CreateIncome(req *dto.ShareIncomeCreateReq) error {
	now := time.Now()

	income := &ShopDB.ShareIncomeDO{
		ReferrerId:       req.ReferrerId,
		UserId:           req.UserId,
		InviteeId:        req.InviteeId,
		InviteeName:      req.InviteeName,
		CommissionAmount: req.CommissionAmount,
		CommissionType:   req.CommissionType,
		CommissionStatus: ShopDB.CommissionStatusPending, // 默认待结算
		SourceOrderNo:    req.SourceOrderNo,
		OrderAmount:      req.OrderAmount,
		CommissionRate:   req.CommissionRate,
		Level:            req.Level,
		Remark:           req.Remark,
		CreateTime:       now,
		UpdateTime:       now,
	}

	err := income.Query().Create(income).Error
	if err != nil {
		return werror.New(500, "创建收益记录失败", map[string]interface{}{
			"error": err.Error(),
		})
	}

	return nil
}

// UpdateIncomeStatus 更新收益状态
func (s *ShareService) UpdateIncomeStatus(req *dto.ShareIncomeUpdateStatusReq) error {
	now := time.Now()

	updates := map[string]interface{}{
		"commission_status": req.CommissionStatus,
		"update_time":       now,
	}

	// 如果有备注，添加备注
	if req.Remark != "" {
		updates["remark"] = req.Remark
	}

	// 如果是设置为已结算状态，记录处理时间
	if req.CommissionStatus == ShopDB.CommissionStatusSettled {
		updates["process_time"] = &now
	}

	err := (&ShopDB.ShareIncomeDO{}).Query().
		Where("id = ?", req.ID).
		Updates(updates).Error
	if err != nil {
		return werror.New(500, "更新收益状态失败", map[string]interface{}{
			"id":    req.ID,
			"error": err.Error(),
		})
	}

	return nil
}

// createDistributorApplication 创建分销员申请记录（待审核状态）
func (s *ShareService) createDistributorApplication(req *dto.ApplyDistributorReq) error {
	now := time.Now()
	distributor := &ShopDB.ShareUserDO{
		UserId:           req.UserId,
		RealName:         req.RealName,
		Phone:            req.Phone,
		IdCard:           req.IdCard,
		ApplyTime:        &now,
		Level:            1, // 默认一级分销员
		Status:           0, // 待审核状态
		TotalIncome:      0,
		AvailableBalance: 0,
		FrozenBalance:    0,
		TeamCount:        0,
		Remark:           req.Remark,
		CreateTime:       now,
		UpdateTime:       now,
	}

	err := (&ShopDB.ShareUserDO{}).Query().Create(distributor).Error
	if err != nil {
		return werror.New(500, "创建分销员申请失败", map[string]interface{}{
			"userId": req.UserId,
			"error":  err.Error(),
		})
	}
	return nil
}

// updateDistributorApplication 更新分销员申请记录（重新申请）
func (s *ShareService) updateDistributorApplication(req *dto.ApplyDistributorReq) error {
	now := time.Now()

	updates := map[string]interface{}{
		"real_name":     req.RealName,
		"phone":         req.Phone,
		"id_card":       req.IdCard,
		"apply_time":    &now,
		"status":        0,   // 重置为待审核状态
		"reject_reason": nil, // 清空拒绝原因
		"remark":        req.Remark,
		"update_time":   now,
	}

	err := (&ShopDB.ShareUserDO{}).Query().
		Where("user_id = ?", req.UserId).
		Updates(updates).Error
	if err != nil {
		return werror.New(500, "更新分销员申请失败", map[string]interface{}{
			"userId": req.UserId,
			"error":  err.Error(),
		})
	}
	return nil
}
