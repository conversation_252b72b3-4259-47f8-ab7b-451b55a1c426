package sms

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/url"
	"sort"
	"strings"
	"time"
	"wnsys/shop/app/common/myhttp"
	"wnsys/shop/app/config"

	"github.com/google/uuid"
)

// AliyunSmsService 阿里云短信服务
type AliyunSmsService struct {
	httpClient *myhttp.HTTPClient
}

// NewAliyunSmsService 创建阿里云短信服务实例
func NewAliyunSmsService() *AliyunSmsService {
	// 创建HTTP客户端并设置超时时间
	client := myhttp.NewHTTPClient().SetTimeout(10 * time.Second)

	return &AliyunSmsService{
		httpClient: client,
	}
}

// SendSmsCode 发送短信验证码
func (s *AliyunSmsService) SendSmsCode(phone, code, smsType string) error {
	// 设置模板参数（验证码）
	templateParam := map[string]string{
		"code": code,
	}
	templateParamJson, err := json.Marshal(templateParam)
	if err != nil {
		return fmt.Errorf("序列化模板参数失败: %w", err)
	}

	// 构建请求参数
	params := map[string]string{
		"Action":           "SendSms",
		"Version":          "2017-05-25",
		"RegionId":         "cn-hangzhou", // 默认区域
		"PhoneNumbers":     phone,
		"SignName":         config.App.Aliyun.SMS.SignName,
		"TemplateCode":     s.getTemplateCode(smsType),
		"TemplateParam":    string(templateParamJson),
		"Format":           "JSON",
		"AccessKeyId":      config.App.Aliyun.SMS.AccessKeyId,
		"SignatureMethod":  "HMAC-SHA1",
		"Timestamp":        time.Now().UTC().Format("2006-01-02T15:04:05Z"),
		"SignatureVersion": "1.0",
		"SignatureNonce":   strings.ReplaceAll(uuid.New().String(), "-", ""),
	}

	// 生成签名
	signature, err := s.generateSignature(params, config.App.Aliyun.SMS.AccessKeySecret)
	if err != nil {
		return fmt.Errorf("生成签名失败: %w", err)
	}
	params["Signature"] = signature

	// 使用自己的HTTP客户端发送GET请求
	body, err := s.httpClient.Get("https://dysmsapi.aliyuncs.com/", params)
	if err != nil {
		return fmt.Errorf("发送HTTP请求失败: %w", err)
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查发送结果
	if code, ok := result["Code"].(string); !ok || code != "OK" {
		message := "未知错误"
		if msg, ok := result["Message"].(string); ok {
			message = msg
		}
		return fmt.Errorf("短信发送失败: [%s] %s", code, message)
	}

	fmt.Printf("短信发送成功: 手机号=%s, 验证码=%s\n", phone, code)
	return nil
}

// generateSignature 生成阿里云API签名
func (s *AliyunSmsService) generateSignature(params map[string]string, accessKeySecret string) (string, error) {
	// 1. 对参数进行排序
	var keys []string
	for k := range params {
		if k != "Signature" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 2. 构建待签名字符串
	var sortedParams []string
	for _, k := range keys {
		sortedParams = append(sortedParams, url.QueryEscape(k)+"="+url.QueryEscape(params[k]))
	}
	queryString := strings.Join(sortedParams, "&")

	// 3. 构建StringToSign
	stringToSign := "GET&" + url.QueryEscape("/") + "&" + url.QueryEscape(queryString)

	// 4. 计算HMAC-SHA1签名
	key := accessKeySecret + "&"
	mac := hmac.New(sha1.New, []byte(key))
	mac.Write([]byte(stringToSign))
	signature := base64.StdEncoding.EncodeToString(mac.Sum(nil))

	return signature, nil
}

// SendCustomSms 发送自定义短信
func (s *AliyunSmsService) SendCustomSms(phone, templateCode string, templateParams map[string]string) error {
	// 设置模板参数
	var templateParamJson []byte
	var err error
	if templateParams != nil && len(templateParams) > 0 {
		templateParamJson, err = json.Marshal(templateParams)
		if err != nil {
			return fmt.Errorf("序列化模板参数失败: %w", err)
		}
	}

	// 构建请求参数
	params := map[string]string{
		"Action":           "SendSms",
		"Version":          "2017-05-25",
		"RegionId":         "cn-hangzhou", // 默认区域
		"PhoneNumbers":     phone,
		"SignName":         config.App.Aliyun.SMS.SignName,
		"TemplateCode":     templateCode,
		"Format":           "JSON",
		"AccessKeyId":      config.App.Aliyun.SMS.AccessKeyId,
		"SignatureMethod":  "HMAC-SHA1",
		"Timestamp":        time.Now().UTC().Format("2006-01-02T15:04:05Z"),
		"SignatureVersion": "1.0",
		"SignatureNonce":   strings.ReplaceAll(uuid.New().String(), "-", ""),
	}

	if len(templateParamJson) > 0 {
		params["TemplateParam"] = string(templateParamJson)
	}

	// 生成签名
	signature, err := s.generateSignature(params, config.App.Aliyun.SMS.AccessKeySecret)
	if err != nil {
		return fmt.Errorf("生成签名失败: %w", err)
	}
	params["Signature"] = signature

	// 使用自己的HTTP客户端发送GET请求
	body, err := s.httpClient.Get("https://dysmsapi.aliyuncs.com/", params)
	if err != nil {
		return fmt.Errorf("发送HTTP请求失败: %w", err)
	}

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查发送结果
	if code, ok := result["Code"].(string); !ok || code != "OK" {
		message := "未知错误"
		if msg, ok := result["Message"].(string); ok {
			message = msg
		}
		return fmt.Errorf("短信发送失败: [%s] %s", code, message)
	}

	fmt.Printf("短信发送成功: 手机号=%s, 模板=%s\n", phone, templateCode)
	return nil
}

// getTemplateCode 根据短信类型获取模板代码
func (s *AliyunSmsService) getTemplateCode(smsType string) string {
	if templateCode, exists := config.App.Aliyun.SMS.TemplateCode[smsType]; exists {
		return templateCode
	}
	// 默认返回注册模板
	return config.App.Aliyun.SMS.TemplateCode["register"]
}
