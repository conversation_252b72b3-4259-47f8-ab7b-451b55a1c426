package dining

import (
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"
)

type Service struct{}

// NewService 创建堂食外卖服务
func NewService() *Service {
	return &Service{}
}

// GetDiningTypes 获取用餐类型列表
func (s *Service) GetDiningTypes() ([]dto.DiningTypeResp, error) {
	var list []ShopDB.DiningTypeDO
	err := (&ShopDB.DiningTypeDO{}).Query().
		Where("status = ?", 1).
		Order("sort DESC, id ASC").
		Find(&list).Error
	if err != nil {
		return nil, err
	}

	result := make([]dto.DiningTypeResp, 0, len(list))
	for _, v := range list {
		result = append(result, dto.DiningTypeResp{
			ID:          v.ID,
			Name:        v.Name,
			Code:        v.Code,
			Icon:        v.Icon,
			Description: v.Description,
			Sort:        v.Sort,
		})
	}

	return result, nil
}

// GetDiningTables 获取餐桌列表
func (s *Service) GetDiningTables() (*dto.DiningTableListResp, error) {
	var list []ShopDB.DiningTableDO
	err := (&ShopDB.DiningTableDO{}).Query().
		Where("status != ?", 0). // 排除禁用状态
		Order("table_no ASC").
		Find(&list).Error
	if err != nil {
		return nil, err
	}

	result := &dto.DiningTableListResp{
		Total: int64(len(list)),
		List:  make([]dto.DiningTableResp, 0, len(list)),
	}

	for _, v := range list {
		result.List = append(result.List, dto.DiningTableResp{
			ID:        v.ID,
			TableNo:   v.TableNo,
			TableName: v.DiningTableName,
			Capacity:  v.Capacity,
			Status:    v.Status,
			QrCode:    v.QrCode,
		})
	}

	return result, nil
}

// GetTableByNo 根据餐桌编号获取餐桌信息
func (s *Service) GetTableByNo(tableNo string) (*dto.DiningTableResp, error) {
	var table ShopDB.DiningTableDO
	err := (&ShopDB.DiningTableDO{}).Query().
		Where("table_no = ? AND status != ?", tableNo, 0).
		First(&table).Error
	if err != nil {
		return nil, err
	}

	return &dto.DiningTableResp{
		ID:        table.ID,
		TableNo:   table.TableNo,
		TableName: table.DiningTableName,
		Capacity:  table.Capacity,
		Status:    table.Status,
		QrCode:    table.QrCode,
	}, nil
}

// UpdateTableStatus 更新餐桌状态
func (s *Service) UpdateTableStatus(tableID int64, status int8) error {
	return (&ShopDB.DiningTableDO{}).Query().
		Where("id = ?", tableID).
		Update("status", status).Error
}
