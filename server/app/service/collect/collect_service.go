package collect

import (
	"errors"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// Service 收藏点赞服务
type Service struct{}

// NewService 创建收藏点赞服务
func NewService() *Service {
	return &Service{}
}

// Collect 收藏/取消收藏
func (s *Service) Collect(userID int64, req *dto.CommonCollectReq) error {
	return s.handleAction(userID, req.TargetID, req.TargetType, ShopDB.ActionTypeCollect, req.Action)
}

// Like 点赞/取消点赞
func (s *Service) Like(userID int64, req *dto.CommonLikeReq) error {
	return s.handleAction(userID, req.TargetID, req.TargetType, ShopDB.ActionTypeLike, req.Action)
}

// GetCollectStatus 获取收藏状态
func (s *Service) GetCollectStatus(userID int64, targetID int64, targetType string) (*dto.CollectStatusResp, error) {
	return s.getStatus(userID, targetID, targetType, ShopDB.ActionTypeCollect)
}

// GetLikeStatus 获取点赞状态
func (s *Service) GetLikeStatus(userID int64, targetID int64, targetType string) (*dto.LikeStatusResp, error) {
	status, err := s.getStatus(userID, targetID, targetType, ShopDB.ActionTypeLike)
	if err != nil {
		return nil, err
	}
	return &dto.LikeStatusResp{
		IsLiked: status.IsCollected,
		Count:   status.Count,
	}, nil
}

// handleAction 处理收藏/点赞操作
func (s *Service) handleAction(userID, targetID int64, targetType string, actionType, action int) error {

	if action == 1 {
		// 添加收藏/点赞
		// 检查是否已经存在
		var count int64
		err := (&ShopDB.CollectDO{}).Query().
			Where("user_id = ? AND target_id = ? AND target_type = ? AND action_type = ? AND is_delete = 0",
				userID, targetID, targetType, actionType).
			Count(&count).Error
		if err != nil {
			return err
		}

		if count > 0 {
			actionName := "收藏"
			if actionType == ShopDB.ActionTypeLike {
				actionName = "点赞"
			}
			return errors.New("已经" + actionName + "过了")
		}

		// 使用ON DUPLICATE KEY UPDATE处理并发情况
		return db.DB.Shop.Exec(`
			INSERT INTO collect (user_id, target_id, target_type, action_type, is_delete, create_time, update_time)
			VALUES (?, ?, ?, ?, 0, NOW(), NOW())
			ON DUPLICATE KEY UPDATE 
			is_delete = 0, update_time = NOW()
		`, userID, targetID, targetType, actionType).Error
	} else {
		// 取消收藏/点赞
		return (&ShopDB.CollectDO{}).Query().
			Where("user_id = ? AND target_id = ? AND target_type = ? AND action_type = ?",
				userID, targetID, targetType, actionType).
			Updates(map[string]interface{}{
				"is_delete":   1,
				"update_time": gorm.Expr("NOW()"),
			}).Error
	}
}

// getStatus 获取状态
func (s *Service) getStatus(userID, targetID int64, targetType string, actionType int) (*dto.CollectStatusResp, error) {

	// 检查用户是否已操作
	var isActioned bool
	if userID > 0 {
		var count int64
		err := (&ShopDB.CollectDO{}).Query().
			Where("user_id = ? AND target_id = ? AND target_type = ? AND action_type = ? AND is_delete = 0",
				userID, targetID, targetType, actionType).
			Count(&count).Error
		if err != nil {
			return nil, err
		}
		isActioned = count > 0
	}

	// 获取总数
	var totalCount int64
	err := (&ShopDB.CollectDO{}).Query().
		Where("target_id = ? AND target_type = ? AND action_type = ? AND is_delete = 0",
			targetID, targetType, actionType).
		Count(&totalCount).Error
	if err != nil {
		return nil, err
	}

	return &dto.CollectStatusResp{
		IsCollected: isActioned,
		Count:       totalCount,
	}, nil
}
