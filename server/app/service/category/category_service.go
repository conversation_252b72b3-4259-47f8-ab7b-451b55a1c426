package category

import (
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"
)

type Service struct{}

// GetCategoryTree 获取分类树形结构
func (s *Service) GetCategoryTree() ([]dto.CategoryTreeResp, error) {
	// 获取所有分类
	var categories []ShopDB.CategoryDO
	categoryDO := &ShopDB.CategoryDO{}
	if err := categoryDO.Query().Where("status = ?", 1).Order("sort DESC").Find(&categories).Error; err != nil {
		return nil, err
	}

	// 构建分类树
	return buildCategoryTree(categories, 0), nil
}

// GetCategoryGoods 获取分类商品列表
func (s *Service) GetCategoryGoods(req *dto.CategoryGoodsReq) (*dto.CategoryGoodsResp, error) {
	var total int64
	var goods []ShopDB.GoodsDO
	goodsDO := &ShopDB.GoodsDO{}

	// 构建查询条件
	query := goodsDO.Query().Where("status = ?", 1)

	// 如果提供了子分类ID，则使用子分类ID查询
	if req.SubCategoryID > 0 {
		query = query.Where("category_id = ?", req.SubCategoryID)
	} else {
		// 否则查询该分类下所有商品（包括子分类）
		// 先获取所有子分类ID
		var categories []ShopDB.CategoryDO
		if err := (&ShopDB.CategoryDO{}).Query().Where("parent_id = ? OR id = ?", req.CategoryID, req.CategoryID).Find(&categories).Error; err != nil {
			return nil, err
		}

		var categoryIDs []int64
		for _, c := range categories {
			categoryIDs = append(categoryIDs, c.ID)
		}

		if len(categoryIDs) > 0 {
			query = query.Where("category_id IN (?)", categoryIDs)
		} else {
			query = query.Where("category_id = ?", req.CategoryID)
		}
	}

	// 查询商品总数
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 分页查询商品列表
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(int(offset)).Limit(int(req.PageSize)).Order("sort DESC,id desc").Find(&goods).Error; err != nil {
		return nil, err
	}

	// 获取商品ID列表
	var goodsIDs []int64
	for _, g := range goods {
		goodsIDs = append(goodsIDs, g.ID)
	}

	// 查询商品主图
	var images []ShopDB.GoodsImageDO
	if err := (&ShopDB.GoodsImageDO{}).Query().Where("goods_id IN (?) AND position = ?", goodsIDs, 1).Find(&images).Error; err != nil {
		return nil, err
	}

	// 构建商品ID到主图的映射
	imageMap := make(map[int64]string)
	for _, img := range images {
		imageMap[img.GoodsID] = img.URL
	}

	// 查询商品SKU信息（获取最低价格和总库存）
	var skuList []struct {
		GoodsID       int64   `gorm:"column:goods_id"`
		MinPrice      float64 `gorm:"column:min_price"`
		OriginalPrice float64 `gorm:"column:original_price"`
		TotalStock    int     `gorm:"column:total_stock"`
	}
	if len(goodsIDs) > 0 {
		err := (&ShopDB.GoodsSkuDO{}).Query().
			Select("goods_id, MIN(price) as min_price, MIN(original_price) as original_price, SUM(stock) as total_stock").
			Where("goods_id IN (?) AND status = ?", goodsIDs, 1).
			Group("goods_id").
			Find(&skuList).Error
		if err != nil {
			return nil, err
		}
	}

	// 构建商品ID到SKU信息的映射
	skuMap := make(map[int64]struct {
		MinPrice      float64
		OriginalPrice float64
		TotalStock    int
	})
	for _, sku := range skuList {
		skuMap[sku.GoodsID] = struct {
			MinPrice      float64
			OriginalPrice float64
			TotalStock    int
		}{
			MinPrice:      sku.MinPrice,
			OriginalPrice: sku.OriginalPrice,
			TotalStock:    sku.TotalStock,
		}
	}

	// 转换为响应结构
	resp := &dto.CategoryGoodsResp{
		Total: uint64(total),
		List:  make([]dto.CategoryGoodsItemResp, 0, len(goods)),
	}

	for _, g := range goods {
		// 获取SKU信息
		skuInfo := skuMap[g.ID]

		resp.List = append(resp.List, dto.CategoryGoodsItemResp{
			ID:            uint(g.ID),
			Name:          g.Name,
			Price:         skuInfo.MinPrice,
			OriginalPrice: skuInfo.OriginalPrice,
			Image:         imageMap[g.ID],
			Sales:         0, // 销量字段已移除，暂时设为0
			Stock:         uint(skuInfo.TotalStock),
			Status:        uint8(g.Status),
			Description:   g.Description,
		})
	}

	return resp, nil
}

// buildCategoryTree 构建分类树
func buildCategoryTree(categories []ShopDB.CategoryDO, parentID uint) []dto.CategoryTreeResp {
	var tree []dto.CategoryTreeResp
	for _, c := range categories {
		if uint(c.ParentID) == parentID {
			node := dto.CategoryTreeResp{
				ID:       uint(c.ID),
				Name:     c.Name,
				Icon:     c.Icon,
				Image:    c.Image,
				Level:    uint8(c.Level),
				Sort:     uint(c.Sort),
				Children: buildCategoryTree(categories, uint(c.ID)),
			}
			tree = append(tree, node)
		}
	}
	return tree
}
