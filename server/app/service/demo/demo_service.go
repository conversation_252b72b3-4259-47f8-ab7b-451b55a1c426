package demo

import (
	"wnsys/shop/app/model/do"
	"wnsys/shop/app/model/dto"
)

// DemoService 示例服务
type DemoService struct{}

// NewDemoService 创建服务实例
func NewDemoService() *DemoService {
	return &DemoService{}
}

// GetDemoList 获取列表
func (s *DemoService) GetDemoList(query *dto.DemoQuery) ([]do.DemoDO, int64, error) {
	var list []do.DemoDO
	var total int64

	demoDO := &do.DemoDO{}
	db := demoDO.Query()

	// 添加查询条件
	if query.Keyword != "" {
		db = db.Where("name LIKE ?", "%"+query.Keyword+"%")
	}
	if query.Status != "" {
		db = db.Where("status = ?", query.Status)
	}

	// 查询总数
	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询数据
	err = db.Offset((query.PageNum - 1) * query.PageSize).
		Limit(query.PageSize).
		Find(&list).Error
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}
