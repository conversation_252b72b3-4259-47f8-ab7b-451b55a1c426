package comment

import (
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strings"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/model/vo"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// CommentService 评论服务
type CommentService struct{}

// NewService 创建评论服务
func NewService() *CommentService {
	return &CommentService{}
}

// CreateComment 创建评论
func (s *CommentService) CreateComment(req *dto.CreateCommentReq, userId int64) error {
	// 将图片数组转为JSON字符串
	images, err := json.Marshal(req.Images)
	if err != nil {
		return err
	}

	// 创建评论
	comment := &ShopDB.GoodsCommentDO{
		GoodsID:     req.GoodsID,
		OrderID:     req.OrderID,
		UserID:      userId,
		Content:     req.Content,
		Images:      string(images),
		Rate:        req.Rate,
		IsAnonymous: boolToInt8(req.IsAnonymous),
	}

	return db.DB.Shop.Create(comment).Error
}

// GetGoodsComments 获取商品评论列表
func (s *CommentService) GetGoodsComments(req *dto.GetGoodsCommentsReq) (*vo.CommentListVO, error) {
	var total int64
	var comments []ShopDB.GoodsCommentDO

	// 查询总数
	if err := db.DB.Shop.Model(&ShopDB.GoodsCommentDO{}).
		Where("goods_id = ? AND is_delete = ?", req.GoodsID, 0).
		Count(&total).Error; err != nil {
		return nil, err
	}

	// 查询评论列表
	if err := db.DB.Shop.Model(&ShopDB.GoodsCommentDO{}).
		Where("goods_id = ? AND is_delete = ?", req.GoodsID, 0).
		Order("create_time DESC").
		Offset((req.Page - 1) * req.Size).
		Limit(req.Size).
		Find(&comments).Error; err != nil {
		return nil, err
	}

	// 转换为VO
	commentVOs := make([]vo.CommentVO, 0, len(comments))
	for _, comment := range comments {
		var images []string
		if err := json.Unmarshal([]byte(comment.Images), &images); err != nil {
			return nil, err
		}

		// 获取用户信息
		var user ShopDB.UserDO
		if err := db.DB.Shop.First(&user, comment.UserID).Error; err != nil {
			return nil, err
		}

		// 获取订单商品规格信息
		var orderGoods ShopDB.OrderGoodsDO
		err := db.DB.Shop.Where("order_id = ? AND goods_id = ?", comment.OrderID, comment.GoodsID).
			First(&orderGoods).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}

		// 处理规格信息
		var specs string
		if orderGoods.ID > 0 && orderGoods.Specs != "" {
			// 解析规格JSON
			var specsMap map[string]struct {
				Value string `json:"value"`
			}
			if err := json.Unmarshal([]byte(orderGoods.Specs), &specsMap); err == nil {
				// 将规格信息转换为可读的字符串
				var specValues []string
				for key, spec := range specsMap {
					if spec.Value != "" {
						specValues = append(specValues, fmt.Sprintf("%s: %s", key, spec.Value))
					}
				}
				// 按规格名称排序
				sort.Strings(specValues)
				specs = strings.Join(specValues, " | ")
			}
		}

		commentVO := vo.CommentVO{
			ID:           comment.ID,
			UserID:       comment.UserID,
			Nickname:     user.Nickname,
			Avatar:       user.Avatar,
			Content:      comment.Content,
			Images:       images,
			Rate:         comment.Rate,
			IsAnonymous:  comment.IsAnonymous == 1,
			ReplyContent: comment.ReplyContent,
			ReplyTime:    comment.ReplyTime,
			CreateTime:   comment.CreateTime,
			Specs:        specs,
		}

		// 如果是匿名评论，隐藏用户信息
		if comment.IsAnonymous == 1 {
			commentVO.Nickname = "匿名用户"
			commentVO.Avatar = ""
		}

		commentVOs = append(commentVOs, commentVO)
	}

	return &vo.CommentListVO{
		Total: total,
		List:  commentVOs,
	}, nil
}

// boolToInt8 bool转int8
func boolToInt8(b bool) int8 {
	if b {
		return 1
	}
	return 0
}
