package goods

import (
	"fmt"
	"sort"
	"strings"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/vo"
	"wnsys/shop/app/provider/db"
)

// MultiSpecService 多规格服务
type MultiSpecService struct{}

// AllSpecData 所有规格类型的数据
type AllSpecData struct {
	SingleSpecData *SingleSpecData `json:"singleSpecData,omitempty"`
	MultiSpecData  *MultiSpecData  `json:"multiSpecData,omitempty"`
	ComboSpecData  *ComboSpecData  `json:"comboSpecData,omitempty"`
}

// SingleSpecData 单规格数据
type SingleSpecData struct {
	Sku vo.Sku `json:"sku"`
}

// MultiSpecData 多规格数据
type MultiSpecData struct {
	SpecGroups           []vo.SpecGroup    `json:"specGroups"`
	Skus                 []vo.Sku          `json:"skus"`
	SpecValueSelectable  map[string]bool   `json:"specValueSelectable"`
	SpecCombinationToSku map[string]string `json:"specCombinationToSku"`
	DefaultSelected      map[string]string `json:"defaultSelected"`
}

// ComboSpecData 套餐规格数据
type ComboSpecData struct {
	ComboGroups []ComboGroup `json:"comboGroups"`
	BaseSku     vo.Sku       `json:"baseSku"`
}

// ComboGroup 套餐规格组
type ComboGroup struct {
	Name        string        `json:"name"`
	Description string        `json:"description"`
	MinSelect   int           `json:"minSelect"`
	MaxSelect   int           `json:"maxSelect"`
	IsRequired  bool          `json:"isRequired"`
	Options     []ComboOption `json:"options"`
}

// ComboOption 套餐选项
type ComboOption struct {
	GoodsID     int64   `json:"goodsId"`
	Name        string  `json:"name"`
	Image       string  `json:"image"`
	AddPrice    float64 `json:"addPrice"`
	Quantity    int     `json:"quantity"`
	IsDefault   bool    `json:"isDefault"`
	Description string  `json:"description"`
}

// LoadAllSpecData 加载所有类型的规格数据
func (s *MultiSpecService) LoadAllSpecData(goodsId int64) (*AllSpecData, error) {
	// 查询商品基本信息
	var goods ShopDB.GoodsDO
	if err := (&ShopDB.GoodsDO{}).Query().Where("id = ?", goodsId).First(&goods).Error; err != nil {
		return nil, err
	}

	result := &AllSpecData{}

	switch goods.SpecType {
	case 1: // 单规格
		singleData, err := s.loadSingleSpecData(goodsId)
		if err != nil {
			return nil, err
		}
		result.SingleSpecData = singleData

	case 2: // 多规格
		multiData, err := s.loadMultiSpecData(goodsId)
		if err != nil {
			return nil, err
		}
		result.MultiSpecData = multiData

	case 3: // 套餐规格
		comboData, err := s.loadComboSpecData(goodsId)
		if err != nil {
			return nil, err
		}
		result.ComboSpecData = comboData
	}

	return result, nil
}

// loadSingleSpecData 加载单规格数据
func (s *MultiSpecService) loadSingleSpecData(goodsId int64) (*SingleSpecData, error) {
	// 查询SKU信息
	var sku ShopDB.GoodsSkuDO
	if err := (&ShopDB.GoodsSkuDO{}).Query().
		Where("goods_id = ? AND status = 1", goodsId).
		First(&sku).Error; err != nil {
		return nil, err
	}

	return &SingleSpecData{
		Sku: vo.Sku{
			SkuCode: sku.SkuCode,
			Price:   sku.Price,
			Stock:   sku.Stock,
			Specs:   make(map[string]string), // 单规格无规格信息
		},
	}, nil
}

// loadMultiSpecData 加载多规格数据
func (s *MultiSpecService) loadMultiSpecData(goodsId int64) (*MultiSpecData, error) {
	var specGroups []ShopDB.GoodsSpecGroupDO
	// 只查询多规格数据 (spec_type = 2)
	err := (&ShopDB.GoodsSpecGroupDO{}).Query().
		Where("goods_id = ? AND spec_type = ?", goodsId, 2).
		Order("sort ASC").
		Find(&specGroups).Error
	if err != nil {
		return nil, err
	}

	var specValues []ShopDB.GoodsSpecValueDO
	if len(specGroups) > 0 {
		var groupIds []int64
		for _, group := range specGroups {
			groupIds = append(groupIds, group.ID)
		}
		// 只查询多规格数据 (spec_type = 2)
		err = (&ShopDB.GoodsSpecValueDO{}).Query().
			Where("spec_group_id IN ? AND spec_type = ?", groupIds, 2).
			Order("sort ASC").
			Find(&specValues).Error
		if err != nil {
			return nil, err
		}
	}

	var skus []ShopDB.GoodsSkuDO
	err = (&ShopDB.GoodsSkuDO{}).Query().
		Where("goods_id = ?", goodsId).
		Find(&skus).Error
	if err != nil {
		return nil, err
	}

	return s.buildMultiSpecInfo(specGroups, specValues, skus)
}

// loadComboSpecData 加载套餐规格数据
func (s *MultiSpecService) loadComboSpecData(goodsId int64) (*ComboSpecData, error) {
	var specGroups []ShopDB.GoodsSpecGroupDO
	// 只查询套餐规格数据 (spec_type = 3)
	err := (&ShopDB.GoodsSpecGroupDO{}).Query().
		Where("goods_id = ? AND spec_type = ?", goodsId, 3).
		Order("sort ASC").
		Find(&specGroups).Error
	if err != nil {
		return nil, err
	}

	var specValues []ShopDB.GoodsSpecValueDO
	if len(specGroups) > 0 {
		var groupIds []int64
		for _, group := range specGroups {
			groupIds = append(groupIds, group.ID)
		}
		// 只查询套餐规格数据 (spec_type = 3)
		err = (&ShopDB.GoodsSpecValueDO{}).Query().
			Where("spec_group_id IN ? AND spec_type = ?", groupIds, 3).
			Order("sort ASC").
			Find(&specValues).Error
		if err != nil {
			return nil, err
		}
	}

	return s.buildComboSpecInfo(specGroups, specValues)
}

// buildMultiSpecInfo 构建多规格信息
func (s *MultiSpecService) buildMultiSpecInfo(specGroups []ShopDB.GoodsSpecGroupDO, specValues []ShopDB.GoodsSpecValueDO, skus []ShopDB.GoodsSkuDO) (*MultiSpecData, error) {
	multiData := &MultiSpecData{
		SpecGroups:           make([]vo.SpecGroup, 0),
		Skus:                 make([]vo.Sku, 0),
		SpecValueSelectable:  make(map[string]bool),
		SpecCombinationToSku: make(map[string]string),
		DefaultSelected:      make(map[string]string),
	}

	// 构建规格组ID到规格值列表的映射
	specValueMap := make(map[int64][]string)
	for _, value := range specValues {
		specValueMap[value.SpecGroupID] = append(specValueMap[value.SpecGroupID], value.Value)
	}

	// 构建规格组信息
	for _, group := range specGroups {
		values := specValueMap[group.ID]
		if len(values) == 0 {
			continue
		}
		sort.Strings(values) // 对规格值进行排序，保证顺序一致性
		multiData.SpecGroups = append(multiData.SpecGroups, vo.SpecGroup{
			Name:   group.Name,
			Values: values,
		})
	}

	// 如果没有SKU，直接返回
	if len(skus) == 0 {
		return multiData, nil
	}

	// 查询所有SKU的规格关联信息
	var skuIDs []int64
	for _, sku := range skus {
		skuIDs = append(skuIDs, sku.ID)
	}

	// 查询SKU规格关联信息
	var skuSpecInfos []struct {
		SkuID         int64  `gorm:"column:sku_id"`
		SpecGroupName string `gorm:"column:spec_group_name"`
		SpecValue     string `gorm:"column:spec_value"`
	}

	err := db.DB.Shop.Table("shop_goods_sku_spec ss").
		Select("ss.sku_id, sg.name as spec_group_name, sv.value as spec_value").
		Joins("LEFT JOIN shop_goods_spec_group sg ON ss.spec_group_id = sg.id").
		Joins("LEFT JOIN shop_goods_spec_value sv ON ss.spec_value_id = sv.id").
		Where("ss.sku_id IN (?)", skuIDs).
		Find(&skuSpecInfos).Error
	if err != nil {
		return nil, err
	}

	// 构建SKU ID到规格信息的映射
	skuSpecMap := make(map[int64]map[string]string)
	for _, specInfo := range skuSpecInfos {
		if skuSpecMap[specInfo.SkuID] == nil {
			skuSpecMap[specInfo.SkuID] = make(map[string]string)
		}
		skuSpecMap[specInfo.SkuID][specInfo.SpecGroupName] = specInfo.SpecValue
	}

	// 构建 SKU 信息
	var maxStockSku *ShopDB.GoodsSkuDO
	for _, sku := range skus {
		// 获取SKU的规格信息
		simpleSpecs := skuSpecMap[sku.ID]
		if simpleSpecs == nil {
			simpleSpecs = make(map[string]string)
		}

		// 添加 SKU
		multiData.Skus = append(multiData.Skus, vo.Sku{
			SkuCode: sku.SkuCode,
			Price:   sku.Price,
			Stock:   sku.Stock,
			Specs:   simpleSpecs,
		})

		// 构建规格组合到 SKU 的映射
		values := make([]string, 0)
		// 按规格组的顺序构建规格值数组
		for _, group := range multiData.SpecGroups {
			if val, ok := simpleSpecs[group.Name]; ok {
				values = append(values, val)
			}
		}
		if len(values) > 0 {
			multiData.SpecCombinationToSku[strings.Join(values, "_")] = sku.SkuCode
		}

		// 构建规格值可选状态
		for name, value := range simpleSpecs {
			key := name + "_" + value
			if sku.Stock > 0 {
				multiData.SpecValueSelectable[key] = true
			}
		}

		// 更新最大库存SKU
		if maxStockSku == nil || sku.Stock > maxStockSku.Stock {
			maxStockSku = &sku
		}
	}

	// 设置默认选中的规格值(选择库存最多的 SKU)
	if maxStockSku != nil {
		defaultSpecs := skuSpecMap[maxStockSku.ID]
		if defaultSpecs != nil {
			multiData.DefaultSelected = defaultSpecs
		}
	}

	return multiData, nil
}

// buildComboSpecInfo 构建套餐规格信息
func (s *MultiSpecService) buildComboSpecInfo(specGroups []ShopDB.GoodsSpecGroupDO, specValues []ShopDB.GoodsSpecValueDO) (*ComboSpecData, error) {
	comboData := &ComboSpecData{
		ComboGroups: make([]ComboGroup, 0),
	}

	// 查询套餐商品的基础SKU
	if len(specGroups) > 0 {
		goodsId := specGroups[0].GoodsID
		var sku ShopDB.GoodsSkuDO
		err := (&ShopDB.GoodsSkuDO{}).Query().
			Where("goods_id = ? AND status = 1", goodsId).
			First(&sku).Error
		if err != nil {
			return nil, fmt.Errorf("查询套餐基础SKU失败: %v", err)
		}

		// 设置基础SKU信息
		comboData.BaseSku = vo.Sku{
			SkuCode: sku.SkuCode,
			Price:   sku.Price,
			Stock:   sku.Stock,
			Specs:   make(map[string]string), // 套餐商品无传统规格信息
		}
	}

	// 构建规格组ID到规格值列表的映射
	specValueMap := make(map[int64][]ShopDB.GoodsSpecValueDO)
	for _, value := range specValues {
		specValueMap[value.SpecGroupID] = append(specValueMap[value.SpecGroupID], value)
	}

	// 构建套餐规格组信息
	for _, group := range specGroups {
		values := specValueMap[group.ID]
		if len(values) == 0 {
			continue
		}

		comboGroup := ComboGroup{
			Name:        group.Name,
			Description: group.Config.Description,
			MinSelect:   group.Config.MinSelect,
			MaxSelect:   group.Config.MaxSelect,
			IsRequired:  group.Config.IsRequired,
			Options:     make([]ComboOption, 0),
		}

		// 如果配置为空，设置默认值
		if comboGroup.MinSelect == 0 {
			comboGroup.MinSelect = 1
		}
		if comboGroup.MaxSelect == 0 {
			comboGroup.MaxSelect = 1
		}

		// 构建套餐选项
		for _, value := range values {
			option := ComboOption{
				GoodsID:     0, // 默认为0
				Name:        value.Name,
				Image:       value.Config.Image,
				AddPrice:    value.Config.AddPrice,
				Quantity:    value.Config.Quantity,
				IsDefault:   value.Config.IsDefault,
				Description: value.Config.Description,
			}

			// 如果配置中有商品ID，使用它
			if value.Config.OptionGoodsID != nil {
				option.GoodsID = *value.Config.OptionGoodsID
			}

			// 如果数量为0，设置默认值1
			if option.Quantity == 0 {
				option.Quantity = 1
			}

			comboGroup.Options = append(comboGroup.Options, option)
		}

		// 按sort排序选项
		sort.Slice(comboGroup.Options, func(i, j int) bool {
			return values[i].Sort < values[j].Sort
		})

		comboData.ComboGroups = append(comboData.ComboGroups, comboGroup)
	}

	return comboData, nil
}

// GetSpecInfo 获取规格信息（兼容方法，用于前端）
func (s *MultiSpecService) GetSpecInfo(goodsId int64) (vo.SpecInfo, error) {
	allData, err := s.LoadAllSpecData(goodsId)
	if err != nil {
		return vo.SpecInfo{}, err
	}

	// 根据规格类型返回相应的数据结构
	if allData.SingleSpecData != nil {
		// 单规格转换为SpecInfo格式
		return vo.SpecInfo{
			SpecGroups:           make([]vo.SpecGroup, 0),
			Skus:                 []vo.Sku{allData.SingleSpecData.Sku},
			SpecValueSelectable:  make(map[string]bool),
			SpecCombinationToSku: make(map[string]string),
			DefaultSelected:      make(map[string]string),
		}, nil
	}

	if allData.MultiSpecData != nil {
		// 多规格直接返回
		return vo.SpecInfo{
			SpecGroups:           allData.MultiSpecData.SpecGroups,
			Skus:                 allData.MultiSpecData.Skus,
			SpecValueSelectable:  allData.MultiSpecData.SpecValueSelectable,
			SpecCombinationToSku: allData.MultiSpecData.SpecCombinationToSku,
			DefaultSelected:      allData.MultiSpecData.DefaultSelected,
		}, nil
	}

	if allData.ComboSpecData != nil {
		// 套餐规格不返回SpecInfo，让前端直接使用comboSpecData
		// 套餐商品的SKU信息应该从comboSpecData中获取，而不是通过specInfo
		return vo.SpecInfo{
			SpecGroups:           make([]vo.SpecGroup, 0),
			Skus:                 make([]vo.Sku, 0),
			SpecValueSelectable:  make(map[string]bool),
			SpecCombinationToSku: make(map[string]string),
			DefaultSelected:      make(map[string]string),
		}, nil
	}

	// 默认返回空的SpecInfo
	return vo.SpecInfo{
		SpecGroups:           make([]vo.SpecGroup, 0),
		Skus:                 make([]vo.Sku, 0),
		SpecValueSelectable:  make(map[string]bool),
		SpecCombinationToSku: make(map[string]string),
		DefaultSelected:      make(map[string]string),
	}, nil
}
