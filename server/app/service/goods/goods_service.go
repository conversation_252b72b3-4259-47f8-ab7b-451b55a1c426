package goods

import (
	"errors"
	"fmt"
	"wnsys/shop/app/common/utils"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/model/vo"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// Service 商品服务
type Service struct{}

// NewService 创建商品服务
func NewService() *Service {
	return &Service{}
}

// GetDetail 获取商品详情
func (s *Service) GetDetail(id int64, userID int64) (*vo.GoodsDetailVO, error) {
	// 查询商品基本信息
	var goods ShopDB.GoodsDO
	if err := (&ShopDB.GoodsDO{}).Query().Where("id = ?", id).First(&goods).Error; err != nil {
		return nil, err
	}

	// 查询商品详情
	var goodsDetail ShopDB.GoodsDetailDO
	if err := (&ShopDB.GoodsDetailDO{}).Query().Where("goods_id = ?", id).First(&goodsDetail).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		// 如果没有找到详情记录，使用空字符串
		goodsDetail.DetailDesc = ""
	}

	// 查询SKU信息获取价格和库存
	var skuList []ShopDB.GoodsSkuDO
	if err := (&ShopDB.GoodsSkuDO{}).Query().
		Where("goods_id = ? AND status = 1", id).
		Find(&skuList).Error; err != nil {
		return nil, err
	}

	// 计算最低价格、原价和总库存
	var minPrice, minOriginalPrice float64
	var totalStock int
	if len(skuList) > 0 {
		minPrice = skuList[0].Price
		minOriginalPrice = skuList[0].OriginalPrice
		for _, sku := range skuList {
			if sku.Price < minPrice {
				minPrice = sku.Price
			}
			if sku.OriginalPrice < minOriginalPrice {
				minOriginalPrice = sku.OriginalPrice
			}
			totalStock += sku.Stock
		}
	}

	// 查询商品销量（从订单商品表统计）
	var totalSales int64
	salesErr := db.DB.Shop.Table("shop_order_goods og").
		Select("COALESCE(SUM(og.quantity), 0)").
		Joins("LEFT JOIN shop_order o ON og.order_id = o.id").
		Where("og.goods_id = ? AND og.is_delete = 0 AND o.status IN ('completed', 'unreceived')", id).
		Row().Scan(&totalSales)
	if salesErr != nil {
		fmt.Printf("Error querying sales for goods %d: %v\n", id, salesErr)
		totalSales = 0 // 如果查询失败，设为0
	}

	// 转换为VO
	detail := &vo.GoodsDetailVO{
		ID:            goods.ID,
		CategoryID:    goods.CategoryID,
		Name:          goods.Name,
		Description:   goods.Description,
		DetailDesc:    goodsDetail.DetailDesc,
		Price:         minPrice,
		OriginalPrice: minOriginalPrice,
		Stock:         totalStock,
		WarnStock:     0, // 库存预警值已移除，设为0
		Unit:          goods.Unit,
		Sales:         int(totalSales), // 使用真实销量
		VirtualSales:  0,               // 虚拟销量字段已移除，设为0
		TotalSales:    int(totalSales), // 总销量等于真实销量
		Status:        goods.Status,
		SpecType:      goods.SpecType,
		Images:        make([]string, 0),
		DetailImages:  make([]string, 0),
		Tags:          make([]string, 0),
		Services:      make([]string, 0),
	}

	// 查询商品图片
	var images []ShopDB.GoodsImageDO
	if err := (&ShopDB.GoodsImageDO{}).Query().
		Where("goods_id = ?", id).
		Order("sort ASC").
		Find(&images).Error; err != nil {
		return nil, err
	}

	// 处理图片数据
	for _, img := range images {
		switch img.Position {
		case 1: // 主图
			detail.Images = append(detail.Images, utils.BuildImageURL(img.URL))
		case 3: // 详情图片
			detail.DetailImages = append(detail.DetailImages, utils.BuildImageURL(img.URL))
		}
	}

	fmt.Printf("Found %d SKUs for goods %d\n", len(skuList), id)
	for i, sku := range skuList {
		fmt.Printf("SKU %d: code=%s, price=%.2f, stock=%d\n", i+1, sku.SkuCode, sku.Price, sku.Stock)
	}

	// 根据商品规格类型构建规格信息
	multiSpecService := &MultiSpecService{}

	// 获取兼容性的规格信息（为已有前端）
	specInfo, err := multiSpecService.GetSpecInfo(id)
	if err != nil {
		fmt.Printf("Error getting spec info: %v\n", err)
		// 如果获取规格信息失败，使用空的规格信息
		specInfo = vo.SpecInfo{
			SpecGroups:           make([]vo.SpecGroup, 0),
			Skus:                 make([]vo.Sku, 0),
			SpecValueSelectable:  make(map[string]bool),
			SpecCombinationToSku: make(map[string]string),
			DefaultSelected:      make(map[string]string),
		}
	}
	detail.SpecInfo = specInfo

	// 获取完整的规格数据（支持不同规格类型）
	allSpecData, err := multiSpecService.LoadAllSpecData(id)
	if err != nil {
		fmt.Printf("Error loading all spec data: %v\n", err)
	} else {
		// 根据规格类型设置对应的数据
		if allSpecData.SingleSpecData != nil {
			detail.SingleSpecData = &vo.SingleSpecData{
				Sku: allSpecData.SingleSpecData.Sku,
			}
		}
		if allSpecData.MultiSpecData != nil {
			detail.MultiSpecData = &vo.MultiSpecData{
				SpecGroups:           allSpecData.MultiSpecData.SpecGroups,
				Skus:                 allSpecData.MultiSpecData.Skus,
				SpecValueSelectable:  allSpecData.MultiSpecData.SpecValueSelectable,
				SpecCombinationToSku: allSpecData.MultiSpecData.SpecCombinationToSku,
				DefaultSelected:      allSpecData.MultiSpecData.DefaultSelected,
			}
		}
		if allSpecData.ComboSpecData != nil {
			// 转换ComboGroup结构
			comboGroups := make([]vo.ComboGroup, 0, len(allSpecData.ComboSpecData.ComboGroups))
			for _, group := range allSpecData.ComboSpecData.ComboGroups {
				voOptions := make([]vo.ComboOption, 0, len(group.Options))
				for _, option := range group.Options {
					voOptions = append(voOptions, vo.ComboOption{
						GoodsID:     option.GoodsID,
						Name:        option.Name,
						Image:       option.Image,
						AddPrice:    option.AddPrice,
						Quantity:    option.Quantity,
						IsDefault:   option.IsDefault,
						Description: option.Description,
					})
				}
				comboGroups = append(comboGroups, vo.ComboGroup{
					Name:        group.Name,
					Description: group.Description,
					MinSelect:   group.MinSelect,
					MaxSelect:   group.MaxSelect,
					IsRequired:  group.IsRequired,
					Options:     voOptions,
				})
			}
			detail.ComboSpecData = &vo.ComboSpecData{
				BaseSku:     allSpecData.ComboSpecData.BaseSku,
				ComboGroups: comboGroups,
			}
		}
	}

	fmt.Printf("Spec info: %d groups, %d skus, specType=%d\n", len(specInfo.SpecGroups), len(specInfo.Skus), goods.SpecType)

	// 查询是否已收藏
	if userID > 0 {
		var count int64
		err := (&ShopDB.CollectDO{}).Query().
			Where("user_id = ? AND target_id = ? AND target_type = ? AND action_type = ? AND is_delete = 0",
				userID, id, "goods", ShopDB.ActionTypeCollect).
			Count(&count).Error
		if err != nil {
			return nil, err
		}
		detail.IsCollected = count > 0
	}

	return detail, nil
}

// Collect 收藏/取消收藏商品
func (s *Service) Collect(userId, goodsId int64, collectType int) error {
	if collectType == 1 {
		// 收藏
		collect := &ShopDB.CollectDO{
			UserID:     userId,
			TargetID:   goodsId,
			TargetType: "goods",
			ActionType: ShopDB.ActionTypeCollect,
			IsDelete:   0,
		}
		return (&ShopDB.CollectDO{}).Query().Create(collect).Error
	} else {
		// 取消收藏
		return (&ShopDB.CollectDO{}).Query().
			Where("user_id = ? AND target_id = ? AND target_type = ? AND action_type = ?",
				userId, goodsId, "goods", ShopDB.ActionTypeCollect).
			Update("is_delete", 1).Error
	}
}

// Like 点赞/取消点赞商品
func (s *Service) Like(userId, goodsId int64, likeType int) error {
	if likeType == 1 {
		// 点赞
		like := &ShopDB.CollectDO{
			UserID:     userId,
			TargetID:   goodsId,
			TargetType: "goods",
			ActionType: ShopDB.ActionTypeLike,
			IsDelete:   0,
		}
		return (&ShopDB.CollectDO{}).Query().Create(like).Error
	} else {
		// 取消点赞
		return (&ShopDB.CollectDO{}).Query().
			Where("user_id = ? AND target_id = ? AND target_type = ? AND action_type = ?",
				userId, goodsId, "goods", ShopDB.ActionTypeLike).
			Update("is_delete", 1).Error
	}
}

// CheckSpecStock 检查商品规格库存
func (s *Service) CheckSpecStock(goodsID uint, specs map[string]string) (int, error) {
	// 查询商品的所有SKU
	var skus []ShopDB.GoodsSkuDO
	err := (&ShopDB.GoodsSkuDO{}).Query().
		Where("goods_id = ? AND status = 1", goodsID).
		Find(&skus).Error
	if err != nil {
		return 0, err
	}

	if len(skus) == 0 {
		return 0, nil
	}

	// 获取所有SKU的ID
	var skuIDs []int64
	for _, sku := range skus {
		skuIDs = append(skuIDs, sku.ID)
	}

	// 查询SKU规格关联信息
	var skuSpecInfos []struct {
		SkuID         int64  `gorm:"column:sku_id"`
		SpecGroupName string `gorm:"column:spec_group_name"`
		SpecValue     string `gorm:"column:spec_value"`
	}

	err = db.DB.Shop.Table("shop_goods_sku_spec ss").
		Select("ss.sku_id, sg.name as spec_group_name, sv.value as spec_value").
		Joins("LEFT JOIN shop_goods_spec_group sg ON ss.spec_group_id = sg.id").
		Joins("LEFT JOIN shop_goods_spec_value sv ON ss.spec_value_id = sv.id").
		Where("ss.sku_id IN (?)", skuIDs).
		Find(&skuSpecInfos).Error
	if err != nil {
		return 0, err
	}

	// 构建SKU ID到规格信息的映射
	skuSpecMap := make(map[int64]map[string]string)
	for _, specInfo := range skuSpecInfos {
		if skuSpecMap[specInfo.SkuID] == nil {
			skuSpecMap[specInfo.SkuID] = make(map[string]string)
		}
		skuSpecMap[specInfo.SkuID][specInfo.SpecGroupName] = specInfo.SpecValue
	}

	// 查找匹配的SKU
	for _, sku := range skus {
		skuSpecs := skuSpecMap[sku.ID]
		if skuSpecs == nil {
			continue
		}

		// 检查规格是否完全匹配
		match := true
		for specName, specValue := range specs {
			if skuSpecs[specName] != specValue {
				match = false
				break
			}
		}

		if match && len(specs) == len(skuSpecs) {
			return sku.Stock, nil
		}
	}

	return 0, nil
}

// GetGoodsSkuByCode 根据SKU编码获取商品SKU信息
func (s *Service) GetGoodsSkuByCode(skuCode string) (*ShopDB.GoodsSkuDO, error) {
	var sku ShopDB.GoodsSkuDO
	err := (&ShopDB.GoodsSkuDO{}).Query().
		Where("sku_code = ? AND status = 1", skuCode).
		First(&sku).Error
	if err != nil {
		return nil, err
	}
	return &sku, nil
}

// DeductStock 扣减商品库存
func (s *Service) DeductStock(goodsID int64, skuCode string, quantity int) error {
	// 更新SKU库存
	return (&ShopDB.GoodsSkuDO{}).Query().
		Where("goods_id = ? AND sku_code = ? AND status = 1", goodsID, skuCode).
		Updates(map[string]interface{}{
			"stock":       gorm.Expr("stock - ?", quantity),
			"update_time": gorm.Expr("NOW()"),
		}).Error
}

// AddFootprint 添加商品足迹
func (s *Service) AddFootprint(userId, goodsId int64) error {
	return db.DB.Shop.Transaction(func(tx *gorm.DB) error {
		// 检查是否已存在足迹记录
		var footprint ShopDB.GoodsFootprintDO
		err := tx.Where("user_id = ? AND goods_id = ? AND is_delete = 0", userId, goodsId).
			First(&footprint).Error

		if err == nil {
			// 已存在记录，更新时间
			return tx.Model(&footprint).
				Updates(map[string]interface{}{
					"update_time": gorm.Expr("NOW()"),
				}).Error
		}

		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}

		// 不存在记录，创建新记录
		footprint = ShopDB.GoodsFootprintDO{
			UserID:   userId,
			GoodsID:  goodsId,
			IsDelete: 0,
		}
		return tx.Create(&footprint).Error
	})
}

// DeductGoodsStock 扣减商品总库存（用于单规格商品）
func (s *Service) DeductGoodsStock(goodsID int64, quantity int) error {
	// 对于单规格商品，查找对应的SKU记录
	var sku ShopDB.GoodsSkuDO
	err := (&ShopDB.GoodsSkuDO{}).Query().
		Where("goods_id = ? AND status = 1", goodsID).
		First(&sku).Error
	if err != nil {
		return err
	}

	// 扣减SKU库存
	return (&ShopDB.GoodsSkuDO{}).Query().
		Where("id = ? AND stock >= ?", sku.ID, quantity).
		Updates(map[string]interface{}{
			"stock":       gorm.Expr("stock - ?", quantity),
			"update_time": gorm.Expr("NOW()"),
		}).Error
}

// GetGoodsList 获取商品列表
func (s *Service) GetGoodsList(query *dto.GoodsQuery, userID int64) (*dto.GoodsListResp, error) {
	var list []ShopDB.GoodsDO
	goodsDO := &ShopDB.GoodsDO{}

	db := goodsDO.Query().Where("status = ?", 1)

	// 分类筛选 - 修改判断逻辑
	if query.CategoryID > 0 {
		// 获取分类信息
		category := &ShopDB.CategoryDO{}
		err := category.Query().Where("id = ? AND module = ?", query.CategoryID, "goods").First(category).Error
		if err != nil {
			return nil, err
		}

		if category.Level == 1 {
			// 一级分类，查询所有子分类
			var subCategories []ShopDB.CategoryDO
			err = (&ShopDB.CategoryDO{}).Query().Where("parent_id = ? AND module = ?", category.ID, "goods").Find(&subCategories).Error
			if err != nil {
				return nil, err
			}

			// 构建分类ID列表
			categoryIds := make([]int64, 0, len(subCategories)+1)
			categoryIds = append(categoryIds, category.ID)
			for _, sub := range subCategories {
				categoryIds = append(categoryIds, sub.ID)
			}

			db = db.Where("category_id IN ?", categoryIds)
		} else {
			// 二级分类直接查询
			db = db.Where("category_id = ?", query.CategoryID)
		}
	}

	// 标签筛选
	if query.TagID > 0 {
		db = db.Joins("INNER JOIN shop_goods_tag_relation ON shop_goods.id = shop_goods_tag_relation.goods_id").
			Where("shop_goods_tag_relation.tag_id = ?", query.TagID)
	}

	// 关键词搜索
	if query.Keyword != "" {
		db = db.Where("name LIKE ?", "%"+query.Keyword+"%")
	}

	// 价格区间筛选需要通过SKU表
	if query.PriceMin > 0 || query.PriceMax > 0 {
		subQuery := (&ShopDB.GoodsSkuDO{}).Query().Select("goods_id")
		if query.PriceMin > 0 {
			subQuery = subQuery.Where("price >= ?", query.PriceMin)
		}
		if query.PriceMax > 0 {
			subQuery = subQuery.Where("price <= ?", query.PriceMax)
		}
		db = db.Where("id IN (?)", subQuery)
	}

	// 先查询总数（在JOIN之前）
	var total int64
	err := db.Count(&total).Error
	if err != nil {
		return nil, err
	}

	// 排序
	switch query.Sort {
	case "price", "price_asc":
		// 按最低价格排序
		db = db.Joins("LEFT JOIN (SELECT goods_id, MIN(price) as min_price FROM shop_goods_sku WHERE status = 1 GROUP BY goods_id) sku ON shop_goods.id = sku.goods_id").
			Order("sku.min_price ASC")
	case "price-desc", "price_desc":
		// 按最高价格排序
		db = db.Joins("LEFT JOIN (SELECT goods_id, MAX(price) as max_price FROM shop_goods_sku WHERE status = 1 GROUP BY goods_id) sku ON shop_goods.id = sku.goods_id").
			Order("sku.max_price DESC")
	case "sales":
		// 按销量排序 - 通过订单商品表统计销量
		db = db.Joins("LEFT JOIN (SELECT goods_id, SUM(quantity) as total_sales FROM shop_order_goods WHERE is_delete = 0 GROUP BY goods_id) sales ON shop_goods.id = sales.goods_id").
			Order("COALESCE(sales.total_sales, 0) DESC")
	default:
		db = db.Order("shop_goods.sort DESC, shop_goods.id DESC")
	}

	// 分页查询商品基本信息，包含规格类型
	// 注意：这里不能重新构建SELECT，因为会丢失之前的JOIN和ORDER BY
	err = db.Offset((query.Page - 1) * query.PageSize).
		Limit(query.PageSize).
		Find(&list).Error
	if err != nil {
		return nil, err
	}

	// 获取商品ID列表
	var goodsIDs []int64
	for _, v := range list {
		goodsIDs = append(goodsIDs, v.ID)
	}

	if len(goodsIDs) == 0 {
		return &dto.GoodsListResp{
			Total: total,
			List:  make([]dto.GoodsResp, 0),
		}, nil
	}

	// 查询商品SKU信息（获取最低价格和库存）
	var skuList []struct {
		GoodsID       int64   `gorm:"column:goods_id"`
		MinPrice      float64 `gorm:"column:min_price"`
		OriginalPrice float64 `gorm:"column:original_price"`
		TotalStock    int     `gorm:"column:total_stock"`
	}
	err = (&ShopDB.GoodsSkuDO{}).Query().
		Select("goods_id, MIN(price) as min_price, MIN(original_price) as original_price, SUM(stock) as total_stock").
		Where("goods_id IN (?) AND status = ?", goodsIDs, 1).
		Group("goods_id").
		Find(&skuList).Error
	if err != nil {
		return nil, err
	}

	// 构建商品ID到SKU信息的映射
	skuMap := make(map[int64]struct {
		MinPrice      float64
		OriginalPrice float64
		TotalStock    int
	})
	for _, sku := range skuList {
		skuMap[sku.GoodsID] = struct {
			MinPrice      float64
			OriginalPrice float64
			TotalStock    int
		}{
			MinPrice:      sku.MinPrice,
			OriginalPrice: sku.OriginalPrice,
			TotalStock:    sku.TotalStock,
		}
	}

	// 查询商品主图
	var images []struct {
		GoodsID int64  `gorm:"column:goods_id"`
		URL     string `gorm:"column:url"`
	}
	err = (&ShopDB.GoodsImageDO{}).Query().
		Select("goods_id, url").
		Where("goods_id IN (?) AND position = ?", goodsIDs, 1).
		Find(&images).Error
	if err != nil {
		return nil, err
	}

	// 构建商品ID到主图的映射
	imageMap := make(map[int64]string)
	for _, img := range images {
		imageMap[img.GoodsID] = utils.BuildImageURL(img.URL)
	}

	// 查询商品标签
	var tags []struct {
		GoodsID int64  `gorm:"column:goods_id"`
		Name    string `gorm:"column:tag_name"`
	}
	err = (&ShopDB.GoodsDO{}).Query().Raw(`
		SELECT goods_id, tag_name 
		FROM shop_goods_tag_relation 
		WHERE goods_id IN (?)
	`, goodsIDs).Scan(&tags).Error
	if err != nil {
		// 如果查询失败，记录错误但不中断流程
		tags = []struct {
			GoodsID int64  `gorm:"column:goods_id"`
			Name    string `gorm:"column:tag_name"`
		}{}
	}

	// 构建商品ID到标签的映射
	tagMap := make(map[int64][]string)
	for _, tag := range tags {
		tagMap[tag.GoodsID] = append(tagMap[tag.GoodsID], tag.Name)
	}

	// 查询用户收藏和点赞状态（如果用户已登录）
	collectMap := make(map[int64]bool)
	likeMap := make(map[int64]bool)
	if userID > 0 {
		// 查询收藏状态
		var collects []struct {
			TargetID int64 `gorm:"column:target_id"`
		}
		err = (&ShopDB.CollectDO{}).Query().
			Select("target_id").
			Where("user_id = ? AND target_id IN ? AND target_type = ? AND action_type = ? AND is_delete = 0",
				userID, goodsIDs, "goods", ShopDB.ActionTypeCollect).
			Find(&collects).Error
		if err != nil {
			return nil, err
		}
		for _, collect := range collects {
			collectMap[collect.TargetID] = true
		}

		// 查询点赞状态
		var likes []struct {
			TargetID int64 `gorm:"column:target_id"`
		}
		err = (&ShopDB.CollectDO{}).Query().
			Select("target_id").
			Where("user_id = ? AND target_id IN ? AND target_type = ? AND action_type = ? AND is_delete = 0",
				userID, goodsIDs, "goods", ShopDB.ActionTypeLike).
			Find(&likes).Error
		if err != nil {
			return nil, err
		}
		for _, like := range likes {
			likeMap[like.TargetID] = true
		}
	}

	// 查询商品点赞数量
	var likeCounts []struct {
		TargetID int64 `gorm:"column:target_id"`
		Count    int   `gorm:"column:count"`
	}
	err = (&ShopDB.CollectDO{}).Query().
		Select("target_id, COUNT(*) as count").
		Where("target_id IN ? AND target_type = ? AND action_type = ? AND is_delete = 0",
			goodsIDs, "goods", ShopDB.ActionTypeLike).
		Group("target_id").
		Find(&likeCounts).Error
	if err != nil {
		return nil, err
	}

	// 构建商品ID到点赞数量的映射
	likeCountMap := make(map[int64]int)
	for _, likeCount := range likeCounts {
		likeCountMap[likeCount.TargetID] = likeCount.Count
	}

	result := &dto.GoodsListResp{
		Total: total,
		List:  make([]dto.GoodsResp, 0, len(list)),
	}

	for _, v := range list {
		skuInfo := skuMap[v.ID]
		result.List = append(result.List, dto.GoodsResp{
			ID:            v.ID,
			Name:          v.Name,
			Description:   v.Description,
			Price:         fmt.Sprintf("%.2f", skuInfo.MinPrice),
			OriginalPrice: fmt.Sprintf("%.2f", skuInfo.OriginalPrice),
			Image:         imageMap[v.ID],
			Sales:         0, // 销量字段已移除，暂时设为0
			Stock:         skuInfo.TotalStock,
			SpecType:      v.SpecType,
			IsCollected:   collectMap[v.ID],
			IsLiked:       likeMap[v.ID],
			Likes:         likeCountMap[v.ID],
			Tags:          tagMap[v.ID],
		})
	}

	return result, nil
}
