package beauty

import (
	"encoding/json"
	"fmt"
	"time"
	"wnsys/shop/app/common/utils"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/do/BeautyDB"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/provider/db"
	"wnsys/shop/app/service/payment"

	"gorm.io/gorm"
)

// BookingService 预约服务层
type BookingService struct {
	db *gorm.DB
}

// NewBookingService 创建预约服务实例
func NewBookingService() *BookingService {
	return &BookingService{
		db: db.DB.Shop,
	}
}

// CreateBooking 创建预约
func (s *BookingService) CreateBooking(userID int64, req *dto.BeautyBookingCreateRequest) (*dto.BeautyBookingCreateResponse, error) {
	fmt.Printf("开始创建预约 - 用户ID: %d, 请求数据: %+v\n", userID, req)

	// 1. 验证服务是否存在
	var service BeautyDB.BeautyServiceDO
	if err := s.db.Where("id = ? AND is_delete = 0 AND status = 1", req.ServiceID).First(&service).Error; err != nil {
		fmt.Printf("查询服务失败 - 服务ID: %d, 错误: %v\n", req.ServiceID, err)
		if err == gorm.ErrRecordNotFound {
			return nil, werror.New(404, "服务不存在")
		}
		return nil, werror.New(500, "查询服务失败")
	}
	fmt.Printf("找到服务: %s, 需要技师: %d\n", service.Name, service.NeedTechnician)

	// 2. 验证技师是否存在（如果需要技师）
	var technician BeautyDB.BeautyTechnicianDO
	if service.NeedTechnician == 1 {
		if req.TechnicianUserID == 0 {
			fmt.Printf("该服务需要选择技师，但未提供技师ID\n")
			return nil, werror.New(400, "该服务需要选择技师")
		}
		if err := s.db.Where("user_id = ? AND is_delete = 0 AND status = 1", req.TechnicianUserID).First(&technician).Error; err != nil {
			fmt.Printf("查询技师失败 - 技师ID: %d, 错误: %v\n", req.TechnicianUserID, err)
			if err == gorm.ErrRecordNotFound {
				return nil, werror.New(404, "技师不存在")
			}
			return nil, werror.New(500, "查询技师失败")
		}
		fmt.Printf("找到技师: %s, 额外费用: %.2f\n", technician.Name, technician.ExtraFee)
	}

	// 3. 解析预约时间
	bookingDate, err := time.Parse("2006-01-02", req.BookingDate)
	if err != nil {
		fmt.Printf("预约日期格式错误 - 日期: %s, 错误: %v\n", req.BookingDate, err)
		return nil, werror.New(400, "预约日期格式错误")
	}

	startTime, err := time.Parse("15:04", req.StartTime)
	if err != nil {
		fmt.Printf("预约时间格式错误 - 时间: %s, 错误: %v\n", req.StartTime, err)
		return nil, werror.New(400, "预约时间格式错误")
	}

	fmt.Printf("解析时间成功 - 日期: %s, 时间: %s\n", req.BookingDate, req.StartTime)

	// 4. 计算结束时间
	endTime := startTime.Add(time.Duration(service.Duration) * time.Minute)

	// 5. 检查时间冲突
	if err := s.checkTimeConflict(int64(req.TechnicianUserID), bookingDate, req.StartTime, endTime.Format("15:04")); err != nil {
		fmt.Printf("时间冲突检查失败: %v\n", err)
		return nil, err
	}
	fmt.Printf("时间冲突检查通过\n")

	// 6. 计算价格
	finalPrice := service.Price
	if service.NeedTechnician == 1 && technician.ID > 0 {
		finalPrice += technician.ExtraFee
	}

	// 处理优惠券
	couponAmount := 0.0
	if req.CouponID > 0 {
		// TODO: 实现优惠券逻辑
	}

	finalPrice -= couponAmount
	fmt.Printf("价格计算完成 - 服务价格: %.2f, 技师费用: %.2f, 最终价格: %.2f\n", service.Price, technician.ExtraFee, finalPrice)

	// 7. 创建预约记录（使用统一订单表）
	// 开始数据库事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 创建统一订单
	orderNo := fmt.Sprintf("BOOK%d", time.Now().Unix())
	order := ShopDB.OrderDO{
		OrderNo:        orderNo,
		BusinessType:   "booking",
		UserID:         userID,
		TotalAmount:    finalPrice,
		PayAmount:      finalPrice,
		FreightAmount:  0,
		DiscountAmount: 0,
		Status:         "unpaid",
		IsDelete:       0,
		CreateTime:     time.Now(),
		UpdateTime:     time.Now(),
	}

	err = tx.Create(&order).Error
	if err != nil {
		tx.Rollback()
		return nil, werror.New(500, "创建订单失败: "+err.Error())
	}

	// 2. 创建预约记录（作为订单详情）
	booking := &BeautyDB.BeautyBookingDO{
		OrderID:          &order.ID, // 关联订单ID
		BookingNo:        utils.GenerateOrderNo(),
		UserID:           userID,
		ServiceID:        int64(req.ServiceID),
		TechnicianUserID: int64(req.TechnicianUserID),
		BookingDate:      bookingDate,
		StartTime:        req.StartTime,
		EndTime:          endTime.Format("15:04"),
		Duration:         service.Duration,

		// 客户信息
		ContactName:    req.ContactName,
		ContactPhone:   req.ContactPhone,
		CustomerGender: req.CustomerGender,
		CustomerAge:    req.CustomerAge,

		// 个性化需求 - 设置默认值
		SkinType:        req.SkinType,
		Allergies:       req.Allergies,
		SpecialRequests: req.SpecialRequests,

		// 价格信息
		ServicePrice:   service.Price,
		TechnicianFee:  technician.ExtraFee,
		DiscountAmount: 0.0, // 默认折扣为0
		CouponID:       int64(req.CouponID),
		CouponAmount:   couponAmount,
		FinalPrice:     finalPrice,

		// 状态
		BookingStatus: "pending",
		PaymentStatus: "unpaid",
		ServiceStatus: "waiting",
	}

	// 处理皮肤问题数组
	if len(req.SkinConcerns) > 0 {
		skinConcernsJSON, _ := json.Marshal(req.SkinConcerns)
		booking.SkinConcerns = string(skinConcernsJSON)
	} else {
		// 如果没有皮肤问题，设置为空数组的JSON字符串
		booking.SkinConcerns = "[]"
	}

	// 处理评价图片字段，插入时必须为合法JSON
	booking.ReviewImages = "[]" // 设置为空数组的JSON字符串

	fmt.Printf("创建预约对象成功 - 预约号: %s\n", booking.BookingNo)

	// 保存预约到数据库
	if err := tx.Create(booking).Error; err != nil {
		tx.Rollback()
		fmt.Printf("保存预约到数据库失败: %v\n", err)
		return nil, werror.New(500, "创建预约失败")
	}

	// 3. 更新订单的业务ID
	err = tx.Model(&order).Update("business_id", booking.ID).Error
	if err != nil {
		tx.Rollback()
		return nil, werror.New(500, "更新订单业务ID失败: "+err.Error())
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, werror.New(500, "提交事务失败: "+err.Error())
	}

	fmt.Printf("预约创建成功 - ID: %d, 预约号: %s, 订单ID: %d\n", booking.ID, booking.BookingNo, order.ID)

	return &dto.BeautyBookingCreateResponse{
		ID:           uint(booking.ID),
		BookingNo:    booking.BookingNo,
		ServicePrice: booking.ServicePrice,
		FinalPrice:   booking.FinalPrice,
		BookingDate:  booking.BookingDate.Format("2006-01-02"),
		StartTime:    booking.StartTime,
		EndTime:      booking.EndTime,
		Status:       booking.BookingStatus,
		OrderID:      order.ID, // 返回统一订单ID
		OrderNo:      orderNo,  // 返回统一订单号
	}, nil
}

// GetBookingList 获取预约列表
func (s *BookingService) GetBookingList(userID int64, req *dto.BeautyBookingListRequest) (*dto.BeautyBookingListResponse, error) {
	var bookings []BeautyDB.BeautyBookingDO
	var total int64

	query := s.db.Model(&BeautyDB.BeautyBookingDO{}).Where("user_id = ? AND is_delete = 0", userID)

	// 状态筛选
	if req.Status != "" {
		query = query.Where("booking_status = ?", req.Status)
	}

	// 日期筛选
	if req.DateFrom != "" {
		query = query.Where("booking_date >= ?", req.DateFrom)
	}
	if req.DateTo != "" {
		query = query.Where("booking_date <= ?", req.DateTo)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, werror.New(500, "获取预约列表失败")
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("create_time DESC").Offset(offset).Limit(req.PageSize).Find(&bookings).Error; err != nil {
		return nil, werror.New(500, "获取预约列表失败")
	}

	// 转换为响应格式
	items := make([]dto.BeautyBookingItem, 0, len(bookings))
	for _, booking := range bookings {
		item, err := s.convertToBookingItem(&booking)
		if err != nil {
			continue
		}
		items = append(items, *item)
	}

	return &dto.BeautyBookingListResponse{
		List:  items,
		Total: total,
		Page:  req.Page,
		Size:  req.PageSize,
	}, nil
}

// GetBookingDetail 获取预约详情
func (s *BookingService) GetBookingDetail(userID int64, bookingID int64) (*dto.BeautyBookingDetailResponse, error) {
	var booking BeautyDB.BeautyBookingDO

	if err := s.db.Where("id = ? AND user_id = ? AND is_delete = 0", bookingID, userID).First(&booking).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, werror.New(404, "预约不存在")
		}
		return nil, werror.New(500, "获取预约详情失败")
	}

	return s.convertToBookingDetail(&booking)
}

// CancelBooking 取消预约
func (s *BookingService) CancelBooking(userID int64, req *dto.BeautyBookingCancelRequest) error {
	var booking BeautyDB.BeautyBookingDO
	if err := s.db.Where("id = ? AND user_id = ? AND is_delete = 0", req.ID, userID).First(&booking).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return werror.New(404, "预约不存在")
		}
		return werror.New(500, "查询预约失败")
	}

	// 检查预约状态
	if booking.BookingStatus == "cancelled" {
		return werror.New(400, "预约已取消")
	}

	if booking.BookingStatus == "completed" {
		return werror.New(400, "预约已完成，无法取消")
	}

	// 更新预约状态
	if err := s.db.Model(&booking).Update("booking_status", "cancelled").Error; err != nil {
		return werror.New(500, "取消预约失败")
	}

	return nil
}

// GetBookingStats 获取预约统计
func (s *BookingService) GetBookingStats(userID int64) (*dto.BeautyBookingStatsResponse, error) {
	var stats dto.BeautyBookingStatsResponse

	// 统计各状态的预约数量
	var pendingCount int64
	var confirmedCount int64
	var inServiceCount int64
	var completedCount int64

	// 待确认
	if err := s.db.Model(&BeautyDB.BeautyBookingDO{}).
		Where("user_id = ? AND booking_status = 'pending' AND is_delete = 0", userID).
		Count(&pendingCount).Error; err != nil {
		return nil, werror.New(500, "统计待确认预约失败")
	}

	// 已确认
	if err := s.db.Model(&BeautyDB.BeautyBookingDO{}).
		Where("user_id = ? AND booking_status = 'confirmed' AND is_delete = 0", userID).
		Count(&confirmedCount).Error; err != nil {
		return nil, werror.New(500, "统计已确认预约失败")
	}

	// 服务中
	if err := s.db.Model(&BeautyDB.BeautyBookingDO{}).
		Where("user_id = ? AND booking_status = 'in_service' AND is_delete = 0", userID).
		Count(&inServiceCount).Error; err != nil {
		return nil, werror.New(500, "统计服务中预约失败")
	}

	// 已完成
	if err := s.db.Model(&BeautyDB.BeautyBookingDO{}).
		Where("user_id = ? AND booking_status = 'completed' AND is_delete = 0", userID).
		Count(&completedCount).Error; err != nil {
		return nil, werror.New(500, "统计已完成预约失败")
	}

	stats.Pending = int(pendingCount)
	stats.Confirmed = int(confirmedCount)
	stats.InService = int(inServiceCount)
	stats.Completed = int(completedCount)

	return &stats, nil
}

// GetTechnicianTodayBookings 获取技师今日预约列表
func (s *BookingService) GetTechnicianTodayBookings(userID int64) ([]dto.BeautyBookingItem, error) {
	today := time.Now().Format("2006-01-02")
	var bookings []BeautyDB.BeautyBookingDO
	err := s.db.Where("technician_user_id = ? AND booking_date = ? AND is_delete = 0", userID, today).Order("start_time").Find(&bookings).Error
	if err != nil {
		return nil, werror.New(500, "查询今日预约失败")
	}
	var result []dto.BeautyBookingItem
	for _, b := range bookings {
		item := dto.BeautyBookingItem{
			ID:             uint(b.ID),
			BookingNo:      b.BookingNo,
			ServiceID:      uint(b.ServiceID), // 添加服务ID
			ServiceName:    "",                // 可补充服务名
			TechnicianName: "",                // 可补充技师名
			BookingDate:    b.BookingDate.Format("2006-01-02"),
			StartTime:      b.StartTime,
			EndTime:        b.EndTime,
			Duration:       b.Duration,
			FinalPrice:     b.FinalPrice,
			BookingStatus:  b.BookingStatus,
			PaymentStatus:  b.PaymentStatus,
			ServiceStatus:  b.ServiceStatus,
			CreateTime:     b.CreateTime.Format("2006-01-02 15:04:05"),
		}
		result = append(result, item)
	}
	return result, nil
}

// GetTechnicianTodayStats 获取技师今日统计
func (s *BookingService) GetTechnicianTodayStats(userID int64) (map[string]interface{}, error) {
	today := time.Now().Format("2006-01-02")
	var expectedIncome float64
	var count, completedCount int64
	s.db.Model(&BeautyDB.BeautyBookingDO{}).Where("technician_user_id = ? AND booking_date = ? AND is_delete = 0", userID, today).Count(&count)
	s.db.Model(&BeautyDB.BeautyBookingDO{}).Where("technician_user_id = ? AND booking_date = ? AND booking_status = ? AND is_delete = 0", userID, today, "completed").Count(&completedCount)
	s.db.Model(&BeautyDB.BeautyBookingDO{}).Where("technician_user_id = ? AND booking_date = ? AND is_delete = 0", userID, today).Select("SUM(final_price)").Row().Scan(&expectedIncome)
	return map[string]interface{}{
		"bookingCount":   count,
		"completedCount": completedCount,
		"expectedIncome": expectedIncome,
		"satisfaction":   98, // 可后续完善
	}, nil
}

// GetTechnicianDateBookings 获取技师指定日期预约列表
func (s *BookingService) GetTechnicianDateBookings(userID int64, date string) ([]dto.BeautyBookingItem, error) {
	var bookings []BeautyDB.BeautyBookingDO
	err := s.db.Where("technician_user_id = ? AND booking_date = ? AND is_delete = 0", userID, date).Order("start_time").Find(&bookings).Error
	if err != nil {
		return nil, werror.New(500, "查询指定日期预约失败")
	}
	var result []dto.BeautyBookingItem
	for _, b := range bookings {
		item, _ := s.convertToBookingItem(&b)
		result = append(result, *item)
	}
	return result, nil
}

// GetTechnicianDateStats 获取技师指定日期统计
func (s *BookingService) GetTechnicianDateStats(userID int64, date string) (map[string]interface{}, error) {
	var expectedIncome float64
	var count, completedCount int64
	s.db.Model(&BeautyDB.BeautyBookingDO{}).Where("technician_user_id = ? AND booking_date = ? AND is_delete = 0", userID, date).Count(&count)
	s.db.Model(&BeautyDB.BeautyBookingDO{}).Where("technician_user_id = ? AND booking_date = ? AND booking_status = ? AND is_delete = 0", userID, date, "completed").Count(&completedCount)
	s.db.Model(&BeautyDB.BeautyBookingDO{}).Where("technician_user_id = ? AND booking_date = ? AND is_delete = 0", userID, date).Select("SUM(final_price)").Row().Scan(&expectedIncome)
	return map[string]interface{}{
		"bookingCount":   count,
		"completedCount": completedCount,
		"expectedIncome": expectedIncome,
		"satisfaction":   98, // 可后续完善
	}, nil
}

// GetTechnicianBookingList 获取技师全部预约列表
func (s *BookingService) GetTechnicianBookingList(userID int64, req *dto.TechnicianBookingListRequest) (*dto.TechnicianBookingListResponse, error) {
	fmt.Printf("[预约列表] userID=%v, req=%+v\n", userID, req)
	var bookings []BeautyDB.BeautyBookingDO
	var total int64

	query := s.db.Model(&BeautyDB.BeautyBookingDO{}).Where("technician_user_id = ? AND is_delete = 0", userID)
	if req.Status != "" {
		query = query.Where("booking_status = ?", req.Status)
	}
	if req.DateFrom != "" {
		query = query.Where("booking_date >= ?", req.DateFrom)
	}
	if req.DateTo != "" {
		query = query.Where("booking_date <= ?", req.DateTo)
	}
	// 打印SQL
	sql := query.Statement.SQL.String()
	fmt.Printf("[预约列表] SQL: %s\n", sql)
	err := query.Count(&total).
		Order("booking_date desc, start_time desc").
		Offset((req.Page - 1) * req.PageSize).
		Limit(req.PageSize).
		Find(&bookings).Error
	fmt.Printf("[预约列表] 查到数量: %d\n", len(bookings))
	if err != nil {
		return nil, werror.New(500, "查询预约列表失败")
	}

	var list []dto.BeautyBookingItem
	for _, b := range bookings {
		item, _ := s.convertToBookingItem(&b)
		list = append(list, *item)
	}
	// 保证list为数组
	if list == nil {
		list = []dto.BeautyBookingItem{}
	}
	return &dto.TechnicianBookingListResponse{
		List:  list,
		Total: total,
		Page:  req.Page,
		Size:  req.PageSize,
	}, nil
}

// checkTimeConflict 检查时间冲突
func (s *BookingService) checkTimeConflict(technicianUserID int64, bookingDate time.Time, startTime, endTime string) error {
	if technicianUserID == 0 {
		return nil // 不需要技师的服务不检查冲突
	}

	var count int64
	err := s.db.Model(&BeautyDB.BeautyBookingDO{}).
		Where("technician_user_id = ? AND booking_date = ? AND booking_status IN ('pending', 'confirmed') AND is_delete = 0", technicianUserID, bookingDate.Format("2006-01-02")).
		Where("(start_time < ? AND end_time > ?) OR (start_time < ? AND end_time > ?)", endTime, startTime, startTime, endTime).
		Count(&count).Error

	if err != nil {
		return werror.New(500, "检查时间冲突失败")
	}

	if count > 0 {
		return werror.New(400, "该时间段已被预约")
	}

	return nil
}

// convertToBookingItem 转换为预约项
func (s *BookingService) convertToBookingItem(booking *BeautyDB.BeautyBookingDO) (*dto.BeautyBookingItem, error) {
	// 获取服务信息
	var service BeautyDB.BeautyServiceDO
	s.db.Where("id = ?", booking.ServiceID).First(&service)

	// 获取技师信息
	var technician BeautyDB.BeautyTechnicianDO
	technicianName := ""
	if booking.TechnicianUserID > 0 {
		if err := s.db.Where("user_id = ?", booking.TechnicianUserID).First(&technician).Error; err == nil {
			technicianName = technician.Name
		}
	}

	// 解析服务图片
	var images []string
	if service.Images != "" {
		json.Unmarshal([]byte(service.Images), &images)
	}
	serviceImage := ""
	if len(images) > 0 {
		serviceImage = images[0]
	}

	// 判断操作权限
	canCancel := booking.BookingStatus == "pending" || booking.BookingStatus == "confirmed"
	canReschedule := booking.BookingStatus == "pending"

	return &dto.BeautyBookingItem{
		ID:             uint(booking.ID),
		BookingNo:      booking.BookingNo,
		ServiceID:      uint(booking.ServiceID), // 添加服务ID
		ServiceName:    service.Name,
		ServiceImage:   serviceImage,
		TechnicianName: technicianName,
		BookingDate:    booking.BookingDate.Format("2006-01-02"),
		StartTime:      booking.StartTime,
		EndTime:        booking.EndTime,
		Duration:       booking.Duration,
		FinalPrice:     booking.FinalPrice,
		BookingStatus:  booking.BookingStatus,
		PaymentStatus:  booking.PaymentStatus,
		ServiceStatus:  booking.ServiceStatus,
		CanCancel:      canCancel,
		CanReschedule:  canReschedule,
		CreateTime:     booking.CreateTime.Format("2006-01-02 15:04:05"),
	}, nil
}

// convertToBookingDetail 转换为预约详情
func (s *BookingService) convertToBookingDetail(booking *BeautyDB.BeautyBookingDO) (*dto.BeautyBookingDetailResponse, error) {
	// 获取服务信息
	var service BeautyDB.BeautyServiceDO
	if err := s.db.Where("id = ?", booking.ServiceID).First(&service).Error; err != nil {
		return nil, werror.New(500, "获取服务信息失败")
	}

	// 获取技师信息
	var technician BeautyDB.BeautyTechnicianDO
	technicianName := ""
	technicianLevel := ""
	technicianPhone := ""
	if booking.TechnicianUserID > 0 {
		if err := s.db.Where("user_id = ?", booking.TechnicianUserID).First(&technician).Error; err == nil {
			technicianName = technician.Name
			technicianLevel = technician.Level
		}

		// 查询用户信息获取手机号
		var user ShopDB.UserDO
		if err := s.db.Where("id = ?", booking.TechnicianUserID).First(&user).Error; err == nil {
			technicianPhone = user.Phone
		}
	}

	// 解析服务图片和标签
	var images []string
	var tags []string
	if service.Images != "" {
		json.Unmarshal([]byte(service.Images), &images)
	}
	if service.Tags != "" {
		json.Unmarshal([]byte(service.Tags), &tags)
	}
	serviceImage := ""
	if len(images) > 0 {
		serviceImage = images[0]
	}

	// 解析皮肤问题
	var skinConcerns []string
	if booking.SkinConcerns != "" {
		json.Unmarshal([]byte(booking.SkinConcerns), &skinConcerns)
	}

	// 判断操作权限
	canCancel := booking.BookingStatus == "pending" || booking.BookingStatus == "confirmed"
	canReschedule := booking.BookingStatus == "pending"
	canReview := booking.ServiceStatus == "completed" && booking.Rating == 0
	canPay := booking.PaymentStatus == "unpaid" && booking.BookingStatus != "cancelled"

	return &dto.BeautyBookingDetailResponse{
		ID:        uint(booking.ID),
		BookingNo: booking.BookingNo,

		// 服务信息
		ServiceID:    uint(booking.ServiceID),
		ServiceName:  service.Name,
		ServiceImage: serviceImage,
		ServiceDesc:  service.Description,
		Duration:     booking.Duration,
		Tags:         tags,

		// 技师信息
		TechnicianUserID: uint(booking.TechnicianUserID),
		TechnicianName:   technicianName,
		TechnicianLevel:  technicianLevel,
		TechnicianPhone:  technicianPhone,

		// 预约时间
		BookingDate: booking.BookingDate.Format("2006-01-02"),
		StartTime:   booking.StartTime,
		EndTime:     booking.EndTime,

		// 客户信息
		ContactName:    booking.ContactName,
		ContactPhone:   booking.ContactPhone,
		CustomerGender: booking.CustomerGender,
		CustomerAge:    booking.CustomerAge,

		// 个性化需求
		SkinType:        booking.SkinType,
		SkinConcerns:    skinConcerns,
		Allergies:       booking.Allergies,
		SpecialRequests: booking.SpecialRequests,

		// 价格信息
		ServicePrice:   booking.ServicePrice,
		TechnicianFee:  booking.TechnicianFee,
		DiscountAmount: booking.DiscountAmount,
		CouponAmount:   booking.CouponAmount,
		FinalPrice:     booking.FinalPrice,

		// 状态信息
		BookingStatus: booking.BookingStatus,
		PaymentStatus: booking.PaymentStatus,
		ServiceStatus: booking.ServiceStatus,

		// 时间记录
		ConfirmedTime:    booking.ConfirmedTime,
		CheckinTime:      booking.CheckinTime,
		ServiceStartTime: booking.ServiceStartTime,
		ServiceEndTime:   booking.ServiceEndTime,
		CancelledTime:    booking.CancelledTime,
		CancelReason:     booking.CancelReason,

		// 评价信息
		Rating:     booking.Rating,
		Review:     booking.Review,
		ReviewTime: booking.ReviewTime,

		// 操作权限
		CanCancel:     canCancel,
		CanReschedule: canReschedule,
		CanReview:     canReview,
		CanPay:        canPay,

		CreateTime: booking.CreateTime.Format("2006-01-02 15:04:05"),
	}, nil
}

// UpdateBookingStatus 更新预约状态
func (s *BookingService) UpdateBookingStatus(userID int64, req *dto.BeautyBookingUpdateStatusRequest) error {
	// 查询预约是否存在且属于该技师
	var booking BeautyDB.BeautyBookingDO
	if err := s.db.Where("id = ? AND technician_user_id = ? AND is_delete = 0", req.ID, userID).First(&booking).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return werror.New(404, "预约不存在")
		}
		return werror.New(500, "查询预约失败")
	}

	// 验证状态流转的合法性
	if !s.isValidStatusTransition(booking.BookingStatus, req.Status) {
		return werror.New(400, "状态流转不合法")
	}

	// 更新预约状态
	updates := map[string]interface{}{
		"booking_status": req.Status,
	}

	// 根据新状态设置相应的时间字段
	now := time.Now()
	switch req.Status {
	case "confirmed":
		updates["confirmed_time"] = &now
	case "in_service":
		updates["service_start_time"] = &now
	case "completed":
		updates["service_end_time"] = &now
	case "cancelled":
		updates["cancelled_time"] = &now
	}

	if err := s.db.Model(&booking).Updates(updates).Error; err != nil {
		return werror.New(500, "更新预约状态失败")
	}

	return nil
}

// isValidStatusTransition 验证状态流转是否合法
func (s *BookingService) isValidStatusTransition(currentStatus, newStatus string) bool {
	validTransitions := map[string][]string{
		"pending":    {"confirmed", "cancelled"},
		"confirmed":  {"in_service", "cancelled"},
		"in_service": {"completed", "cancelled"},
		"completed":  {},
		"cancelled":  {},
	}

	if allowedStatuses, exists := validTransitions[currentStatus]; exists {
		for _, status := range allowedStatuses {
			if status == newStatus {
				return true
			}
		}
	}
	return false
}

// PayBeautyBooking 美容预约支付
func (s *BookingService) PayBeautyBooking(userID int64, req *dto.BeautyBookingPayRequest) (*dto.BeautyBookingPayResponse, error) {
	// 查询预约是否存在且属于该用户
	var booking BeautyDB.BeautyBookingDO
	if err := s.db.Where("id = ? AND user_id = ? AND is_delete = 0", req.BookingID, userID).First(&booking).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, werror.New(404, "预约不存在")
		}
		return nil, werror.New(500, "查询预约失败")
	}

	// 检查预约状态 - 除了取消状态外，其他状态都可以支付
	if booking.BookingStatus == "cancelled" {
		return nil, werror.New(400, "已取消的预约不能支付")
	}

	// 检查支付状态
	if booking.PaymentStatus == "paid" {
		return nil, werror.New(400, "该预约已支付")
	}

	// 查询关联的统一订单
	var order ShopDB.OrderDO
	err := s.db.Where("id = ? AND user_id = ? AND business_type = 'booking'", booking.OrderID, userID).First(&order).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, werror.New(404, "关联订单不存在")
		}
		return nil, werror.New(500, "查询订单失败")
	}

	// 检查订单状态
	if order.Status != "unpaid" {
		return nil, werror.New(400, "订单状态异常，无法支付")
	}

	// 使用订单的支付金额
	payAmount := order.PayAmount

	// 调用支付服务
	paymentFactory := payment.NewPaymentFactory()
	paymentService := paymentFactory.GetPaymentService(req.PayType)
	if paymentService == nil {
		return nil, werror.New(400, "不支持的支付方式")
	}

	// 创建支付订单，使用统一订单的order_id和order_no
	payResult, err := paymentService.Pay(order.ID, order.OrderNo, payAmount, "booking")
	if err != nil {
		return nil, err
	}

	// 如果是余额支付，直接处理支付成功
	if req.PayType == 3 {
		// 构造回调数据
		notifyData := map[string]string{
			"user_id":    fmt.Sprintf("%d", booking.UserID),
			"booking_id": fmt.Sprintf("%d", booking.ID),
			"booking_no": booking.BookingNo,
			"order_id":   fmt.Sprintf("%d", order.ID),
			"order_no":   order.OrderNo,
			"amount":     fmt.Sprintf("%.2f", payAmount),
		}

		// 处理支付成功（跳过重复检查，避免重复支付错误）
		err = payment.HandlePaymentSuccessWithSkipCheck(order.OrderNo, int8(req.PayType), "", notifyData, "booking", nil)
		if err != nil {
			return nil, err
		}
	}

	return &dto.BeautyBookingPayResponse{
		Success: true,
		Data:    payResult,
		Message: "支付成功",
	}, nil
}
