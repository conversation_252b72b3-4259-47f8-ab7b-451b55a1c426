package beauty

import (
	"encoding/json"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/do/BeautyDB"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// ReviewService 评价服务层
type ReviewService struct {
	db *gorm.DB
}

// NewReviewService 创建评价服务实例
func NewReviewService() *ReviewService {
	return &ReviewService{
		db: db.DB.Shop,
	}
}

// GetTechnicianReviews 获取技师评价列表
func (s *ReviewService) GetTechnicianReviews(req *dto.BeautyTechnicianReviewListRequest) (*dto.BeautyTechnicianReviewListResponse, error) {
	var bookings []BeautyDB.BeautyBookingDO
	var total int64

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 查询已完成且有评价的预约
	query := s.db.Model(&BeautyDB.BeautyBookingDO{}).
		Where("technician_user_id = ? AND service_status = 'completed' AND rating > 0 AND review IS NOT NULL AND review != '' AND review_time IS NOT NULL", req.TechnicianUserID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, werror.New(500, "获取评价列表失败")
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("review_time DESC").Offset(offset).Limit(req.PageSize).Find(&bookings).Error; err != nil {
		return nil, werror.New(500, "获取评价列表失败")
	}

	// 转换为响应格式
	items := make([]dto.BeautyReviewItem, 0, len(bookings))
	for _, booking := range bookings {
		item, err := s.convertToReviewItem(&booking)
		if err != nil {
			continue
		}
		items = append(items, *item)
	}

	return &dto.BeautyTechnicianReviewListResponse{
		List:  items,
		Total: total,
		Page:  req.Page,
		Size:  req.PageSize,
	}, nil
}

// GetServiceReviews 获取服务评价列表
func (s *ReviewService) GetServiceReviews(req *dto.BeautyServiceReviewListRequest) (*dto.BeautyServiceReviewListResponse, error) {
	var bookings []BeautyDB.BeautyBookingDO
	var total int64

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 查询已完成且有评价的预约
	query := s.db.Model(&BeautyDB.BeautyBookingDO{}).
		Where("service_id = ? AND service_status = 'completed' AND rating > 0 AND review IS NOT NULL AND review != '' AND review_time IS NOT NULL", req.ServiceID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, werror.New(500, "获取评价列表失败")
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("review_time DESC").Offset(offset).Limit(req.PageSize).Find(&bookings).Error; err != nil {
		return nil, werror.New(500, "获取评价列表失败")
	}

	// 转换为响应格式
	items := make([]dto.BeautyReviewItem, 0, len(bookings))
	for _, booking := range bookings {
		item, err := s.convertToReviewItem(&booking)
		if err != nil {
			continue
		}
		items = append(items, *item)
	}

	return &dto.BeautyServiceReviewListResponse{
		List:  items,
		Total: total,
		Page:  req.Page,
		Size:  req.PageSize,
	}, nil
}

// GetTechnicianReviewStats 获取技师评价统计
func (s *ReviewService) GetTechnicianReviewStats(technicianUserID uint) (*dto.BeautyReviewStatsResponse, error) {
	var stats dto.BeautyReviewStatsResponse

	// 查询评价统计
	query := s.db.Model(&BeautyDB.BeautyBookingDO{}).
		Where("technician_user_id = ? AND service_status = 'completed' AND rating > 0", technicianUserID)

	// 总数和平均评分
	if err := query.Count(&stats.TotalCount).Error; err != nil {
		return nil, werror.New(500, "获取评价统计失败")
	}

	if stats.TotalCount > 0 {
		var avgRating float64
		if err := query.Select("AVG(rating)").Scan(&avgRating).Error; err != nil {
			return nil, werror.New(500, "获取评价统计失败")
		}
		stats.AverageRating = avgRating

		// 各星级评价数量
		for i := 1; i <= 5; i++ {
			var count int64
			countQuery := s.db.Model(&BeautyDB.BeautyBookingDO{}).
				Where("technician_user_id = ? AND service_status = 'completed' AND rating = ?", technicianUserID, i)
			if err := countQuery.Count(&count).Error; err != nil {
				return nil, werror.New(500, "获取评价统计失败")
			}
			switch i {
			case 1:
				stats.Rating1Count = count
			case 2:
				stats.Rating2Count = count
			case 3:
				stats.Rating3Count = count
			case 4:
				stats.Rating4Count = count
			case 5:
				stats.Rating5Count = count
			}
		}
	}

	return &stats, nil
}

// GetServiceReviewStats 获取服务评价统计
func (s *ReviewService) GetServiceReviewStats(serviceID uint) (*dto.BeautyReviewStatsResponse, error) {
	var stats dto.BeautyReviewStatsResponse

	// 查询评价统计
	query := s.db.Model(&BeautyDB.BeautyBookingDO{}).
		Where("service_id = ? AND service_status = 'completed' AND rating > 0", serviceID)

	// 总数和平均评分
	if err := query.Count(&stats.TotalCount).Error; err != nil {
		return nil, werror.New(500, "获取评价统计失败")
	}

	if stats.TotalCount > 0 {
		var avgRating float64
		if err := query.Select("AVG(rating)").Scan(&avgRating).Error; err != nil {
			return nil, werror.New(500, "获取评价统计失败")
		}
		stats.AverageRating = avgRating

		// 各星级评价数量
		for i := 1; i <= 5; i++ {
			var count int64
			countQuery := s.db.Model(&BeautyDB.BeautyBookingDO{}).
				Where("service_id = ? AND service_status = 'completed' AND rating = ?", serviceID, i)
			if err := countQuery.Count(&count).Error; err != nil {
				return nil, werror.New(500, "获取评价统计失败")
			}
			switch i {
			case 1:
				stats.Rating1Count = count
			case 2:
				stats.Rating2Count = count
			case 3:
				stats.Rating3Count = count
			case 4:
				stats.Rating4Count = count
			case 5:
				stats.Rating5Count = count
			}
		}
	}

	return &stats, nil
}

// convertToReviewItem 转换为评价项
func (s *ReviewService) convertToReviewItem(booking *BeautyDB.BeautyBookingDO) (*dto.BeautyReviewItem, error) {
	// 获取用户信息
	var user ShopDB.UserDO
	if err := s.db.First(&user, int64(booking.UserID)).Error; err != nil {
		return nil, err
	}

	// 获取服务信息
	var service BeautyDB.BeautyServiceDO
	if err := s.db.First(&service, booking.ServiceID).Error; err != nil {
		return nil, err
	}

	// 解析评价图片
	var reviewImages []string
	if booking.ReviewImages != "" {
		if err := json.Unmarshal([]byte(booking.ReviewImages), &reviewImages); err != nil {
			reviewImages = []string{}
		}
	}

	return &dto.BeautyReviewItem{
		ID:               uint(booking.ID),
		BookingID:        uint(booking.ID),
		BookingNo:        booking.BookingNo,
		UserID:           uint(booking.UserID),
		UserNickname:     user.Nickname,
		UserAvatar:       user.Avatar,
		ServiceID:        uint(booking.ServiceID),
		ServiceName:      service.Name,
		TechnicianUserID: uint(booking.TechnicianUserID),
		Rating:           booking.Rating,
		Review:           booking.Review,
		ReviewImages:     reviewImages,
		ReviewTime:       *booking.ReviewTime,
		BookingDate:      booking.BookingDate.Format("2006-01-02"),
	}, nil
}
