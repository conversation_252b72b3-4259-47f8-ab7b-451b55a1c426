package beauty

import (
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// NotificationService 通知服务层
type NotificationService struct {
	db *gorm.DB
}

// NewNotificationService 创建通知服务实例
func NewNotificationService() *NotificationService {
	return &NotificationService{
		db: db.DB.Shop,
	}
}

// GetNotificationList 获取通知列表
func (s *NotificationService) GetNotificationList(userID int64, req *dto.BeautyNotificationListRequest) (*dto.BeautyNotificationListResponse, error) {
	// TODO: 实现通知列表查询逻辑
	// 目前返回空数据，因为还没有通知相关的数据库表

	result := &dto.BeautyNotificationListResponse{
		List:  []dto.BeautyNotificationItem{},
		Total: 0,
		Page:  req.Page,
		Size:  req.PageSize,
	}

	return result, nil
}

// GetUnreadNotificationCount 获取未读通知数量
func (s *NotificationService) GetUnreadNotificationCount(userID int64) (*dto.BeautyNotificationUnreadCountResponse, error) {
	// TODO: 实现未读通知数量查询逻辑
	// 目前返回0，因为还没有通知相关的数据库表

	result := &dto.BeautyNotificationUnreadCountResponse{
		Count: 0,
	}

	return result, nil
}

// MarkNotificationRead 标记通知为已读
func (s *NotificationService) MarkNotificationRead(userID int64, req *dto.BeautyNotificationReadRequest) error {
	// TODO: 实现标记通知已读逻辑
	// 目前直接返回成功，因为还没有通知相关的数据库表

	return nil
}
