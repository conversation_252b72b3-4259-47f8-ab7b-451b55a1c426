package beauty

import (
	"encoding/json"
	"fmt"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/do/BeautyDB"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// ServiceService 美容服务服务层
type ServiceService struct {
	db *gorm.DB
}

// NewServiceService 创建服务实例
func NewServiceService() *ServiceService {
	return &ServiceService{
		db: db.DB.Shop,
	}
}

// GetServiceList 获取服务列表
func (s *ServiceService) GetServiceList(req *dto.BeautyServiceListRequest, userID int64) (*dto.BeautyServiceListResponse, error) {
	var services []BeautyDB.BeautyServiceDO
	var total int64

	query := s.db.Model(&BeautyDB.BeautyServiceDO{}).Where("is_delete = 0 AND status = 1")

	// 分类筛选
	if req.CategoryID > 0 {
		query = query.Where("category_id = ?", req.CategoryID)
	}

	// 关键词搜索
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		query = query.Where("name LIKE ? OR description LIKE ?", keyword, keyword)
	}

	// 热门/新品筛选
	if req.IsHot > 0 {
		query = query.Where("is_hot = 1")
	}
	if req.IsNew > 0 {
		query = query.Where("is_new = 1")
	}

	// 排序
	switch req.Sort {
	case "price_asc":
		query = query.Order("price ASC")
	case "price_desc":
		query = query.Order("price DESC")
	case "rating_desc":
		query = query.Order("rating_avg DESC")
	case "booking_desc":
		query = query.Order("booking_count DESC")
	default:
		query = query.Order("sort ASC, id DESC")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, werror.New(500, "获取服务列表失败")
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Find(&services).Error; err != nil {
		return nil, werror.New(500, "获取服务列表失败")
	}

	// 转换为响应格式
	items := make([]dto.BeautyServiceItem, 0, len(services))
	for _, service := range services {
		item, err := s.convertToServiceItem(&service, userID)
		if err != nil {
			continue
		}
		items = append(items, *item)
	}

	return &dto.BeautyServiceListResponse{
		List:  items,
		Total: total,
		Page:  req.Page,
		Size:  req.PageSize,
	}, nil
}

// GetServiceDetail 获取服务详情
func (s *ServiceService) GetServiceDetail(serviceID uint) (*dto.BeautyServiceDetailResponse, error) {
	var service BeautyDB.BeautyServiceDO

	if err := s.db.Where("id = ? AND is_delete = 0 AND status = 1", serviceID).First(&service).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, werror.New(404, "服务不存在")
		}
		return nil, werror.New(500, "获取服务详情失败")
	}

	// 转换为响应格式
	detail, err := s.convertToServiceDetail(&service)
	if err != nil {
		return nil, err
	}

	// 获取可用技师
	technicians, err := s.getAvailableTechnicians(serviceID)
	if err != nil {
		return nil, err
	}
	detail.AvailableTechnicians = technicians

	return detail, nil
}

// GetServiceCategories 获取美容服务分类列表
func (s *ServiceService) GetServiceCategories() ([]dto.CategoryItem, error) {
	var categories []ShopDB.CategoryDO

	// 查询美容服务相关的分类
	if err := s.db.Where("module = ? AND status = 1 AND is_delete = 0", "beauty").
		Order("sort ASC, id ASC").
		Find(&categories).Error; err != nil {
		return nil, werror.New(500, "获取分类列表失败")
	}

	items := make([]dto.CategoryItem, 0, len(categories))
	for _, category := range categories {
		items = append(items, dto.CategoryItem{
			ID:   uint(category.ID),
			Name: category.Name,
			Icon: category.Icon,
		})
	}

	return items, nil
}

// convertToServiceItem 转换为服务项
func (s *ServiceService) convertToServiceItem(service *BeautyDB.BeautyServiceDO, userID int64) (*dto.BeautyServiceItem, error) {
	// 解析图片数组
	var images []string
	if service.Images != "" {
		if err := json.Unmarshal([]byte(service.Images), &images); err != nil {
			images = []string{}
		}
	}

	// 解析标签数组
	var tags []string
	if service.Tags != "" {
		if err := json.Unmarshal([]byte(service.Tags), &tags); err != nil {
			tags = []string{}
		}
	}

	// 获取分类名称
	var category ShopDB.CategoryDO
	categoryName := ""
	if err := s.db.Where("id = ?", service.CategoryID).First(&category).Error; err == nil {
		categoryName = category.Name
	}

	// 查询收藏状态
	isCollected := false
	if userID > 0 {
		var count int64
		if err := s.db.Model(&ShopDB.CollectDO{}).
			Where("user_id = ? AND target_id = ? AND target_type = ? AND action_type = ? AND is_delete = 0",
				userID, service.ID, "beauty_service", 1).
			Count(&count).Error; err == nil && count > 0 {
			isCollected = true
		}
	}

	return &dto.BeautyServiceItem{
		ID:            service.ID,
		CategoryID:    service.CategoryID,
		CategoryName:  categoryName,
		Name:          service.Name,
		Subtitle:      service.Subtitle,
		Description:   service.Description,
		Images:        images,
		Price:         service.Price,
		OriginalPrice: service.OriginalPrice,
		Duration:      service.Duration,
		Tags:          tags,
		BookingCount:  service.BookingCount,
		RatingAvg:     service.RatingAvg,
		RatingCount:   service.RatingCount,
		IsHot:         service.IsHot == 1,
		IsNew:         service.IsNew == 1,
		IsCollected:   isCollected,
	}, nil
}

// convertToServiceDetail 转换为服务详情
func (s *ServiceService) convertToServiceDetail(service *BeautyDB.BeautyServiceDO) (*dto.BeautyServiceDetailResponse, error) {
	// 解析图片数组
	var images []string
	if service.Images != "" {
		if err := json.Unmarshal([]byte(service.Images), &images); err != nil {
			images = []string{}
		}
	}

	// 解析标签数组
	var tags []string
	if service.Tags != "" {
		if err := json.Unmarshal([]byte(service.Tags), &tags); err != nil {
			tags = []string{}
		}
	}

	// 获取分类名称
	var category ShopDB.CategoryDO
	categoryName := ""
	if err := s.db.Where("id = ?", service.CategoryID).First(&category).Error; err == nil {
		categoryName = category.Name
	}

	return &dto.BeautyServiceDetailResponse{
		ID:                service.ID,
		CategoryID:        service.CategoryID,
		CategoryName:      categoryName,
		Name:              service.Name,
		Subtitle:          service.Subtitle,
		Description:       service.Description,
		Images:            images,
		Price:             service.Price,
		OriginalPrice:     service.OriginalPrice,
		Duration:          service.Duration,
		NeedTechnician:    service.NeedTechnician == 1,
		MaxAdvanceDays:    service.MaxAdvanceDays,
		MinAdvanceHours:   service.MinAdvanceHours,
		AllowCancelHours:  service.AllowCancelHours,
		GenderLimit:       service.GenderLimit,
		AgeMin:            service.AgeMin,
		AgeMax:            service.AgeMax,
		Tags:              tags,
		Contraindications: service.Contraindications,
		PreparationNotes:  service.PreparationNotes,
		BookingCount:      service.BookingCount,
		RatingAvg:         service.RatingAvg,
		RatingCount:       service.RatingCount,
		IsHot:             service.IsHot == 1,
		IsNew:             service.IsNew == 1,
	}, nil
}

// getAvailableTechnicians 获取可用技师
func (s *ServiceService) getAvailableTechnicians(serviceID uint) ([]dto.TechnicianItem, error) {
	var technicians []BeautyDB.BeautyTechnicianDO

	// 查询能提供该服务的技师
	query := s.db.Model(&BeautyDB.BeautyTechnicianDO{}).Where("is_delete = 0 AND status = 1")
	query = query.Where("JSON_CONTAINS(service_ids, ?)", fmt.Sprintf(`%d`, serviceID))

	if err := query.Order("is_featured DESC, rating_avg DESC").Find(&technicians).Error; err != nil {
		return nil, werror.New(500, "获取技师列表失败")
	}

	items := make([]dto.TechnicianItem, 0, len(technicians))
	for _, tech := range technicians {
		item, err := s.convertToTechnicianItem(&tech)
		if err != nil {
			continue
		}
		items = append(items, *item)
	}

	return items, nil
}

// convertToTechnicianItem 转换为技师项
func (s *ServiceService) convertToTechnicianItem(tech *BeautyDB.BeautyTechnicianDO) (*dto.TechnicianItem, error) {
	// 解析专长数组
	var specialties []string
	if tech.Specialties != "" {
		if err := json.Unmarshal([]byte(tech.Specialties), &specialties); err != nil {
			specialties = []string{}
		}
	}

	return &dto.TechnicianItem{
		ID:           tech.ID,
		Name:         tech.Name,
		Level:        tech.Level,
		Experience:   tech.Experience,
		Specialties:  specialties,
		Introduction: tech.Introduction,
		RatingAvg:    tech.RatingAvg,
		RatingCount:  tech.RatingCount,
		ExtraFee:     tech.ExtraFee,
		IsFeatured:   tech.IsFeatured == 1,
	}, nil
}
