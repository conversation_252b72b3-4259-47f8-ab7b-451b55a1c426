package payment

import (
	"context"
	"fmt"
	"strings"
	"time"
	"wnsys/shop/app/common/werror"     // 导入错误处理
	"wnsys/shop/app/config"            // 导入配置
	"wnsys/shop/app/model/do"          // 导入套餐卡订单
	"wnsys/shop/app/model/do/BeautyDB" // 导入美容预约订单
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/provider/db"
	"wnsys/shop/app/provider/mylog" // 导入日志

	"github.com/wechatpay-apiv3/wechatpay-go/core" // 导入 validators
	// 导入 auth 包
	"github.com/wechatpay-apiv3/wechatpay-go/core/option" // 导入 payments 包
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
	// 导入验签器
)

// WechatPayService 微信支付服务
type WechatPayService struct {
	client    *core.Client
	appId     string // 从 config.App.Wx.MiniApp.AppID 获取
	mchId     string // 从 config.App.Wx.Pay.MchID 获取
	notifyUrl string // 从 config.App.Wx.Pay.NotifyUrl 获取
	apiV3Key  string // 从 config.App.Wx.Pay.ApiKey 获取
}

// NewWechatPayService 创建微信支付服务
func NewWechatPayService() PaymentService {
	cfg := config.App // 获取应用配置

	// 加载商户私钥
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath(cfg.Wx.Pay.PrivateKeyPath)
	if err != nil {
		mylog.GetDefaultLogger().Fatal("加载商户私钥失败: " + err.Error())
		return nil // 或者 panic
	}

	// 初始化 V3 客户端的 Context
	ctx := context.Background()

	// 使用 core.NewClient 初始化客户端
	// 使用 WithWechatPayAutoAuthCipher 一次性设置签名/验签/平台证书下载
	client, err := core.NewClient(ctx,
		option.WithWechatPayAutoAuthCipher(cfg.Wx.Pay.MchID, cfg.Wx.Pay.SerialNo, mchPrivateKey, cfg.Wx.Pay.ApiKey),
	)
	if err != nil {
		mylog.GetDefaultLogger().Fatal(fmt.Sprintf("初始化微信支付 V3 Client 失败: %s", err.Error()))
		return nil // 或者 panic
	}

	mylog.GetDefaultLogger().Info("微信支付服务初始化成功")

	return &WechatPayService{
		client:    client,
		appId:     cfg.Wx.MiniApp.AppID,
		mchId:     cfg.Wx.Pay.MchID,
		notifyUrl: cfg.Wx.Pay.NotifyUrl,
		apiV3Key:  cfg.Wx.Pay.ApiKey,
	}
}

// Pay 创建支付订单 (JSAPI)
func (s *WechatPayService) Pay(orderId int64, orderNo string, amount float64, businessType string) (map[string]interface{}, error) {
	logger := mylog.GetDefaultLogger()
	logger.Info(fmt.Sprintf("开始处理微信 JSAPI 支付，订单号: %s, 订单ID: %d, 金额: %.2f, 业务类型: %s", orderNo, orderId, amount, businessType))

	var userID int64
	var description string

	// 根据业务类型获取用户ID和设置商品描述
	switch businessType {
	case "booking":
		// 获取美容预约信息
		var booking BeautyDB.BeautyBookingDO
		err := db.DB.Shop.Select("user_id").Where("id = ?", orderId).First(&booking).Error
		if err != nil {
			logger.Error(fmt.Sprintf("无法获取预约 %d 信息: %v", orderId, err))
			return nil, werror.New(500, "获取预约信息失败", map[string]interface{}{"detail": "预约不存在"})
		}
		userID = booking.UserID
		description = fmt.Sprintf("美容服务-预约%s", orderNo)

	case "card":
		// 获取套餐卡信息
		var userCard do.UserCardDO
		err := db.DB.Shop.Select("user_id").Where("id = ?", orderId).First(&userCard).Error
		if err != nil {
			logger.Error(fmt.Sprintf("无法获取套餐卡 %d 信息: %v", orderId, err))
			return nil, werror.New(500, "获取套餐卡信息失败", map[string]interface{}{"detail": "套餐卡不存在"})
		}
		userID = userCard.UserID
		description = fmt.Sprintf("美容套餐卡-%s", orderNo)

	case "order":
		// 获取商品订单信息
		var order ShopDB.OrderDO
		err := db.DB.Shop.Select("user_id").Where("id = ?", orderId).First(&order).Error
		if err != nil {
			logger.Error(fmt.Sprintf("无法获取订单 %d 信息: %v", orderId, err))
			return nil, werror.New(500, "获取订单信息失败", map[string]interface{}{"detail": "订单不存在"})
		}
		userID = order.UserID
		description = fmt.Sprintf("商品订单-%s", orderNo)

	default:
		logger.Error(fmt.Sprintf("不支持的业务类型: %s", businessType))
		return nil, werror.New(400, "不支持的业务类型", map[string]interface{}{"businessType": businessType})
	}

	var user ShopDB.UserDO
	err := db.DB.Shop.Select("openid").Where("id = ?", userID).First(&user).Error
	if err != nil {
		logger.Error(fmt.Sprintf("无法获取用户 %d 信息: %v", userID, err))
		return nil, werror.New(500, "获取用户信息失败", map[string]interface{}{"detail": "用户不存在"})
	}

	// 检查用户是否有有效的OpenID
	if user.OpenID == nil || *user.OpenID == "" {
		logger.Error(fmt.Sprintf("用户 %d 没有有效的 OpenID，请先完成微信登录授权", userID))
		return nil, werror.New(400, "用户未完成微信登录授权", map[string]interface{}{
			"detail": "请先在小程序中完成微信登录，获取OpenID后再进行支付",
			"userId": userID,
		})
	}

	openid := *user.OpenID

	// 验证OpenID格式（微信OpenID通常是28位字符）
	if !s.isValidOpenID(openid) {
		logger.Error(fmt.Sprintf("用户 %d 的 OpenID 格式无效: %s", userID, openid))
		return nil, werror.New(400, "OpenID格式无效", map[string]interface{}{
			"detail": "请重新登录获取有效的OpenID",
			"userId": userID,
			"openid": openid,
		})
	}

	// 校验金额，微信支付要求最小金额为1分
	totalFee := int64(amount * 100)
	if totalFee < 1 {
		logger.Error(fmt.Sprintf("支付金额过小，订单号: %s, 金额: %.2f元, 转换后: %d分", orderNo, amount, totalFee))
		return nil, werror.New(400, "支付金额不能小于0.01元", map[string]interface{}{
			"amount":   amount,
			"totalFee": totalFee,
		})
	}

	// 2. 调用微信支付 V3 JSAPI 预下单接口 (使用 PrepayWithRequestPayment 获取前端参数)
	svc := jsapi.JsapiApiService{Client: s.client}
	ctx := context.Background()
	resp, result, err := svc.PrepayWithRequestPayment(ctx,
		jsapi.PrepayRequest{
			Appid:       core.String(s.appId),
			Mchid:       core.String(s.mchId),
			Description: core.String(description),
			OutTradeNo:  core.String(orderNo),
			NotifyUrl:   core.String(s.notifyUrl),
			Amount: &jsapi.Amount{
				Total:    core.Int64(totalFee),
				Currency: core.String("CNY"),
			},
			Payer: &jsapi.Payer{
				Openid: core.String(openid),
			},
			TimeExpire: core.Time(time.Now().Add(15 * time.Minute)),
		},
	)

	if err != nil {
		logger.Error(fmt.Sprintf("[微信支付异常] 创建微信支付预付单失败，订单号：%s，错误：%v, 返回：%s", orderNo, err, result.Response.Body))
		return nil, werror.New(500, "创建微信支付订单失败", map[string]interface{}{"detail": err.Error()})
	}

	// 3. PrepayWithRequestPayment 直接返回前端所需参数
	logger.Info(fmt.Sprintf("[微信支付] 成功生成 JSAPI 支付参数，订单号：%s", orderNo))

	// 4. 返回前端调起支付所需的参数
	// 注意：V3 SDK PrepayWithRequestPayment 返回的 *jsapi.PrepayWithRequestPaymentResponse 已经是所需格式
	return map[string]interface{}{
		"appId":     *resp.Appid,
		"timeStamp": *resp.TimeStamp,
		"nonceStr":  *resp.NonceStr,
		"package":   *resp.Package,
		"signType":  *resp.SignType,
		"paySign":   *resp.PaySign,
		"success":   true,
	}, nil
}

// Notify 支付回调处理 (V3 版本)
// !! 注意：此签名与 PaymentService 接口匹配，但与 V3 回调所需参数不符 !!
// !! V3 回调需要 *http.Request 来解析和验签。 !!
// !! 此处实现已注释，需要架构层面决策如何处理接口与 V3 的差异 !!
func (s *WechatPayService) Notify(notifyData map[string]string) error {
	logger := mylog.GetDefaultLogger()
	logger.Info("收到支付回调通知（接口匹配，但V3逻辑已注释）")

	// --- V3 回调处理逻辑（已注释） ---
	/*
		// 1. 初始化回调处理器 (需要传入 *http.Request，此处无法获取)
		// h := notify.NewNotifyHandler(s.apiV3Key, s.verifier)

		// 2. 解析回调请求并验签、解密 (需要传入 *http.Request)
		// transaction := new(payments.Transaction)
		// notifyReq, err := h.ParseNotifyRequest(context.Background(), req, transaction) // req 未定义
		// if err != nil {
		// 	logger.Error(fmt.Sprintf("解析微信回调失败: %v", err))
		// 	return err
		// }

		// 3. 处理不同类型的通知
		// switch notifyReq.EventType {
		// case "TRANSACTION.SUCCESS":
		// 	logger.Info(fmt.Sprintf("支付成功回调，订单号: %s, 微信支付订单号: %s", *transaction.OutTradeNo, *transaction.TransactionId))

		// 	// 4. 处理支付成功逻辑
		// 	notifyParams := map[string]string{
		// 		"out_trade_no":   *transaction.OutTradeNo,
		// 		"transaction_id": *transaction.TransactionId,
		// 		"total_fee":      fmt.Sprintf("%d", *transaction.Amount.Total),
		// 		"openid":         *transaction.Payer.Openid,
		// 	}
		// 	if transaction.SuccessTime != nil && *transaction.SuccessTime != "" {
		// 		// 解析 RFC3339 时间字符串
		// 		successTimeStr := *transaction.SuccessTime
		// 		successTime, timeParseErr := time.Parse(time.RFC3339, successTimeStr)
		// 		if timeParseErr == nil {
		// 			notifyParams["time_end"] = successTime.Format("20060102150405") // 格式化为需要的时间格式
		// 		} else {
		// 			logger.Warn(fmt.Sprintf("解析支付成功时间失败 (%s): %v", successTimeStr, timeParseErr))
		// 		}
		// 	}

		// 	if err := HandlePaymentSuccess(*transaction.OutTradeNo, 1, *transaction.TransactionId, notifyParams, nil); err != nil {
		// 	    logger.Error(fmt.Sprintf("处理支付成功逻辑失败，订单号: %s, 错误: %v", *transaction.OutTradeNo, err))
		// 	    // 即使处理失败，也应返回 nil，表示已接收通知，防止微信重复发送。内部应有重试或告警。
		// 	    // return err // 不直接返回错误给微信
		// 	 }
		// 	return nil // 成功处理
		// default:
		// 	logger.Warn(fmt.Sprintf("收到未处理的回调通知类型: %s", notifyReq.EventType))
		// 	return nil // 对于未处理的类型也返回成功，避免重复通知
		// }
	*/

	// 因为无法处理 V3 回调，暂时返回 nil
	return nil
}

// isValidOpenID 检查openid是否有效（微信openid格式验证）
func (s *WechatPayService) isValidOpenID(openid string) bool {
	if openid == "" {
		return false
	}

	// 微信openid通常是28位字符，由字母、数字、下划线、连字符组成
	// 格式通常为：o + 22位字符 + 5位字符 = 28位
	if len(openid) != 28 {
		return false
	}

	// 微信openid通常以'o'开头
	if !strings.HasPrefix(openid, "o") {
		return false
	}

	// 检查字符是否都是有效字符（字母、数字、下划线、连字符）
	for _, char := range openid {
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == '_' || char == '-') {
			return false
		}
	}

	// 检查是否包含明显的测试字符串
	lowerOpenID := strings.ToLower(openid)
	if strings.Contains(lowerOpenID, "test") ||
		strings.Contains(lowerOpenID, "demo") ||
		strings.Contains(lowerOpenID, "fake") {
		return false
	}

	return true
}

// --- 其他支付方式的方法 (例如 BalancePay) ---
// ... 需要实现 PaymentService 接口的其他方法 ...
