package payment

import (
	"context"
	"encoding/json"
	"time"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/config"
	"wnsys/shop/app/model/do"
	"wnsys/shop/app/model/do/BeautyDB"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// BusinessType 业务类型
const (
	BusinessTypeBooking = "booking" // 美容预约
	BusinessTypeCard    = "card"    // 套餐卡
	BusinessTypeOrder   = "order"   // 商品订单
)

// PaymentService 支付服务接口
type PaymentService interface {
	// Pay 创建支付订单
	Pay(orderId int64, orderNo string, amount float64, businessType string) (map[string]interface{}, error)

	// Notify 支付回调
	Notify(params map[string]string) error
}

// PaymentFactory 支付服务工厂
type PaymentFactory struct {
	services map[int]PaymentService
}

// NewPaymentFactory 创建支付服务工厂
func NewPaymentFactory() *PaymentFactory {
	return &PaymentFactory{
		services: make(map[int]PaymentService),
	}
}

// GetPaymentService 获取支付服务
func (f *PaymentFactory) GetPaymentService(payType int) PaymentService {
	// 临时禁用缓存，确保每次都重新创建服务（用于调试配置问题）
	// if service, ok := f.services[payType]; ok {
	// 	return service
	// }

	// 根据支付类型初始化对应的服务
	var service PaymentService
	switch payType {
	case 1: // 微信支付
		// 根据配置选择微信支付版本
		if config.App.Wx.Pay.Version == "v2" {
			service = NewWechatPayV2Service()
		} else {
			// 默认使用V3版本
			service = NewWechatPayService()
		}
	case 2: // 支付宝支付
		service = NewAlipayService()
	case 3: // 余额支付
		service = NewBalancePayService()
	default:
		return nil
	}

	// 缓存服务实例
	f.services[payType] = service
	return service
}

// HandlePaymentSuccess 处理支付成功
func HandlePaymentSuccess(orderNo string, payType int8, tradeNo string, notifyData map[string]string, businessType string, existingTx *gorm.DB) error {
	var tx *gorm.DB
	var shouldCommit bool

	// 如果没有传入事务，创建新事务
	if existingTx == nil {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		tx = db.DB.Shop.WithContext(ctx).Begin()
		if tx.Error != nil {
			return werror.New(500, "开启事务失败", map[string]interface{}{
				"error": tx.Error.Error(),
			})
		}
		shouldCommit = true
	} else {
		tx = existingTx
		shouldCommit = false
	}

	defer func() {
		if r := recover(); r != nil {
			if shouldCommit {
				tx.Rollback()
			}
		}
	}()

	// 根据业务类型处理支付成功
	switch businessType {
	case BusinessTypeBooking:
		return handleBeautyBookingPaymentSuccess(tx, orderNo, payType, tradeNo, notifyData, shouldCommit)
	case BusinessTypeCard:
		return handleBeautyCardPaymentSuccess(tx, orderNo, payType, tradeNo, notifyData, shouldCommit)
	default:
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(400, "不支持的业务类型", map[string]interface{}{
			"businessType": businessType,
			"orderNo":      orderNo,
		})
	}
}

// HandlePaymentSuccessWithSkipCheck 处理支付成功（跳过重复检查）
func HandlePaymentSuccessWithSkipCheck(orderNo string, payType int8, tradeNo string, notifyData map[string]string, businessType string, existingTx *gorm.DB) error {
	var tx *gorm.DB
	var shouldCommit bool

	// 如果没有传入事务，创建新事务
	if existingTx == nil {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		tx = db.DB.Shop.WithContext(ctx).Begin()
		if tx.Error != nil {
			return werror.New(500, "开启事务失败", map[string]interface{}{
				"error": tx.Error.Error(),
			})
		}
		shouldCommit = true
	} else {
		tx = existingTx
		shouldCommit = false
	}

	defer func() {
		if r := recover(); r != nil {
			if shouldCommit {
				tx.Rollback()
			}
		}
	}()

	// 根据业务类型处理支付成功（跳过重复检查）
	switch businessType {
	case BusinessTypeBooking:
		return handleBeautyBookingPaymentSuccessWithSkipCheck(tx, orderNo, payType, tradeNo, notifyData, shouldCommit)
	case BusinessTypeCard:
		return handleBeautyCardPaymentSuccessWithSkipCheck(tx, orderNo, payType, tradeNo, notifyData, shouldCommit)
	default:
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(400, "不支持的业务类型", map[string]interface{}{
			"businessType": businessType,
			"orderNo":      orderNo,
		})
	}
}

// handleBeautyBookingPayment 处理美容预约支付
func handleBeautyBookingPayment(tx *gorm.DB, booking BeautyDB.BeautyBookingDO, payType int8, tradeNo string, notifyData map[string]string, shouldCommit bool) error {
	// 检查预约状态 - 除了取消状态外，其他状态都可以支付
	if booking.BookingStatus == "cancelled" {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(400, "已取消的预约不能支付", map[string]interface{}{
			"bookingNo":     booking.BookingNo,
			"bookingStatus": booking.BookingStatus,
			"payType":       payType,
		})
	}

	// 检查支付状态
	if booking.PaymentStatus == "paid" {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(400, "预约已支付", map[string]interface{}{
			"bookingNo":     booking.BookingNo,
			"paymentStatus": booking.PaymentStatus,
		})
	}

	return processPaymentUpdate(tx, booking, payType, tradeNo, notifyData, shouldCommit)
}

// handleBeautyBookingPaymentWithSkipCheck 处理美容预约支付（跳过重复检查）
func handleBeautyBookingPaymentWithSkipCheck(tx *gorm.DB, booking BeautyDB.BeautyBookingDO, payType int8, tradeNo string, notifyData map[string]string, shouldCommit bool) error {
	// 只检查预约状态，不检查支付状态（避免重复支付检查）
	if booking.BookingStatus == "cancelled" {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(400, "已取消的预约不能支付", map[string]interface{}{
			"bookingNo":     booking.BookingNo,
			"bookingStatus": booking.BookingStatus,
			"payType":       payType,
		})
	}

	// 如果已经支付，直接返回成功（避免重复支付错误）
	if booking.PaymentStatus == "paid" {
		// 如果是新创建的事务，需要提交
		if shouldCommit {
			if err := tx.Commit().Error; err != nil {
				tx.Rollback()
				return werror.New(500, "提交事务失败", map[string]interface{}{
					"bookingNo": booking.BookingNo,
					"error":     err.Error(),
				})
			}
		}
		return nil
	}

	return processPaymentUpdate(tx, booking, payType, tradeNo, notifyData, shouldCommit)
}

// handleBeautyBookingPaymentSuccess 处理美容预约支付成功
func handleBeautyBookingPaymentSuccess(tx *gorm.DB, orderNo string, payType int8, tradeNo string, notifyData map[string]string, shouldCommit bool) error {
	// 查询统一订单
	var order ShopDB.OrderDO
	err := tx.Set("gorm:query_option", "FOR UPDATE").
		Where("order_no = ? AND business_type = 'booking'", orderNo).
		First(&order).Error

	if err != nil {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(404, "订单不存在", map[string]interface{}{
			"orderNo": orderNo,
			"error":   err.Error(),
		})
	}

	// 查询关联的预约
	var booking BeautyDB.BeautyBookingDO
	err = tx.Set("gorm:query_option", "FOR UPDATE").
		Where("order_id = ?", order.ID).
		First(&booking).Error

	if err != nil {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(404, "预约不存在", map[string]interface{}{
			"orderNo": orderNo,
			"error":   err.Error(),
		})
	}

	// 处理美容预约支付
	return handleBeautyBookingPayment(tx, booking, payType, tradeNo, notifyData, shouldCommit)
}

// handleBeautyBookingPaymentSuccessWithSkipCheck 处理美容预约支付成功（跳过重复检查）
func handleBeautyBookingPaymentSuccessWithSkipCheck(tx *gorm.DB, orderNo string, payType int8, tradeNo string, notifyData map[string]string, shouldCommit bool) error {
	// 查询统一订单
	var order ShopDB.OrderDO
	err := tx.Set("gorm:query_option", "FOR UPDATE").
		Where("order_no = ? AND business_type = 'booking'", orderNo).
		First(&order).Error

	if err != nil {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(404, "订单不存在", map[string]interface{}{
			"orderNo": orderNo,
			"error":   err.Error(),
		})
	}

	// 查询关联的预约
	var booking BeautyDB.BeautyBookingDO
	err = tx.Set("gorm:query_option", "FOR UPDATE").
		Where("order_id = ?", order.ID).
		First(&booking).Error

	if err != nil {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(404, "预约不存在", map[string]interface{}{
			"orderNo": orderNo,
			"error":   err.Error(),
		})
	}

	// 处理美容预约支付（跳过重复检查）
	return handleBeautyBookingPaymentWithSkipCheck(tx, booking, payType, tradeNo, notifyData, shouldCommit)
}

// handleBeautyCardPayment 处理套餐卡支付
func handleBeautyCardPayment(tx *gorm.DB, userCard do.UserCardDO, payType int8, tradeNo string, notifyData map[string]string, shouldCommit bool) error {
	// 检查套餐卡状态
	if userCard.Status != 1 {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(400, "套餐卡状态异常", map[string]interface{}{
			"orderNo": userCard.OrderNo,
			"status":  userCard.Status,
		})
	}

	// 检查是否已支付
	if userCard.OrderNo != nil && *userCard.OrderNo != "" {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(400, "套餐卡已支付", map[string]interface{}{
			"orderNo": userCard.OrderNo,
		})
	}

	return processCardPaymentUpdate(tx, userCard, payType, tradeNo, notifyData, shouldCommit)
}

// handleBeautyCardPaymentWithSkipCheck 处理套餐卡支付（跳过重复检查）
func handleBeautyCardPaymentWithSkipCheck(tx *gorm.DB, userCard do.UserCardDO, payType int8, tradeNo string, notifyData map[string]string, shouldCommit bool) error {
	// 检查套餐卡状态
	if userCard.Status != 1 {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(400, "套餐卡状态异常", map[string]interface{}{
			"orderNo": userCard.OrderNo,
			"status":  userCard.Status,
		})
	}

	// 如果已经支付，直接返回成功（避免重复支付错误）
	if userCard.OrderNo != nil && *userCard.OrderNo != "" {
		// 如果是新创建的事务，需要提交
		if shouldCommit {
			if err := tx.Commit().Error; err != nil {
				tx.Rollback()
				return werror.New(500, "提交事务失败", map[string]interface{}{
					"orderNo": userCard.OrderNo,
					"error":   err.Error(),
				})
			}
		}
		return nil
	}

	return processCardPaymentUpdate(tx, userCard, payType, tradeNo, notifyData, shouldCommit)
}

// handleBeautyCardPaymentSuccess 处理套餐卡支付成功
func handleBeautyCardPaymentSuccess(tx *gorm.DB, orderNo string, payType int8, tradeNo string, notifyData map[string]string, shouldCommit bool) error {
	// 查询统一订单
	var order ShopDB.OrderDO
	err := tx.Set("gorm:query_option", "FOR UPDATE").
		Where("order_no = ? AND business_type = 'card'", orderNo).
		First(&order).Error

	if err != nil {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(404, "订单不存在", map[string]interface{}{
			"orderNo": orderNo,
			"error":   err.Error(),
		})
	}

	// 查询关联的套餐卡
	var userCard do.UserCardDO
	err = tx.Set("gorm:query_option", "FOR UPDATE").
		Where("order_id = ?", order.ID).
		First(&userCard).Error

	if err != nil {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(404, "套餐卡不存在", map[string]interface{}{
			"orderNo": orderNo,
			"error":   err.Error(),
		})
	}

	// 处理套餐卡支付
	return handleBeautyCardPayment(tx, userCard, payType, tradeNo, notifyData, shouldCommit)
}

// handleBeautyCardPaymentSuccessWithSkipCheck 处理套餐卡支付成功（跳过重复检查）
func handleBeautyCardPaymentSuccessWithSkipCheck(tx *gorm.DB, orderNo string, payType int8, tradeNo string, notifyData map[string]string, shouldCommit bool) error {
	// 查询统一订单
	var order ShopDB.OrderDO
	err := tx.Set("gorm:query_option", "FOR UPDATE").
		Where("order_no = ? AND business_type = 'card'", orderNo).
		First(&order).Error

	if err != nil {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(404, "订单不存在", map[string]interface{}{
			"orderNo": orderNo,
			"error":   err.Error(),
		})
	}

	// 查询关联的套餐卡
	var userCard do.UserCardDO
	err = tx.Set("gorm:query_option", "FOR UPDATE").
		Where("order_id = ?", order.ID).
		First(&userCard).Error

	if err != nil {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(404, "套餐卡不存在", map[string]interface{}{
			"orderNo": orderNo,
			"error":   err.Error(),
		})
	}

	// 处理套餐卡支付（跳过重复检查）
	return handleBeautyCardPaymentWithSkipCheck(tx, userCard, payType, tradeNo, notifyData, shouldCommit)
}

// processPaymentUpdate 处理支付更新逻辑
func processPaymentUpdate(tx *gorm.DB, booking BeautyDB.BeautyBookingDO, payType int8, tradeNo string, notifyData map[string]string, shouldCommit bool) error {
	// 更新预约支付状态
	now := time.Now()

	// 1. 更新统一订单表状态
	err := tx.Model(&ShopDB.OrderDO{}).
		Where("id = ?", booking.OrderID).
		Updates(map[string]interface{}{
			"status":      "paid", // 更新订单状态为已支付
			"pay_time":    &now,
			"update_time": now,
		}).Error
	if err != nil {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(500, "更新订单状态失败", map[string]interface{}{
			"orderId": booking.OrderID,
			"error":   err.Error(),
		})
	}

	// 2. 更新预约支付状态
	err = tx.Model(&booking).Updates(map[string]interface{}{
		"payment_status": "paid",
		"update_time":    now,
	}).Error
	if err != nil {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(500, "更新预约支付状态失败", map[string]interface{}{
			"bookingNo": booking.BookingNo,
			"bookingId": booking.ID,
		})
	}

	// 3. 更新统一支付记录状态（如果存在）
	notifyDataStr, _ := json.Marshal(notifyData)
	err = tx.Model(&ShopDB.OrderPaymentDO{}).
		Where("order_id = ?", booking.OrderID).
		Updates(map[string]interface{}{
			"pay_status":  1, // 1: 支付成功
			"trade_no":    tradeNo,
			"pay_time":    &now,
			"notify_time": &now,
			"notify_data": string(notifyDataStr),
			"update_time": now,
		}).Error
	if err != nil {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(500, "更新支付记录失败", map[string]interface{}{
			"orderId": booking.OrderID,
			"error":   err.Error(),
		})
	}

	// 4. 如果是余额支付，扣减用户余额
	if payType == 3 {
		err = tx.Model(&ShopDB.UserDO{}).
			Where("id = ? AND balance >= ?", booking.UserID, booking.FinalPrice).
			Update("balance", gorm.Expr("balance - ?", booking.FinalPrice)).Error
		if err != nil {
			if shouldCommit {
				tx.Rollback()
			}
			return werror.New(500, "扣减余额失败", map[string]interface{}{
				"bookingNo": booking.BookingNo,
				"userId":    booking.UserID,
				"payAmount": booking.FinalPrice,
			})
		}
	}

	// 5. 如果是新创建的事务，需要提交
	if shouldCommit {
		if err := tx.Commit().Error; err != nil {
			tx.Rollback()
			return werror.New(500, "提交事务失败", map[string]interface{}{
				"orderId": booking.OrderID,
				"error":   err.Error(),
			})
		}
	}

	return nil
}

// processCardPaymentUpdate 处理套餐卡支付更新逻辑
func processCardPaymentUpdate(tx *gorm.DB, userCard do.UserCardDO, payType int8, tradeNo string, notifyData map[string]string, shouldCommit bool) error {
	// 更新套餐卡支付状态
	now := time.Now()

	// 1. 更新统一订单表状态
	err := tx.Model(&ShopDB.OrderDO{}).
		Where("id = ?", userCard.OrderID).
		Updates(map[string]interface{}{
			"status":      "paid", // 更新订单状态为已支付
			"pay_time":    &now,
			"update_time": now,
		}).Error
	if err != nil {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(500, "更新订单状态失败", map[string]interface{}{
			"orderId": userCard.OrderID,
			"error":   err.Error(),
		})
	}

	// 2. 更新套餐卡状态
	err = tx.Model(&userCard).Updates(map[string]interface{}{
		"update_time": now,
	}).Error
	if err != nil {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(500, "更新套餐卡状态失败", map[string]interface{}{
			"userCardId": userCard.ID,
		})
	}

	// 3. 更新统一支付记录状态（如果存在）
	notifyDataStr, _ := json.Marshal(notifyData)
	err = tx.Model(&ShopDB.OrderPaymentDO{}).
		Where("order_id = ?", userCard.OrderID).
		Updates(map[string]interface{}{
			"pay_status":  1, // 1: 支付成功
			"trade_no":    tradeNo,
			"pay_time":    &now,
			"notify_time": &now,
			"notify_data": string(notifyDataStr),
			"update_time": now,
		}).Error
	if err != nil {
		if shouldCommit {
			tx.Rollback()
		}
		return werror.New(500, "更新支付记录失败", map[string]interface{}{
			"orderId": userCard.OrderID,
			"error":   err.Error(),
		})
	}

	// 4. 如果是余额支付，扣减用户余额
	if payType == 3 {
		err = tx.Model(&ShopDB.UserDO{}).
			Where("id = ? AND balance >= ?", userCard.UserID, userCard.PurchasePrice).
			Update("balance", gorm.Expr("balance - ?", userCard.PurchasePrice)).Error
		if err != nil {
			if shouldCommit {
				tx.Rollback()
			}
			return werror.New(500, "扣减余额失败", map[string]interface{}{
				"userCardId": userCard.ID,
				"userId":     userCard.UserID,
				"payAmount":  userCard.PurchasePrice,
			})
		}
	}

	// 5. 如果是新创建的事务，需要提交
	if shouldCommit {
		if err := tx.Commit().Error; err != nil {
			tx.Rollback()
			return werror.New(500, "提交事务失败", map[string]interface{}{
				"orderId": userCard.OrderID,
				"error":   err.Error(),
			})
		}
	}

	return nil
}
