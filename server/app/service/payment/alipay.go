package payment

import (
	"fmt"
	"strings"

	"github.com/smartwalle/alipay/v3"
)

// AlipayService 支付宝支付服务
type AlipayService struct {
	client *alipay.Client
	appId  string
}

// NewAlipayService 创建支付宝支付服务
func NewAlipayService() PaymentService {
	// TODO: 从配置文件读取
	client, err := alipay.New("your_app_id", "your_private_key", false)
	if err != nil {
		panic(fmt.Sprintf("初始化支付宝客户端失败: %v", err))
	}
	// 加载支付宝公钥
	err = client.LoadAliPayPublicKey("your_alipay_public_key")
	if err != nil {
		panic(fmt.Sprintf("加载支付宝公钥失败: %v", err))
	}

	return &AlipayService{
		client: client,
		appId:  "your_app_id",
	}
}

// Pay 创建支付订单
func (s *AlipayService) Pay(orderId int64, orderNo string, amount float64, businessType string) (map[string]interface{}, error) {
	// 支付宝支付逻辑
	// 这里暂时返回模拟数据
	return map[string]interface{}{
		"orderNo": orderNo,
		"amount":  amount,
		"status":  "pending",
	}, nil
}

// Notify 支付回调
func (s *AlipayService) Notify(params map[string]string) error {
	// 获取必要参数
	orderNo := params["out_trade_no"]
	tradeNo := params["trade_no"]

	// 根据订单号判断业务类型
	var businessType string
	if strings.HasPrefix(orderNo, "CARD") {
		businessType = "card"
	} else if strings.HasPrefix(orderNo, "BOOK") {
		businessType = "booking"
	} else {
		businessType = "order" // 默认为商品订单
	}

	// 处理支付成功
	return HandlePaymentSuccess(orderNo, 2, tradeNo, params, businessType, nil)
}
