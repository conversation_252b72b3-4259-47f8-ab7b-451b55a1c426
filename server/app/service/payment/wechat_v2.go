package payment

import (
	"crypto/md5"
	"encoding/xml"
	"fmt"
	"io/ioutil"
	"net/http"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"
	"wnsys/shop/app/common/utils"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/config"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/provider/db"
	"wnsys/shop/app/provider/mylog"
)

// WechatPayV2Service 微信支付V2服务
type WechatPayV2Service struct {
	appId     string // 微信公众号AppID
	mchId     string // 商户号
	apiKey    string // API密钥
	notifyUrl string // 回调地址
	signType  string // 签名类型
}

// UnifiedOrderRequest 统一下单请求结构
type UnifiedOrderRequest struct {
	XMLName        xml.Name `xml:"xml"`
	AppID          string   `xml:"appid"`
	MchID          string   `xml:"mch_id"`
	NonceStr       string   `xml:"nonce_str"`
	Sign           string   `xml:"sign"`
	Body           string   `xml:"body"`
	OutTradeNo     string   `xml:"out_trade_no"`
	TotalFee       int      `xml:"total_fee"`
	SpbillCreateIP string   `xml:"spbill_create_ip"`
	NotifyURL      string   `xml:"notify_url"`
	TradeType      string   `xml:"trade_type"`
	OpenID         string   `xml:"openid,omitempty"`
	SignType       string   `xml:"sign_type,omitempty"`
}

// UnifiedOrderResponse 统一下单响应结构
type UnifiedOrderResponse struct {
	XMLName    xml.Name `xml:"xml"`
	ReturnCode string   `xml:"return_code"`
	ReturnMsg  string   `xml:"return_msg"`
	AppID      string   `xml:"appid"`
	MchID      string   `xml:"mch_id"`
	NonceStr   string   `xml:"nonce_str"`
	Sign       string   `xml:"sign"`
	ResultCode string   `xml:"result_code"`
	PrepayID   string   `xml:"prepay_id"`
	TradeType  string   `xml:"trade_type"`
	ErrCode    string   `xml:"err_code"`
	ErrCodeDes string   `xml:"err_code_des"`
}

// NewWechatPayV2Service 创建微信支付V2服务
func NewWechatPayV2Service() PaymentService {
	cfg := config.App

	logger := mylog.GetDefaultLogger()
	logger.Info(fmt.Sprintf("初始化微信支付V2服务，配置信息: AppID=%s, MchID=%s, NotifyUrl=%s",
		cfg.Wx.MiniApp.AppID, cfg.Wx.Pay.MchID, cfg.Wx.Pay.NotifyUrl))

	return &WechatPayV2Service{
		appId:     cfg.Wx.MiniApp.AppID,
		mchId:     cfg.Wx.Pay.MchID,
		apiKey:    cfg.Wx.Pay.ApiKey,
		notifyUrl: cfg.Wx.Pay.NotifyUrl,
		signType:  "MD5", // V2版本默认使用MD5签名
	}
}

// Pay 创建支付订单 (JSAPI)
func (s *WechatPayV2Service) Pay(orderId int64, orderNo string, amount float64, businessType string) (map[string]interface{}, error) {
	logger := mylog.GetDefaultLogger()
	logger.Info(fmt.Sprintf("开始处理微信V2 JSAPI 支付，订单号: %s, 订单ID: %d, 金额: %.2f, 业务类型: %s", orderNo, orderId, amount, businessType))

	// 从统一订单表获取用户ID和订单信息
	var order ShopDB.OrderDO
	err := db.DB.Shop.Select("user_id, business_type, business_id").Where("id = ?", orderId).First(&order).Error
	if err != nil {
		logger.Error(fmt.Sprintf("无法获取订单 %d 信息: %v", orderId, err))
		return nil, werror.New(500, "获取订单信息失败", map[string]interface{}{"detail": "订单不存在"})
	}

	userID := order.UserID
	var body string

	// 根据业务类型设置商品描述
	switch businessType {
	case "booking":
		body = fmt.Sprintf("美容服务-预约%s", orderNo)
	case "card":
		body = fmt.Sprintf("美容套餐卡-%s", orderNo)
	case "order":
		body = fmt.Sprintf("商品订单-%s", orderNo)
	default:
		logger.Error(fmt.Sprintf("不支持的业务类型: %s", businessType))
		return nil, werror.New(400, "不支持的业务类型", map[string]interface{}{"businessType": businessType})
	}

	var user ShopDB.UserDO
	err = db.DB.Shop.Select("openid").Where("id = ?", userID).First(&user).Error
	if err != nil {
		logger.Error(fmt.Sprintf("无法获取用户 %d 信息: %v", userID, err))
		return nil, werror.New(500, "获取用户信息失败", map[string]interface{}{"detail": "用户不存在"})
	}

	// 检查用户是否有有效的OpenID
	if user.OpenID == nil || *user.OpenID == "" {
		logger.Error(fmt.Sprintf("用户 %d 没有有效的 OpenID，请先完成微信登录授权", userID))
		return nil, werror.New(400, "用户未完成微信登录授权", map[string]interface{}{
			"detail": "请先在小程序中完成微信登录，获取OpenID后再进行支付",
			"userId": userID,
		})
	}

	openid := *user.OpenID

	// 验证OpenID格式（微信OpenID通常是28位字符）
	if !s.isValidOpenID(openid) {
		logger.Error(fmt.Sprintf("用户 %d 的 OpenID 格式无效: %s", userID, openid))
		return nil, werror.New(400, "OpenID格式无效", map[string]interface{}{
			"detail": "请重新登录获取有效的OpenID",
			"userId": userID,
			"openid": openid,
		})
	}

	// 2. 构建统一下单请求
	nonceStr := utils.GenerateNonceStr()
	totalFee := int(amount * 100) // 转换为分

	// 校验金额，微信支付要求最小金额为1分
	if totalFee < 1 {
		logger.Error(fmt.Sprintf("支付金额过小，订单号: %s, 金额: %.2f元, 转换后: %d分", orderNo, amount, totalFee))
		return nil, werror.New(400, "支付金额不能小于0.01元", map[string]interface{}{
			"amount":   amount,
			"totalFee": totalFee,
		})
	}

	// 调试：打印notifyUrl的值
	logger.Info(fmt.Sprintf("微信支付V2服务notifyUrl: '%s'", s.notifyUrl))

	// 临时解决方案：如果配置的notifyUrl为空，使用硬编码的URL
	notifyUrl := s.notifyUrl
	if notifyUrl == "" {
		notifyUrl = "https://44799531.r18.cpolar.top/api/payment/wx/notify"
		logger.Warn(fmt.Sprintf("配置的notifyUrl为空，使用临时URL: %s", notifyUrl))
	}

	req := &UnifiedOrderRequest{
		AppID:          s.appId,
		MchID:          s.mchId,
		NonceStr:       nonceStr,
		Body:           body,
		OutTradeNo:     orderNo,
		TotalFee:       totalFee,
		SpbillCreateIP: "127.0.0.1", // 可以从请求中获取真实IP
		NotifyURL:      notifyUrl,
		TradeType:      "JSAPI",
		OpenID:         openid,
		SignType:       s.signType,
	}

	// 3. 生成签名
	req.Sign = s.generateSign(req)

	// 4. 发送统一下单请求
	resp, err := s.unifiedOrder(req)
	if err != nil {
		logger.Error(fmt.Sprintf("[微信V2支付异常] 统一下单失败，订单号：%s，错误：%v", orderNo, err))
		return nil, werror.New(500, "创建微信支付订单失败", map[string]interface{}{"detail": err.Error()})
	}

	// 5. 检查响应结果
	if resp.ReturnCode != "SUCCESS" {
		logger.Error(fmt.Sprintf("[微信V2支付异常] 统一下单返回失败，订单号：%s，错误：%s", orderNo, resp.ReturnMsg))
		return nil, werror.New(500, "微信支付统一下单失败", map[string]interface{}{"detail": resp.ReturnMsg})
	}

	if resp.ResultCode != "SUCCESS" {
		logger.Error(fmt.Sprintf("[微信V2支付异常] 统一下单业务失败，订单号：%s，错误代码：%s，错误描述：%s", orderNo, resp.ErrCode, resp.ErrCodeDes))
		return nil, werror.New(500, "微信支付下单失败", map[string]interface{}{"detail": resp.ErrCodeDes})
	}

	// 6. 生成前端调起支付所需的参数
	timeStamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonceStr2 := utils.GenerateNonceStr()
	packageStr := fmt.Sprintf("prepay_id=%s", resp.PrepayID)

	// 生成paySign
	paySignParams := map[string]string{
		"appId":     s.appId,
		"timeStamp": timeStamp,
		"nonceStr":  nonceStr2,
		"package":   packageStr,
		"signType":  s.signType,
	}
	paySign := s.generateMapSign(paySignParams)

	logger.Info(fmt.Sprintf("[微信V2支付] 成功生成 JSAPI 支付参数，订单号：%s，prepay_id：%s", orderNo, resp.PrepayID))

	// 7. 创建统一支付记录
	paymentRecord := ShopDB.OrderPaymentDO{
		OrderID:    orderId,
		OrderNo:    orderNo,
		PayType:    1, // 1: 微信支付
		PayAmount:  amount,
		PayStatus:  0, // 0: 待支付
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}

	err = db.DB.Shop.Create(&paymentRecord).Error
	if err != nil {
		logger.Error(fmt.Sprintf("创建支付记录失败，订单号：%s，错误：%v", orderNo, err))
		// 不返回错误，因为支付参数已经生成成功
		logger.Warn("支付记录创建失败，但支付参数生成成功，继续返回支付参数")
	} else {
		logger.Info(fmt.Sprintf("成功创建支付记录，订单号：%s，支付记录ID：%d", orderNo, paymentRecord.ID))
	}

	return map[string]interface{}{
		"appId":     s.appId,
		"timeStamp": timeStamp,
		"nonceStr":  nonceStr2,
		"package":   packageStr,
		"signType":  s.signType,
		"paySign":   paySign,
		"success":   true,
	}, nil
}

// Notify 支付回调处理
func (s *WechatPayV2Service) Notify(notifyData map[string]string) error {
	logger := mylog.GetDefaultLogger()
	logger.Info("收到微信V2支付回调通知")

	// 1. 验签
	if !s.verifyNotifySign(notifyData) {
		logger.Error("微信V2支付回调验签失败")
		return werror.New(500, "支付回调验签失败", nil)
	}

	// 2. 检查支付结果
	if notifyData["return_code"] != "SUCCESS" || notifyData["result_code"] != "SUCCESS" {
		logger.Error(fmt.Sprintf("微信V2支付回调失败，return_code：%s，result_code：%s",
			notifyData["return_code"], notifyData["result_code"]))
		return werror.New(500, "支付失败", nil)
	}

	// 3. 处理支付成功
	orderNo := notifyData["out_trade_no"]
	transactionId := notifyData["transaction_id"]

	logger.Info(fmt.Sprintf("微信V2支付成功回调，订单号: %s, 微信支付订单号: %s", orderNo, transactionId))

	// 4. 更新统一支付记录
	now := time.Now()
	notifyDataStr := fmt.Sprintf("%v", notifyData)

	err := db.DB.Shop.Model(&ShopDB.OrderPaymentDO{}).
		Where("order_no = ?", orderNo).
		Updates(map[string]interface{}{
			"pay_status":  1, // 1: 支付成功
			"trade_no":    transactionId,
			"pay_time":    &now,
			"notify_time": &now,
			"notify_data": notifyDataStr,
			"update_time": now,
		}).Error

	if err != nil {
		logger.Error(fmt.Sprintf("更新支付记录失败，订单号：%s，错误：%v", orderNo, err))
		// 不返回错误，继续处理业务逻辑
	} else {
		logger.Info(fmt.Sprintf("成功更新支付记录，订单号：%s", orderNo))
	}

	// 5. 根据订单号判断业务类型
	var businessType string
	if strings.HasPrefix(orderNo, "CARD") {
		businessType = "card"
	} else if strings.HasPrefix(orderNo, "BOOK") {
		businessType = "booking"
	} else {
		businessType = "order" // 默认为商品订单
	}

	return HandlePaymentSuccess(orderNo, 1, transactionId, notifyData, businessType, nil)
}

// unifiedOrder 统一下单
func (s *WechatPayV2Service) unifiedOrder(req *UnifiedOrderRequest) (*UnifiedOrderResponse, error) {
	// 手动构建XML（使用CDATA格式，参考Java实现）
	xmlData := s.buildRequestXML(req)

	logger := mylog.GetDefaultLogger()
	logger.Info(fmt.Sprintf("微信支付V2请求XML: %s", xmlData))

	// 发送HTTP请求
	url := "https://api.mch.weixin.qq.com/pay/unifiedorder"

	// 创建HTTP客户端和请求
	client := &http.Client{}
	reqBody := strings.NewReader(xmlData)
	httpReq, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头（参考Java实现）
	httpReq.Header.Set("Content-Type", "application/xml; charset=UTF-8")
	httpReq.Header.Set("Accept-Charset", "UTF-8")

	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	logger.Info(fmt.Sprintf("微信支付V2响应XML: %s", string(body)))

	// 解析XML响应
	response := s.parseResponseXML(string(body))
	if response == nil {
		return nil, fmt.Errorf("解析响应XML失败")
	}

	return response, nil
}

// generateSign 生成签名
func (s *WechatPayV2Service) generateSign(req *UnifiedOrderRequest) string {
	// 构建签名参数
	params := map[string]string{
		"appid":            req.AppID,
		"mch_id":           req.MchID,
		"nonce_str":        req.NonceStr,
		"body":             req.Body,
		"out_trade_no":     req.OutTradeNo,
		"total_fee":        strconv.Itoa(req.TotalFee),
		"spbill_create_ip": req.SpbillCreateIP,
		"notify_url":       req.NotifyURL,
		"trade_type":       req.TradeType,
	}

	if req.OpenID != "" {
		params["openid"] = req.OpenID
	}
	if req.SignType != "" {
		params["sign_type"] = req.SignType
	}

	return s.generateMapSign(params)
}

// generateMapSign 根据参数map生成签名（参考Java实现）
func (s *WechatPayV2Service) generateMapSign(params map[string]string) string {
	// 1. 参数名按ASCII码从小到大排序（参考Java实现）
	var keys []string
	for k := range params {
		// 排除sign字段和空值字段
		if k != "sign" && params[k] != "" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 2. 构建待签名字符串（参考Java实现的格式）
	var signStr strings.Builder
	for i, k := range keys {
		if i > 0 {
			signStr.WriteString("&")
		}
		signStr.WriteString(k)
		signStr.WriteString("=")
		signStr.WriteString(params[k])
	}

	// 3. 拼接API密钥（参考Java实现）
	signStr.WriteString("&key=")
	signStr.WriteString(s.apiKey)

	// 4. MD5加密并转大写（参考Java实现）
	hash := md5.Sum([]byte(signStr.String()))
	return fmt.Sprintf("%X", hash)
}

// verifyNotifySign 验证回调签名
func (s *WechatPayV2Service) verifyNotifySign(params map[string]string) bool {
	sign := params["sign"]
	if sign == "" {
		return false
	}

	// 生成签名进行对比
	calculatedSign := s.generateMapSign(params)
	return sign == calculatedSign
}

// buildRequestXML 构建请求XML（使用CDATA格式，参考Java实现）
func (s *WechatPayV2Service) buildRequestXML(req *UnifiedOrderRequest) string {
	var sb strings.Builder
	sb.WriteString("<xml>")

	// 按照Java实现的方式，使用CDATA包装所有参数值
	sb.WriteString("<appid><![CDATA[")
	sb.WriteString(req.AppID)
	sb.WriteString("]]></appid>")

	sb.WriteString("<mch_id><![CDATA[")
	sb.WriteString(req.MchID)
	sb.WriteString("]]></mch_id>")

	sb.WriteString("<nonce_str><![CDATA[")
	sb.WriteString(req.NonceStr)
	sb.WriteString("]]></nonce_str>")

	sb.WriteString("<body><![CDATA[")
	sb.WriteString(req.Body)
	sb.WriteString("]]></body>")

	sb.WriteString("<out_trade_no><![CDATA[")
	sb.WriteString(req.OutTradeNo)
	sb.WriteString("]]></out_trade_no>")

	sb.WriteString("<total_fee><![CDATA[")
	sb.WriteString(strconv.Itoa(req.TotalFee))
	sb.WriteString("]]></total_fee>")

	sb.WriteString("<spbill_create_ip><![CDATA[")
	sb.WriteString(req.SpbillCreateIP)
	sb.WriteString("]]></spbill_create_ip>")

	sb.WriteString("<notify_url><![CDATA[")
	sb.WriteString(req.NotifyURL)
	sb.WriteString("]]></notify_url>")

	sb.WriteString("<trade_type><![CDATA[")
	sb.WriteString(req.TradeType)
	sb.WriteString("]]></trade_type>")

	if req.OpenID != "" {
		sb.WriteString("<openid><![CDATA[")
		sb.WriteString(req.OpenID)
		sb.WriteString("]]></openid>")
	}

	if req.SignType != "" {
		sb.WriteString("<sign_type><![CDATA[")
		sb.WriteString(req.SignType)
		sb.WriteString("]]></sign_type>")
	}

	sb.WriteString("<sign><![CDATA[")
	sb.WriteString(req.Sign)
	sb.WriteString("]]></sign>")

	sb.WriteString("</xml>")

	return sb.String()
}

// parseResponseXML 解析响应XML
func (s *WechatPayV2Service) parseResponseXML(xmlData string) *UnifiedOrderResponse {
	response := &UnifiedOrderResponse{}

	// 使用正则表达式解析XML（参考Java实现的思路）
	parseField := func(fieldName string) string {
		// 匹配 <fieldName><![CDATA[value]]></fieldName> 或 <fieldName>value</fieldName>
		pattern1 := fmt.Sprintf(`<%s><!\[CDATA\[([^\]]*)\]\]></%s>`, fieldName, fieldName)
		pattern2 := fmt.Sprintf(`<%s>([^<]*)</%s>`, fieldName, fieldName)

		re1 := regexp.MustCompile(pattern1)
		if matches := re1.FindStringSubmatch(xmlData); len(matches) > 1 {
			return matches[1]
		}

		re2 := regexp.MustCompile(pattern2)
		if matches := re2.FindStringSubmatch(xmlData); len(matches) > 1 {
			return matches[1]
		}

		return ""
	}

	response.ReturnCode = parseField("return_code")
	response.ReturnMsg = parseField("return_msg")
	response.AppID = parseField("appid")
	response.MchID = parseField("mch_id")
	response.NonceStr = parseField("nonce_str")
	response.Sign = parseField("sign")
	response.ResultCode = parseField("result_code")
	response.PrepayID = parseField("prepay_id")
	response.TradeType = parseField("trade_type")
	response.ErrCode = parseField("err_code")
	response.ErrCodeDes = parseField("err_code_des")

	return response
}

// isValidOpenID 检查openid是否有效（微信openid格式验证）
func (s *WechatPayV2Service) isValidOpenID(openid string) bool {
	if openid == "" {
		return false
	}

	// 微信openid通常是28位字符，由字母、数字、下划线、连字符组成
	// 格式通常为：o + 22位字符 + 5位字符 = 28位
	if len(openid) != 28 {
		return false
	}

	// 微信openid通常以'o'开头
	if !strings.HasPrefix(openid, "o") {
		return false
	}

	// 检查字符是否都是有效字符（字母、数字、下划线、连字符）
	for _, char := range openid {
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == '_' || char == '-') {
			return false
		}
	}

	// 检查是否包含明显的测试字符串
	lowerOpenID := strings.ToLower(openid)
	if strings.Contains(lowerOpenID, "test") ||
		strings.Contains(lowerOpenID, "demo") ||
		strings.Contains(lowerOpenID, "fake") {
		return false
	}

	return true
}
