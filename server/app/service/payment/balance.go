package payment

import (
	"context"
	"fmt"
	"time"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/provider/db"
	"wnsys/shop/app/provider/mylog"
)

// BalancePayService 余额支付服务
type BalancePayService struct{}

// NewBalancePayService 创建余额支付服务
func NewBalancePayService() PaymentService {
	return &BalancePayService{}
}

// Pay 创建支付订单
func (s *BalancePayService) Pay(orderId int64, orderNo string, amount float64, businessType string) (map[string]interface{}, error) {
	return s.executePayment(orderId, orderNo, amount, businessType)
}

// executePayment 执行支付处理
func (s *BalancePayService) executePayment(orderId int64, orderNo string, amount float64, businessType string) (map[string]interface{}, error) {
	// 设置上下文超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 开启事务
	tx := db.DB.Shop.WithContext(ctx).Begin()
	if tx.Error != nil {
		return nil, werror.New(500, "开启事务失败", map[string]interface{}{
			"orderNo": orderNo,
		})
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			mylog.Log.Error(nil, fmt.Sprintf("[余额支付异常] 支付时发生panic，订单号：%s，异常：%v", orderNo, r))
		}
	}()

	// 根据业务类型获取用户ID
	var userID int64
	switch businessType {
	case "booking":
		// 从统一订单表获取用户ID
		var order ShopDB.OrderDO
		err := tx.Set("gorm:query_option", "FOR UPDATE").
			Where("id = ? AND business_type = 'booking' AND is_delete = 0", orderId).
			First(&order).Error
		if err != nil {
			tx.Rollback()
			return nil, werror.New(404, "订单不存在", map[string]interface{}{
				"orderId": orderId,
				"orderNo": orderNo,
				"error":   err.Error(),
			})
		}
		userID = order.UserID

	case "card":
		// 从统一订单表获取用户ID
		var order ShopDB.OrderDO
		err := tx.Set("gorm:query_option", "FOR UPDATE").
			Where("id = ? AND business_type = 'card' AND is_delete = 0", orderId).
			First(&order).Error
		if err != nil {
			tx.Rollback()
			return nil, werror.New(404, "订单不存在", map[string]interface{}{
				"orderId": orderId,
				"orderNo": orderNo,
				"error":   err.Error(),
			})
		}
		userID = order.UserID

	case "order":
		// 查询商品订单信息
		var order ShopDB.OrderDO
		err := tx.Set("gorm:query_option", "FOR UPDATE").
			Where("id = ? AND is_delete = 0", orderId).
			First(&order).Error
		if err != nil {
			tx.Rollback()
			return nil, werror.New(404, "订单不存在", map[string]interface{}{
				"orderId": orderId,
				"orderNo": orderNo,
				"error":   err.Error(),
			})
		}
		userID = order.UserID

	default:
		tx.Rollback()
		return nil, werror.New(400, "不支持的业务类型", map[string]interface{}{
			"businessType": businessType,
		})
	}

	// 查询用户余额（使用FOR UPDATE锁定记录）
	var user ShopDB.UserDO
	err := tx.Set("gorm:query_option", "FOR UPDATE").
		Where("id = ?", userID).
		First(&user).Error
	if err != nil {
		tx.Rollback()
		return nil, werror.New(404, "用户不存在", map[string]interface{}{
			"userId":  userID,
			"orderId": orderId,
			"error":   err.Error(),
		})
	}

	// 检查余额是否足够
	if user.Balance < amount {
		tx.Rollback()
		return nil, werror.New(400, "余额不足", map[string]interface{}{
			"userId":  userID,
			"balance": user.Balance,
			"amount":  amount,
		})
	}

	// 构造回调数据
	notifyData := map[string]string{
		"user_id":  fmt.Sprintf("%d", userID),
		"balance":  fmt.Sprintf("%.2f", user.Balance),
		"amount":   fmt.Sprintf("%.2f", amount),
		"order_no": orderNo,
	}

	// 创建统一支付记录
	paymentRecord := ShopDB.OrderPaymentDO{
		OrderID:    orderId,
		OrderNo:    orderNo,
		PayType:    3, // 3: 余额支付
		PayAmount:  amount,
		PayStatus:  0, // 0: 待支付
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}

	err = tx.Create(&paymentRecord).Error
	if err != nil {
		tx.Rollback()
		return nil, werror.New(500, "创建支付记录失败", map[string]interface{}{
			"orderId": orderId,
			"orderNo": orderNo,
			"error":   err.Error(),
		})
	}

	// 处理支付成功
	err = HandlePaymentSuccess(orderNo, 3, "", notifyData, businessType, tx)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, werror.New(500, "提交事务失败", map[string]interface{}{
			"orderNo": orderNo,
			"orderId": orderId,
			"error":   err.Error(),
		})
	}

	// 查询最新余额（不在事务中查询）
	var updatedUser ShopDB.UserDO
	if err := db.DB.Shop.Where("id = ?", userID).First(&updatedUser).Error; err != nil {
		mylog.Log.Warn(nil, fmt.Sprintf("[余额支付警告] 获取最新余额失败，订单号：%s，用户ID：%d，错误：%v",
			orderNo, userID, err))
		// 不影响支付结果，使用计算后的余额
		updatedUser.Balance = user.Balance - amount
	}

	mylog.Log.Info(nil, fmt.Sprintf("[余额支付成功] 支付成功，订单号：%s，用户ID：%d，支付金额：%.2f，剩余余额：%.2f",
		orderNo, userID, amount, updatedUser.Balance))

	return map[string]interface{}{
		"success": true,
		"balance": updatedUser.Balance,
	}, nil
}

// Notify 支付回调（余额支付不需要回调）
func (s *BalancePayService) Notify(params map[string]string) error {
	return nil
}
