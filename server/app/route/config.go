package route

import (
	"wnsys/shop/app/controller/common"

	"github.com/gin-gonic/gin"
)

// RegisterConfigRoutes 注册配置相关路由
func RegisterConfigRoutes(rg *gin.RouterGroup) {
	configController := common.NewConfigController()

	// 配置接口
	configGroup := rg.Group("/config")
	{
		configGroup.POST("/list", configController.GetConfigs)        // 获取配置列表
		configGroup.GET("/get", configController.GetConfig)           // 获取单个配置
		configGroup.GET("/shop", configController.GetShopConfigs)     // 获取商城配置
		configGroup.POST("/cache/clear", configController.ClearCache) // 清除缓存
	}
}
