package route

import (
	"wnsys/shop/app/controller/order"
	"wnsys/shop/app/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterOrderRoutes 注册订单相关路由
func RegisterOrderRoutes(rg *gin.RouterGroup) {
	orderController := order.NewController()
	// 订单接口都需要登录
	orderGroup := rg.Group("/order").Use(middleware.JWT())
	{
		orderGroup.POST("/calculate", orderController.CalculateOrder)   // 计算订单金额
		orderGroup.POST("/create", orderController.CreateOrder)         // 创建订单
		orderGroup.GET("/:id", orderController.GetDetail)               // 获取订单详情
		orderGroup.POST("/pay", orderController.Pay)                    // 支付订单
		orderGroup.GET("/list", orderController.GetList)                // 获取订单列表
		orderGroup.POST("/:id/cancel", orderController.CancelOrder)     // 取消订单
		orderGroup.POST("/:id/confirm", orderController.ConfirmReceive) // 确认收货
		orderGroup.DELETE("/:id", orderController.DeleteOrder)          // 删除订单
	}
}
