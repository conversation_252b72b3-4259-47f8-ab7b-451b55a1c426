package route

import (
	"wnsys/shop/app/controller/goods"
	"wnsys/shop/app/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterGoodsRoutes 注册商品相关路由
func RegisterGoodsRoutes(rg *gin.RouterGroup) {
	goodsController := goods.NewController()
	goodsGroup := rg.Group("/goods")
	{
		// 公开接口，但支持可选的用户认证（用于获取点赞收藏状态）
		goodsGroup.GET("/list", middleware.OptionalJWT(), goodsController.GetGoodsList) // 获取商品列表
		goodsGroup.GET("/detail", middleware.OptionalJWT(), goodsController.GetDetail)  // 获取商品详情
		goodsGroup.GET("/comments", goodsController.GetComments)                        // 获取商品评价列表
		goodsGroup.POST("/spec/check-stock", goodsController.CheckStock)                // 检查规格库存

		// 需要登录的接口
		authGroup := goodsGroup.Group("", middleware.JWT())
		{
			authGroup.POST("/collect", goodsController.Collect)        // 收藏/取消收藏商品
			authGroup.POST("/like", goodsController.Like)              // 点赞/取消点赞商品
			authGroup.POST("/footprint", goodsController.AddFootprint) // 添加商品足迹
		}
	}
}
