package route

import (
	"wnsys/shop/app/controller/home"
	"wnsys/shop/app/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterHomeRoutes 注册首页相关路由
func RegisterHomeRoutes(rg *gin.RouterGroup) {
	homeController := home.NewHomeController()
	homeGroup := rg.Group("/home")
	{
		homeGroup.GET("/banner", homeController.GetBannerList)                         // 获取轮播图列表
		homeGroup.GET("/category", homeController.GetCategoryList)                     // 获取分类列表
		homeGroup.GET("/activity", homeController.GetActivityList)                     // 获取活动列表
		homeGroup.GET("/goods", middleware.OptionalJWT(), homeController.GetGoodsList) // 获取商品列表
		homeGroup.GET("/tag", homeController.GetTagList)                               // 获取标签列表
	}
}
