package route

import (
	"wnsys/shop/app/controller/share"
	"wnsys/shop/app/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterShareRoutes 注册分销相关路由
func RegisterShareRoutes(r *gin.RouterGroup) {
	shareGroup := r.Group("/share")
	{
		ctrl := share.NewController()

		// 不需要登录的接口
		shareGroup.GET("/config", ctrl.GetConfig)

		// 需要登录的接口
		shareGroup.Use(middleware.JWT())
		{
			// 分销员信息
			shareGroup.GET("/info", ctrl.GetDistributorInfo)
			// 申请分销
			shareGroup.POST("/apply", ctrl.ApplyDistributor)
			// 检查分销资格
			shareGroup.GET("/check-qualification", ctrl.CheckQualification)
			// 团队统计
			shareGroup.GET("/team-stats", ctrl.GetTeamStats)
			// 团队成员
			shareGroup.GET("/team", ctrl.GetTeamMembers)
			// 分销订单列表
			shareGroup.GET("/orders", ctrl.GetDistributionOrders)
			// 分销订单统计
			shareGroup.GET("/order-stats", ctrl.GetDistributionOrderStats)
			// 余额信息
			shareGroup.GET("/balance", ctrl.GetBalanceInfo)
			// 提现配置
			shareGroup.GET("/withdraw/config", ctrl.GetWithdrawConfig)
			// 提现
			shareGroup.POST("/withdraw", ctrl.Withdraw)
			// 获取提现列表
			shareGroup.GET("/withdraw/list", ctrl.GetWithdrawList)
			// 提现统计
			shareGroup.GET("/withdraw/stats", ctrl.GetWithdrawStats)
		}
	}
}
