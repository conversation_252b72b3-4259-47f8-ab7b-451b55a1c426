package route

import (
	"wnsys/shop/app/controller/user"
	"wnsys/shop/app/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterUserRouter 注册用户相关路由
func RegisterUserRoutes(r *gin.RouterGroup) {
	userController := user.NewController()
	authController := user.NewAuthController()
	userGroup := r.Group("/user")
	{
		// 无需登录的接口 - 认证相关
		userGroup.POST("/login", authController.Login)                         // 用户登录
		userGroup.POST("/login/wechat-phone", authController.WechatPhoneLogin) // 微信手机号登录
		userGroup.POST("/register", authController.Register)                   // 用户注册
		userGroup.POST("/sms/send", authController.SendSms)                    // 发送短信验证码
		userGroup.POST("/sms/verify", authController.VerifySms)                // 验证短信验证码

		// 需要登录的接口
		auth := userGroup.Group("", middleware.JWT())
		{
			// 认证相关
			auth.POST("/logout", authController.Logout) // 用户退出登录

			// 用户信息相关
			auth.GET("/info", userController.GetInfo)           // 获取用户信息
			auth.POST("/update", userController.UpdateUserInfo) // 更新用户信息
			auth.POST("/avatar", userController.UploadAvatar)   // 上传头像
			auth.PUT("/password", userController.ResetPassword) // 修改密码（不需要验证当前密码）

			// 收藏相关
			auth.GET("/collect/list", userController.GetCollectList)   // 获取收藏列表
			auth.POST("/collect/delete", userController.DeleteCollect) // 删除收藏

			// 足迹相关
			auth.GET("/footprint/list", userController.GetFootprintList)   // 获取足迹列表
			auth.POST("/footprint/delete", userController.DeleteFootprint) // 删除足迹
			auth.POST("/footprint/clear", userController.ClearFootprint)   // 清空足迹

			// 添加新的统计接口
			auth.GET("/stats", userController.GetStats)            // 获取用户数据统计
			auth.GET("/order/stats", userController.GetOrderStats) // 获取订单统计

			// 邀请关系相关
			auth.GET("/invite/relations", userController.GetInviteRelations) // 获取邀请关系列表
			auth.GET("/invite/stats", userController.GetInviteStats)         // 获取邀请统计

			// 分销员相关
			auth.POST("/distributor/apply", userController.ApplyDistributor) // 申请成为分销员
		}
	}
}
