package route

import (
	"wnsys/shop/app/controller/address"
	"wnsys/shop/app/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterAddressRoutes 注册地址相关路由
func RegisterAddressRoutes(rg *gin.RouterGroup) {
	addressController := address.NewController()
	// 地址接口都需要登录
	addressGroup := rg.Group("/address").Use(middleware.JWT())
	{
		addressGroup.GET("/default", addressController.GetDefault)      // 获取默认地址
		addressGroup.GET("/list", addressController.GetList)            // 获取地址列表
		addressGroup.GET("/detail/:id", addressController.GetDetail)    // 获取地址详情
		addressGroup.POST("/add", addressController.Add)                // 添加地址
		addressGroup.PUT("/update", addressController.Update)           // 修改地址
		addressGroup.POST("/delete", addressController.Delete)          // 删除地址
		addressGroup.POST("/default/:id", addressController.SetDefault) // 设置默认地址
	}
}
