package route

import (
	"wnsys/shop/app/controller/payment"

	"github.com/gin-gonic/gin"
)

// RegisterPaymentRoutes 注册支付相关路由
func RegisterPaymentRoutes(apiGroup *gin.RouterGroup) {
	paymentController := payment.NewPaymentController()

	// 支付回调路由组
	paymentGroup := apiGroup.Group("/payment")
	{
		// 微信支付回调
		paymentGroup.POST("/wx/notify", paymentController.WechatNotify)

		// 支付宝支付回调
		paymentGroup.POST("/alipay/notify", paymentController.AlipayNotify)

		// 测试回调接口
		paymentGroup.GET("/test", paymentController.TestNotify)

		// 测试微信支付回调
		paymentGroup.GET("/test/wx", paymentController.TestWechatNotify)

		// 测试支付宝支付回调
		paymentGroup.GET("/test/alipay", paymentController.TestAlipayNotify)
	}
}
