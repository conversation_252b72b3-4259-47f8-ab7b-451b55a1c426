package route

import (
	"wnsys/shop/app/controller/common"
	"wnsys/shop/app/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterFeedbackRoutes 注册反馈相关路由
func RegisterFeedbackRoutes(apiGroup *gin.RouterGroup) {
	feedbackController := common.NewFeedbackController()

	// 需要登录的反馈接口
	authGroup := apiGroup.Group("/feedback")
	authGroup.Use(middleware.JWT()) // 使用JWT认证中间件
	{
		authGroup.POST("/submit", feedbackController.SubmitFeedback) // 提交反馈
		authGroup.GET("/list", feedbackController.GetUserFeedbacks)  // 获取用户反馈列表
		authGroup.GET("/:id", feedbackController.GetFeedbackDetail)  // 获取反馈详情
	}
}
