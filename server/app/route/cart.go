package route

import (
	"wnsys/shop/app/controller/cart"
	"wnsys/shop/app/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterCartRoutes 注册购物车相关路由
func RegisterCartRoutes(rg *gin.RouterGroup) {
	cartController := cart.NewController()
	// 购物车接口都需要登录
	cartGroup := rg.Group("/cart").Use(middleware.JWT())
	{
		cartGroup.GET("/count", cartController.GetCount)          // 获取购物车商品数量
		cartGroup.POST("/add", cartController.Add)                // 加入购物车
		cartGroup.GET("/list", cartController.GetList)            // 获取购物车列表
		cartGroup.PUT("/quantity", cartController.UpdateQuantity) // 修改购物车商品数量
		cartGroup.PUT("/selected", cartController.UpdateSelected) // 修改购物车商品选中状态
		cartGroup.POST("/delete", cartController.Delete)          // 删除购物车商品
		cartGroup.POST("/clear", cartController.Clear)            // 清空购物车
	}
}
