package route

import (
	"wnsys/shop/app/controller/dining"

	"github.com/gin-gonic/gin"
)

// RegisterDiningRoutes 注册堂食外卖相关路由
func RegisterDiningRoutes(rg *gin.RouterGroup) {
	diningController := dining.NewController()
	diningGroup := rg.Group("/dining")
	{
		diningGroup.GET("/types", diningController.GetDiningTypes)   // 获取用餐类型列表
		diningGroup.GET("/tables", diningController.GetDiningTables) // 获取餐桌列表
		diningGroup.GET("/table", diningController.GetTableByNo)     // 根据餐桌编号获取餐桌信息
	}
}
