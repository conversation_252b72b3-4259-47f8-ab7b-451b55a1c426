package route

import (
	"wnsys/shop/app/controller/category"

	"github.com/gin-gonic/gin"
)

// RegisterCategoryRoutes 注册分类相关路由
func RegisterCategoryRoutes(rg *gin.RouterGroup) {
	categoryController := category.NewController()
	categoryGroup := rg.Group("/category")
	{
		categoryGroup.GET("/tree", categoryController.GetCategoryTree)   // 获取分类树形结构
		categoryGroup.GET("/goods", categoryController.GetCategoryGoods) // 获取分类商品列表
	}
}
