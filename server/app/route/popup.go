package route

import (
	"wnsys/shop/app/controller/popup"
	"wnsys/shop/app/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterPopupRoutes 注册弹窗相关路由
func RegisterPopupRoutes(r *gin.RouterGroup) {
	popupController := popup.NewPopupController()

	// 弹窗API路由组
	popupGroup := r.Group("/popup")
	{
		// 获取生效弹窗列表 - 支持可选的用户认证
		popupGroup.GET("/active", middleware.OptionalJWT(), popupController.GetActivePopups)

		// 记录弹窗操作日志
		popupGroup.POST("/log", popupController.RecordPopupLog)

		// 获取弹窗统计信息
		popupGroup.GET("/stats", popupController.GetPopupStats)
	}
}
