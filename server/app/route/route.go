package route

import (
	// 移除不再需要的 controller 导入
	// "wnsys/shop/app/controller/address"
	// "wnsys/shop/app/controller/cart"
	// "wnsys/shop/app/controller/category"
	// "wnsys/shop/app/controller/city"
	// "wnsys/shop/app/controller/comment"
	// "wnsys/shop/app/controller/goods"
	// "wnsys/shop/app/controller/home"
	// "wnsys/shop/app/controller/order"
	// "wnsys/shop/app/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterRoutes 注册所有模块的路由
func RegisterRoutes(engine *gin.Engine) {
	// API 根路由组
	apiGroup := engine.Group("/api")

	// 调用各个模块的路由注册函数
	RegisterCollectRoutes(apiGroup)  // 收藏点赞
	RegisterUserRoutes(apiGroup)     // 用户
	RegisterShareRoutes(apiGroup)    // 分销
	RegisterHomeRoutes(apiGroup)     // 首页
	RegisterDiningRoutes(apiGroup)   // 堂食外卖
	RegisterCategoryRoutes(apiGroup) // 分类
	RegisterGoodsRoutes(apiGroup)    // 商品
	RegisterCartRoutes(apiGroup)     // 购物车
	RegisterOrderRoutes(apiGroup)    // 订单
	RegisterAddressRoutes(apiGroup)  // 地址
	RegisterCityRoutes(apiGroup)     // 城市
	RegisterCommentRoutes(apiGroup)  // 评论
	RegisterPaymentRoutes(apiGroup)  // 支付回调
	RegisterConfigRoutes(apiGroup)   // 系统配置
	RegisterFeedbackRoutes(apiGroup) // 反馈系统
	RegisterRelationRoutes(apiGroup) // 用户关系
	RegisterPopupRoutes(apiGroup)    // 弹窗系统
	RegisterBeautyRoutes(apiGroup)   // 美容预约
}
