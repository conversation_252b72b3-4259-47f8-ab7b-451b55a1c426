package route

import (
	"wnsys/shop/app/controller/collect"
	"wnsys/shop/app/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterCollectRoutes 注册收藏点赞相关路由
func RegisterCollectRoutes(rg *gin.RouterGroup) {
	collectController := collect.NewController()

	// 需要认证的路由组
	collectGroup := rg.Group("/collect")
	collectGroup.Use(middleware.JWT())
	{
		// 收藏相关接口
		collectGroup.POST("/action", collectController.Collect) // 收藏/取消收藏
		collectGroup.POST("/like", collectController.Like)      // 点赞/取消点赞
	}

	// 不需要认证的路由组
	collectPublicGroup := rg.Group("/collect")
	{
		collectPublicGroup.GET("/status", collectController.GetCollectStatus)   // 获取收藏状态
		collectPublicGroup.GET("/like/status", collectController.GetLikeStatus) // 获取点赞状态
	}
}
