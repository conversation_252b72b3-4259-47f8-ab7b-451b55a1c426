package route

import (
	"wnsys/shop/app/controller/beauty"
	"wnsys/shop/app/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterBeautyRoutes 注册美容预约相关路由
func RegisterBeautyRoutes(apiGroup *gin.RouterGroup) {
	// 创建控制器实例
	homeController := beauty.NewHomeController()
	serviceController := beauty.NewServiceController()
	technicianController := beauty.NewTechnicianController()
	bookingController := beauty.NewBookingController()
	reviewController := beauty.NewReviewController()
	notificationController := beauty.NewNotificationController()
	cardController := beauty.NewCardController()

	// 美容模块路由组
	beautyGroup := apiGroup.Group("/beauty")
	{
		// 首页数据（可选身份验证，支持获取收藏状态）
		beautyGroup.GET("/home", middleware.OptionalJWT(), homeController.GetHomeData) // 获取首页数据

		// 服务相关路由
		serviceGroup := beautyGroup.Group("/service")
		{
			serviceGroup.GET("/categories", serviceController.GetServiceCategories)               // 获取服务分类列表
			serviceGroup.GET("/list", middleware.OptionalJWT(), serviceController.GetServiceList) // 获取服务列表
			serviceGroup.GET("/:id", serviceController.GetServiceDetail)                          // 获取服务详情
		}

		// 技师相关路由
		technicianGroup := beautyGroup.Group("/technician")
		{
			technicianGroup.GET("/list", technicianController.GetTechnicianList)                        // 获取技师列表
			technicianGroup.GET("/:id", technicianController.GetTechnicianDetail)                       // 获取技师详情
			technicianGroup.GET("/:id/available-time", technicianController.GetTechnicianAvailableTime) // 获取技师可用时间
		}

		// 技师设置相关路由（需要登录）
		technicianSettingsGroup := beautyGroup.Group("/technician-settings")
		technicianSettingsGroup.Use(middleware.JWT())
		{
			technicianSettingsGroup.GET("/profile", technicianController.GetTechnicianProfile)               // 获取技师个人信息
			technicianSettingsGroup.PUT("/profile", technicianController.UpdateTechnicianProfile)            // 更新技师个人信息
			technicianSettingsGroup.GET("/work-settings", technicianController.GetTechnicianWorkSettings)    // 获取工作设置
			technicianSettingsGroup.PUT("/work-settings", technicianController.UpdateTechnicianWorkSettings) // 更新工作设置

		}

		// 预约相关路由
		bookingGroup := beautyGroup.Group("/booking")
		bookingGroup.Use(middleware.JWT())
		{
			bookingGroup.POST("", bookingController.CreateBooking)      // 创建预约
			bookingGroup.GET("/list", bookingController.GetBookingList) // 获取预约列表
			// 新增：技师今日预约和统计静态路由，必须放在:id前面
			bookingGroup.GET("/technician-today", bookingController.GetTechnicianTodayBookings)    // 技师今日预约
			bookingGroup.GET("/technician-today-stats", bookingController.GetTechnicianTodayStats) // 技师今日统计
			bookingGroup.GET("/technician-date", bookingController.GetTechnicianDateBookings)      // 技师指定日期预约
			bookingGroup.GET("/technician-date-stats", bookingController.GetTechnicianDateStats)   // 技师指定日期统计
			bookingGroup.GET("/technician-list", bookingController.GetTechnicianBookingList)       // 技师全部预约列表
			bookingGroup.GET("/available-time", bookingController.GetAvailableTime)                // 获取可用时间
			bookingGroup.GET("/stats", bookingController.GetBookingStats)                          // 获取预约统计
			bookingGroup.POST("/cancel", bookingController.CancelBooking)                          // 取消预约
			bookingGroup.POST("/update-status", bookingController.UpdateBookingStatus)             // 更新预约状态
			bookingGroup.POST("/pay", bookingController.PayBeautyBooking)                          // 美容预约支付
			bookingGroup.GET("/:id", bookingController.GetBookingDetail)                           // 获取预约详情
		}

		// 评价相关路由
		reviewGroup := beautyGroup.Group("/review")
		{
			reviewGroup.GET("/technician", reviewController.GetTechnicianReviews)               // 获取技师评价列表
			reviewGroup.GET("/service", reviewController.GetServiceReviews)                     // 获取服务评价列表
			reviewGroup.GET("/technician/:id/stats", reviewController.GetTechnicianReviewStats) // 获取技师评价统计
			reviewGroup.GET("/service/:id/stats", reviewController.GetServiceReviewStats)       // 获取服务评价统计
		}

		// 通知相关路由
		notificationGroup := beautyGroup.Group("/notification")
		notificationGroup.Use(middleware.JWT())
		{
			notificationGroup.GET("/list", notificationController.GetNotificationList)                // 获取通知列表
			notificationGroup.GET("/unread-count", notificationController.GetUnreadNotificationCount) // 获取未读通知数量
			notificationGroup.POST("/read", notificationController.MarkNotificationRead)              // 标记通知为已读
		}

		// 套餐卡相关路由
		cardGroup := beautyGroup.Group("/card")
		{
			cardGroup.GET("/list", cardController.List)
			cardGroup.GET("/detail", cardController.Detail)
			cardGroup.POST("/purchase", middleware.JWT(), cardController.Purchase)
			cardGroup.POST("/pay", middleware.JWT(), cardController.Pay)
		}
	}
}
