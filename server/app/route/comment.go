package route

import (
	"wnsys/shop/app/controller/comment"
	"wnsys/shop/app/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterCommentRoutes 注册评论相关路由
func RegisterCommentRoutes(rg *gin.RouterGroup) {
	commentController := comment.NewController()
	commentGroup := rg.Group("/comment")
	{
		commentGroup.POST("/list", commentController.GetGoodsComments)                  // 获取商品评论列表 (POST? 确认一下)
		commentGroup.POST("/create", middleware.JWT(), commentController.CreateComment) // 创建评论 (需要登录)
	}
}
