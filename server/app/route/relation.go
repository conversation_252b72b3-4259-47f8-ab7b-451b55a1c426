package route

import (
	"wnsys/shop/app/controller/relation"
	"wnsys/shop/app/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterRelationRoutes 注册用户关系相关路由
func RegisterRelationRoutes(r *gin.RouterGroup) {
	relationController := relation.NewRelationController()

	// 用户关系相关路由
	relationGroup := r.Group("/relation")
	relationGroup.Use(middleware.JWT()) // 需要登录
	{
		// 通用关系管理
		relationGroup.POST("/create", relationController.CreateRelation)             // 创建关系
		relationGroup.GET("/list", relationController.GetRelations)                  // 获取关系列表
		relationGroup.GET("/check", relationController.CheckRelation)                // 检查关系
		relationGroup.DELETE("/:related_user_id", relationController.DeleteRelation) // 删除关系

		// 邀请/分销相关
		relationGroup.GET("/invite/tree", relationController.GetInviteTree) // 获取邀请树
		relationGroup.GET("/team/count", relationController.GetTeamCount)   // 获取团队人数
		relationGroup.GET("/ancestors", relationController.GetAncestors)    // 获取上级链

		// 好友相关
		relationGroup.POST("/friend/add", relationController.AddFriend) // 添加好友

		// 关注相关
		relationGroup.POST("/follow", relationController.FollowUser) // 关注用户
	}
}
