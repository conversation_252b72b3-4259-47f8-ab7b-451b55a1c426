package dto

// OrderListRequest 订单列表请求
type OrderListRequest struct {
	BusinessType string `form:"business_type" json:"business_type"`
	Status       string `form:"status" json:"status"`
	DateFrom     string `form:"date_from" json:"date_from"`
	DateTo       string `form:"date_to" json:"date_to"`
	Page         int    `form:"page" json:"page"`
	PageSize     int    `form:"page_size" json:"page_size"`
}

// OrderListResponse 订单列表响应
type OrderListResponse struct {
	Total int64       `json:"total"`
	Page  int         `json:"page"`
	Size  int         `json:"size"`
	List  []OrderItem `json:"list"`
}

// OrderItem 订单列表项
type OrderItem struct {
	ID             int64                  `json:"id"`
	OrderNo        string                 `json:"order_no"`
	BusinessType   string                 `json:"business_type"`
	BusinessID     *int64                 `json:"business_id"`
	TotalAmount    float64                `json:"total_amount"`
	PayAmount      float64                `json:"pay_amount"`
	Status         string                 `json:"status"`
	CreateTime     string                 `json:"create_time"`
	PayTime        *string                `json:"pay_time,omitempty"`
	BusinessDetail map[string]interface{} `json:"business_detail,omitempty"`
}

// OrderDetailResponse 订单详情响应
type OrderDetailResponse struct {
	ID             int64                  `json:"id"`
	OrderNo        string                 `json:"order_no"`
	BusinessType   string                 `json:"business_type"`
	BusinessID     *int64                 `json:"business_id"`
	TotalAmount    float64                `json:"total_amount"`
	PayAmount      float64                `json:"pay_amount"`
	Status         string                 `json:"status"`
	CreateTime     string                 `json:"create_time"`
	UpdateTime     string                 `json:"update_time"`
	PayTime        *string                `json:"pay_time,omitempty"`
	BusinessDetail map[string]interface{} `json:"business_detail,omitempty"`
	Payment        *PaymentInfo           `json:"payment,omitempty"`
}

// PaymentInfo 支付信息
type PaymentInfo struct {
	PayType    int8    `json:"pay_type"`
	PayAmount  float64 `json:"pay_amount"`
	PayStatus  int8    `json:"pay_status"`
	TradeNo    string  `json:"trade_no"`
	PayTime    *string `json:"pay_time,omitempty"`
	NotifyTime *string `json:"notify_time,omitempty"`
}

// 订单统计
// OrderStatsResponse 订单统计响应
type OrderStatsResponse struct {
	TotalOrders       int64              `json:"total_orders"`
	PaidOrders        int64              `json:"paid_orders"`
	UnpaidOrders      int64              `json:"unpaid_orders"`
	TotalAmount       float64            `json:"total_amount"`
	BusinessTypeStats []BusinessTypeStat `json:"business_type_stats"`
}

// BusinessTypeStat 按业务类型统计
type BusinessTypeStat struct {
	BusinessType string  `json:"business_type"`
	Count        int64   `json:"count"`
	Amount       float64 `json:"amount"`
}
