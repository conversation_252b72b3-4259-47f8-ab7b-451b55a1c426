package dto

// BeautyServiceListRequest 服务列表请求
type BeautyServiceListRequest struct {
	CategoryID int    `form:"category_id" json:"category_id"`
	Keyword    string `form:"keyword" json:"keyword"`
	Page       int    `form:"page" json:"page" binding:"required,min=1"`
	PageSize   int    `form:"page_size" json:"page_size" binding:"required,min=1,max=100"`
	Sort       string `form:"sort" json:"sort"` // price_asc, price_desc, rating_desc, booking_desc
	IsHot      int    `form:"is_hot" json:"is_hot"`
	IsNew      int    `form:"is_new" json:"is_new"`
}

// BeautyServiceListResponse 服务列表响应
type BeautyServiceListResponse struct {
	List  []BeautyServiceItem `json:"list"`
	Total int64               `json:"total"`
	Page  int                 `json:"page"`
	Size  int                 `json:"size"`
}

// BeautyServiceItem 服务项
type BeautyServiceItem struct {
	ID            uint     `json:"id"`
	CategoryID    uint     `json:"category_id"`
	CategoryName  string   `json:"category_name"`
	Name          string   `json:"name"`
	Subtitle      string   `json:"subtitle"`
	Description   string   `json:"description"`
	Images        []string `json:"images"`
	Price         float64  `json:"price"`
	OriginalPrice float64  `json:"original_price"`
	Duration      int      `json:"duration"`
	Tags          []string `json:"tags"`
	BookingCount  int      `json:"booking_count"`
	RatingAvg     float64  `json:"rating_avg"`
	RatingCount   int      `json:"rating_count"`
	IsHot         bool     `json:"is_hot"`
	IsNew         bool     `json:"is_new"`
	IsCollected   bool     `json:"is_collected"` // 是否已收藏
}

// CategoryItem 分类项
type CategoryItem struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
	Icon string `json:"icon"`
}

// BeautyServiceDetailResponse 服务详情响应
type BeautyServiceDetailResponse struct {
	ID                   uint             `json:"id"`
	CategoryID           uint             `json:"category_id"`
	CategoryName         string           `json:"category_name"`
	Name                 string           `json:"name"`
	Subtitle             string           `json:"subtitle"`
	Description          string           `json:"description"`
	Images               []string         `json:"images"`
	Price                float64          `json:"price"`
	OriginalPrice        float64          `json:"original_price"`
	Duration             int              `json:"duration"`
	NeedTechnician       bool             `json:"need_technician"`
	MaxAdvanceDays       int              `json:"max_advance_days"`
	MinAdvanceHours      int              `json:"min_advance_hours"`
	AllowCancelHours     int              `json:"allow_cancel_hours"`
	GenderLimit          int              `json:"gender_limit"`
	AgeMin               int              `json:"age_min"`
	AgeMax               int              `json:"age_max"`
	Tags                 []string         `json:"tags"`
	Contraindications    string           `json:"contraindications"`
	PreparationNotes     string           `json:"preparation_notes"`
	BookingCount         int              `json:"booking_count"`
	RatingAvg            float64          `json:"rating_avg"`
	RatingCount          int              `json:"rating_count"`
	IsHot                bool             `json:"is_hot"`
	IsNew                bool             `json:"is_new"`
	AvailableTechnicians []TechnicianItem `json:"available_technicians"`
}

// TechnicianItem 技师项
type TechnicianItem struct {
	ID           uint     `json:"id"`
	UserID       uint     `json:"user_id"` // 用户ID，用于技师详情查询
	Name         string   `json:"name"`
	Level        string   `json:"level"`
	Experience   int      `json:"experience"`
	Specialties  []string `json:"specialties"`
	Introduction string   `json:"introduction"`
	RatingAvg    float64  `json:"rating_avg"`
	RatingCount  int      `json:"rating_count"`
	ExtraFee     float64  `json:"extra_fee"`
	IsFeatured   bool     `json:"is_featured"`
	Avatar       string   `json:"avatar"` // 技师头像
}
