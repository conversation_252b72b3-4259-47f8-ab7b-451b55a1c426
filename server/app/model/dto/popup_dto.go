package dto

import "time"

// PopupCreateReq 创建弹窗请求
type PopupCreateReq struct {
	Title        string            `json:"title" binding:"max=100"`                                   // 弹窗标题
	Content      string            `json:"content" binding:"required,max=1000"`                       // 弹窗内容
	TitleStyle   *PopupTitleStyle  `json:"titleStyle"`                                                // 标题样式
	Buttons      []PopupButton     `json:"buttons" binding:"required,min=1,max=5"`                    // 按钮配置
	TriggerType  string            `json:"triggerType" binding:"required,oneof=manual auto schedule"` // 触发类型
	TriggerRule  *PopupTriggerRule `json:"triggerRule"`                                               // 触发规则
	Priority     int               `json:"priority" binding:"min=0,max=100"`                          // 优先级
	AutoClose    int               `json:"autoClose" binding:"min=0,max=300000"`                      // 自动关闭时间(毫秒)
	Delay        int               `json:"delay" binding:"min=0,max=60000"`                           // 延迟显示时间(毫秒)
	MaskClosable bool              `json:"maskClosable"`                                              // 点击遮罩是否关闭
	StartTime    *time.Time        `json:"startTime"`                                                 // 开始时间
	EndTime      *time.Time        `json:"endTime"`                                                   // 结束时间
	Status       int               `json:"status" binding:"oneof=0 1"`                                // 状态
}

// PopupUpdateReq 更新弹窗请求
type PopupUpdateReq struct {
	ID           int64             `json:"id" binding:"required,min=1"`                               // 弹窗ID
	Title        string            `json:"title" binding:"max=100"`                                   // 弹窗标题
	Content      string            `json:"content" binding:"required,max=1000"`                       // 弹窗内容
	TitleStyle   *PopupTitleStyle  `json:"titleStyle"`                                                // 标题样式
	Buttons      []PopupButton     `json:"buttons" binding:"required,min=1,max=5"`                    // 按钮配置
	TriggerType  string            `json:"triggerType" binding:"required,oneof=manual auto schedule"` // 触发类型
	TriggerRule  *PopupTriggerRule `json:"triggerRule"`                                               // 触发规则
	Priority     int               `json:"priority" binding:"min=0,max=100"`                          // 优先级
	AutoClose    int               `json:"autoClose" binding:"min=0,max=300000"`                      // 自动关闭时间(毫秒)
	Delay        int               `json:"delay" binding:"min=0,max=60000"`                           // 延迟显示时间(毫秒)
	MaskClosable bool              `json:"maskClosable"`                                              // 点击遮罩是否关闭
	StartTime    *time.Time        `json:"startTime"`                                                 // 开始时间
	EndTime      *time.Time        `json:"endTime"`                                                   // 结束时间
	Status       int               `json:"status" binding:"oneof=0 1"`                                // 状态
}

// PopupListReq 弹窗列表请求
type PopupListReq struct {
	Page        int    `json:"page" form:"page" binding:"min=1"`                   // 页码
	PageSize    int    `json:"pageSize" form:"pageSize" binding:"min=1,max=100"`   // 每页数量
	Title       string `json:"title" form:"title"`                                 // 标题搜索
	TriggerType string `json:"triggerType" form:"triggerType"`                     // 触发类型
	Status      *int   `json:"status" form:"status" binding:"omitempty,oneof=0 1"` // 状态
}

// PopupActiveListReq 获取生效弹窗列表请求
type PopupActiveListReq struct {
	UserID int64 `json:"userId"` // 用户ID，0表示未登录用户
}

// PopupLogReq 弹窗日志记录请求
type PopupLogReq struct {
	PopupID    int64  `json:"popupId" binding:"required,min=1"`                 // 弹窗ID
	UserID     int64  `json:"userId" binding:"required,min=1"`                  // 用户ID
	Action     string `json:"action" binding:"required,oneof=show click close"` // 操作类型
	ButtonText string `json:"buttonText"`                                       // 点击的按钮文字
}

// PopupTitleStyle 弹窗标题样式
type PopupTitleStyle struct {
	Color           string `json:"color"`           // 文字颜色
	BackgroundImage string `json:"backgroundImage"` // 背景图片
	BackgroundColor string `json:"backgroundColor"` // 背景颜色
	BackgroundSize  string `json:"backgroundSize"`  // 背景尺寸
	FontSize        string `json:"fontSize"`        // 字体大小
	FontWeight      string `json:"fontWeight"`      // 字体粗细
	TextAlign       string `json:"textAlign"`       // 文字对齐
}

// PopupButton 弹窗按钮配置
type PopupButton struct {
	Text  string                 `json:"text" binding:"required,max=20"`                              // 按钮文字
	Type  string                 `json:"type" binding:"oneof=default primary success warning danger"` // 按钮类型
	Style map[string]interface{} `json:"style"`                                                       // 自定义样式
}

// PopupTriggerRule 弹窗触发规则
type PopupTriggerRule struct {
	// 自动触发规则
	Pages         []string `json:"pages"`         // 触发页面列表
	UserTypes     []string `json:"userTypes"`     // 用户类型：new/old/vip等
	VisitCount    *int     `json:"visitCount"`    // 访问次数条件
	RegisterDays  *int     `json:"registerDays"`  // 注册天数条件
	LastLoginDays *int     `json:"lastLoginDays"` // 最后登录天数条件

	// 定时触发规则
	ScheduleType string `json:"scheduleType"` // 定时类型：once/daily/weekly/monthly
	ScheduleTime string `json:"scheduleTime"` // 定时时间
	ScheduleDays []int  `json:"scheduleDays"` // 定时日期（周几或几号）

	// 频次控制
	MaxShowTimes *int `json:"maxShowTimes"` // 最大显示次数
	ShowInterval *int `json:"showInterval"` // 显示间隔(小时)
}

// PopupResp 弹窗响应
type PopupResp struct {
	ID           int64             `json:"id"`           // 弹窗ID
	Title        string            `json:"title"`        // 弹窗标题
	Content      string            `json:"content"`      // 弹窗内容
	TitleStyle   *PopupTitleStyle  `json:"titleStyle"`   // 标题样式
	Buttons      []PopupButton     `json:"buttons"`      // 按钮配置
	TriggerType  string            `json:"triggerType"`  // 触发类型
	TriggerRule  *PopupTriggerRule `json:"triggerRule"`  // 触发规则
	Priority     int               `json:"priority"`     // 优先级
	AutoClose    int               `json:"autoClose"`    // 自动关闭时间
	Delay        int               `json:"delay"`        // 延迟时间
	MaskClosable bool              `json:"maskClosable"` // 遮罩关闭
	StartTime    *time.Time        `json:"startTime"`    // 开始时间
	EndTime      *time.Time        `json:"endTime"`      // 结束时间
	Status       int               `json:"status"`       // 状态
	ShowCount    int64             `json:"showCount"`    // 显示次数
	ClickCount   int64             `json:"clickCount"`   // 点击次数
	CreateTime   time.Time         `json:"createTime"`   // 创建时间
	UpdateTime   time.Time         `json:"updateTime"`   // 更新时间
}

// PopupListResp 弹窗列表响应
type PopupListResp struct {
	List     []PopupResp `json:"list"`     // 弹窗列表
	Total    int64       `json:"total"`    // 总数
	Page     int         `json:"page"`     // 当前页
	PageSize int         `json:"pageSize"` // 每页数量
}

// PopupActiveResp 生效弹窗响应（前端使用）
type PopupActiveResp struct {
	ID           string           `json:"id"`           // 弹窗ID（字符串格式）
	Title        string           `json:"title"`        // 弹窗标题
	Content      string           `json:"content"`      // 弹窗内容
	TitleStyle   *PopupTitleStyle `json:"titleStyle"`   // 标题样式
	Buttons      []PopupButton    `json:"buttons"`      // 按钮配置
	Priority     int              `json:"priority"`     // 优先级
	AutoClose    int              `json:"autoClose"`    // 自动关闭时间
	Delay        int              `json:"delay"`        // 延迟时间
	MaskClosable bool             `json:"maskClosable"` // 遮罩关闭
}

// PopupStatsResp 弹窗统计响应
type PopupStatsResp struct {
	TotalPopups  int64   `json:"totalPopups"`  // 总弹窗数
	ActivePopups int64   `json:"activePopups"` // 生效弹窗数
	TotalShows   int64   `json:"totalShows"`   // 总显示次数
	TotalClicks  int64   `json:"totalClicks"`  // 总点击次数
	ClickRate    float64 `json:"clickRate"`    // 点击率
}
