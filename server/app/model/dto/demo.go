package dto

// DemoQuery 查询参数
type DemoQuery struct {
	PageNum  int    `form:"pageNum" binding:"required,min=1"`          // 页码
	PageSize int    `form:"pageSize" binding:"required,min=1,max=100"` // 每页数量
	Keyword  string `form:"keyword"`                                   // 关键字
	Status   string `form:"status" binding:"omitempty,oneof=0 1"`      // 状态(0-正常 1-停用)
}

// CreateDemoReq 创建请求
type CreateDemoReq struct {
	Name   string `json:"name" binding:"required,min=2,max=30"` // 名称
	Status string `json:"status" binding:"omitempty,oneof=0 1"` // 状态(0-正常 1-停用)
}

// UpdateDemoReq 更新请求
type UpdateDemoReq struct {
	ID     int64  `json:"id" binding:"required,min=1"`          // ID
	Name   string `json:"name" binding:"required,min=2,max=30"` // 名称
	Status string `json:"status" binding:"omitempty,oneof=0 1"` // 状态(0-正常 1-停用)
}

// DemoInfoResp 信息响应
type DemoInfoResp struct {
	ID         int64  `json:"id"`         // ID
	Name       string `json:"name"`       // 名称
	Status     string `json:"status"`     // 状态
	CreateTime string `json:"createTime"` // 创建时间
	CreateBy   string `json:"createBy"`   // 创建者
	UpdateTime string `json:"updateTime"` // 更新时间
	UpdateBy   string `json:"updateBy"`   // 更新者
}

// DemoListResp 列表响应
type DemoListResp struct {
	Total int64          `json:"total"` // 总数
	Rows  []DemoInfoResp `json:"rows"`  // 数据列表
}
