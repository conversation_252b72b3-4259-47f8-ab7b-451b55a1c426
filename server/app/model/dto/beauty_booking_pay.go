package dto

// BeautyBookingPayRequest 美容预约支付请求
type BeautyBookingPayRequest struct {
	BookingID int64 `json:"booking_id" binding:"required"` // 预约ID
	PayType   int   `json:"pay_type" binding:"required"`   // 支付方式：1-微信 2-支付宝 3-余额
}

// BeautyBookingPayResponse 美容预约支付响应
type BeautyBookingPayResponse struct {
	Success bool                   `json:"success"` // 支付是否成功
	Data    map[string]interface{} `json:"data"`    // 支付数据（第三方支付时使用）
	Message string                 `json:"message"` // 支付结果消息
}
