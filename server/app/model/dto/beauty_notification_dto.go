package dto

import "time"

// BeautyNotificationListRequest 通知列表请求
type BeautyNotificationListRequest struct {
	Page     int    `form:"page" json:"page" binding:"required,min=1"`
	PageSize int    `form:"pageSize" json:"pageSize" binding:"required,min=1,max=100"`
	Type     string `form:"type" json:"type"`     // 通知类型：system, booking, promotion
	IsRead   *bool  `form:"isRead" json:"isRead"` // 是否已读
}

// BeautyNotificationListResponse 通知列表响应
type BeautyNotificationListResponse struct {
	List  []BeautyNotificationItem `json:"list"`
	Total int64                    `json:"total"`
	Page  int                      `json:"page"`
	Size  int                      `json:"size"`
}

// BeautyNotificationItem 通知项
type BeautyNotificationItem struct {
	ID         uint      `json:"id"`
	Title      string    `json:"title"`
	Content    string    `json:"content"`
	Type       string    `json:"type"`
	IsRead     bool      `json:"is_read"`
	CreateTime time.Time `json:"create_time"`
}

// BeautyNotificationReadRequest 标记通知已读请求
type BeautyNotificationReadRequest struct {
	ID uint `json:"id" binding:"required"`
}

// BeautyNotificationUnreadCountResponse 未读通知数量响应
type BeautyNotificationUnreadCountResponse struct {
	Count int `json:"count"`
}
