package dto

import "time"

// BeautyTechnicianReviewListRequest 技师评价列表请求
// TechnicianUserID 字段专门存储技师user_id
type BeautyTechnicianReviewListRequest struct {
	TechnicianUserID uint `form:"technician_user_id" json:"technician_user_id" binding:"required"`
	Page             int  `form:"page" json:"page" binding:"required,min=1"`
	PageSize         int  `form:"page_size" json:"page_size" binding:"required,min=1,max=100"`
}

// BeautyTechnicianReviewListResponse 技师评价列表响应
type BeautyTechnicianReviewListResponse struct {
	List  []BeautyReviewItem `json:"list"`
	Total int64              `json:"total"`
	Page  int                `json:"page"`
	Size  int                `json:"size"`
}

// BeautyServiceReviewListRequest 服务评价列表请求
type BeautyServiceReviewListRequest struct {
	ServiceID uint `form:"service_id" json:"service_id" binding:"required"`
	Page      int  `form:"page" json:"page" binding:"required,min=1"`
	PageSize  int  `form:"page_size" json:"page_size" binding:"required,min=1,max=100"`
}

// BeautyServiceReviewListResponse 服务评价列表响应
type BeautyServiceReviewListResponse struct {
	List  []BeautyReviewItem `json:"list"`
	Total int64              `json:"total"`
	Page  int                `json:"page"`
	Size  int                `json:"size"`
}

// BeautyReviewItem 评价项
type BeautyReviewItem struct {
	ID               uint      `json:"id"`
	BookingID        uint      `json:"booking_id"`
	BookingNo        string    `json:"booking_no"`
	UserID           uint      `json:"user_id"`
	UserNickname     string    `json:"user_nickname"`
	UserAvatar       string    `json:"user_avatar"`
	ServiceID        uint      `json:"service_id"`
	ServiceName      string    `json:"service_name"`
	TechnicianUserID uint      `json:"technician_user_id"`
	Rating           int       `json:"rating"`
	Review           string    `json:"review"`
	ReviewImages     []string  `json:"review_images"`
	ReviewTime       time.Time `json:"review_time"`
	BookingDate      string    `json:"booking_date"`
}

// BeautyReviewStatsResponse 评价统计响应
type BeautyReviewStatsResponse struct {
	TotalCount    int64   `json:"total_count"`
	AverageRating float64 `json:"average_rating"`
	Rating5Count  int64   `json:"rating_5_count"`
	Rating4Count  int64   `json:"rating_4_count"`
	Rating3Count  int64   `json:"rating_3_count"`
	Rating2Count  int64   `json:"rating_2_count"`
	Rating1Count  int64   `json:"rating_1_count"`
}
