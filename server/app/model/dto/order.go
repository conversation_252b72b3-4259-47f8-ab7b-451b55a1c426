package dto

// OrderCalculateReq 订单计算请求
type OrderCalculateReq struct {
	Goods []OrderGoodsItem `json:"goods"`
}

// OrderGoodsItem 订单商品项
type OrderGoodsItem struct {
	GoodsId int64  `json:"goodsId"`
	SkuCode string `json:"skuCode"`
	Num     int    `json:"num"`
}

// OrderCalculateResp 订单计算响应
type OrderCalculateResp struct {
	TotalAmount    float64 `json:"totalAmount"`    // 商品总金额
	FreightAmount  float64 `json:"freightAmount"`  // 运费金额
	DiscountAmount float64 `json:"discountAmount"` // 优惠金额
	PayAmount      float64 `json:"payAmount"`      // 实付金额
}

// CreateOrderReq 创建订单请求
type CreateOrderReq struct {
	ReceiverName     string           `json:"receiverName"`                           // 收货人姓名
	ReceiverPhone    string           `json:"receiverPhone"`                          // 收货人电话
	ReceiverProvince string           `json:"receiverProvince"`                       // 省份
	ReceiverCity     string           `json:"receiverCity"`                           // 城市
	ReceiverDistrict string           `json:"receiverDistrict"`                       // 区县
	ReceiverAddress  string           `json:"receiverAddress"`                        // 详细地址
	PayType          int              `json:"payType"`                                // 支付方式(1:微信 2:支付宝 3:余额)
	Remark           string           `json:"remark"`                                 // 备注
	Goods            []OrderGoodsItem `json:"goods"`                                  // 商品列表
	Type             string           `json:"type" binding:"required,oneof=cart buy"` // 订单来源(cart:购物车 buy:直接购买)
	DiningType       string           `json:"diningType"`                             // 用餐类型(dine_in:堂食 takeout:外卖)
	TableID          *int64           `json:"tableId"`                                // 餐桌ID(堂食时使用)
	TableNo          string           `json:"tableNo"`                                // 餐桌编号(堂食时使用)
}

// CreateOrderResp 创建订单响应
type CreateOrderResp struct {
	OrderId   int64   `json:"orderId"`   // 订单ID
	OrderNo   string  `json:"orderNo"`   // 订单编号
	PayAmount float64 `json:"payAmount"` // 支付金额
}

// PayOrderReq 支付订单请求
type PayOrderReq struct {
	OrderId    int64  `json:"orderId" binding:"required"` // 订单ID
	PayType    int    `json:"payType" binding:"required"` // 支付方式(1:微信 2:支付宝 3:余额)
	WechatCode string `json:"wechatCode"`                 // 微信登录凭证(微信支付时必填)
}
