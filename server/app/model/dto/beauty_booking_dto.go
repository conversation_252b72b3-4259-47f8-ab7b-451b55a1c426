package dto

import "time"

// BeautyBookingCreateRequest 创建预约请求
// TechnicianUserID 字段专门存储技师user_id
type BeautyBookingCreateRequest struct {
	ServiceID        uint   `json:"service_id" binding:"required"`
	TechnicianUserID uint   `json:"technician_user_id"` // 即user_id
	BookingDate      string `json:"booking_date" binding:"required"`
	StartTime        string `json:"start_time" binding:"required"`

	// 客户信息
	ContactName    string `json:"contact_name" binding:"required"`
	ContactPhone   string `json:"contact_phone" binding:"required"`
	CustomerGender int    `json:"customer_gender"`
	CustomerAge    int    `json:"customer_age"`

	// 个性化需求
	SkinType        string   `json:"skin_type"`
	SkinConcerns    []string `json:"skin_concerns"`
	Allergies       string   `json:"allergies"`
	SpecialRequests string   `json:"special_requests"`

	// 优惠信息
	CouponID uint `json:"coupon_id"`
}

// BeautyBookingCreateResponse 创建预约响应
type BeautyBookingCreateResponse struct {
	ID           uint    `json:"id"`
	BookingNo    string  `json:"booking_no"`
	ServicePrice float64 `json:"service_price"`
	FinalPrice   float64 `json:"final_price"`
	BookingDate  string  `json:"booking_date"`
	StartTime    string  `json:"start_time"`
	EndTime      string  `json:"end_time"`
	Status       string  `json:"status"`
	OrderID      int64   `json:"order_id"` // 统一订单ID
	OrderNo      string  `json:"order_no"` // 统一订单号
}

// BeautyBookingListRequest 预约列表请求
type BeautyBookingListRequest struct {
	Page     int    `form:"page" json:"page" binding:"required,min=1"`
	PageSize int    `form:"page_size" json:"page_size" binding:"required,min=1,max=100"`
	Status   string `form:"status" json:"status"`
	DateFrom string `form:"date_from" json:"date_from"`
	DateTo   string `form:"date_to" json:"date_to"`
}

// BeautyBookingListResponse 预约列表响应
type BeautyBookingListResponse struct {
	List  []BeautyBookingItem `json:"list"`
	Total int64               `json:"total"`
	Page  int                 `json:"page"`
	Size  int                 `json:"size"`
}

// BeautyBookingItem 预约项
type BeautyBookingItem struct {
	ID             uint    `json:"id"`
	BookingNo      string  `json:"booking_no"`
	ServiceID      uint    `json:"service_id"` // 服务ID，用于重新预约
	ServiceName    string  `json:"service_name"`
	ServiceImage   string  `json:"service_image"`
	TechnicianName string  `json:"technician_name"`
	BookingDate    string  `json:"booking_date"`
	StartTime      string  `json:"start_time"`
	EndTime        string  `json:"end_time"`
	Duration       int     `json:"duration"`
	FinalPrice     float64 `json:"final_price"`
	BookingStatus  string  `json:"booking_status"`
	PaymentStatus  string  `json:"payment_status"`
	ServiceStatus  string  `json:"service_status"`
	CanCancel      bool    `json:"can_cancel"`
	CanReschedule  bool    `json:"can_reschedule"`
	CreateTime     string  `json:"create_time"`
}

// BeautyBookingDetailResponse 预约详情响应
// TechnicianUserID 字段即为user_id
type BeautyBookingDetailResponse struct {
	ID        uint   `json:"id"`
	BookingNo string `json:"booking_no"`

	// 服务信息
	ServiceID    uint     `json:"service_id"`
	ServiceName  string   `json:"service_name"`
	ServiceImage string   `json:"service_image"`
	ServiceDesc  string   `json:"service_desc"`
	Duration     int      `json:"duration"`
	Tags         []string `json:"tags"`

	// 技师信息
	TechnicianUserID uint   `json:"technician_user_id"` // 即user_id
	TechnicianName   string `json:"technician_name"`
	TechnicianLevel  string `json:"technician_level"`
	TechnicianPhone  string `json:"technician_phone"`

	// 预约时间
	BookingDate string `json:"booking_date"`
	StartTime   string `json:"start_time"`
	EndTime     string `json:"end_time"`

	// 客户信息
	ContactName    string `json:"contact_name"`
	ContactPhone   string `json:"contact_phone"`
	CustomerGender int    `json:"customer_gender"`
	CustomerAge    int    `json:"customer_age"`

	// 个性化需求
	SkinType        string   `json:"skin_type"`
	SkinConcerns    []string `json:"skin_concerns"`
	Allergies       string   `json:"allergies"`
	SpecialRequests string   `json:"special_requests"`

	// 价格信息
	ServicePrice   float64 `json:"service_price"`
	TechnicianFee  float64 `json:"technician_fee"`
	DiscountAmount float64 `json:"discount_amount"`
	CouponAmount   float64 `json:"coupon_amount"`
	FinalPrice     float64 `json:"final_price"`

	// 状态信息
	BookingStatus string `json:"booking_status"`
	PaymentStatus string `json:"payment_status"`
	ServiceStatus string `json:"service_status"`

	// 时间记录
	ConfirmedTime    *time.Time `json:"confirmed_time"`
	CheckinTime      *time.Time `json:"checkin_time"`
	ServiceStartTime *time.Time `json:"service_start_time"`
	ServiceEndTime   *time.Time `json:"service_end_time"`
	CancelledTime    *time.Time `json:"cancelled_time"`
	CancelReason     string     `json:"cancel_reason"`

	// 评价信息
	Rating       int        `json:"rating"`
	Review       string     `json:"review"`
	ReviewImages []string   `json:"review_images"`
	ReviewTime   *time.Time `json:"review_time"`

	// 操作权限
	CanCancel     bool `json:"can_cancel"`
	CanReschedule bool `json:"can_reschedule"`
	CanReview     bool `json:"can_review"`
	CanPay        bool `json:"can_pay"`

	CreateTime string `json:"create_time"`
}

// BeautyBookingCancelRequest 取消预约请求
type BeautyBookingCancelRequest struct {
	ID           uint   `json:"id" binding:"required"`
	CancelReason string `json:"cancel_reason" binding:"required"`
}

// BeautyBookingUpdateStatusRequest 预约状态更新请求
type BeautyBookingUpdateStatusRequest struct {
	ID     uint   `json:"id" binding:"required"`
	Status string `json:"status" binding:"required,oneof=pending confirmed in_service completed cancelled"`
}

// BeautyBookingRescheduleRequest 改期预约请求
type BeautyBookingRescheduleRequest struct {
	ID          uint   `json:"id" binding:"required"`
	BookingDate string `json:"booking_date" binding:"required"`
	StartTime   string `json:"start_time" binding:"required"`
	Reason      string `json:"reason"`
}

// BeautyAvailableTimeRequest 查询可用时间请求
type BeautyAvailableTimeRequest struct {
	ServiceID        uint   `form:"service_id" json:"service_id" binding:"required"`
	TechnicianUserID uint   `form:"technician_id" json:"technician_id"`
	Date             string `form:"date" json:"date" binding:"required"`
}

// BeautyAvailableTimeResponse 可用时间响应
type BeautyAvailableTimeResponse struct {
	Date           string     `json:"date"`
	AvailableSlots []TimeSlot `json:"available_slots"`
}

// TimeSlot 时间段
type TimeSlot struct {
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
	Available bool   `json:"available"`
	Reason    string `json:"reason,omitempty"`
}

// BeautyBookingStatsResponse 预约统计响应
type BeautyBookingStatsResponse struct {
	Pending   int `json:"pending"`   // 待确认
	Confirmed int `json:"confirmed"` // 已确认
	InService int `json:"inService"` // 服务中
	Completed int `json:"completed"` // 已完成
}

// TechnicianBookingListRequest 技师全部预约列表请求
type TechnicianBookingListRequest struct {
	Page     int    `form:"page" json:"page" binding:"min=1"`
	PageSize int    `form:"pageSize" json:"pageSize" binding:"min=1,max=100"`
	DateFrom string `form:"date_from" json:"date_from"`
	DateTo   string `form:"date_to" json:"date_to"`
	Status   string `form:"status" json:"status"`
}

// TechnicianBookingListResponse 技师全部预约列表响应
type TechnicianBookingListResponse struct {
	List  []BeautyBookingItem `json:"list"`
	Total int64               `json:"total"`
	Page  int                 `json:"page"`
	Size  int                 `json:"size"`
}
