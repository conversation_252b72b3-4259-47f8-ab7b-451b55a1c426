package dto

// CommonCollectReq 通用收藏请求
type CommonCollectReq struct {
	TargetID   int64  `json:"targetId" binding:"required"`   // 目标ID（商品ID、活动ID等）
	TargetType string `json:"targetType" binding:"required"` // 目标类型（goods、activity等）
	Action     int    `json:"action" binding:"min=0,max=1"`  // 操作类型（1：收藏，0：取消收藏）
}

// CommonLikeReq 通用点赞请求
type CommonLikeReq struct {
	TargetID   int64  `json:"targetId" binding:"required"`   // 目标ID
	TargetType string `json:"targetType" binding:"required"` // 目标类型
	Action     int    `json:"action" binding:"min=0,max=1"`  // 操作类型（1：点赞，0：取消点赞）
}

// CollectStatusResp 收藏状态响应
type CollectStatusResp struct {
	IsCollected bool  `json:"isCollected"` // 是否已收藏
	Count       int64 `json:"count"`       // 收藏总数
}

// LikeStatusResp 点赞状态响应
type LikeStatusResp struct {
	IsLiked bool  `json:"isLiked"` // 是否已点赞
	Count   int64 `json:"count"`   // 点赞总数
}
