package dto

import "time"

// ShareIncomeListReq 收益记录列表请求
type ShareIncomeListReq struct {
	PageReq
	CommissionType   string `json:"commission_type,omitempty"`   // 佣金类型筛选
	CommissionStatus *int   `json:"commission_status,omitempty"` // 佣金状态筛选
	StartTime        string `json:"start_time,omitempty"`        // 开始时间
	EndTime          string `json:"end_time,omitempty"`          // 结束时间
}

// ShareIncomeResp 收益记录响应
type ShareIncomeResp struct {
	ID               int64      `json:"id"`                        // 记录ID
	ReferrerId       *int64     `json:"referrer_id,omitempty"`     // 推荐官ID
	UserId           int64      `json:"user_id"`                   // 推荐官用户ID
	InviteeId        *int64     `json:"invitee_id,omitempty"`      // 被邀请人ID
	InviteeName      string     `json:"invitee_name,omitempty"`    // 被邀请人姓名
	CommissionAmount float64    `json:"commission_amount"`         // 佣金金额
	CommissionType   string     `json:"commission_type"`           // 佣金类型
	CommissionStatus int        `json:"commission_status"`         // 佣金状态
	SourceOrderNo    string     `json:"source_order_no,omitempty"` // 来源订单号
	OrderAmount      float64    `json:"order_amount"`              // 订单金额
	CommissionRate   float64    `json:"commission_rate"`           // 佣金比例
	Level            int        `json:"level"`                     // 分销层级
	UnfreezeTime     *time.Time `json:"unfreeze_time,omitempty"`   // 解冻时间
	ProcessTime      *time.Time `json:"process_time,omitempty"`    // 处理时间
	Remark           string     `json:"remark,omitempty"`          // 备注
	CreateTime       time.Time  `json:"create_time"`               // 创建时间
	UpdateTime       time.Time  `json:"update_time"`               // 更新时间
}

// ShareIncomeStatsResp 收益统计响应
type ShareIncomeStatsResp struct {
	TotalIncome      float64 `json:"total_income"`      // 累计收益
	AvailableBalance float64 `json:"available_balance"` // 可用余额
	FrozenBalance    float64 `json:"frozen_balance"`    // 冻结余额
	TodayIncome      float64 `json:"today_income"`      // 今日收益
	MonthIncome      float64 `json:"month_income"`      // 本月收益
	PendingCount     int64   `json:"pending_count"`     // 待结算数量
	SettledCount     int64   `json:"settled_count"`     // 已结算数量
}

// ShareIncomeCreateReq 创建收益记录请求
type ShareIncomeCreateReq struct {
	ReferrerId       *int64  `json:"referrer_id,omitempty"`                     // 推荐官ID
	UserId           int64   `json:"user_id" binding:"required"`                // 推荐官用户ID
	InviteeId        *int64  `json:"invitee_id,omitempty"`                      // 被邀请人ID
	InviteeName      string  `json:"invitee_name,omitempty"`                    // 被邀请人姓名
	CommissionAmount float64 `json:"commission_amount" binding:"required,gt=0"` // 佣金金额
	CommissionType   string  `json:"commission_type" binding:"required"`        // 佣金类型
	SourceOrderNo    string  `json:"source_order_no,omitempty"`                 // 来源订单号
	OrderAmount      float64 `json:"order_amount"`                              // 订单金额
	CommissionRate   float64 `json:"commission_rate"`                           // 佣金比例
	Level            int     `json:"level" binding:"required,gte=1,lte=2"`      // 分销层级
	Remark           string  `json:"remark,omitempty"`                          // 备注
}

// ShareIncomeUpdateStatusReq 更新收益状态请求
type ShareIncomeUpdateStatusReq struct {
	ID               int64  `json:"id" binding:"required"`                // 记录ID
	CommissionStatus int    `json:"commission_status" binding:"required"` // 佣金状态
	Remark           string `json:"remark,omitempty"`                     // 处理备注
}
