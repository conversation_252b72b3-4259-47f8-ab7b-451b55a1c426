package dto

// DiningTypeResp 用餐类型响应
type DiningTypeResp struct {
	ID          int64  `json:"id"`          // ID
	Name        string `json:"name"`        // 类型名称
	Code        string `json:"code"`        // 类型编码
	Icon        string `json:"icon"`        // 图标
	Description string `json:"description"` // 描述
	Sort        int    `json:"sort"`        // 排序
}

// DiningTableResp 餐桌响应
type DiningTableResp struct {
	ID        int64  `json:"id"`        // 餐桌ID
	TableNo   string `json:"tableNo"`   // 餐桌编号
	TableName string `json:"tableName"` // 餐桌名称
	Capacity  int    `json:"capacity"`  // 容纳人数
	Status    int8   `json:"status"`    // 状态
	QrCode    string `json:"qrCode"`    // 二维码
}

// DiningTableListResp 餐桌列表响应
type DiningTableListResp struct {
	Total int64             `json:"total"` // 总数
	List  []DiningTableResp `json:"list"`  // 餐桌列表
}

// DiningOrderReq 堂食订单请求
type DiningOrderReq struct {
	CreateOrderReq
	DiningType string `json:"diningType" binding:"required,oneof=dine_in takeout"` // 用餐类型
	TableID    *int64 `json:"tableId"`                                             // 餐桌ID(堂食时必填)
	TableNo    string `json:"tableNo"`                                             // 餐桌编号(堂食时必填)
}
