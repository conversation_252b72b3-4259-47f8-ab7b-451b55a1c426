package dto

// BeautyCardPayRequest 套餐卡支付请求
type BeautyCardPayRequest struct {
	UserCardID int64 `json:"user_card_id" binding:"required"` // 用户套餐卡ID
	PayType    int   `json:"pay_type" binding:"required"`     // 支付方式：1-微信 2-支付宝 3-余额
}

// BeautyCardPayResponse 套餐卡支付响应
type BeautyCardPayResponse struct {
	Success bool                   `json:"success"` // 支付是否成功
	Data    map[string]interface{} `json:"data"`    // 支付数据（第三方支付时使用）
	Message string                 `json:"message"` // 支付结果消息
}
