package dto

// AddressReq 添加/修改地址请求
type AddressReq struct {
	ID             int64  `json:"id"`              // 地址ID，添加时不需要传
	ReceiverName   string `json:"receiver_name"`   // 收货人姓名
	ReceiverMobile string `json:"receiver_mobile"` // 收货人手机号
	Province       string `json:"province"`        // 省份
	City           string `json:"city"`            // 城市
	District       string `json:"district"`        // 区县
	Detail         string `json:"detail"`          // 详细地址
	IsDefault      bool   `json:"is_default"`      // 是否默认地址
}

// DeleteAddressReq 删除地址请求
type DeleteAddressReq struct {
	ID int64 `json:"id"` // 地址ID
}

// SetDefaultAddressReq 设置默认地址请求
type SetDefaultAddressReq struct {
	ID int64 `json:"id"` // 地址ID
}
