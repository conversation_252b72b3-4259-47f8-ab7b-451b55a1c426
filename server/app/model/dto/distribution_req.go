package dto

// TeamMembersReq 获取团队成员列表请求
type TeamMembersReq struct {
	PageReq
	UserId int64 `json:"-"` // 用户ID，从上下文获取，不需要在请求中传递
}

// DistributionOrdersReq 获取分销订单列表请求
type DistributionOrdersReq struct {
	PageReq
	UserId int64 `json:"-"` // 用户ID，从上下文获取，不需要在请求中传递
}

// WithdrawReq 提现请求
type WithdrawReq struct {
	UserId   int64   `json:"-"`                              // 用户ID，从上下文获取
	Amount   float64 `json:"amount" binding:"required,gt=0"` // 提现金额
	Method   string  `json:"method" binding:"required"`      // 提现方式：wechat-微信 alipay-支付宝 bank-银行卡
	Account  string  `json:"account" binding:"required"`     // 收款账号
	RealName string  `json:"realName" binding:"required"`    // 真实姓名
}

// WithdrawListReq 提现列表请求
type WithdrawListReq struct {
	PageReq
	UserId int64 `json:"-"` // 用户ID，从上下文获取，不需要在请求中传递
}

// ApplyDistributorReq 申请分销员请求
type ApplyDistributorReq struct {
	UserId   int64  `json:"-"`                           // 用户ID，从上下文获取
	RealName string `json:"realName" binding:"required"` // 真实姓名
	Phone    string `json:"phone" binding:"required"`    // 手机号码
	IdCard   string `json:"idCard" binding:"required"`   // 身份证号
	Remark   string `json:"remark"`                      // 申请理由
}
