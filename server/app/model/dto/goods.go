package dto

// AddCartReq 添加购物车请求
type AddCartReq struct {
	GoodsID   int64  `json:"goodsId" binding:"required"`   // 商品ID
	SkuCode   string `json:"skuCode" binding:"required"`   // SKU编码
	SpecsJSON string `json:"specsJson" binding:"required"` // 规格JSON
	Quantity  int    `json:"quantity" binding:"required"`  // 数量
}

// CollectReq 收藏请求
type CollectReq struct {
	GoodsID   int64 `json:"goods_id" binding:"required"` // 商品ID
	IsCollect bool  `json:"is_collect"`                  // 是否收藏
}

// CheckStockReq 检查库存请求
type CheckStockReq struct {
	GoodsID uint              `json:"goodsId" binding:"required"` // 商品ID
	Specs   map[string]string `json:"specs" binding:"required"`   // 规格值
}

// UpdateCartReq 修改购物车请求
type UpdateCartReq struct {
	ID       int64 `json:"id" binding:"required"`       // 购物车ID
	Quantity int   `json:"quantity" binding:"required"` // 数量
}

// UpdateCartSelectedReq 修改购物车商品选中状态请求
type UpdateCartSelectedReq struct {
	IDs      []int64 `json:"ids" binding:"required"` // 购物车ID列表
	Selected bool    `json:"selected"`               // 是否选中
}

// DeleteCartReq 删除购物车请求
type DeleteCartReq struct {
	IDs []int64 `json:"ids" binding:"required"` // 购物车ID列表
}
