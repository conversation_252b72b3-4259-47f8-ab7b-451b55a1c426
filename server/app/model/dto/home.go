package dto

// BannerQuery 轮播图查询参数
type BannerQuery struct {
	Position int `form:"position" binding:"omitempty,oneof=1 2 3"` // 位置(1:首页顶部 2:分类页 3:活动页)
}

// BannerResp 轮播图响应
type BannerResp struct {
	ID       int64  `json:"id"`       // 轮播图ID
	Image    string `json:"image"`    // 图片地址
	URL      string `json:"url"`      // 跳转链接
	Type     int    `json:"type"`     // 跳转类型(1:商品 2:活动 3:外链)
	TargetID *int64 `json:"targetId"` // 目标ID
	Title    string `json:"title"`    // 标题
	Sort     int    `json:"sort"`     // 排序
}

// CategoryQuery 分类查询参数
type CategoryQuery struct {
	Level    int   `form:"level" binding:"omitempty,oneof=1 2 3"` // 分类层级
	ParentID int64 `form:"parentId" binding:"omitempty,min=0"`    // 父级分类ID
}

// CategoryResp 分类响应
type CategoryResp struct {
	ID   int64  `json:"id"`   // 分类ID
	Name string `json:"name"` // 分类名称
	Icon string `json:"icon"` // 分类图标
	Sort int    `json:"sort"` // 排序
}

// GoodsQuery 商品查询参数
type GoodsQuery struct {
	Page       int     `form:"page" binding:"required,min=1"`            // 页码
	PageSize   int     `form:"pageSize" binding:"required,min=1,max=50"` // 每页数量
	CategoryID int64   `form:"categoryId" binding:"omitempty,min=0"`     // 分类ID，允许为0
	TagID      int64   `form:"tagId" binding:"omitempty,min=0"`          // 标签ID
	Keyword    string  `form:"keyword"`                                  // 搜索关键词
	Sort       string  `form:"sort"`                                     // 排序方式
	PriceMin   float64 `form:"priceMin" binding:"omitempty,min=0"`       // 最低价格
	PriceMax   float64 `form:"priceMax" binding:"omitempty,min=0"`       // 最高价格
}

// GoodsResp 商品响应
type GoodsResp struct {
	ID            int64    `json:"id"`            // 商品ID
	Name          string   `json:"name"`          // 商品名称
	Description   string   `json:"description"`   // 商品描述
	Price         string   `json:"price"`         // 商品价格
	OriginalPrice string   `json:"originalPrice"` // 原价
	Image         string   `json:"image"`         // 商品主图
	Sales         int      `json:"sales"`         // 销量
	Stock         int      `json:"stock"`         // 库存
	SpecType      int8     `json:"specType"`      // 规格类型：1-单规格 2-多规格 3-套餐规格
	IsCollected   bool     `json:"isCollected"`   // 是否已收藏
	IsLiked       bool     `json:"isLiked"`       // 是否已点赞
	Likes         int      `json:"likes"`         // 点赞数量
	Tags          []string `json:"tags"`          // 商品标签
}

// GoodsListResp 商品列表响应
type GoodsListResp struct {
	Total int64       `json:"total"` // 总数
	List  []GoodsResp `json:"list"`  // 商品列表
}

// ActivityQuery 活动查询参数
type ActivityQuery struct {
	Type     int `form:"type" binding:"omitempty,oneof=1 2 3"`     // 活动类型
	Status   int `form:"status" binding:"omitempty,oneof=0 1 2"`   // 活动状态
	PageNum  int `form:"page" binding:"required,min=1"`            // 页码
	PageSize int `form:"pageSize" binding:"required,min=1,max=50"` // 每页数量
}

// ActivityResp 活动响应
type ActivityResp struct {
	ID        int64  `json:"id"`        // 活动ID
	Name      string `json:"name"`      // 活动名称
	Desc      string `json:"desc"`      // 活动描述
	Image     string `json:"image"`     // 活动图片
	StartTime string `json:"startTime"` // 开始时间
	EndTime   string `json:"endTime"`   // 结束时间
	Type      int    `json:"type"`      // 活动类型
	Status    int    `json:"status"`    // 活动状态
}

// TagQuery 标签查询参数
type TagQuery struct {
	Status int `form:"status" binding:"omitempty,oneof=0 1"` // 状态(0:禁用 1:启用)
}

// TagResp 标签响应
type TagResp struct {
	ID   int64  `json:"id"`   // 标签ID
	Name string `json:"name"` // 标签名称
}

// HomeDataResp 首页数据响应
type HomeDataResp struct {
	Banners     []BannerResp     `json:"banners"`     // 轮播图
	Categories  []CategoryResp   `json:"categories"`  // 分类
	DiningTypes []DiningTypeResp `json:"diningTypes"` // 用餐类型
	Activities  []ActivityResp   `json:"activities"`  // 活动
	Tags        []TagResp        `json:"tags"`        // 标签
	Goods       *GoodsListResp   `json:"goods"`       // 商品列表
}
