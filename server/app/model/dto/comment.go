package dto

// CreateCommentReq 创建评论请求
type CreateCommentReq struct {
	OrderID     int64    `json:"orderId"`     // 订单ID
	GoodsID     int64    `json:"goodsId"`     // 商品ID
	Content     string   `json:"content"`     // 评论内容
	Images      []string `json:"images"`      // 评论图片
	Rate        int8     `json:"rate"`        // 评分(1-5星)
	IsAnonymous bool     `json:"isAnonymous"` // 是否匿名
}

// GetGoodsCommentsReq 获取商品评论请求
type GetGoodsCommentsReq struct {
	GoodsID int64 `json:"goodsId"` // 商品ID
	Page    int   `json:"page"`    // 页码
	Size    int   `json:"size"`    // 每页数量
}
