package dto

import "time"

// SubmitFeedbackRequest 提交反馈请求
type SubmitFeedbackRequest struct {
	Content string `json:"content" binding:"required,min=1,max=500" label:"反馈内容"`
	Contact string `json:"contact" binding:"max=100" label:"联系方式"`
}

// FeedbackResponse 反馈响应
type FeedbackResponse struct {
	ID         int64      `json:"id"`
	Content    string     `json:"content"`
	Contact    string     `json:"contact"`
	Status     int8       `json:"status"`
	StatusText string     `json:"status_text"`
	Reply      string     `json:"reply"`
	ReplyTime  *time.Time `json:"reply_time"`
	CreateTime time.Time  `json:"create_time"`
}

// FeedbackListRequest 反馈列表请求
type FeedbackListRequest struct {
	Page     int   `json:"page" form:"page" binding:"min=1"`
	PageSize int   `json:"page_size" form:"page_size" binding:"min=1,max=100"`
	Status   *int8 `json:"status" form:"status" binding:"omitempty,oneof=0 1 2"`
}
