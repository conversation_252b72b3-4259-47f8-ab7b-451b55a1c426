package dto

// CategoryTreeResp 分类树形结构响应
type CategoryTreeResp struct {
	ID       uint               `json:"id"`       // 分类ID
	Name     string             `json:"name"`     // 分类名称
	Icon     string             `json:"icon"`     // 分类图标
	Image    string             `json:"image"`    // 分类图片
	Level    uint8              `json:"level"`    // 层级
	Sort     uint               `json:"sort"`     // 排序
	Children []CategoryTreeResp `json:"children"` // 子分类
}

// CategoryGoodsReq 获取分类商品列表请求
type CategoryGoodsReq struct {
	CategoryID    uint `form:"categoryId" binding:"required"` // 分类ID
	SubCategoryID uint `form:"subCategoryId"`                 // 子分类ID，可选
	Page          uint `form:"page,default=1"`                // 页码
	PageSize      uint `form:"pageSize,default=10"`           // 每页数量
}

// CategoryGoodsItemResp 分类商品响应项
type CategoryGoodsItemResp struct {
	ID            uint    `json:"id"`             // 商品ID
	Name          string  `json:"name"`           // 商品名称
	Price         float64 `json:"price"`          // 商品价格
	OriginalPrice float64 `json:"original_price"` // 原价
	Image         string  `json:"image"`          // 商品主图
	Sales         uint    `json:"sales"`          // 销量
	Stock         uint    `json:"stock"`          // 库存
	Status        uint8   `json:"status"`         // 状态
	Description   string  `json:"description"`    // 商品描述
}

// CategoryGoodsResp 分类商品列表响应
type CategoryGoodsResp struct {
	Total uint64                  `json:"total"` // 总数量
	List  []CategoryGoodsItemResp `json:"list"`  // 商品列表
}
