package dto

// LoginReq 登录请求
type LoginReq struct {
	Username string `json:"username" binding:"required"` // 用户名
	Password string `json:"password" binding:"required"` // 密码
}

// LoginResp 登录响应
type LoginResp struct {
	Token     string  `json:"token"`     // token
	ID        int64   `json:"id"`        // 用户ID
	Username  string  `json:"username"`  // 用户名
	Nickname  string  `json:"nickname"`  // 昵称
	Avatar    string  `json:"avatar"`    // 头像
	Balance   float64 `json:"balance"`   // 余额
	Gender    int     `json:"gender"`    // 性别：0-未知 1-男 2-女
	Phone     string  `json:"phone"`     // 手机号
	Birthday  string  `json:"birthday"`  // 生日
	ShareCode string  `json:"shareCode"` // 分享码

	// 新增字段
	IsTechnician   bool                   `json:"isTechnician"`   // 是否技师
	TechnicianInfo map[string]interface{} `json:"technicianInfo"` // 技师信息
}

// CollectListReq 收藏列表请求
type CollectListReq struct {
	Page int `form:"page" binding:"required,min=1"`
	Size int `form:"size" binding:"required,min=1,max=50"`
}

// DeleteCollectReq 删除收藏请求
type DeleteCollectReq struct {
	ID int64 `json:"id" binding:"required,min=1"`
}

// FootprintListReq 足迹列表请求
type FootprintListReq struct {
	Page int `form:"page" binding:"required,min=1"`
	Size int `form:"size" binding:"required,min=1,max=50"`
}

// DeleteFootprintReq 删除足迹请求
type DeleteFootprintReq struct {
	ID int64 `json:"id" binding:"required,min=1"`
}

// WechatPhoneLoginReq 微信手机号登录请求
type WechatPhoneLoginReq struct {
	Code          string `json:"code" binding:"required"`          // 微信登录凭证
	EncryptedData string `json:"encryptedData" binding:"required"` // 包括敏感数据在内的完整用户信息的加密数据
	Iv            string `json:"iv" binding:"required"`            // 加密算法的初始向量
	InviterId     *int64 `json:"inviterId"`                        // 邀请者ID（可选）
}

// RegisterReq 注册请求
type RegisterReq struct {
	Phone     string `json:"phone" binding:"required,len=11"`          // 手机号
	Code      string `json:"code" binding:"required,len=6"`            // 验证码
	Password  string `json:"password" binding:"required,min=6,max=20"` // 密码
	InviterId *int64 `json:"inviterId"`                                // 邀请者ID（可选）
}

// RegisterResp 注册响应
type RegisterResp struct {
	ID       int64  `json:"id"`       // 用户ID
	Phone    string `json:"phone"`    // 手机号
	Nickname string `json:"nickname"` // 昵称
}

// SendSmsReq 发送短信验证码请求
type SendSmsReq struct {
	Phone string `json:"phone" binding:"required,len=11"` // 手机号
	Type  string `json:"type" binding:"required"`         // 验证码类型：register-注册，login-登录，reset-重置密码
}

// VerifySmsReq 验证短信验证码请求
type VerifySmsReq struct {
	Phone string `json:"phone" binding:"required,len=11"` // 手机号
	Code  string `json:"code" binding:"required,len=6"`   // 验证码
	Type  string `json:"type" binding:"required"`         // 验证码类型
}

// ChangePasswordReq 修改密码请求
type ChangePasswordReq struct {
	OldPassword string `json:"oldPassword" binding:"required,min=6,max=20"` // 旧密码
	NewPassword string `json:"newPassword" binding:"required,min=6,max=20"` // 新密码
}

// ResetPasswordReq 重置密码请求（不需要验证当前密码）
type ResetPasswordReq struct {
	NewPassword string `json:"newPassword" binding:"required,min=6,max=20"` // 新密码
}
