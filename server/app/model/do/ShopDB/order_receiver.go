package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// OrderReceiverDO 订单收货地址表
type OrderReceiverDO struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement"`  // ID
	OrderID    int64     `gorm:"column:order_id;not null"`            // 订单ID
	Receiver   string    `gorm:"column:receiver;not null"`            // 收货人
	Phone      string    `gorm:"column:phone;not null"`               // 手机号
	Province   string    `gorm:"column:province;not null"`            // 省份
	City       string    `gorm:"column:city;not null"`                // 城市
	District   string    `gorm:"column:district;not null"`            // 区县
	Detail     string    `gorm:"column:detail;not null"`              // 详细地址
	IsDelete   int8      `gorm:"column:is_delete;not null;default:0"` // 是否删除
	CreateTime time.Time `gorm:"column:create_time;autoCreateTime"`   // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;autoUpdateTime"`   // 更新时间
}

// TableName 表名
func (m *OrderReceiverDO) TableName() string {
	return "shop_order_receiver"
}

// Query 查询非删除的记录
func (m *OrderReceiverDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m).Where("is_delete = ?", 0)
}
