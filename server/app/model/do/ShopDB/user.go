package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// UserDO 用户模型
type UserDO struct {
	ID            int64      `gorm:"column:id;primary_key"`                                  // 用户ID
	Username      string     `gorm:"column:username"`                                        // 用户名
	Password      string     `gorm:"column:password"`                                        // 密码
	Nickname      string     `gorm:"column:nickname"`                                        // 昵称
	Avatar        string     `gorm:"column:avatar"`                                          // 头像
	Phone         string     `gorm:"column:phone"`                                           // 手机号
	Gender        int        `gorm:"column:gender"`                                          // 性别
	Birthday      *string    `gorm:"column:birthday"`                                        // 生日
	Email         string     `gorm:"column:email"`                                           // 邮箱
	Balance       float64    `gorm:"column:balance"`                                         // 余额
	Points        int        `gorm:"column:points"`                                          // 积分
	Level         int8       `gorm:"column:level"`                                           // 会员等级(1:普通会员 2:黄金会员 3:钻石会员)
	TotalAmount   float64    `gorm:"column:total_amount"`                                    // 累计消费金额
	LastLoginTime *time.Time `gorm:"column:last_login_time"`                                 // 最后登录时间
	LastLoginIP   string     `gorm:"column:last_login_ip"`                                   // 最后登录IP
	Status        int8       `gorm:"column:status"`                                          // 状态(0:禁用 1:正常)
	IsDelete      int8       `gorm:"column:is_delete"`                                       // 是否删除
	CreateTime    time.Time  `gorm:"column:create_time"`                                     // 创建时间
	UpdateTime    time.Time  `gorm:"column:update_time"`                                     // 更新时间
	OpenID        *string    `gorm:"column:openid;type:varchar(128);uniqueIndex:idx_openid"` // 微信 OpenID，设为指针允许为空，添加唯一索引
}

// TableName 表名
func (m *UserDO) TableName() string {
	return "user"
}

// Query 查询构造器
func (m *UserDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m)
}
