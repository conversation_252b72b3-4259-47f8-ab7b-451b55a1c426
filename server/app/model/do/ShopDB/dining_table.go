package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// DiningTableDO 餐桌表
type DiningTableDO struct {
	ID              int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`         // 餐桌ID
	TableNo         string    `gorm:"column:table_no;not null;uniqueIndex" json:"table_no"` // 餐桌编号
	DiningTableName string    `gorm:"column:table_name;not null" json:"table_name"`         // 餐桌名称
	Capacity        int       `gorm:"column:capacity;not null" json:"capacity"`             // 容纳人数
	QrCode          string    `gorm:"column:qr_code" json:"qr_code"`                        // 二维码
	Status          int8      `gorm:"column:status;not null;default:1" json:"status"`       // 状态(0:禁用 1:空闲 2:使用中 3:预订)
	IsDelete        int8      `gorm:"column:is_delete;not null;default:0" json:"is_delete"` // 是否删除
	CreateTime      time.Time `gorm:"column:create_time;autoCreateTime" json:"create_time"` // 创建时间
	UpdateTime      time.Time `gorm:"column:update_time;autoUpdateTime" json:"update_time"` // 更新时间
}

// TableName 表名
func (m *DiningTableDO) TableName() string {
	return "shop_dining_table"
}

// Query 查询非删除的记录
func (m *DiningTableDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m).Where("is_delete = ?", 0)
}
