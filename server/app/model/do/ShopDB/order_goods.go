package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// OrderGoodsDO 订单商品表
type OrderGoodsDO struct {
	ID          int64     `gorm:"column:id;primaryKey;autoIncrement"`              // ID
	OrderID     int64     `gorm:"column:order_id;not null"`                        // 订单ID
	GoodsID     int64     `gorm:"column:goods_id;not null"`                        // 商品ID
	GoodsName   string    `gorm:"column:goods_name;not null"`                      // 商品名称
	GoodsImage  string    `gorm:"column:goods_image;not null"`                     // 商品图片
	SkuCode     string    `gorm:"column:sku_code;not null"`                        // SKU编码
	Specs       string    `gorm:"column:specs;not null"`                           // 规格信息
	Price       float64   `gorm:"column:price;type:decimal(10,2);not null"`        // 商品单价
	Quantity    int       `gorm:"column:quantity;not null"`                        // 购买数量
	TotalAmount float64   `gorm:"column:total_amount;type:decimal(10,2);not null"` // 商品总价
	IsDelete    int8      `gorm:"column:is_delete;not null;default:0"`             // 是否删除
	CreateTime  time.Time `gorm:"column:create_time;autoCreateTime"`               // 创建时间
	UpdateTime  time.Time `gorm:"column:update_time;autoUpdateTime"`               // 更新时间
}

// TableName 表名
func (m *OrderGoodsDO) TableName() string {
	return "shop_order_goods"
}

// Query 查询非删除的记录
func (m *OrderGoodsDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m).Where("is_delete = ?", 0)
}
