package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// ShareSettingDO 分享设置(简化版)
type ShareSettingDO struct {
	ID int64 `gorm:"primaryKey" json:"id"`

	// 基础开关
	Enabled bool `gorm:"column:enabled" json:"enabled"` // 分享功能启用状态

	// 注册佣金配置
	RegisterCommissionEnabled bool    `gorm:"column:register_commission_enabled" json:"register_commission_enabled"` // 注册佣金开关
	RegisterCommissionAmount  float64 `gorm:"column:register_commission_amount" json:"register_commission_amount"`   // 注册佣金金额

	// 购买佣金配置
	PurchaseCommissionEnabled bool    `gorm:"column:purchase_commission_enabled" json:"purchase_commission_enabled"` // 购买佣金开关
	Level1Rate                float64 `gorm:"column:level1_rate" json:"level1_rate"`                                 // 一级返佣比例
	Level2Rate                float64 `gorm:"column:level2_rate" json:"level2_rate"`                                 // 二级返佣比例

	// 佣金管理
	FreezeDays int `gorm:"column:freeze_days" json:"freeze_days"` // 佣金冻结天数

	// 提现配置
	MinWithdraw     float64 `gorm:"column:min_withdraw" json:"min_withdraw"`           // 最低提现金额
	MaxWithdraw     float64 `gorm:"column:max_withdraw" json:"max_withdraw"`           // 最高提现金额
	WithdrawFeeRate float64 `gorm:"column:withdraw_fee_rate" json:"withdraw_fee_rate"` // 提现手续费率

	// 提现方式
	WechatWithdrawEnabled bool `gorm:"column:wechat_withdraw_enabled" json:"wechat_withdraw_enabled"` // 微信提现开关
	AlipayWithdrawEnabled bool `gorm:"column:alipay_withdraw_enabled" json:"alipay_withdraw_enabled"` // 支付宝提现开关

	// 系统字段
	CreateTime time.Time `gorm:"column:create_time" json:"create_time"` // 创建时间
	UpdateTime time.Time `gorm:"column:update_time" json:"update_time"` // 更新时间
}

func (m *ShareSettingDO) TableName() string {
	return "share_setting"
}

func (m *ShareSettingDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m)
}
