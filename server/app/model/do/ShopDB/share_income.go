package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// ShareIncomeDO 分销收益记录表
type ShareIncomeDO struct {
	ID               int64      `gorm:"primaryKey;column:id" json:"id"`
	ReferrerId       *int64     `gorm:"column:referrer_id" json:"referrer_id"`             // 推荐官ID
	UserId           int64      `gorm:"column:user_id" json:"user_id"`                     // 推荐官用户ID
	InviteeId        *int64     `gorm:"column:invitee_id" json:"invitee_id"`               // 被邀请人ID
	InviteeName      string     `gorm:"column:invitee_name" json:"invitee_name"`           // 被邀请人姓名
	CommissionAmount float64    `gorm:"column:commission_amount" json:"commission_amount"` // 佣金金额
	CommissionType   string     `gorm:"column:commission_type" json:"commission_type"`     // 佣金类型(register:注册,purchase:购买,order:订单)
	CommissionStatus int        `gorm:"column:commission_status" json:"commission_status"` // 佣金状态(0-待结算,1-已结算,2-已取消)
	SourceOrderNo    string     `gorm:"column:source_order_no" json:"source_order_no"`     // 来源订单号
	OrderAmount      float64    `gorm:"column:order_amount" json:"order_amount"`           // 订单金额，注册佣金时为0
	CommissionRate   float64    `gorm:"column:commission_rate" json:"commission_rate"`     // 佣金比例
	Level            int        `gorm:"column:level" json:"level"`                         // 分销层级
	UnfreezeTime     *time.Time `gorm:"column:unfreeze_time" json:"unfreeze_time"`         // 解冻时间
	ProcessTime      *time.Time `gorm:"column:process_time" json:"process_time"`           // 处理时间
	Remark           string     `gorm:"column:remark" json:"remark"`                       // 备注
	CreateTime       time.Time  `gorm:"column:create_time" json:"create_time"`
	UpdateTime       time.Time  `gorm:"column:update_time" json:"update_time"`
}

func (m *ShareIncomeDO) TableName() string {
	return "share_income"
}

func (m *ShareIncomeDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m)
}

// 佣金类型常量
const (
	CommissionTypeRegister = "register" // 注册佣金
	CommissionTypePurchase = "purchase" // 购买佣金
	CommissionTypeOrder    = "order"    // 订单佣金
)

// 佣金状态常量
const (
	CommissionStatusPending   = 0 // 待结算
	CommissionStatusSettled   = 1 // 已结算
	CommissionStatusCancelled = 2 // 已取消
)
