package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// OrderDO 订单表
type OrderDO struct {
	ID             int64     `gorm:"column:id;primaryKey;autoIncrement"`                 // 订单ID
	OrderNo        string    `gorm:"column:order_no;not null;uniqueIndex"`               // 订单编号
	BusinessType   string    `gorm:"column:business_type;default:'goods'"`               // 业务类型：goods-商品 booking-服务预约 card-套餐卡
	BusinessID     *int64    `gorm:"column:business_id"`                                 // 业务ID（关联具体业务表）
	UserID         int64     `gorm:"column:user_id;not null"`                            // 用户ID
	TotalAmount    float64   `gorm:"column:total_amount;type:decimal(10,2);not null"`    // 订单总金额
	FreightAmount  float64   `gorm:"column:freight_amount;type:decimal(10,2);not null"`  // 运费
	DiscountAmount float64   `gorm:"column:discount_amount;type:decimal(10,2);not null"` // 优惠金额
	PayAmount      float64   `gorm:"column:pay_amount;type:decimal(10,2);not null"`      // 实付金额
	PayType        int8       `gorm:"column:pay_type;not null"`                           // 支付方式
	PayTime        *time.Time `gorm:"column:pay_time"`                                    // 支付时间
	Status         string     `gorm:"column:status;not null"`                            // 订单状态
	DiningType     string    `gorm:"column:dining_type;default:'takeout'"`               // 用餐类型(dine_in:堂食 takeout:外卖)
	TableID        *int64    `gorm:"column:table_id"`                                    // 餐桌ID(堂食时使用)
	TableNo        string    `gorm:"column:table_no"`                                    // 餐桌编号(堂食时使用)
	Remark         string    `gorm:"column:remark"`                                      // 订单备注
	IsDelete       int8      `gorm:"column:is_delete;not null;default:0"`                // 是否删除
	CreateTime     time.Time `gorm:"column:create_time;autoCreateTime"`                  // 创建时间
	UpdateTime     time.Time `gorm:"column:update_time;autoUpdateTime"`                  // 更新时间
}

// TableName 表名
func (m *OrderDO) TableName() string {
	return "shop_order"
}

// Query 查询非删除的记录
func (m *OrderDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m).Where("is_delete = ?", 0)
}
