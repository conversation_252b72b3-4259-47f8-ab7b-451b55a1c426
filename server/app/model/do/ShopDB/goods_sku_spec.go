package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// GoodsSkuSpecDO SKU规格关联表
type GoodsSkuSpecDO struct {
	ID          int64     `gorm:"column:id;primary_key" json:"id"`                    // 关联ID
	SkuID       int64     `gorm:"column:sku_id;not null" json:"sku_id"`               // SKU ID
	SpecGroupID int64     `gorm:"column:spec_group_id;not null" json:"spec_group_id"` // 规格组ID
	SpecValueID int64     `gorm:"column:spec_value_id;not null" json:"spec_value_id"` // 规格值ID
	CreateTime  time.Time `gorm:"column:create_time;not null" json:"create_time"`     // 创建时间
}

// TableName 表名
func (m *GoodsSkuSpecDO) TableName() string {
	return "shop_goods_sku_spec"
}

// Query 查询记录
func (m *GoodsSkuSpecDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m)
}
