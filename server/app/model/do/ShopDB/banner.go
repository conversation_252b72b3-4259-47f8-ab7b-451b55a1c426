package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// BannerDO 轮播图数据对象
type BannerDO struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement"` // 轮播图ID
	Title      string    `gorm:"column:title;not null"`              // 标题
	Image      string    `gorm:"column:image;not null"`              // 图片地址
	URL        string    `gorm:"column:url"`                         // 跳转链接
	Type       int       `gorm:"column:type;not null"`               // 跳转类型
	TargetID   *int64    `gorm:"column:target_id"`                   // 目标ID
	Position   int       `gorm:"column:position;not null"`           // 位置
	Sort       int       `gorm:"column:sort;not null"`               // 排序
	Status     int       `gorm:"column:status;not null"`             // 状态
	CreateTime time.Time `gorm:"column:create_time;autoCreateTime"`  // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;autoUpdateTime"`  // 更新时间
}

// TableName 表名
func (BannerDO) TableName() string {
	return "shop_banner"
}

// Query 查询非删除的记录
func (m *BannerDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m).Where("is_delete = ?", 0)
}
