package ShopDB

import (
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// GoodsTagDO 商品标签表
type GoodsTagDO struct {
	ID         int64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`         // 标签ID
	Name       string `gorm:"column:name;not null" json:"name"`                     // 标签名称
	Sort       int    `gorm:"column:sort;not null;default:0" json:"sort"`           // 排序
	IsDelete   int8   `gorm:"column:is_delete;not null;default:0" json:"is_delete"` // 是否删除
	CreateTime string `gorm:"column:create_time;autoCreateTime" json:"create_time"` // 创建时间
	UpdateTime string `gorm:"column:update_time;autoUpdateTime" json:"update_time"` // 更新时间
}

// TableName 表名
func (m *GoodsTagDO) TableName() string {
	return "shop_goods_tag"
}

// Query 查询非删除的记录
func (m *GoodsTagDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m).Where("is_delete = ?", 0)
}
