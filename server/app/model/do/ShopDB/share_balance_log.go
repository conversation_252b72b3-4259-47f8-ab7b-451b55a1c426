package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// ShareBalanceLogDO 分销资金变动记录
type ShareBalanceLogDO struct {
	ID            int64     `gorm:"primaryKey"`
	UserId        int64     `gorm:"column:user_id"`        // 用户ID
	Type          int       `gorm:"column:type"`           // 变动类型(1-收入 2-支出)
	Amount        float64   `gorm:"column:amount"`         // 变动金额
	BeforeBalance float64   `gorm:"column:before_balance"` // 变动前余额
	AfterBalance  float64   `gorm:"column:after_balance"`  // 变动后余额
	SourceType    int       `gorm:"column:source_type"`    // 来源类型(1-分销收益 2-提现)
	SourceId      *int64    `gorm:"column:source_id"`      // 来源ID
	Remark        string    `gorm:"column:remark"`         // 备注
	CreateTime    time.Time `gorm:"column:create_time"`    // 创建时间
}

func (m *ShareBalanceLogDO) TableName() string {
	return "share_balance_log"
}

func (m *ShareBalanceLogDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m)
}
