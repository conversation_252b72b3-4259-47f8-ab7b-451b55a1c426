package ShopDB

import (
	"time"
)

// SysDictData 系统字典数据表
type SysDictData struct {
	DictCode   int64     `gorm:"primarykey;column:dict_code" json:"dictCode"`
	DictSort   int       `gorm:"column:dict_sort;comment:字典排序" json:"dictSort"`
	DictLabel  string    `gorm:"column:dict_label;comment:字典标签" json:"dictLabel"`
	DictValue  string    `gorm:"column:dict_value;comment:字典键值" json:"dictValue"`
	DictType   string    `gorm:"column:dict_type;comment:字典类型" json:"dictType"`
	CssClass   string    `gorm:"column:css_class" json:"cssClass"`
	ListClass  string    `gorm:"column:list_class" json:"listClass"`
	IsDefault  string    `gorm:"column:is_default" json:"isDefault"`
	Status     string    `gorm:"column:status;default:'0';comment:状态（0正常 1停用）" json:"status"`
	CreateBy   string    `gorm:"column:create_by" json:"createBy"`
	CreateTime time.Time `gorm:"column:create_time" json:"createTime"`
	UpdateBy   string    `gorm:"column:update_by" json:"updateBy"`
	UpdateTime time.Time `gorm:"column:update_time" json:"updateTime"`
	Remark     string    `gorm:"column:remark" json:"remark"`
}

// TableName 指定表名
func (SysDictData) TableName() string {
	return "sys_dict_data"
}
