package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// ShareUserDO 分销用户
type ShareUserDO struct {
	ID               int64      `gorm:"primaryKey"`
	UserId           int64      `gorm:"column:user_id"`
	RealName         string     `gorm:"column:real_name"`
	Phone            string     `gorm:"column:phone"`
	IdCard           string     `gorm:"column:id_card"`
	ApplyTime        *time.Time `gorm:"column:apply_time"`
	AuditTime        *time.Time `gorm:"column:audit_time"`
	AuditUser        string     `gorm:"column:audit_user"`
	RejectReason     string     `gorm:"column:reject_reason"`
	ParentId         *int64     `gorm:"column:parent_id"`
	Level            int        `gorm:"column:level"`
	Status           int        `gorm:"column:status"`
	TotalIncome      float64    `gorm:"column:total_income"`
	AvailableBalance float64    `gorm:"column:available_balance"`
	FrozenBalance    float64    `gorm:"column:frozen_balance"`
	TotalWithdraw    float64    `gorm:"column:total_withdraw"`
	TeamCount        int        `gorm:"column:team_count"`
	BindTime         *time.Time `gorm:"column:bind_time"`
	ExpireTime       *time.Time `gorm:"column:expire_time"`
	CreateTime       time.Time  `gorm:"column:create_time"`
	UpdateTime       time.Time  `gorm:"column:update_time"`
	CreateBy         string     `gorm:"column:create_by"`
	UpdateBy         string     `gorm:"column:update_by"`
	Remark           string     `gorm:"column:remark"`
	Version          int        `gorm:"column:version"`
}

func (m *ShareUserDO) TableName() string {
	return "share_user"
}

func (m *ShareUserDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m)
}
