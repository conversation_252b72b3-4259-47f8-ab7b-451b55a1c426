package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// GoodsImageDO 商品图片表
type GoodsImageDO struct {
	ID         int64     `gorm:"column:id;primary_key" json:"id"`                // 图片ID
	GoodsID    int64     `gorm:"column:goods_id;not null" json:"goods_id"`       // 商品ID
	Position   int8      `gorm:"column:position;not null" json:"position"`       // 图片位置(0:普通图片 1:主图 2:列表图 3:详情主图 4:分享图)
	URL        string    `gorm:"column:url;not null" json:"url"`                 // 图片地址
	Sort       int       `gorm:"column:sort;not null;default:0" json:"sort"`     // 排序
	IsDelete   int8      `gorm:"column:is_delete;not null" json:"is_delete"`     // 是否删除
	CreateTime time.Time `gorm:"column:create_time;not null" json:"create_time"` // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;not null" json:"update_time"` // 更新时间
}

// TableName 表名
func (m *GoodsImageDO) TableName() string {
	return "shop_goods_image"
}

// Query 查询非删除的记录
func (m *GoodsImageDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m).Where("is_delete = ?", 0)
}
