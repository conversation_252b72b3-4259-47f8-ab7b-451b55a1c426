package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// GoodsDetailDO 商品详情表
type GoodsDetailDO struct {
	ID          int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`         // ID
	GoodsID     int64     `gorm:"column:goods_id;not null" json:"goods_id"`             // 商品ID
	VideoURL    string    `gorm:"column:video_url" json:"video_url"`                    // 主视频
	DetailDesc  string    `gorm:"column:detail_desc" json:"detail_desc"`                // 详细描述
	SpecDesc    string    `gorm:"column:spec_desc" json:"spec_desc"`                    // 规格说明
	ServiceDesc string    `gorm:"column:service_desc" json:"service_desc"`              // 服务说明
	CreateTime  time.Time `gorm:"column:create_time;autoCreateTime" json:"create_time"` // 创建时间
	UpdateTime  time.Time `gorm:"column:update_time;autoUpdateTime" json:"update_time"` // 更新时间
}

// TableName 表名
func (m *GoodsDetailDO) TableName() string {
	return "shop_goods_detail"
}

// Query 查询记录
func (m *GoodsDetailDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m)
}
