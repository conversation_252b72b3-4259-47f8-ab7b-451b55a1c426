package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// ActivityDO 活动数据对象
type ActivityDO struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement"` // 活动ID
	Name       string    `gorm:"column:name;not null"`               // 活动名称
	Desc       string    `gorm:"column:desc"`                        // 活动描述
	Image      string    `gorm:"column:image;not null"`              // 活动图片
	StartTime  time.Time `gorm:"column:start_time;not null"`         // 开始时间
	EndTime    time.Time `gorm:"column:end_time;not null"`           // 结束时间
	Type       int       `gorm:"column:type;not null"`               // 活动类型
	Status     int       `gorm:"column:status;not null"`             // 状态
	CreateTime time.Time `gorm:"column:create_time;autoCreateTime"`  // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;autoUpdateTime"`  // 更新时间
}

// TableName 表名
func (ActivityDO) TableName() string {
	return "shop_activity"
}

// Query 查询非删除的记录
func (m *ActivityDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m).Where("is_delete = ?", 0)
}
