package ShopDB

import "time"

type DataCity struct {
	Id         int       `json:"id" gorm:"column:id"`
	CityId     int       `json:"cityId" gorm:"column:city_id"`
	Level      int       `json:"level" gorm:"column:level"`
	ParentId   int       `json:"parentId" gorm:"column:parent_id"`
	AreaCode   string    `json:"areaCode" gorm:"column:area_code"`
	Name       string    `json:"name" gorm:"column:name"`
	MergerName string    `json:"mergerName" gorm:"column:merger_name"`
	Lng        string    `json:"lng" gorm:"column:lng"`
	Lat        string    `json:"lat" gorm:"column:lat"`
	IsShow     int       `json:"isShow" gorm:"column:is_show"`
	CreateTime time.Time `json:"createTime" gorm:"column:create_time"`
	UpdateTime time.Time `json:"updateTime" gorm:"column:update_time"`
}

func (m *DataCity) TableName() string {
	return "data_city"
}
