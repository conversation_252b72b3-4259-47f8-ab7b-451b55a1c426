package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// ShareWithdrawDO 分销提现
type ShareWithdrawDO struct {
	ID           int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UserId       int64     `gorm:"column:user_id;not null;index" json:"userId"`
	Amount       float64   `gorm:"column:amount;type:decimal(10,2);not null" json:"amount"`
	Fee          float64   `gorm:"column:fee;type:decimal(10,2);not null;default:0" json:"fee"`
	ActualAmount float64   `gorm:"column:actual_amount;type:decimal(10,2);not null" json:"actualAmount"`
	Method       string    `gorm:"column:method;size:20;not null" json:"method"`
	Account      string    `gorm:"column:account;size:100;not null" json:"account"`
	RealName     string    `gorm:"column:real_name;size:50;not null" json:"realName"`
	Status       string    `gorm:"column:status;size:20;not null;default:'pending'" json:"status"`
	Remark       string    `gorm:"column:remark;size:500" json:"remark"`
	CreateTime   time.Time `gorm:"column:create_time;not null" json:"createTime"`
	UpdateTime   time.Time `gorm:"column:update_time;not null" json:"updateTime"`
}

// 提现状态常量
const (
	WithdrawStatusPending = "pending" // 审核中
	WithdrawStatusSuccess = "success" // 已成功
	WithdrawStatusFailed  = "failed"  // 已拒绝
)

func (m *ShareWithdrawDO) TableName() string {
	return "share_withdraw"
}

func (m *ShareWithdrawDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m)
}
