package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// GoodsCollectDO 商品收藏表
type GoodsCollectDO struct {
	ID         int64     `gorm:"column:id;primary_key" json:"id"`                      // 收藏ID
	UserID     int64     `gorm:"column:user_id;not null" json:"user_id"`               // 用户ID
	GoodsID    int64     `gorm:"column:goods_id;not null" json:"goods_id"`             // 商品ID
	IsDelete   int8      `gorm:"column:is_delete;not null" json:"is_delete"`           // 是否删除
	CreateTime time.Time `gorm:"column:create_time;autoCreateTime" json:"create_time"` // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;autoUpdateTime" json:"update_time"` // 更新时间
}

// TableName 表名
func (m *GoodsCollectDO) TableName() string {
	return "shop_goods_collect"
}

// Query 查询非删除的记录
func (m *GoodsCollectDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m).Where("is_delete = ?", 0)
}
