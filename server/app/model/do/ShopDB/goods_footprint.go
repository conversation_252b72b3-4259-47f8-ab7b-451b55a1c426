package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// GoodsFootprintDO 商品足迹表
type GoodsFootprintDO struct {
	ID         int64     `gorm:"column:id;primary_key" json:"id"`                      // 足迹ID
	UserID     int64     `gorm:"column:user_id;not null" json:"user_id"`               // 用户ID
	GoodsID    int64     `gorm:"column:goods_id;not null" json:"goods_id"`             // 商品ID
	IsDelete   int8      `gorm:"column:is_delete;not null" json:"is_delete"`           // 是否删除
	CreateTime time.Time `gorm:"column:create_time;autoCreateTime" json:"create_time"` // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;autoUpdateTime" json:"update_time"` // 更新时间
}

// TableName 表名
func (m *GoodsFootprintDO) TableName() string {
	return "shop_goods_footprint"
}

// Query 查询非删除的记录
func (m *GoodsFootprintDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m).Where("is_delete = ?", 0)
}
