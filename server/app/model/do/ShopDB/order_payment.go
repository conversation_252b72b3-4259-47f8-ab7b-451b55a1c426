package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// OrderPaymentDO 支付订单表
type OrderPaymentDO struct {
	ID         int64      `gorm:"column:id;primaryKey;autoIncrement"`   // 支付订单ID
	OrderID    int64      `gorm:"column:order_id;not null"`             // 订单ID
	OrderNo    string     `gorm:"column:order_no;not null"`             // 订单编号
	PayType    int8       `gorm:"column:pay_type;not null"`             // 支付方式(1:微信 2:支付宝 3:余额)
	PayAmount  float64    `gorm:"column:pay_amount;not null"`           // 支付金额
	PayStatus  int8       `gorm:"column:pay_status;not null;default:0"` // 支付状态(0:未支付 1:支付成功 2:支付失败)
	TradeNo    string     `gorm:"column:trade_no"`                      // 支付平台交易号
	PayTime    *time.Time `gorm:"column:pay_time"`                      // 支付时间
	NotifyTime *time.Time `gorm:"column:notify_time"`                   // 回调时间
	NotifyData string     `gorm:"column:notify_data"`                   // 回调数据
	CreateTime time.Time  `gorm:"column:create_time;autoCreateTime"`    // 创建时间
	UpdateTime time.Time  `gorm:"column:update_time;autoUpdateTime"`    // 更新时间
}

// TableName 表名
func (m *OrderPaymentDO) TableName() string {
	return "shop_order_payment"
}

// Query 查询构造器
func (m *OrderPaymentDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m)
}
