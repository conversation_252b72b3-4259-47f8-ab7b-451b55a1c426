package ShopDB

import (
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// GoodsDO 商品表
type GoodsDO struct {
	ID          int64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`         // 商品ID
	CategoryID  int64  `gorm:"column:category_id;not null" json:"category_id"`       // 分类ID
	Supplier    string `gorm:"column:supplier;not null;default:''" json:"supplier"`  // 供应商名称
	Brand       string `gorm:"column:brand;not null;default:''" json:"brand"`        // 品牌
	Type        int8   `gorm:"column:type;not null;default:1" json:"type"`           // 商品类型：1-实物商品 2-虚拟商品 3-套餐商品
	Name        string `gorm:"column:name;not null" json:"name"`                     // 商品名称
	Description string `gorm:"column:description;not null" json:"description"`       // 商品描述
	Unit        string `gorm:"column:unit;not null;default:'件'" json:"unit"`         // 商品单位
	SpecType    int8   `gorm:"column:spec_type;not null;default:1" json:"spec_type"` // 规格类型：1-单规格 2-多规格
	Status      int8   `gorm:"column:status;not null;default:1" json:"status"`       // 状态(0:下架 1:上架)
	Sort        int    `gorm:"column:sort;not null;default:0" json:"sort"`           // 排序
	IsDelete    int8   `gorm:"column:is_delete;not null;default:0" json:"is_delete"` // 是否删除
	CreateTime  string `gorm:"column:create_time;autoCreateTime" json:"create_time"` // 创建时间
	UpdateTime  string `gorm:"column:update_time;autoUpdateTime" json:"update_time"` // 更新时间
}

// TableName 表名
func (m *GoodsDO) TableName() string {
	return "shop_goods"
}

// Query 查询非删除的记录
func (m *GoodsDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m).Where("is_delete = ?", 0)
}
