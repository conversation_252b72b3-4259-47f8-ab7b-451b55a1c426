package ShopDB

import (
	"database/sql/driver"
	"encoding/json"
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// SpecGroupConfig 规格组配置
type SpecGroupConfig struct {
	MaxSelect   int    `json:"max_select"`
	MinSelect   int    `json:"min_select"`
	Description string `json:"description"`
	IsRequired  bool   `json:"is_required"`
	DisplayType string `json:"display_type"`
}

// Scan 实现 sql.Scanner 接口
func (c *SpecGroupConfig) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	return json.Unmarshal(bytes, c)
}

// Value 实现 driver.Valuer 接口
func (c SpecGroupConfig) Value() (driver.Value, error) {
	return json.Marshal(c)
}

// GoodsSpecGroupDO 商品规格组表
type GoodsSpecGroupDO struct {
	ID         int64           `gorm:"column:id;primary_key" json:"id"`                // 规格组ID
	GoodsID    int64           `gorm:"column:goods_id;not null" json:"goods_id"`       // 商品ID
	Name       string          `gorm:"column:name;not null" json:"name"`               // 规格组名称
	SpecType   int8            `gorm:"column:spec_type;default:1" json:"spec_type"`    // 规格类型：1-单规格 2-多规格 3-套餐规格
	Config     SpecGroupConfig `gorm:"column:config;type:json" json:"config"`          // 规格组配置
	Sort       int             `gorm:"column:sort;not null;default:0" json:"sort"`     // 排序
	Status     int8            `gorm:"column:status;not null;default:1" json:"status"` // 状态(0:禁用 1:启用)
	CreateTime time.Time       `gorm:"column:create_time;not null" json:"create_time"` // 创建时间
	UpdateTime time.Time       `gorm:"column:update_time;not null" json:"update_time"` // 更新时间
}

// TableName 表名
func (m *GoodsSpecGroupDO) TableName() string {
	return "shop_goods_spec_group"
}

// Query 查询非删除的记录
func (m *GoodsSpecGroupDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m).Where("status = ?", 1)
}
