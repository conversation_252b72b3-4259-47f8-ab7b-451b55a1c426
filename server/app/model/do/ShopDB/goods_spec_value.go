package ShopDB

import (
	"database/sql/driver"
	"encoding/json"
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// SpecValueConfig 规格值配置
type SpecValueConfig struct {
	Image         string  `json:"image"`
	Quantity      int     `json:"quantity"`
	AddPrice      float64 `json:"add_price"`
	IsDefault     bool    `json:"is_default"`
	Description   string  `json:"description"`
	OptionSkuID   *int64  `json:"option_sku_id"`
	OptionGoodsID *int64  `json:"option_goods_id"`
}

// Scan 实现 sql.Scanner 接口
func (c *SpecValueConfig) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	return json.Unmarshal(bytes, c)
}

// Value 实现 driver.Valuer 接口
func (c SpecValueConfig) Value() (driver.Value, error) {
	return json.Marshal(c)
}

// GoodsSpecValueDO 商品规格值表
type GoodsSpecValueDO struct {
	ID          int64           `gorm:"column:id;primary_key" json:"id"`                    // 规格值ID
	GoodsID     int64           `gorm:"column:goods_id;not null" json:"goods_id"`           // 商品ID
	SpecGroupID int64           `gorm:"column:spec_group_id;not null" json:"spec_group_id"` // 规格组ID
	Name        string          `gorm:"column:name;not null" json:"name"`                   // 规格值名称
	Value       string          `gorm:"column:value;not null" json:"value"`                 // 规格值
	SpecType    int8            `gorm:"column:spec_type;default:1" json:"spec_type"`        // 规格类型：1-单规格 2-多规格 3-套餐规格
	Config      SpecValueConfig `gorm:"column:config;type:json" json:"config"`              // 规格值配置
	Sort        int             `gorm:"column:sort;not null;default:0" json:"sort"`         // 排序
	Status      int8            `gorm:"column:status;not null;default:1" json:"status"`     // 状态(0:禁用 1:启用)
	CreateTime  time.Time       `gorm:"column:create_time;not null" json:"create_time"`     // 创建时间
	UpdateTime  time.Time       `gorm:"column:update_time;not null" json:"update_time"`     // 更新时间
}

// TableName 表名
func (m *GoodsSpecValueDO) TableName() string {
	return "shop_goods_spec_value"
}

// Query 查询非删除的记录
func (m *GoodsSpecValueDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m).Where("status = ?", 1)
}
