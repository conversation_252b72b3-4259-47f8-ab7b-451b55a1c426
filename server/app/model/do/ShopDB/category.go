package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// CategoryDO 商品分类数据对象
type CategoryDO struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement"` // 分类ID
	Module     string    `gorm:"column:module;not null"`             // 模块标识
	ParentID   int64     `gorm:"column:parent_id;not null"`          // 父级ID
	Name       string    `gorm:"column:name;not null"`               // 分类名称
	Icon       string    `gorm:"column:icon"`                        // 分类图标
	Image      string    `gorm:"column:image"`                       // 分类图片
	Level      int       `gorm:"column:level;not null"`              // 层级
	Sort       int       `gorm:"column:sort;not null"`               // 排序
	Status     int       `gorm:"column:status;not null"`             // 状态
	IsDelete   int       `gorm:"column:is_delete;not null"`          // 是否删除
	CreateTime time.Time `gorm:"column:create_time;autoCreateTime"`  // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;autoUpdateTime"`  // 更新时间
}

// TableName 表名
func (CategoryDO) TableName() string {
	return "category"
}

// Query 查询非删除的记录
func (m *CategoryDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m).Where("is_delete = ?", 0)
}
