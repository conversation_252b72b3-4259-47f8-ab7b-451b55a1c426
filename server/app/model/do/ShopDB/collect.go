package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// CollectDO 统一收藏点赞表模型
type CollectDO struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UserID     int64     `gorm:"column:user_id;not null" json:"user_id"`                          // 用户ID
	TargetID   int64     `gorm:"column:target_id;not null" json:"target_id"`                      // 目标ID（商品ID、活动ID等）
	TargetType string    `gorm:"column:target_type;type:varchar(32);not null" json:"target_type"` // 目标类型（goods、activity等）
	ActionType int       `gorm:"column:action_type;type:tinyint(1);not null" json:"action_type"`  // 操作类型（1:收藏 2:点赞）
	IsDelete   int       `gorm:"column:is_delete;type:tinyint(1);default:0" json:"is_delete"`     // 是否删除
	CreateTime time.Time `gorm:"column:create_time;type:datetime;default:CURRENT_TIMESTAMP" json:"create_time"`
	UpdateTime time.Time `gorm:"column:update_time;type:datetime;default:CURRENT_TIMESTAMP" json:"update_time"`
}

// TableName 表名
func (CollectDO) TableName() string {
	return "collect"
}

// Query 查询构建器
func (c *CollectDO) Query() *gorm.DB {
	return db.DB.Shop.Model(c)
}

// 常量定义
const (
	ActionTypeCollect = 1 // 收藏
	ActionTypeLike    = 2 // 点赞
)

const (
	TargetTypeGoods    = "goods"    // 商品
	TargetTypeActivity = "activity" // 活动
)
