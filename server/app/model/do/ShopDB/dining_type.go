package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// DiningTypeDO 用餐类型表
type DiningTypeDO struct {
	ID          int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`         // ID
	Name        string    `gorm:"column:name;not null" json:"name"`                     // 类型名称(堂食/外卖)
	Code        string    `gorm:"column:code;not null;uniqueIndex" json:"code"`         // 类型编码(dine_in/takeout)
	Icon        string    `gorm:"column:icon" json:"icon"`                              // 图标
	Description string    `gorm:"column:description" json:"description"`                // 描述
	Sort        int       `gorm:"column:sort;not null;default:0" json:"sort"`           // 排序
	Status      int8      `gorm:"column:status;not null;default:1" json:"status"`       // 状态(0:禁用 1:启用)
	IsDelete    int8      `gorm:"column:is_delete;not null;default:0" json:"is_delete"` // 是否删除
	CreateTime  time.Time `gorm:"column:create_time;autoCreateTime" json:"create_time"` // 创建时间
	UpdateTime  time.Time `gorm:"column:update_time;autoUpdateTime" json:"update_time"` // 更新时间
}

// TableName 表名
func (m *DiningTypeDO) TableName() string {
	return "shop_dining_type"
}

// Query 查询非删除的记录
func (m *DiningTypeDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m).Where("is_delete = ?", 0)
}
