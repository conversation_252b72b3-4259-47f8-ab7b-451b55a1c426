package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// GoodsSkuDO SKU表
type GoodsSkuDO struct {
	ID            int64     `gorm:"column:id;primary_key" json:"id"`                                   // SKU ID
	GoodsID       int64     `gorm:"column:goods_id;not null" json:"goods_id"`                          // 商品ID
	SkuCode       string    `gorm:"column:sku_code;not null" json:"sku_code"`                          // SKU编码
	Image         string    `gorm:"column:image" json:"image"`                                         // SKU图片URL
	Price         float64   `gorm:"column:price;not null;default:0.00" json:"price"`                   // 售价
	OriginalPrice float64   `gorm:"column:original_price;not null;default:0.00" json:"original_price"` // 原价
	CostPrice     float64   `gorm:"column:cost_price;not null;default:0.00" json:"cost_price"`         // 成本价
	Stock         int       `gorm:"column:stock;not null;default:0" json:"stock"`                      // 库存
	Weight        float64   `gorm:"column:weight" json:"weight"`                                       // 重量(kg)
	Volume        float64   `gorm:"column:volume" json:"volume"`                                       // 体积(m³)
	Sort          int       `gorm:"column:sort;not null;default:0" json:"sort"`                        // 排序
	Status        int8      `gorm:"column:status;not null;default:1" json:"status"`                    // 状态(0:禁用 1:启用)
	CreateTime    time.Time `gorm:"column:create_time;not null" json:"create_time"`                    // 创建时间
	UpdateTime    time.Time `gorm:"column:update_time;not null" json:"update_time"`                    // 更新时间
}

// TableName 表名
func (m *GoodsSkuDO) TableName() string {
	return "shop_goods_sku"
}

// Query 查询非删除的记录
func (m *GoodsSkuDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m)
}
