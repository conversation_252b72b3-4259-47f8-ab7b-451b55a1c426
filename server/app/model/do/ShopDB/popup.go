package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// PopupDO 弹窗数据对象
type PopupDO struct {
	ID           int64      `gorm:"column:id;primaryKey;autoIncrement"`  // 弹窗ID
	Title        string     `gorm:"column:title"`                        // 弹窗标题
	Content      string     `gorm:"column:content;not null"`             // 弹窗内容
	TitleStyle   string     `gorm:"column:title_style;type:json"`        // 标题样式JSON
	Buttons      string     `gorm:"column:buttons;type:json"`            // 按钮配置JSON
	TriggerType  string     `gorm:"column:trigger_type;not null"`        // 触发类型：manual/auto/schedule
	TriggerRule  string     `gorm:"column:trigger_rule;type:json"`       // 触发规则JSON
	Priority     int        `gorm:"column:priority;default:0"`           // 优先级
	AutoClose    int        `gorm:"column:auto_close;default:0"`         // 自动关闭时间(毫秒)
	Delay        int        `gorm:"column:delay;default:0"`              // 延迟显示时间(毫秒)
	MaskClosable bool       `gorm:"column:mask_closable;default:true"`   // 点击遮罩是否关闭
	StartTime    *time.Time `gorm:"column:start_time"`                   // 开始时间
	EndTime      *time.Time `gorm:"column:end_time"`                     // 结束时间
	Status       int        `gorm:"column:status;not null;default:1"`    // 状态：0=禁用 1=启用
	ShowCount    int64      `gorm:"column:show_count;default:0"`         // 显示次数
	ClickCount   int64      `gorm:"column:click_count;default:0"`        // 点击次数
	CreateTime   time.Time  `gorm:"column:create_time;autoCreateTime"`   // 创建时间
	UpdateTime   time.Time  `gorm:"column:update_time;autoUpdateTime"`   // 更新时间
	IsDelete     int        `gorm:"column:is_delete;not null;default:0"` // 是否删除：0=否 1=是
}

// TableName 表名
func (PopupDO) TableName() string {
	return "popup"
}

// Query 查询非删除的记录
func (m *PopupDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m).Where("is_delete = ?", 0)
}

// PopupUserLogDO 弹窗用户日志数据对象
type PopupUserLogDO struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement"` // 日志ID
	PopupID    int64     `gorm:"column:popup_id;not null"`           // 弹窗ID
	UserID     int64     `gorm:"column:user_id;not null"`            // 用户ID
	Action     string    `gorm:"column:action;not null"`             // 操作类型：show/click/close
	ButtonText string    `gorm:"column:button_text"`                 // 点击的按钮文字
	CreateTime time.Time `gorm:"column:create_time;autoCreateTime"`  // 创建时间
}

// TableName 表名
func (PopupUserLogDO) TableName() string {
	return "popup_user_log"
}

// Query 查询记录
func (m *PopupUserLogDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m)
}
