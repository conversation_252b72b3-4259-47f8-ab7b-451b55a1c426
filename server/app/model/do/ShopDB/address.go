package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// AddressDO 用户地址表
type AddressDO struct {
	ID             int64     `gorm:"column:id;primaryKey;autoIncrement"` // 地址ID
	UserID         int64     `gorm:"column:user_id;not null"`            // 用户ID
	ReceiverName   string    `gorm:"column:receiver_name;not null"`      // 收货人姓名
	ReceiverMobile string    `gorm:"column:receiver_mobile;not null"`    // 收货人手机号
	Province       string    `gorm:"column:province;not null"`           // 省份
	City           string    `gorm:"column:city;not null"`               // 城市
	District       string    `gorm:"column:district;not null"`           // 区县
	Detail         string    `gorm:"column:detail;not null"`             // 详细地址
	IsDefault      int8      `gorm:"column:is_default;not null"`         // 是否默认地址：0-否，1-是
	IsDelete       int8      `gorm:"column:is_delete;not null"`          // 是否删除：0-否，1-是
	CreateTime     time.Time `gorm:"column:create_time;autoCreateTime"`  // 创建时间
	UpdateTime     time.Time `gorm:"column:update_time;autoUpdateTime"`  // 更新时间
}

// TableName 表名
func (AddressDO) TableName() string {
	return "shop_user_address"
}

// Query 查询构造器
func (a *AddressDO) Query() *gorm.DB {
	return db.DB.Shop.Model(a)
}
