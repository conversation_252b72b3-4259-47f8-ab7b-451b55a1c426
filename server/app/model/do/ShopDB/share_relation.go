package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// ShareRelationDO 分销关系
type ShareRelationDO struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UserId     int64     `gorm:"column:user_id;not null;index" json:"userId"`
	InviterId  int64     `gorm:"column:inviter_id;not null;index" json:"inviterId"`
	Level      int       `gorm:"column:level;not null;default:1" json:"level"`
	Status     int       `gorm:"column:status;not null;default:1" json:"status"`
	CreateTime time.Time `gorm:"column:create_time;not null" json:"createTime"`
	UpdateTime time.Time `gorm:"column:update_time;not null" json:"updateTime"`
}

func (m *ShareRelationDO) TableName() string {
	return "share_relation"
}

func (m *ShareRelationDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m)
}
