package ShopDB

import (
	"wnsys/shop/app/provider/db"

	"time"

	"gorm.io/gorm"
)

// CartDO 购物车表
type CartDO struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`           // 购物车ID
	UserID     int64     `gorm:"column:user_id;not null" json:"user_id"`                 // 用户ID
	GoodsID    int64     `gorm:"column:goods_id;not null" json:"goods_id"`               // 商品ID
	SkuCode    string    `gorm:"column:sku_code" json:"sku_code"`                        // SKU编码
	SpecsJSON  string    `gorm:"column:specs_json;type:json;not null" json:"specs_json"` // 规格JSON
	Quantity   int       `gorm:"column:quantity;not null" json:"quantity"`               // 数量
	Price      float64   `gorm:"column:price;type:decimal(10,2)" json:"price"`           // 加入购物车时的价格
	Selected   int8      `gorm:"column:selected;type:tinyint(11)" json:"selected"`       // 是否选中
	IsDelete   int8      `gorm:"column:is_delete;not null;default:0" json:"is_delete"`   // 是否删除
	CreateTime time.Time `gorm:"column:create_time;autoCreateTime" json:"create_time"`   // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;autoUpdateTime" json:"update_time"`   // 更新时间
}

// TableName 表名
func (m *CartDO) TableName() string {
	return "shop_cart"
}

// Query 查询非删除的记录
func (m *CartDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m).Where("is_delete = ?", 0)
}
