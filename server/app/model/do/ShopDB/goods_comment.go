package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// GoodsCommentDO 商品评论表
type GoodsCommentDO struct {
	ID           int64      `gorm:"column:id;primaryKey;autoIncrement"`     // 评论ID
	GoodsID      int64      `gorm:"column:goods_id;not null"`               // 商品ID
	OrderID      int64      `gorm:"column:order_id;not null"`               // 订单ID
	UserID       int64      `gorm:"column:user_id;not null"`                // 用户ID
	Content      string     `gorm:"column:content;not null"`                // 评论内容
	Images       string     `gorm:"column:images"`                          // 评论图片，JSON数组
	Rate         int8       `gorm:"column:rate;not null;default:5"`         // 评分(1-5星)
	IsAnonymous  int8       `gorm:"column:is_anonymous;not null;default:0"` // 是否匿名
	ReplyContent string     `gorm:"column:reply_content"`                   // 商家回复内容
	ReplyTime    *time.Time `gorm:"column:reply_time"`                      // 商家回复时间
	IsDelete     int8       `gorm:"column:is_delete;not null;default:0"`    // 是否删除
	CreateTime   time.Time  `gorm:"column:create_time;autoCreateTime"`      // 创建时间
	UpdateTime   time.Time  `gorm:"column:update_time;autoUpdateTime"`      // 更新时间
}

// TableName 表名
func (m *GoodsCommentDO) TableName() string {
	return "shop_goods_comment"
}

// Query 查询非删除的记录
func (m *GoodsCommentDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m).Where("is_delete = ?", 0)
}
