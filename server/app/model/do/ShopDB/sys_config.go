package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// SysConfigDO 系统配置表
type SysConfigDO struct {
	ID          int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	ParentID    int64     `gorm:"column:parent_id;default:0" json:"parentId"`
	ConfigName  string    `gorm:"column:config_name;size:100;not null" json:"configName"`
	ConfigKey   string    `gorm:"column:config_key;size:100;not null" json:"configKey"`
	ConfigValue string    `gorm:"column:config_value;size:500;not null" json:"configValue"`
	ConfigType  string    `gorm:"column:config_type;size:20;not null" json:"configType"`
	ModuleKey   string    `gorm:"column:module_key;size:50;not null" json:"moduleKey"`
	ModuleName  string    `gorm:"column:module_name;size:255" json:"moduleName"`
	Level       int8      `gorm:"column:level;default:1" json:"level"`
	Sort        int       `gorm:"column:sort;default:0" json:"sort"`
	Status      int8      `gorm:"column:status;default:1" json:"status"`
	Remark      string    `gorm:"column:remark;size:500" json:"remark"`
	DelFlag     string    `gorm:"column:del_flag;size:1;default:'0'" json:"delFlag"`
	CreateTime  time.Time `gorm:"column:create_time;autoCreateTime" json:"createTime"`
	UpdateTime  time.Time `gorm:"column:update_time;autoUpdateTime" json:"updateTime"`
}

// TableName 指定表名
func (SysConfigDO) TableName() string {
	return "sys_config"
}

// Query 获取查询构建器
func (s *SysConfigDO) Query() *gorm.DB {
	return db.DB.Shop.Model(s)
}
