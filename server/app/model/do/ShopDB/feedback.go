package ShopDB

import (
	"time"
)

// FeedbackDO 用户反馈表
type FeedbackDO struct {
	ID         int64      `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UserID     int64      `gorm:"column:user_id;not null;index" json:"user_id"`
	Content    string     `gorm:"column:content;type:text;not null" json:"content"`
	Contact    string     `gorm:"column:contact;size:100" json:"contact"`
	Status     int8       `gorm:"column:status;default:0;comment:状态(0:待处理 1:已处理 2:已关闭)" json:"status"`
	Reply      string     `gorm:"column:reply;type:text" json:"reply"`
	ReplyTime  *time.Time `gorm:"column:reply_time" json:"reply_time"`
	CreateTime time.Time  `gorm:"column:create_time;autoCreateTime" json:"create_time"`
	UpdateTime time.Time  `gorm:"column:update_time;autoUpdateTime" json:"update_time"`
}

// TableName 表名
func (FeedbackDO) TableName() string {
	return "feedback"
}
