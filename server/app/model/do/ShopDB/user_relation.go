package ShopDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// UserRelationDO 通用用户关系表
type UserRelationDO struct {
	ID            int64      `gorm:"primaryKey"`
	UserId        int64      `gorm:"column:user_id;not null;index"`         // 用户ID
	RelatedUserId int64      `gorm:"column:related_user_id;not null;index"` // 关联用户ID
	RelationType  string     `gorm:"column:relation_type;not null;index"`   // 关系类型：invite-邀请，friend-好友，share-分享裂变，follow-关注
	Direction     string     `gorm:"column:direction;not null"`             // 关系方向：one_way-单向，two_way-双向
	Status        int        `gorm:"column:status;default:1;index"`         // 状态：1-有效 0-无效 2-待确认
	Source        string     `gorm:"column:source"`                         // 关系来源：register-注册，manual-手动添加，share-分享，qr_code-二维码等
	Metadata      string     `gorm:"column:metadata;type:json"`             // 扩展数据（JSON格式）
	CreateTime    time.Time  `gorm:"column:create_time;not null"`
	UpdateTime    time.Time  `gorm:"column:update_time;not null"`
	ExpireTime    *time.Time `gorm:"column:expire_time"` // 过期时间（可选，用于临时关系）
}

func (m *UserRelationDO) TableName() string {
	return "user_relation"
}

func (m *UserRelationDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m)
}

// 关系类型常量
const (
	RelationTypeInvite = "invite" // 邀请关系（分销）
	RelationTypeFriend = "friend" // 好友关系
	RelationTypeShare  = "share"  // 分享裂变关系
	RelationTypeFollow = "follow" // 关注关系
	RelationTypeBlock  = "block"  // 拉黑关系
)

// 关系方向常量
const (
	DirectionOneWay = "one_way" // 单向关系
	DirectionTwoWay = "two_way" // 双向关系
)

// 关系状态常量
const (
	StatusInactive = 0 // 无效
	StatusActive   = 1 // 有效
	StatusPending  = 2 // 待确认
)

// 关系来源常量
const (
	SourceRegister = "register" // 注册时建立
	SourceManual   = "manual"   // 手动添加
	SourceShare    = "share"    // 分享建立
	SourceQRCode   = "qr_code"  // 二维码扫描
	SourceSearch   = "search"   // 搜索添加
	SourceImport   = "import"   // 导入通讯录
)
