package do

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

type UserCardDO struct {
	ID              int64      `gorm:"column:id;primaryKey" json:"id"`
	OrderID         *int64     `gorm:"column:order_id" json:"order_id"` // 关联订单ID
	UserID          int64      `gorm:"column:user_id" json:"user_id"`
	CardID          int64      `gorm:"column:card_id" json:"card_id"`
	CardNo          string     `gorm:"column:card_no" json:"card_no"` // 卡号
	CardName        string     `gorm:"column:card_name" json:"card_name"`
	CardType        int        `gorm:"column:card_type" json:"card_type"`
	TotalTimes      int        `gorm:"column:total_times" json:"total_times"`
	TotalAmount     float64    `gorm:"column:total_amount" json:"total_amount"`
	UsedTimes       int        `gorm:"column:used_times" json:"used_times"`
	UsedAmount      float64    `gorm:"column:used_amount" json:"used_amount"`
	RemainingTimes  *int       `gorm:"column:remaining_times" json:"remaining_times"`
	RemainingAmount *float64   `gorm:"column:remaining_amount" json:"remaining_amount"`
	ValidDays       int        `gorm:"column:valid_days" json:"valid_days"`
	StartTime       time.Time  `gorm:"column:start_time" json:"start_time"`
	EndTime         *time.Time `gorm:"column:end_time" json:"end_time"`
	Status          int        `gorm:"column:status" json:"status"`
	PurchasePrice   float64    `gorm:"column:purchase_price" json:"purchase_price"`
	PaymentAmount   float64    `gorm:"column:payment_amount" json:"payment_amount"` // 支付金额
	PurchaseTime    time.Time  `gorm:"column:purchase_time" json:"purchase_time"`   // 购买时间
	OrderNo         *string    `gorm:"column:order_no" json:"order_no"`
	Remark          *string    `gorm:"column:remark" json:"remark"`
	IsDelete        int        `gorm:"column:is_delete" json:"is_delete"`
	CreateTime      time.Time  `gorm:"column:create_time" json:"create_time"`
	UpdateTime      time.Time  `gorm:"column:update_time" json:"update_time"`
	ExpireTime      *time.Time `gorm:"column:expire_time" json:"expire_time"`
}

func (UserCardDO) TableName() string {
	return "beauty_user_card"
}

func (uc *UserCardDO) Query() *gorm.DB {
	return db.DB.Shop.Model(uc)
}
