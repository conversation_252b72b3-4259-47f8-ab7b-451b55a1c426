package BeautyDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// BeautyTechnicianDO 技师表
type BeautyTechnicianDO struct {
	ID     uint   `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID int64  `gorm:"column:user_id;uniqueIndex;not null" json:"user_id"` // 用户ID(关联user表)
	Name   string `gorm:"type:varchar(100);not null" json:"name"`

	// 专业信息
	Level        string `gorm:"type:varchar(50);not null" json:"level"`
	Experience   int    `gorm:"default:0" json:"experience"`
	Specialties  string `gorm:"type:json" json:"specialties"`
	Certificates string `gorm:"type:json" json:"certificates"`
	Introduction string `gorm:"type:text" json:"introduction"`

	// 服务能力
	ServiceIDs string `gorm:"type:json" json:"service_ids"`
	WorkHours  string `gorm:"type:json" json:"work_hours"`

	// 统计数据
	TotalBookings     int     `gorm:"default:0" json:"total_bookings"`
	CompletedBookings int     `gorm:"default:0" json:"completed_bookings"`
	RatingAvg         float64 `gorm:"type:decimal(3,2);default:0" json:"rating_avg"`
	RatingCount       int     `gorm:"default:0" json:"rating_count"`

	// 价格设置
	ExtraFee float64 `gorm:"type:decimal(10,2);default:0" json:"extra_fee"`

	// 状态控制
	Status     int `gorm:"default:1" json:"status"`
	IsFeatured int `gorm:"default:0" json:"is_featured"`
	Sort       int `gorm:"default:0" json:"sort"`

	// 系统字段
	IsDelete   int            `gorm:"default:0" json:"is_delete"`
	CreateTime time.Time      `gorm:"autoCreateTime" json:"create_time"`
	UpdateTime time.Time      `gorm:"autoUpdateTime" json:"update_time"`
	DeletedAt  gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (BeautyTechnicianDO) TableName() string {
	return "beauty_technician"
}

// Query 查询构造器
func (t *BeautyTechnicianDO) Query() *gorm.DB {
	return db.DB.Shop.Model(t)
}
