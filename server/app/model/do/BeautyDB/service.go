package BeautyDB

import (
	"time"

	"gorm.io/gorm"
)

// BeautyServiceDO 美容服务表
type BeautyServiceDO struct {
	ID          uint   `gorm:"primaryKey;autoIncrement" json:"id"`
	CategoryID  uint   `gorm:"not null;index" json:"category_id"`
	Name        string `gorm:"type:varchar(200);not null" json:"name"`
	Subtitle    string `gorm:"type:varchar(300)" json:"subtitle"`
	Description string `gorm:"type:text" json:"description"`
	Images      string `gorm:"type:json" json:"images"`

	// 价格信息
	Price         float64 `gorm:"type:decimal(10,2);not null" json:"price"`
	OriginalPrice float64 `gorm:"type:decimal(10,2)" json:"original_price"`

	// 服务属性
	Duration int `gorm:"not null" json:"duration"`

	// 预约规则
	NeedTechnician   int `gorm:"default:1" json:"need_technician"`
	MaxAdvanceDays   int `gorm:"default:30" json:"max_advance_days"`
	MinAdvanceHours  int `gorm:"default:2" json:"min_advance_hours"`
	AllowCancelHours int `gorm:"default:24" json:"allow_cancel_hours"`

	// 适用条件
	GenderLimit int `gorm:"default:0" json:"gender_limit"`
	AgeMin      int `gorm:"default:0" json:"age_min"`
	AgeMax      int `gorm:"default:100" json:"age_max"`

	// 服务特性
	Tags              string `gorm:"type:json" json:"tags"`
	Contraindications string `gorm:"type:text" json:"contraindications"`
	PreparationNotes  string `gorm:"type:text" json:"preparation_notes"`

	// 统计字段
	BookingCount int     `gorm:"default:0" json:"booking_count"`
	RatingAvg    float64 `gorm:"type:decimal(3,2);default:0" json:"rating_avg"`
	RatingCount  int     `gorm:"default:0" json:"rating_count"`

	// 状态控制
	Status int `gorm:"default:1" json:"status"`
	Sort   int `gorm:"default:0" json:"sort"`
	IsHot  int `gorm:"default:0" json:"is_hot"`
	IsNew  int `gorm:"default:0" json:"is_new"`

	// 系统字段
	IsDelete   int            `gorm:"default:0" json:"is_delete"`
	CreateTime time.Time      `gorm:"autoCreateTime" json:"create_time"`
	UpdateTime time.Time      `gorm:"autoUpdateTime" json:"update_time"`
	DeletedAt  gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (BeautyServiceDO) TableName() string {
	return "beauty_service"
}
