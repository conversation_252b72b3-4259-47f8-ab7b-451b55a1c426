package BeautyDB

import (
	"time"

	"gorm.io/gorm"
)

// BeautyBookingDO 预约订单表
type BeautyBookingDO struct {
	ID        int64  `gorm:"primaryKey;autoIncrement" json:"id"`
	OrderID   *int64 `gorm:"column:order_id" json:"order_id"` // 关联订单ID
	BookingNo string `gorm:"type:varchar(32);uniqueIndex;not null" json:"booking_no"`
	UserID    int64  `gorm:"not null;index" json:"user_id"`
	ServiceID int64  `gorm:"not null;index" json:"service_id"`
	// TechnicianUserID 专门存储技师user_id
	TechnicianUserID int64 `gorm:"index" json:"technician_user_id"`

	// 预约时间信息
	BookingDate time.Time `gorm:"not null;index" json:"booking_date"`
	StartTime   string    `gorm:"type:time;not null" json:"start_time"`
	EndTime     string    `gorm:"type:time;not null" json:"end_time"`
	Duration    int       `gorm:"not null" json:"duration"`

	// 客户信息
	ContactName    string `gorm:"type:varchar(100);not null" json:"contact_name"`
	ContactPhone   string `gorm:"type:varchar(20);not null" json:"contact_phone"`
	CustomerGender int    `gorm:"type:tinyint" json:"customer_gender"`
	CustomerAge    int    `json:"customer_age"`

	// 个性化需求
	SkinType        string `gorm:"type:varchar(50)" json:"skin_type"`
	SkinConcerns    string `gorm:"type:json" json:"skin_concerns"`
	Allergies       string `gorm:"type:text" json:"allergies"`
	SpecialRequests string `gorm:"type:text" json:"special_requests"`

	// 价格信息
	ServicePrice   float64 `gorm:"type:decimal(10,2);not null" json:"service_price"`
	TechnicianFee  float64 `gorm:"type:decimal(10,2);default:0" json:"technician_fee"`
	DiscountAmount float64 `gorm:"type:decimal(10,2);default:0" json:"discount_amount"`
	FinalPrice     float64 `gorm:"type:decimal(10,2);not null" json:"final_price"`

	// 优惠信息
	CouponID     int64   `json:"coupon_id"`
	CouponAmount float64 `gorm:"type:decimal(10,2);default:0" json:"coupon_amount"`

	// 状态管理
	BookingStatus string `gorm:"type:varchar(20);default:'pending';index" json:"booking_status"`
	PaymentStatus string `gorm:"type:varchar(20);default:'unpaid';index" json:"payment_status"`
	ServiceStatus string `gorm:"type:varchar(20);default:'waiting'" json:"service_status"`

	// 时间记录
	ConfirmedTime    *time.Time `json:"confirmed_time"`
	CheckinTime      *time.Time `json:"checkin_time"`
	ServiceStartTime *time.Time `json:"service_start_time"`
	ServiceEndTime   *time.Time `json:"service_end_time"`
	CancelledTime    *time.Time `json:"cancelled_time"`
	CancelReason     string     `gorm:"type:text" json:"cancel_reason"`

	// 评价信息
	Rating       int        `gorm:"type:tinyint" json:"rating"`
	Review       string     `gorm:"type:text" json:"review"`
	ReviewImages string     `gorm:"type:json" json:"review_images"`
	ReviewTime   *time.Time `json:"review_time"`

	// 系统字段
	IsDelete   int            `gorm:"default:0" json:"is_delete"`
	CreateTime time.Time      `gorm:"autoCreateTime" json:"create_time"`
	UpdateTime time.Time      `gorm:"autoUpdateTime" json:"update_time"`
	DeletedAt  gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (BeautyBookingDO) TableName() string {
	return "beauty_booking"
}
