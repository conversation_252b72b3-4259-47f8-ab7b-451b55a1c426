package BeautyDB

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// BeautyBookingPaymentDO 美容预约支付记录表
type BeautyBookingPaymentDO struct {
	ID         int64      `gorm:"primaryKey;autoIncrement" json:"id"`
	BookingID  int64      `gorm:"not null;index" json:"booking_id"`
	BookingNo  string     `gorm:"type:varchar(32);not null;index" json:"booking_no"`
	PayType    int8       `gorm:"not null" json:"pay_type"` // 支付方式(1:微信 2:支付宝 3:余额)
	PayAmount  float64    `gorm:"type:decimal(10,2);not null" json:"pay_amount"`
	PayStatus  int8       `gorm:"not null;default:0" json:"pay_status"` // 支付状态(0:未支付 1:支付成功 2:支付失败)
	TradeNo    string     `gorm:"type:varchar(64)" json:"trade_no"`     // 支付平台交易号
	PayTime    *time.Time `json:"pay_time"`                             // 支付时间
	NotifyTime *time.Time `json:"notify_time"`                          // 回调时间
	NotifyData string     `gorm:"type:text" json:"notify_data"`         // 回调数据
	CreateTime time.Time  `gorm:"autoCreateTime" json:"create_time"`
	UpdateTime time.Time  `gorm:"autoUpdateTime" json:"update_time"`
}

// TableName 指定表名
func (BeautyBookingPaymentDO) TableName() string {
	return "beauty_booking_payment"
}

// Query 查询构造器
func (m *BeautyBookingPaymentDO) Query() *gorm.DB {
	return db.DB.Shop.Model(m)
}
