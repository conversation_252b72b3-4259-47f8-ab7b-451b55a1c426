package BeautyDB

import (
	"time"

	"gorm.io/gorm"
)

// BeautyUserCardDO 美容会员卡表
type BeautyUserCardDO struct {
	ID              int64          `gorm:"primaryKey;autoIncrement" json:"id"`
	CardNo          string         `gorm:"type:varchar(32);uniqueIndex;not null" json:"card_no"`
	CardName        string         `gorm:"type:varchar(100);not null" json:"card_name"`
	CardType        int            `gorm:"type:tinyint;not null" json:"card_type"` // 卡类型：1-次数卡，2-金额卡
	
	// 次数信息
	TotalTimes      int            `gorm:"default:0" json:"total_times"`       // 总次数
	UsedTimes       int            `gorm:"default:0" json:"used_times"`        // 已使用次数
	RemainingTimes  int            `gorm:"default:0" json:"remaining_times"`   // 剩余次数
	
	// 金额信息
	TotalAmount     float64        `gorm:"type:decimal(10,2);default:0" json:"total_amount"`     // 总金额
	UsedAmount      float64        `gorm:"type:decimal(10,2);default:0" json:"used_amount"`      // 已使用金额
	RemainingAmount float64        `gorm:"type:decimal(10,2);default:0" json:"remaining_amount"` // 剩余金额
	
	// 购买信息
	PurchasePrice   float64        `gorm:"type:decimal(10,2);default:0" json:"purchase_price"`   // 购买价格
	PaymentAmount   float64        `gorm:"type:decimal(10,2);default:0" json:"payment_amount"`   // 实际支付金额
	
	// 有效期信息
	ValidDays       int            `json:"valid_days"`        // 有效天数
	StartTime       time.Time      `json:"start_time"`        // 开始时间
	EndTime         time.Time      `json:"end_time"`          // 结束时间
	ExpireTime      time.Time      `json:"expire_time"`       // 过期时间
	
	// 状态信息
	Status          int            `gorm:"type:tinyint;default:1" json:"status"` // 状态：1-正常，2-已过期，3-已用完
	UserID          int64          `gorm:"not null;index" json:"user_id"`       // 用户ID
	
	// 通用字段
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}