package BeautyDB

import (
	"time"

	"gorm.io/gorm"
)

// BeautyScheduleDO 技师排班表
type BeautyScheduleDO struct {
	ID uint `gorm:"primaryKey;autoIncrement" json:"id"`
	// TechnicianUserID 专门存储技师user_id
	TechnicianUserID uint      `gorm:"not null;index" json:"technician_user_id"`
	WorkDate         time.Time `gorm:"not null;index" json:"work_date"`

	// 时间段配置
	StartTime  string `gorm:"type:time;not null" json:"start_time"`
	EndTime    string `gorm:"type:time;not null" json:"end_time"`
	BreakStart string `gorm:"type:time" json:"break_start"`
	BreakEnd   string `gorm:"type:time" json:"break_end"`

	// 排班类型
	ScheduleType string `gorm:"type:varchar(20);default:'normal'" json:"schedule_type"`

	// 状态控制
	Status int `gorm:"default:1" json:"status"`

	// 系统字段
	IsDelete   int            `gorm:"default:0" json:"is_delete"`
	CreateTime time.Time      `gorm:"autoCreateTime" json:"create_time"`
	UpdateTime time.Time      `gorm:"autoUpdateTime" json:"update_time"`
	DeletedAt  gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (BeautyScheduleDO) TableName() string {
	return "beauty_schedule"
}
