package do

import (
	"time"

	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

type CardDO struct {
	ID            int64     `gorm:"column:id;primaryKey" json:"id"`
	Name          string    `gorm:"column:name" json:"name"`
	Subtitle      string    `gorm:"column:subtitle" json:"subtitle"`
	Description   string    `gorm:"column:description" json:"description"`
	Price         float64   `gorm:"column:price" json:"price"`
	OriginalPrice float64   `gorm:"column:original_price" json:"original_price"`
	CardType      int       `gorm:"column:card_type" json:"card_type"`
	TotalTimes    int       `gorm:"column:total_times" json:"total_times"`
	TotalAmount   float64   `gorm:"column:total_amount" json:"total_amount"`
	ValidDays     int       `gorm:"column:valid_days" json:"valid_days"`
	ServiceItems  string    `gorm:"column:service_items" json:"service_items"`
	Images        string    `gorm:"column:images" json:"images"`
	Status        int       `gorm:"column:status" json:"status"`
	IsHot         int       `gorm:"column:is_hot" json:"is_hot"`
	IsNew         int       `gorm:"column:is_new" json:"is_new"`
	Sort          int       `gorm:"column:sort" json:"sort"`
	SalesCount    int       `gorm:"column:sales_count" json:"sales_count"`
	IsDelete      int       `gorm:"column:is_delete" json:"is_delete"`
	CreateTime    time.Time `gorm:"column:create_time" json:"create_time"`
	UpdateTime    time.Time `gorm:"column:update_time" json:"update_time"`
}

func (CardDO) TableName() string {
	return "beauty_card"
}

func (c *CardDO) Query() *gorm.DB {
	return db.DB.Shop.Model(c)
}
