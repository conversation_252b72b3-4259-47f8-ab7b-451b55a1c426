package do

import (
	"time"
	"wnsys/shop/app/provider/db"

	"gorm.io/gorm"
)

// DemoDO 示例数据对象
type DemoDO struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`        // ID
	Name       string    `gorm:"column:name;not null" json:"name"`                    // 名称
	Status     string    `gorm:"column:status;size:1;default:0" json:"status"`        // 状态(0-正常 1-停用)
	DelFlag    string    `gorm:"column:del_flag;size:1;default:0" json:"delFlag"`     // 删除标志(0-存在 2-删除)
	CreateTime time.Time `gorm:"column:create_time;autoCreateTime" json:"createTime"` // 创建时间
	CreateBy   string    `gorm:"column:create_by" json:"createBy"`                    // 创建者
	UpdateTime time.Time `gorm:"column:update_time;autoUpdateTime" json:"updateTime"` // 更新时间
	UpdateBy   string    `gorm:"column:update_by" json:"updateBy"`                    // 更新者
}

// TableName 表名
func (DemoDO) TableName() string {
	return "demo"
}

// Query 获取查询构建器
func (d *DemoDO) Query() *gorm.DB {
	return db.DB.Shop.Model(d).Where("del_flag = ?", "0")
}

// Create 创建记录
func (d *DemoDO) Create() error {
	return d.Query().Create(d).Error
}

// Update 更新记录
func (d *DemoDO) Update() error {
	return d.Query().Where("id = ?", d.ID).Updates(d).Error
}

// Delete 删除记录
func (d *DemoDO) Delete() error {
	return d.Query().Where("id = ?", d.ID).Update("del_flag", "2").Error
}

// First 获取第一条记录
func (d *DemoDO) First() error {
	return d.Query().Where("id = ?", d.ID).First(d).Error
}
