package vo

// OrderDetailVO 订单详情
type OrderDetailVO struct {
	ID               int64          `json:"id"`                    // 订单ID
	OrderNo          string         `json:"orderNo"`               // 订单编号
	Status           string         `json:"status"`                // 订单状态
	StatusText       string         `json:"statusText"`            // 订单状态文字
	StatusDesc       string         `json:"statusDesc"`            // 订单状态描述
	TotalAmount      float64        `json:"totalAmount"`           // 订单总金额
	FreightAmount    float64        `json:"freightAmount"`         // 运费
	PayAmount        float64        `json:"payAmount"`             // 实付金额
	PayType          int8           `json:"payType"`               // 支付方式
	PayTypeText      string         `json:"payTypeText"`           // 支付方式文字
	Remark           string         `json:"remark"`                // 订单备注
	CreateTime       string         `json:"createTime"`            // 创建时间
	BusinessType     string         `json:"businessType"`          // 业务类型
	BusinessTypeText string         `json:"businessTypeText"`      // 业务类型文字
	Address          OrderAddressVO `json:"address"`               // 收货地址
	Goods            []OrderGoodsVO `json:"goods"`                 // 商品列表
	BookingInfo      *BookingInfoVO `json:"bookingInfo,omitempty"` // 预约信息
	CardInfo         *CardInfoVO    `json:"cardInfo,omitempty"`    // 套餐卡信息
}

// OrderAddressVO 订单收货地址
type OrderAddressVO struct {
	ReceiverName   string `json:"receiverName"`   // 收货人姓名
	ReceiverMobile string `json:"receiverMobile"` // 收货人手机号
	Province       string `json:"province"`       // 省份
	City           string `json:"city"`           // 城市
	District       string `json:"district"`       // 区县
	Detail         string `json:"detail"`         // 详细地址
}

// OrderGoodsVO 订单商品
type OrderGoodsVO struct {
	ID        int64   `json:"id"`        // 订单商品ID
	GoodsID   int64   `json:"goodsId"`   // 商品ID
	GoodsName string  `json:"goodsName"` // 商品名称
	Image     string  `json:"image"`     // 商品图片
	Price     float64 `json:"price"`     // 商品价格
	Quantity  int     `json:"quantity"`  // 购买数量
	Specs     string  `json:"specs"`     // 规格信息
}

// OrderListVO 订单列表
type OrderListVO struct {
	Total int64     `json:"total"` // 总数
	List  []OrderVO `json:"list"`  // 列表
}

type BookingInfoVO struct {
	ServiceName string `json:"serviceName"`
	Technician  string `json:"technician"`
	BookingDate string `json:"bookingDate"`
	StartTime   string `json:"startTime"`
	EndTime     string `json:"endTime"`
	Status      string `json:"status"`
}

type CardInfoVO struct {
	CardName    string `json:"cardName"`
	ValidFrom   string `json:"validFrom"`
	ValidTo     string `json:"validTo"`
	RemainCount int    `json:"remainCount"`
	Status      string `json:"status"`
}

// OrderVO 订单列表项
type OrderVO struct {
	ID            int64          `json:"id"`            // 订单ID
	OrderNo       string         `json:"orderNo"`       // 订单编号
	Status        string         `json:"status"`        // 订单状态
	StatusText    string         `json:"statusText"`    // 订单状态文字
	StatusDesc    string         `json:"statusDesc"`    // 订单状态描述
	TotalAmount   float64        `json:"totalAmount"`   // 订单总金额
	FreightAmount float64        `json:"freightAmount"` // 运费
	PayAmount     float64        `json:"payAmount"`     // 实付金额
	PayType       int8           `json:"payType"`       // 支付方式
	CreateTime    string         `json:"createTime"`    // 创建时间
	Goods         []OrderGoodsVO `json:"goods"`         // 商品列表
	BusinessType  string         `json:"businessType"`  // 业务类型
	BookingInfo   *BookingInfoVO `json:"bookingInfo,omitempty"`
	CardInfo      *CardInfoVO    `json:"cardInfo,omitempty"`
}
