package vo

import "time"

// CommentVO 评论视图对象
type CommentVO struct {
	ID           int64      `json:"id"`           // 评论ID
	UserID       int64      `json:"userId"`       // 用户ID
	Nickname     string     `json:"nickname"`     // 用户昵称
	Avatar       string     `json:"avatar"`       // 用户头像
	Content      string     `json:"content"`      // 评论内容
	Images       []string   `json:"images"`       // 评论图片
	Rate         int8       `json:"rate"`         // 评分(1-5星)
	IsAnonymous  bool       `json:"isAnonymous"`  // 是否匿名
	ReplyContent string     `json:"replyContent"` // 商家回复内容
	ReplyTime    *time.Time `json:"replyTime"`    // 商家回复时间
	CreateTime   time.Time  `json:"createTime"`   // 创建时间
	Specs        string     `json:"specs"`        // 商品规格信息
}

// CommentListVO 评论列表视图对象
type CommentListVO struct {
	Total int64       `json:"total"` // 总数
	List  []CommentVO `json:"list"`  // 评论列表
}
