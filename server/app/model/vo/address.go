package vo

// AddressVO 地址视图对象
type AddressVO struct {
	ID             int64  `json:"id"`              // 地址ID
	ReceiverName   string `json:"receiver_name"`   // 收货人姓名
	ReceiverMobile string `json:"receiver_mobile"` // 收货人手机号
	Province       string `json:"province"`        // 省份
	City           string `json:"city"`            // 城市
	District       string `json:"district"`        // 区县
	Detail         string `json:"detail"`          // 详细地址
	IsDefault      bool   `json:"is_default"`      // 是否默认地址
}
