package vo

// GoodsDetailVO 商品详情响应
type GoodsDetailVO struct {
	ID            int64    `json:"id"`            // 商品ID
	CategoryID    int64    `json:"categoryId"`    // 分类ID
	Name          string   `json:"name"`          // 商品名称
	Description   string   `json:"description"`   // 商品描述
	Detail        string   `json:"detail"`        // 商品详情
	DetailDesc    string   `json:"detail_desc"`   // 商品详情描述
	Price         float64  `json:"price"`         // 售价
	OriginalPrice float64  `json:"originalPrice"` // 原价
	Stock         int      `json:"stock"`         // 库存
	WarnStock     int      `json:"warnStock"`     // 库存预警值
	Unit          string   `json:"unit"`          // 单位
	Weight        float64  `json:"weight"`        // 重量
	Volume        float64  `json:"volume"`        // 体积
	Sales         int      `json:"sales"`         // 实际销量
	VirtualSales  int      `json:"virtualSales"`  // 虚拟销量
	TotalSales    int      `json:"totalSales"`    // 总销量（实际销量+虚拟销量）
	Status        int8     `json:"status"`        // 状态
	SpecType      int8     `json:"specType"`      // 规格类型
	Images        []string `json:"images"`        // 商品图片
	DetailImages  []string `json:"detailImages"`  // 详情图片
	Tags          []string `json:"tags"`          // 标签
	IsCollected   bool     `json:"isCollected"`   // 是否已收藏
	Services      []string `json:"services"`      // 服务承诺
	SpecInfo      SpecInfo `json:"specInfo"`      // 规格信息（兼容性，已有前端）

	// 新增：支持不同规格类型的数据
	SingleSpecData *SingleSpecData `json:"singleSpecData,omitempty"` // 单规格数据
	MultiSpecData  *MultiSpecData  `json:"multiSpecData,omitempty"`  // 多规格数据
	ComboSpecData  *ComboSpecData  `json:"comboSpecData,omitempty"`  // 套餐规格数据
}

// 规格信息
type SpecInfo struct {
	// 规格组
	SpecGroups []SpecGroup `json:"specGroups"`

	// SKU 信息
	Skus []Sku `json:"skus"`

	// 规格值可选状态映射
	// 格式: {"颜色_黑色": true, "颜色_白色": false}
	SpecValueSelectable map[string]bool `json:"specValueSelectable"`

	// 规格组合对应的 SKU 映射
	// 格式: {"黑色_16G": "SKU001", "白色_32G": "SKU002"}
	SpecCombinationToSku map[string]string `json:"specCombinationToSku"`

	// 默认选中的规格值
	DefaultSelected map[string]string `json:"defaultSelected"`
}

// 规格组
type SpecGroup struct {
	Name   string   `json:"name"`   // 规格名称
	Values []string `json:"values"` // 规格值列表
}

// SKU信息
type Sku struct {
	SkuCode string            `json:"skuCode"` // SKU编码
	Price   float64           `json:"price"`   // 价格
	Stock   int               `json:"stock"`   // 库存
	Specs   map[string]string `json:"specs"`   // 规格组合
}

// SingleSpecData 单规格数据
type SingleSpecData struct {
	Sku Sku `json:"sku"`
}

// MultiSpecData 多规格数据
type MultiSpecData struct {
	SpecGroups           []SpecGroup       `json:"specGroups"`
	Skus                 []Sku             `json:"skus"`
	SpecValueSelectable  map[string]bool   `json:"specValueSelectable"`
	SpecCombinationToSku map[string]string `json:"specCombinationToSku"`
	DefaultSelected      map[string]string `json:"defaultSelected"`
}

// ComboSpecData 套餐规格数据
type ComboSpecData struct {
	BaseSku     Sku          `json:"baseSku"`     // 套餐基础SKU
	ComboGroups []ComboGroup `json:"comboGroups"` // 套餐规格组
}

// ComboGroup 套餐规格组
type ComboGroup struct {
	Name        string        `json:"name"`
	Description string        `json:"description"`
	MinSelect   int           `json:"minSelect"`
	MaxSelect   int           `json:"maxSelect"`
	IsRequired  bool          `json:"isRequired"`
	Options     []ComboOption `json:"options"`
}

// ComboOption 套餐选项
type ComboOption struct {
	GoodsID     int64   `json:"goodsId"`
	Name        string  `json:"name"`
	Image       string  `json:"image"`
	AddPrice    float64 `json:"addPrice"`
	Quantity    int     `json:"quantity"`
	IsDefault   bool    `json:"isDefault"`
	Description string  `json:"description"`
}

// SkuStock SKU库存信息
type SkuStock struct {
	SkuCode      string  `json:"skuCode"`      // SKU编码
	SpecValueStr string  `json:"specValueStr"` // 规格值字符串
	Stock        int     `json:"stock"`        // 库存
	Price        float64 `json:"price"`        // 价格
}

// GoodsCommentVO 商品评价响应
type GoodsCommentVO struct {
	ID       int64    `json:"id"`       // 评论ID
	Nickname string   `json:"nickname"` // 用户昵称
	Avatar   string   `json:"avatar"`   // 用户头像
	Rate     int8     `json:"rate"`     // 评分（1-5）
	Content  string   `json:"content"`  // 评价内容
	Images   []string `json:"images"`   // 评价图片
	Time     string   `json:"time"`     // 评价时间
}

// GoodsCommentListVO 商品评价列表响应
type GoodsCommentListVO struct {
	Total int64            `json:"total"` // 总评论数
	List  []GoodsCommentVO `json:"list"`  // 评论列表
}

// CartCountVO 购物车数量响应
type CartCountVO struct {
	Count int `json:"count"` // 购物车商品总数
}

// CartItemVO 购物车商品项
type CartItemVO struct {
	ID       int64      `json:"id"`       // 购物车ID
	GoodsID  int64      `json:"goodsId"`  // 商品ID
	Name     string     `json:"name"`     // 商品名称
	Image    string     `json:"image"`    // 商品图片
	SkuCode  string     `json:"skuCode"`  // SKU编码
	Specs    []SpecItem `json:"specs"`    // 规格信息
	Price    float64    `json:"price"`    // 价格
	Quantity int        `json:"quantity"` // 数量
	Stock    int        `json:"stock"`    // 当前库存
	Status   int8       `json:"status"`   // 商品状态
	Selected bool       `json:"selected"` // 是否选中
	SubTotal float64    `json:"subTotal"` // 小计金额
}

// SpecItem 规格项
type SpecItem struct {
	Name  string `json:"name"`  // 规格名称
	Value string `json:"value"` // 规格值
}

// CartListVO 购物车列表响应
type CartListVO struct {
	Total      int64        `json:"total"`      // 总数
	TotalPrice float64      `json:"totalPrice"` // 总价
	List       []CartItemVO `json:"list"`       // 购物车列表
}
