package vo

import "time"

// DistributionOrderVO 分销订单视图对象
type DistributionOrderVO struct {
	ID               int64      `json:"id"`               // 分销订单ID
	OrderAmount      float64    `json:"orderAmount"`      // 订单金额
	CommissionRate   float64    `json:"commissionRate"`   // 佣金比例
	CommissionAmount float64    `json:"commissionAmount"` // 佣金金额
	Level            int        `json:"level"`            // 分销等级
	Status           int        `json:"status"`           // 状态
	UnfreezeTime     *time.Time `json:"unfreezeTime"`     // 解冻时间
	CreateTime       time.Time  `json:"createTime"`       // 创建时间
	// 关联订单信息
	OrderNo    string `json:"orderNo"`    // 订单编号
	GoodsName  string `json:"goodsName"`  // 商品名称
	GoodsImage string `json:"goodsImage"` // 商品图片
}

// DistributionOrderListVO 分销订单列表视图对象
type DistributionOrderListVO struct {
	List  []DistributionOrderVO `json:"list"`  // 订单列表
	Total int64                 `json:"total"` // 总数
}

// DistributionOrderStatsVO 分销订单统计视图对象
type DistributionOrderStatsVO struct {
	TotalCount      int64   `json:"totalCount"`      // 订单总数
	TotalIncome     float64 `json:"totalIncome"`     // 总收益
	MonthIncome     float64 `json:"monthIncome"`     // 本月收益
	AvailableIncome float64 `json:"availableIncome"` // 可提现金额
	FrozenIncome    float64 `json:"frozenIncome"`    // 冻结金额
	TodayIncome     float64 `json:"todayIncome"`     // 今日收益
}
