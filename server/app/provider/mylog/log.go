package mylog

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

const (
	LogEntryKey = "log_entry"
	LogDir      = "./logs"
	// 定义清理相关常量
	cleanupInterval = 1 * time.Hour  // 清理间隔
	maxIdleTime     = 24 * time.Hour // 最大空闲时间
)

// Logger 是对 logrus.Logger 的包装
type Logger struct {
	*logrus.Logger
	name    string
	writers map[string]*writerInfo // 文件写入器映射
	mu      sync.RWMutex           // 保护writers的并发访问
}

// writerInfo 写入器信息
type writerInfo struct {
	writer     io.Writer // 写入器
	file       *os.File  // 文件句柄
	lastAccess time.Time // 最后访问时间
}

// CustomFormatter 自定义日志格式化器
type CustomFormatter struct {
	TimestampFormat string
}

// Format 实现 logrus.Formatter 接口
func (f *CustomFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	// 获取时间戳
	timestamp := entry.Time.Format(f.TimestampFormat)
	if f.TimestampFormat == "" {
		timestamp = entry.Time.Format("2006-01-02 15:04:05")
	}

	// 获取日志级别
	level := fmt.Sprintf("[%s]", entry.Level.String())

	// 获取日志名称和请求ID
	loggerName := entry.Data["logger_name"]
	if loggerName == nil {
		loggerName = "-"
	}
	requestID := entry.Data["request_id"]
	if requestID == nil {
		requestID = "-"
	}
	delete(entry.Data, "logger_name")
	delete(entry.Data, "request_id")

	// 构建基本日志信息
	msg := fmt.Sprintf("[%s]%s[%s]%s", timestamp, level, requestID, entry.Message)

	// 将剩余字段转换为JSON
	if len(entry.Data) > 0 {
		for k, v := range entry.Data {
			jsonBytes, err := json.Marshal(v)
			if err == nil {
				jsonStr := string(jsonBytes)
				// 移除多余的引号和转义符
				if len(jsonStr) > 0 && jsonStr[0] == '"' && jsonStr[len(jsonStr)-1] == '"' {
					jsonStr = jsonStr[1 : len(jsonStr)-1]
				}
				jsonStr = strings.ReplaceAll(jsonStr, "\\\"", "\"")
				msg += fmt.Sprintf(" %s=%s", k, jsonStr)
			}
		}
	}

	return []byte(msg + "\n"), nil
}

// LogOption 日志选项
type LogOption struct {
	Fields   map[string]interface{}
	Filename string
	Context  *gin.Context
}

// parseLogOptions 解析日志可选参数
func (l *Logger) parseLogOptions(args ...interface{}) (*LogOption, []interface{}) {
	opt := &LogOption{}
	remainingArgs := make([]interface{}, 0)

	// 按顺序解析参数: ctx, message, fields, filename
	for i := 0; i < len(args); i++ {
		switch {
		// 第一个参数可能是上下文
		case i == 0:
			if ctx, ok := args[i].(*gin.Context); ok {
				opt.Context = ctx
				continue
			}
			remainingArgs = append(remainingArgs, args[i])
		// 第二个参数是消息，直接加入remainingArgs
		case i == 1:
			remainingArgs = append(remainingArgs, args[i])
		// 第三个参数可能是字段
		case i == 2:
			if fields, ok := args[i].(map[string]interface{}); ok {
				opt.Fields = fields
				continue
			}
			remainingArgs = append(remainingArgs, args[i])
		// 第四个参数可能是文件名
		case i == 3:
			if filename, ok := args[i].(string); ok {
				opt.Filename = filename
				continue
			}
			remainingArgs = append(remainingArgs, args[i])
		// 其他参数直接加入remainingArgs
		default:
			remainingArgs = append(remainingArgs, args[i])
		}
	}

	return opt, remainingArgs
}

// formatFields 格式化字段
func (l *Logger) formatFields(fields map[string]interface{}) logrus.Fields {
	logFields := make(logrus.Fields)
	for k, v := range fields {
		switch val := v.(type) {
		case map[string]interface{}:
			// 将嵌套的map转换为JSON字符串
			if jsonStr, err := json.Marshal(val); err == nil {
				logFields[k] = string(jsonStr)
			} else {
				logFields[k] = fmt.Sprintf("%+v", val)
			}
		case []interface{}:
			// 将数组转换为JSON字符串
			if jsonStr, err := json.Marshal(val); err == nil {
				logFields[k] = string(jsonStr)
			} else {
				logFields[k] = fmt.Sprintf("%+v", val)
			}
		default:
			logFields[k] = v
		}
	}
	return logFields
}

// Info 记录信息日志
func (l *Logger) Info(args ...interface{}) {
	opt, remainingArgs := l.parseLogOptions(args...)
	l.Log(opt.Context, logrus.InfoLevel, opt, remainingArgs...)
}

// Error 记录错误日志
func (l *Logger) Error(args ...interface{}) {
	opt, remainingArgs := l.parseLogOptions(args...)
	l.Log(opt.Context, logrus.ErrorLevel, opt, remainingArgs...)
}

// Warn 记录警告日志
func (l *Logger) Warn(args ...interface{}) {
	opt, remainingArgs := l.parseLogOptions(args...)
	l.Log(opt.Context, logrus.WarnLevel, opt, remainingArgs...)
}

// Fatal 记录致命错误日志
func (l *Logger) Fatal(args ...interface{}) {
	opt, remainingArgs := l.parseLogOptions(args...)
	l.Log(opt.Context, logrus.FatalLevel, opt, remainingArgs...)
}

// Log 通用日志记录
func (l *Logger) Log(ctx *gin.Context, level logrus.Level, opt *LogOption, args ...interface{}) {
	// 获取或创建writer
	var writer io.Writer
	if opt != nil && opt.Filename != "" {
		writer = l.getWriter(opt.Filename)
	} else {
		writer = l.getWriter(l.name)
	}

	// 创建Entry
	entry := logrus.NewEntry(l.Logger)

	// 添加日志名称
	entry = entry.WithField("logger_name", l.name)

	// 添加请求ID
	if ctx != nil {
		if requestID := getRequestID(ctx); requestID != "" {
			entry = entry.WithField("request_id", requestID)
		}
	}

	// 格式化并添加额外字段
	if opt != nil && opt.Fields != nil {
		entry = entry.WithFields(l.formatFields(opt.Fields))
	}

	// 设置输出并记录日志
	entry.Logger.SetOutput(writer)
	entry.Log(level, args...)
}

// getWriter 获取writer并更新访问时间
func (l *Logger) getWriter(filename string) io.Writer {
	l.mu.Lock()
	defer l.mu.Unlock()

	// 检查writer是否存在
	if info, exists := l.writers[filename]; exists {
		info.lastAccess = time.Now()
		return info.writer
	}

	// 创建新的writer
	logfile := filepath.Join(LogDir, fmt.Sprintf("%s_%s.log", filename, time.Now().Format("20060102")))
	file, err := os.OpenFile(logfile, os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0755)
	if err != nil {
		log.Printf("创建日志文件失败: %v", err)
		// 如果创建失败，返回默认writer
		return l.writers[l.name].writer
	}

	writer := io.MultiWriter(os.Stdout, file)
	l.writers[filename] = &writerInfo{
		writer:     writer,
		file:       file,
		lastAccess: time.Now(),
	}
	return writer
}

// ensureWriter 确保指定文件名的writer存在
func (l *Logger) ensureWriter(filename string) io.Writer {
	return l.getWriter(filename)
}

// getRequestID 从上下文中获取请求ID
func getRequestID(ctx *gin.Context) string {
	if ctx == nil {
		return "-"
	}

	// 从上下文中获取请求ID
	if requestID, exists := ctx.Get("request_id"); exists {
		if id, ok := requestID.(string); ok {
			return id
		}
	}

	// 尝试从请求头获取
	if requestID := ctx.GetHeader("X-Request-ID"); requestID != "" {
		return requestID
	}

	return "-"
}

// LogManager 管理多个日志实例
type LogManager struct {
	loggers map[string]*Logger
	mu      sync.RWMutex
}

// 全局日志管理器实例
var manager = &LogManager{
	loggers: make(map[string]*Logger),
}

// GetLogger 获取指定名称的日志实例
func GetLogger(name string) *Logger {
	manager.mu.Lock()
	defer manager.mu.Unlock()

	if logger, exists := manager.loggers[name]; exists {
		return logger
	}

	logger := newLogger(name)
	manager.loggers[name] = logger

	// 启动清理协程
	go logger.cleanupLoop()

	return logger
}

func GetDefaultLogger() *Logger {
	return GetLogger("wnadmin")
}

func newLogger(filename string) *Logger {
	// 确保日志目录存在
	if err := os.MkdirAll(LogDir, 0777); err != nil {
		log.Fatalf("创建日志目录失败: %v", err)
	}

	// 创建logrus实例
	lg := logrus.New()
	lg.SetFormatter(&CustomFormatter{
		TimestampFormat: "2006-01-02 15:04:05",
	})

	// 创建默认日志文件
	logfile := filepath.Join(LogDir, fmt.Sprintf("%s_%s.log", filename, time.Now().Format("20060102")))
	writer, err := os.OpenFile(logfile, os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0755)
	if err != nil {
		log.Fatalf("创建日志文件失败: %v", err)
	}

	defaultWriter := io.MultiWriter(os.Stdout, writer)
	lg.SetOutput(defaultWriter)
	lg.SetLevel(logrus.InfoLevel)

	return &Logger{
		Logger: lg,
		name:   filename,
		writers: map[string]*writerInfo{
			filename: &writerInfo{
				writer:     defaultWriter,
				file:       writer,
				lastAccess: time.Now(),
			},
		},
		mu: sync.RWMutex{},
	}
}

// 全局实例
var Log *Logger

func init() {
	Log = GetLogger("wnadmin")
	// 启动清理协程
	go Log.cleanupLoop()
}

// cleanupLoop 定期清理过期的writers
func (l *Logger) cleanupLoop() {
	ticker := time.NewTicker(cleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		l.cleanup()
	}
}

// cleanup 清理过期的writers
func (l *Logger) cleanup() {
	l.mu.Lock()
	defer l.mu.Unlock()

	now := time.Now()
	for filename, info := range l.writers {
		// 跳过默认writer
		if filename == l.name {
			continue
		}

		// 如果writer超过最大空闲时间，关闭并删除
		if now.Sub(info.lastAccess) > maxIdleTime {
			if info.file != nil {
				info.file.Close()
			}
			delete(l.writers, filename)
		}
	}
}
