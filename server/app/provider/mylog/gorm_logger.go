package mylog

import (
	"context"
	"errors"
	"fmt"
	"time"

	// "wnsys/shop/app/config" // 暂时注释掉，因为 model.Mysql 不在此包
	"wnsys/shop/app/provider/db/model" // 导入 db model 获取 Mysql 配置结构

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/utils"
)

// GormLogger 适配器，将 GORM 日志转发到 mylog
type GormLogger struct {
	ml            *Logger         // mylog 实例
	LogLevel      logger.LogLevel // GORM 的日志级别
	SlowThreshold time.Duration   // 慢查询阈值
}

// NewGormLogger 创建一个新的 GORM Logger 适配器
func NewGormLogger(ml *Logger, cfg model.Mysql) *GormLogger { // 使用 model.Mysql
	logLevel := logger.Info // 默认 GORM 级别
	switch cfg.LogMode {    // 从配置决定 GORM 日志级别
	case "silent":
		logLevel = logger.Silent
	case "error":
		logLevel = logger.Error
	case "warn":
		logLevel = logger.Warn
	}

	// 从配置读取慢查询阈值，如果配置中有定义的话 (假设有 SlowThresholdMs 字段)
	slowThreshold := 200 * time.Millisecond // 默认值
	if cfg.SlowThresholdMs > 0 {
		slowThreshold = time.Duration(cfg.SlowThresholdMs) * time.Millisecond
	}

	return &GormLogger{
		ml:            ml,
		LogLevel:      logLevel,
		SlowThreshold: slowThreshold,
	}
}

// LogMode 实现 logger.Interface
func (l *GormLogger) LogMode(level logger.LogLevel) logger.Interface {
	newLogger := *l
	newLogger.LogLevel = level
	return &newLogger
}

// Info 实现 logger.Interface
func (l *GormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Info {
		// 尝试从 context 获取 gin.Context
		var ginCtx *gin.Context
		if ctx != nil {
			ginCtx, _ = ctx.(*gin.Context)
		}
		l.ml.Info(ginCtx, fmt.Sprintf(msg, data...))
	}
}

// Warn 实现 logger.Interface
func (l *GormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Warn {
		var ginCtx *gin.Context
		if ctx != nil {
			ginCtx, _ = ctx.(*gin.Context)
		}
		l.ml.Warn(ginCtx, fmt.Sprintf(msg, data...))
	}
}

// Error 实现 logger.Interface
func (l *GormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.LogLevel >= logger.Error {
		var ginCtx *gin.Context
		if ctx != nil {
			ginCtx, _ = ctx.(*gin.Context)
		}
		l.ml.Error(ginCtx, fmt.Sprintf(msg, data...))
	}
}

// Trace 实现 logger.Interface，用于记录 SQL 语句和执行时间
func (l *GormLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	if l.LogLevel <= logger.Silent {
		return
	}

	// 尝试从 context 获取 gin.Context
	var ginCtx *gin.Context
	if ctx != nil {
		ginCtx, _ = ctx.(*gin.Context)
	}

	elapsed := time.Since(begin)
	sql, rows := fc()
	fields := logrus.Fields{ // 使用 logrus.Fields 以便 mylog 正确处理
		"sql":      sql,
		"duration": fmt.Sprintf("%.3fms", float64(elapsed.Nanoseconds())/1e6),
		"rows":     rows,
		"source":   utils.FileWithLineNum(), // GORM 提供的源文件信息
	}

	switch {
	case err != nil && l.LogLevel >= logger.Error && !errors.Is(err, logger.ErrRecordNotFound):
		// SQL 执行出错（且不是 RecordNotFound 错误），记录 Error 日志
		fields["error"] = err.Error()
		l.ml.Error(ginCtx, "GORM Trace", fields) // 使用 map[string]interface{} 传递字段
	case elapsed > l.SlowThreshold && l.SlowThreshold != 0 && l.LogLevel >= logger.Warn:
		// 慢查询，记录 Warn 日志
		slowLog := fmt.Sprintf("SLOW SQL >= %v", l.SlowThreshold)
		l.ml.Warn(ginCtx, slowLog, fields)
	case l.LogLevel >= logger.Info:
		// 普通 SQL 执行，记录 Info 日志
		l.ml.Info(ginCtx, "GORM Trace", fields)
	}
}
