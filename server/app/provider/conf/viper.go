package conf

import (
	"fmt"
	"time"
	"wnsys/shop/app/config"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

// 定义需要加载的配置及其目标变量
var configsToLoad = map[string]interface{}{
	"app": &config.App, // 对应 etc/app.yaml -> config.App
	"db":  &config.DB,  // 对应 etc/db.yaml  -> config.DB
	// 如果将来有其他配置，在这里添加，例如：
	// "redis": &config.Redis,
}

func init() {
	// 遍历注册表并加载配置
	for name, target := range configsToLoad {
		loadAndWatchConfig(name, target)
	}
}

// Viper //
// 优先级: 命令行 > 环境变量 > 默认值
// Author [SliverHorn](https://github.com/SliverHorn)
func Viper(yaml string) *viper.Viper {
	file := fmt.Sprintf("etc/%s.yaml", yaml)
	v := viper.New()
	v.SetConfigFile(file)
	v.SetConfigType("yaml")

	maxRetries := 3
	retryInterval := time.Millisecond * 500

	var err error
	for i := 0; i < maxRetries; i++ {
		err = v.ReadInConfig()
		if err == nil {
			return v
		}

		fmt.Printf("配置文件[%s]读取失败，正在进行第%d次重试: %s\n", file, i+1, err.Error())
		time.Sleep(retryInterval)
		// 指数退避，但最大等待时间不超过1秒
		retryInterval = time.Duration(float64(retryInterval) * 1.5)
		if retryInterval > time.Second {
			retryInterval = time.Second
		}
	}

	// 如果所有重试都失败，则panic
	panic(fmt.Errorf("Fatal error config file [%s]: %w \n", file, err))
}

// 通用的加载和监视配置的函数
func loadAndWatchConfig(name string, target interface{}) {
	v := Viper(name)
	if err := v.Unmarshal(target); err != nil {
		panic(fmt.Errorf("unable to decode config [%s] into struct: %w", name, err))
	}
	// 添加文件更改监视
	v.WatchConfig()
	v.OnConfigChange(func(e fsnotify.Event) {
		fmt.Printf("Config file [%s] changed: %s\n", name, e.Name)
		if err := v.Unmarshal(target); err != nil {
			fmt.Printf("Error re-unmarshalling config [%s]: %v\n", name, err)
		} else {
			fmt.Printf("Config [%s] reloaded successfully\n", name)
		}
	})
	fmt.Printf("Config [%s] loaded and watching for changes.\n", name)
}
