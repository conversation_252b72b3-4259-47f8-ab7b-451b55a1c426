package db

import (
	"wnsys/shop/app/config"
	"wnsys/shop/app/provider/db/driver"
	"wnsys/shop/app/provider/mylog"

	"github.com/go-redis/redis"
	"gorm.io/gorm"
)

type DBModel struct {
	Shop  *gorm.DB
	Redis redis.UniversalClient
}

var DB DBModel

// Setup 初始化数据库连接
func Setup() {
	dbLogger := mylog.GetLogger("db")
	dbLogger.Info("初始化数据库连接")

	// 初始化 MySQL
	mysqlDB := driver.GormMysqlByConfig(config.DB.Mysql["default"])
	if mysqlDB == nil {
		dbLogger.Fatal("MySQL 数据库连接失败")
		return
	}
	DB.Shop = mysqlDB
	dbLogger.Info("MySQL 数据库连接成功")

	// 初始化 Redis
	redisClient := driver.RedisByConfig(config.DB.Redis["default"])
	if redisClient == nil {
		dbLogger.Fatal("Redis 连接失败")
		return
	}
	DB.Redis = redisClient

	// 测试 Redis 连接
	_, err := DB.Redis.Ping().Result()
	if err != nil {
		dbLogger.Fatal("Redis 连接测试失败: " + err.Error())
		return
	}
	dbLogger.Info("Redis 连接成功")
}
