package driver

import (
	"fmt"
	"time"
	"wnsys/shop/app/provider/db/model"

	"github.com/go-redis/redis"
)

func RedisByConfig(redisCfg model.Redis) redis.UniversalClient {
	var client redis.UniversalClient
	maxRetries := 3
	retryInterval := time.Millisecond * 500

	for i := 0; i < maxRetries; i++ {
		// 使用集群模式
		if redisCfg.UseCluster {
			client = redis.NewClusterClient(&redis.ClusterOptions{
				Addrs:    redisCfg.ClusterAddrs,
				Password: redisCfg.Password,
			})
		} else {
			// 使用单例模式
			client = redis.NewClient(&redis.Options{
				Addr:     redisCfg.Addr,
				Password: redisCfg.Password,
				DB:       redisCfg.DB,
			})
		}

		// 测试连接
		pong, err := client.Ping().Result()
		if err == nil {
			fmt.Println("redis链接成功" + pong)
			return client
		}

		fmt.Printf("Redis连接失败，正在进行第%d次重试: %s\n", i+1, err.Error())
		time.Sleep(retryInterval)
		// 指数退避，但最大等待时间不超过1秒
		retryInterval = time.Duration(float64(retryInterval) * 1.5)
		if retryInterval > time.Second {
			retryInterval = time.Second
		}
	}

	panic("Redis连接失败，已达到最大重试次数")
}
