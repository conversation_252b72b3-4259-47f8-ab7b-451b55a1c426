package driver

import (
	"fmt"
	"sync"
	"time"
	"wnsys/shop/app/config"
	"wnsys/shop/app/provider/db/model"

	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"

	_ "github.com/go-sql-driver/mysql"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var (
	// 单例模式，保存全局数据库连接实例
	dbInstance *gorm.DB
	// 数据库连接锁，避免并发问题
	dbLock sync.RWMutex
	// 数据库配置信息
	mysqlConfig mysql.Config
	gormConfig  *gorm.Config
)

// GormMysqlByConfig 初始化Mysql数据库用过传入配置
func GormMysqlByConfig(m model.Mysql) *gorm.DB {
	if m.Dbname == "" {
		return nil
	}

	// 使用读写锁保护数据库连接实例
	dbLock.RLock()
	if dbInstance != nil {
		// 检查连接是否有效
		sqlDB, err := dbInstance.DB()
		if err == nil {
			if err = sqlDB.Ping(); err == nil {
				dbLock.RUnlock()
				return dbInstance
			}
		}
	}
	dbLock.RUnlock()

	// 需要创建或重新创建连接
	dbLock.Lock()
	defer dbLock.Unlock()

	// 再次检查，避免在获取写锁期间其他goroutine已经初始化
	if dbInstance != nil {
		sqlDB, err := dbInstance.DB()
		if err == nil {
			if err = sqlDB.Ping(); err == nil {
				return dbInstance
			}
		}
	}

	// 初始化MySQL配置
	mysqlConfig = mysql.Config{
		DSN:                       m.Dsn(), // DSN data source name
		DefaultStringSize:         191,     // string 类型字段的默认长度
		SkipInitializeWithVersion: false,   // 根据版本自动配置
		DisableDatetimePrecision:  true,    // 禁用 datetime 精度
		DontSupportRenameIndex:    true,    // 重命名索引优化
		DontSupportRenameColumn:   true,    // 重命名列优化
	}
	fmt.Println("MySQL DSN: ", m.Dsn())

	// 获取日志级别配置
	mysqlConfig2 := config.DB.Mysql["default"]
	var logLevel logger.LogLevel
	switch mysqlConfig2.LogMode {
	case "silent":
		logLevel = logger.Silent
	case "error":
		logLevel = logger.Error
	case "warn":
		logLevel = logger.Warn
	case "info":
		logLevel = logger.Info
	default:
		logLevel = logger.Info
	}

	// 初始化GORM配置
	gormConfig = &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true, // 使用单数表名
		},
		SkipDefaultTransaction: true, // 禁用默认事务
		PrepareStmt:            true, // 预编译缓存
	}

	// 尝试连接数据库，带重试机制
	var db *gorm.DB
	var err error
	maxRetries := 3
	retryInterval := time.Millisecond * 500 // 减少初始等待时间到500ms

	for i := 0; i < maxRetries; i++ {
		db, err = gorm.Open(mysql.New(mysqlConfig), gormConfig)
		if err == nil {
			break
		}

		fmt.Printf("数据库连接失败，正在进行第%d次重试: %s\n", i+1, err.Error())
		time.Sleep(retryInterval)
		// 指数退避，但最大等待时间不超过1秒
		retryInterval = time.Duration(float64(retryInterval) * 1.5)
		if retryInterval > time.Second {
			retryInterval = time.Second
		}
	}

	if err != nil {
		panic(fmt.Sprintf("最终数据库连接失败: %s", err.Error()))
	}

	// 配置连接池
	db.InstanceSet("gorm:table_options", "ENGINE=InnoDB")
	sqlDB, _ := db.DB()
	sqlDB.SetMaxIdleConns(m.MaxIdleConns)      // 空闲连接池大小
	sqlDB.SetMaxOpenConns(m.MaxOpenConns)      // 最大连接数
	sqlDB.SetConnMaxLifetime(time.Minute * 1)  // 连接最大生命周期
	sqlDB.SetConnMaxIdleTime(time.Minute * 10) // 空闲连接超时时间，增加到1小时

	// 保存全局实例
	dbInstance = db

	// 启动心跳检测，维护连接
	go startHeartbeat()

	return dbInstance
}

// startHeartbeat 启动数据库连接心跳检测
func startHeartbeat() {
	heartbeatInterval := time.Second * 30 // 将检测频率从1秒改为30秒
	maxRetries := 3

	for {
		time.Sleep(heartbeatInterval)

		// 使用读锁检查连接状态
		dbLock.RLock()
		if dbInstance == nil {
			dbLock.RUnlock()
			continue
		}

		sqlDB, err := dbInstance.DB()
		if err != nil {
			dbLock.RUnlock()
			reconnect(maxRetries)
			continue
		}

		err = sqlDB.Ping()
		dbLock.RUnlock()

		if err != nil {
			fmt.Println("MySQL连接心跳检测失败:", err)
			reconnect(maxRetries)
		}
	}
}

// reconnect 重新连接数据库
func reconnect(maxRetries int) {
	dbLock.Lock()
	defer dbLock.Unlock()

	// 再次检查连接，可能在获取锁的过程中已被其他协程修复
	if dbInstance != nil {
		sqlDB, err := dbInstance.DB()
		if err == nil {
			if err = sqlDB.Ping(); err == nil {
				return
			}
		}
	}

	fmt.Println("尝试重新连接MySQL数据库...")
	retryInterval := time.Second * 2

	for i := 0; i < maxRetries; i++ {
		db, err := gorm.Open(mysql.New(mysqlConfig), gormConfig)
		if err == nil {
			// 配置连接池参数
			sqlDB, _ := db.DB()

			// 从当前配置中获取连接池设置
			currentDB, _ := dbInstance.DB()
			maxOpen := 10 // 默认值
			maxIdle := 5  // 默认值

			stats := currentDB.Stats()
			if stats.MaxOpenConnections > 0 {
				maxOpen = stats.MaxOpenConnections
			}

			sqlDB.SetMaxOpenConns(maxOpen)
			sqlDB.SetMaxIdleConns(maxIdle)
			sqlDB.SetConnMaxLifetime(time.Hour * 4)
			sqlDB.SetConnMaxIdleTime(time.Hour * 1)

			// 更新全局实例
			dbInstance = db
			fmt.Println("MySQL数据库重连成功")
			return
		}

		fmt.Printf("数据库重连失败，第%d次尝试: %s\n", i+1, err.Error())
		time.Sleep(retryInterval)
		// 指数退避策略
		retryInterval *= 2
	}

	fmt.Println("所有重连尝试均失败，请检查数据库配置和网络连接")
}
