package comment

import (
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/comment"

	"github.com/gin-gonic/gin"
)

// Controller 评论控制器
type Controller struct {
	commentService *comment.CommentService
}

// NewController 创建评论控制器
func NewController() *Controller {
	return &Controller{
		commentService: comment.NewService(),
	}
}

// CreateComment 创建评论
func (c *Controller) CreateComment(ctx *gin.Context) {
	var req dto.CreateCommentReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取用户ID
	userID := ctx.GetInt64("userId")
	if userID == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 创建评论
	err := c.commentService.CreateComment(&req, userID)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("评论成功", ctx)
}

// GetGoodsComments 获取商品评论列表
func (c *Controller) GetGoodsComments(ctx *gin.Context) {
	var req dto.GetGoodsCommentsReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取评论列表
	comments, err := c.commentService.GetGoodsComments(&req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(comments, ctx)
}
