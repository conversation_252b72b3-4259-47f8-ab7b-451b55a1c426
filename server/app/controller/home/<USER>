package home

import (
	"fmt"
	"log"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/goods"
	"wnsys/shop/app/service/home"

	"github.com/gin-gonic/gin"
)

type HomeController struct {
	homeService  *home.HomeService
	goodsService *goods.Service
}

func NewHomeController() *HomeController {
	return &HomeController{
		homeService:  home.NewHomeService(),
		goodsService: goods.NewService(),
	}
}

// GetBannerList 获取轮播图列表
func (c *HomeController) GetBannerList(ctx *gin.Context) {
	var query dto.BannerQuery
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	list, err := c.homeService.GetBannerList(&query)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(list, ctx)
}

// GetCategoryList 获取分类导航
func (c *HomeController) GetCategoryList(ctx *gin.Context) {
	var query dto.CategoryQuery
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	list, err := c.homeService.GetCategoryList(&query)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(list, ctx)
}

// GetActivityList 获取活动列表
func (c *HomeController) GetActivityList(ctx *gin.Context) {
	var query dto.ActivityQuery
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	list, err := c.homeService.GetActivityList(&query)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(list, ctx)
}

// GetGoodsList 获取商品列表
func (c *HomeController) GetGoodsList(ctx *gin.Context) {
	var query dto.GoodsQuery
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取用户ID（可能为0，表示未登录）
	userID, _ := ctx.Get("userId")
	log.Println(fmt.Sprintf("userID: %v", userID))
	var uid int64
	if userID != nil {
		uid = userID.(int64)
	}

	list, err := c.goodsService.GetGoodsList(&query, uid)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(list, ctx)
}

// GetTagList 获取标签列表
func (c *HomeController) GetTagList(ctx *gin.Context) {
	var query dto.TagQuery
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	list, err := c.homeService.GetTagList(&query)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(list, ctx)
}
