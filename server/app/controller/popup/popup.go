package popup

import (
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/popup"

	"github.com/gin-gonic/gin"
)

type PopupController struct {
	popupService *popup.PopupService
}

func NewPopupController() *PopupController {
	return &PopupController{
		popupService: &popup.PopupService{},
	}
}

// GetActivePopups 获取用户的生效弹窗列表
// @Summary 获取生效弹窗
// @Description 获取当前用户可以显示的弹窗列表
// @Tags 弹窗管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=[]dto.PopupActiveResp} "成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/popup/active [get]
func (c *PopupController) GetActivePopups(ctx *gin.Context) {
	// 从中间件获取用户ID，如果未登录则为0
	userID := ctx.GetInt64("userId")

	// 构造请求对象
	req := &dto.PopupActiveListReq{
		UserID: userID,
	}

	popups, err := c.popupService.GetActivePopups(req)
	if err != nil {
		response.Error(500, "获取弹窗列表失败: "+err.Error(), ctx)
		return
	}

	response.SuccessWithData(popups, ctx)
}

// RecordPopupLog 记录弹窗操作日志
// @Summary 记录弹窗日志
// @Description 记录用户对弹窗的操作（显示、点击、关闭）
// @Tags 弹窗管理
// @Accept json
// @Produce json
// @Param request body dto.PopupLogReq true "日志记录请求"
// @Success 200 {object} response.Response "成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/popup/log [post]
func (c *PopupController) RecordPopupLog(ctx *gin.Context) {
	var req dto.PopupLogReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(400, "参数错误: "+err.Error(), ctx)
		return
	}

	if err := c.popupService.RecordPopupLog(&req); err != nil {
		response.Error(500, "记录日志失败: "+err.Error(), ctx)
		return
	}

	response.Success(ctx)
}

// GetPopupStats 获取弹窗统计信息
// @Summary 获取弹窗统计
// @Description 获取弹窗的统计数据（总数、显示次数、点击率等）
// @Tags 弹窗管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=dto.PopupStatsResp} "成功"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/popup/stats [get]
func (c *PopupController) GetPopupStats(ctx *gin.Context) {
	stats, err := c.popupService.GetPopupStats()
	if err != nil {
		response.Error(500, "获取统计信息失败: "+err.Error(), ctx)
		return
	}

	response.SuccessWithData(stats, ctx)
}
