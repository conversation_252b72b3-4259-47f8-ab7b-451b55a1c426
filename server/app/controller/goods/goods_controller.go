package goods

import (
	"strconv"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/comment"
	"wnsys/shop/app/service/goods"

	"github.com/gin-gonic/gin"
)

// Controller 商品控制器
type Controller struct {
	service        *goods.Service
	commentService *comment.CommentService
}

// NewController 创建商品控制器
func NewController() *Controller {
	return &Controller{
		service:        &goods.Service{},
		commentService: comment.NewService(),
	}
}

// GetDetail 获取商品详情
func (c *Controller) GetDetail(ctx *gin.Context) {
	// 获取商品ID
	goodsIDStr := ctx.Query("id")
	if goodsIDStr == "" {
		response.Error(400, "商品ID不能为空", ctx)
		return
	}

	goodsID, err := strconv.ParseInt(goodsIDStr, 10, 64)
	if err != nil {
		response.Error(400, "商品ID格式错误", ctx)
		return
	}

	// 获取用户ID（可选）
	userID := ctx.GetInt64("userId")

	// 获取商品详情
	detail, err := c.service.GetDetail(goodsID, userID)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(detail, ctx)
}

// GetComments 获取商品评论
func (c *Controller) GetComments(ctx *gin.Context) {
	// 获取商品ID
	goodsIDStr := ctx.Query("goodsId")
	if goodsIDStr == "" {
		response.Error(400, "商品ID不能为空", ctx)
		return
	}

	goodsID, err := strconv.ParseInt(goodsIDStr, 10, 64)
	if err != nil {
		response.Error(400, "商品ID格式错误", ctx)
		return
	}

	// 获取分页参数
	pageStr := ctx.DefaultQuery("page", "1")
	page, err := strconv.Atoi(pageStr)
	if err != nil {
		response.Error(400, "页码格式错误", ctx)
		return
	}

	sizeStr := ctx.DefaultQuery("size", "10")
	size, err := strconv.Atoi(sizeStr)
	if err != nil {
		response.Error(400, "每页数量格式错误", ctx)
		return
	}

	// 获取评论列表
	comments, err := c.commentService.GetGoodsComments(&dto.GetGoodsCommentsReq{
		GoodsID: goodsID,
		Page:    page,
		Size:    size,
	})
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(comments, ctx)
}

// Collect 收藏/取消收藏商品
func (c *Controller) Collect(ctx *gin.Context) {
	// 获取商品ID（从表单参数）
	goodsIDStr := ctx.PostForm("goodsId")
	if goodsIDStr == "" {
		response.Error(400, "商品ID不能为空", ctx)
		return
	}

	goodsID, err := strconv.ParseInt(goodsIDStr, 10, 64)
	if err != nil {
		response.Error(400, "商品ID格式错误", ctx)
		return
	}

	// 获取操作类型（从表单参数）
	typeStr := ctx.PostForm("type")
	if typeStr == "" {
		response.Error(400, "操作类型不能为空", ctx)
		return
	}

	collectType, err := strconv.Atoi(typeStr)
	if err != nil {
		response.Error(400, "操作类型格式错误", ctx)
		return
	}

	// 获取用户ID
	userID := ctx.GetInt64("userId")
	if userID == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 执行收藏操作
	err = c.service.Collect(userID, goodsID, collectType)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	if collectType == 1 {
		response.SuccessWithMessage("收藏成功", ctx)
	} else {
		response.SuccessWithMessage("取消收藏成功", ctx)
	}
}

// Like 点赞/取消点赞商品
func (c *Controller) Like(ctx *gin.Context) {
	// 获取商品ID（从表单参数）
	goodsIDStr := ctx.PostForm("goodsId")
	if goodsIDStr == "" {
		response.Error(400, "商品ID不能为空", ctx)
		return
	}

	goodsID, err := strconv.ParseInt(goodsIDStr, 10, 64)
	if err != nil {
		response.Error(400, "商品ID格式错误", ctx)
		return
	}

	// 获取操作类型（从表单参数）
	typeStr := ctx.PostForm("type")
	if typeStr == "" {
		response.Error(400, "操作类型不能为空", ctx)
		return
	}

	likeType, err := strconv.Atoi(typeStr)
	if err != nil {
		response.Error(400, "操作类型格式错误", ctx)
		return
	}

	// 获取用户ID
	userID := ctx.GetInt64("userId")
	if userID == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 执行点赞操作
	err = c.service.Like(userID, goodsID, likeType)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	if likeType == 1 {
		response.SuccessWithMessage("点赞成功", ctx)
	} else {
		response.SuccessWithMessage("取消点赞成功", ctx)
	}
}

// CheckStock 检查商品库存
func (c *Controller) CheckStock(ctx *gin.Context) {
	var req dto.CheckStockReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 检查库存
	stock, err := c.service.CheckSpecStock(req.GoodsID, req.Specs)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(gin.H{
		"stock": stock,
	}, ctx)
}

// AddFootprint 添加商品足迹
func (c *Controller) AddFootprint(ctx *gin.Context) {
	// 获取商品ID
	goodsIDStr := ctx.Param("id")
	if goodsIDStr == "" {
		response.Error(400, "商品ID不能为空", ctx)
		return
	}

	goodsID, err := strconv.ParseInt(goodsIDStr, 10, 64)
	if err != nil {
		response.Error(400, "商品ID格式错误", ctx)
		return
	}

	// 获取用户ID
	userID := ctx.GetInt64("userId")
	if userID == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 添加足迹
	err = c.service.AddFootprint(userID, goodsID)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("添加成功", ctx)
}

// GetGoodsList 获取商品列表
func (c *Controller) GetGoodsList(ctx *gin.Context) {
	var query dto.GoodsQuery
	if err := ctx.ShouldBindQuery(&query); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}
	userId := ctx.GetInt64("userId")
	list, err := c.service.GetGoodsList(&query, userId)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(list, ctx)
}
