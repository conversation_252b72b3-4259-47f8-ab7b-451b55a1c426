package order

import (
	"strconv"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/order"

	"github.com/gin-gonic/gin"
)

type OrderController struct {
	orderService *order.OrderService
}

// NewController 创建订单控制器
func NewController() *OrderController {
	return &OrderController{
		orderService: order.NewService(),
	}
}

// GetDetail 获取订单详情
func (c *OrderController) GetDetail(ctx *gin.Context) {
	// 获取订单ID
	orderIDStr := ctx.Param("id")
	if orderIDStr == "" {
		response.Error(400, "订单ID不能为空", ctx)
		return
	}

	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		response.Error(400, "订单ID格式错误", ctx)
		return
	}

	// 获取用户ID
	userID := ctx.GetInt64("userId")
	if userID == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 获取订单详情
	detail, err := c.orderService.GetOrderDetail(orderID, userID)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(detail, ctx)
}

// CalculateOrder 计算订单金额
func (c *OrderController) CalculateOrder(ctx *gin.Context) {
	var req dto.OrderCalculateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 计算订单金额
	result, err := c.orderService.CalculateOrder(&req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(result, ctx)
}

// CreateOrder 创建订单
func (c *OrderController) CreateOrder(ctx *gin.Context) {
	var req dto.CreateOrderReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取用户ID
	userID := ctx.GetInt64("userId")

	// 创建订单
	result, err := c.orderService.CreateOrder(userID, &req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(result, ctx)
}

// Pay 支付订单
func (c *OrderController) Pay(ctx *gin.Context) {
	var req dto.PayOrderReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取用户ID
	userID := ctx.GetInt64("userId")

	// 支付订单
	result, err := c.orderService.PayOrder(userID, &req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(result, ctx)
}

// GetList 获取订单列表
func (c *OrderController) GetList(ctx *gin.Context) {
	// 获取查询参数
	status := ctx.Query("status")
	businessType := ctx.Query("businessType")
	pageStr := ctx.DefaultQuery("page", "1")
	sizeStr := ctx.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取用户ID
	userID := ctx.GetInt64("userId")

	// 获取订单列表
	list, err := c.orderService.GetOrderList(userID, status, page, size, businessType)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(list, ctx)
}

// CancelOrder 取消订单
func (c *OrderController) CancelOrder(ctx *gin.Context) {
	// 获取订单ID
	orderIDStr := ctx.Param("id")
	if orderIDStr == "" {
		response.Error(400, "订单ID不能为空", ctx)
		return
	}

	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取用户ID
	userID := ctx.GetInt64("userId")

	// 取消订单
	err = c.orderService.CancelOrder(userID, orderID)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("取消成功", ctx)
}

// ConfirmReceive 确认收货
func (c *OrderController) ConfirmReceive(ctx *gin.Context) {
	// 获取订单ID
	orderIDStr := ctx.Param("id")
	if orderIDStr == "" {
		response.Error(400, "订单ID不能为空", ctx)
		return
	}

	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取用户ID
	userID := ctx.GetInt64("userId")

	// 确认收货
	err = c.orderService.ConfirmReceive(userID, orderID)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("确认收货成功", ctx)
}

// DeleteOrder 删除订单
func (c *OrderController) DeleteOrder(ctx *gin.Context) {
	// 获取订单ID
	orderIDStr := ctx.Param("id")
	if orderIDStr == "" {
		response.Error(400, "订单ID不能为空", ctx)
		return
	}

	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取用户ID
	userID := ctx.GetInt64("userId")

	// 删除订单
	err = c.orderService.DeleteOrder(userID, orderID)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("删除成功", ctx)
}
