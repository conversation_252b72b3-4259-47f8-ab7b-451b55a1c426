package address

import (
	"strconv"
	"wnsys/shop/app/common/request"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/address"

	"github.com/gin-gonic/gin"
)

type Controller struct {
	service *address.Service
}

func NewController() *Controller {
	return &Controller{
		service: address.NewService(),
	}
}

// GetDefault 获取默认地址
func (c *Controller) GetDefault(ctx *gin.Context) {
	// 从上下文中获取用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 获取默认地址
	address, err := c.service.GetDefaultAddress(userId)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(address, ctx)
}

// GetList 获取地址列表
func (c *Controller) GetList(ctx *gin.Context) {
	// 从上下文中获取用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 获取地址列表
	list, err := c.service.GetAddressList(userId)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(list, ctx)
}

// Add 添加地址
func (c *Controller) Add(ctx *gin.Context) {
	var req dto.AddressReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 从上下文中获取用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 添加地址
	err := c.service.AddAddress(userId, &req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("添加成功", ctx)
}

// Update 更新地址
func (c *Controller) Update(ctx *gin.Context) {
	var req dto.AddressReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 从上下文中获取用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 更新地址
	err := c.service.UpdateAddress(userId, &req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("更新成功", ctx)
}

// Delete 删除地址
func (c *Controller) Delete(ctx *gin.Context) {
	var req dto.DeleteAddressReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 从上下文中获取用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 删除地址
	err := c.service.DeleteAddress(userId, req.ID)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("删除成功", ctx)
}

// SetDefault 设置默认地址
func (c *Controller) SetDefault(ctx *gin.Context) {
	var req dto.SetDefaultAddressReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 从上下文中获取用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 设置默认地址
	err := c.service.SetDefaultAddress(userId, req.ID)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("设置成功", ctx)
}

// GetDetail 获取地址详情
func (c *Controller) GetDetail(ctx *gin.Context) {
	// 从URL参数中获取地址ID
	idStr := ctx.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.Error(400, "无效的地址ID", ctx)
		return
	}

	// 从上下文中获取用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 获取地址详情
	address, err := c.service.GetAddressDetail(userId, id)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(address, ctx)
}
