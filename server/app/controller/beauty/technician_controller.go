package beauty

import (
	"fmt"
	"strconv"
	"wnsys/shop/app/common/request"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/beauty"

	"github.com/gin-gonic/gin"
)

type TechnicianController struct {
	technicianService *beauty.TechnicianService
}

func NewTechnicianController() *TechnicianController {
	return &TechnicianController{
		technicianService: beauty.NewTechnicianService(),
	}
}

// GetTechnicianProfile 获取技师个人信息
func (c *TechnicianController) GetTechnicianProfile(ctx *gin.Context) {
	userID := request.GetUserID(ctx)
	if userID == 0 {
		response.ErrorWithWError(werror.New(401, "用户未登录"), ctx)
		return
	}

	result, err := c.technicianService.GetTechnicianProfile(int64(userID))
	if err != nil {
		response.ErrorWithWError(werror.New(500, "获取技师信息失败: "+err.Error()), ctx)
		return
	}

	response.SuccessWithData(result, ctx)
}

// UpdateTechnicianProfile 更新技师个人信息
func (c *TechnicianController) UpdateTechnicianProfile(ctx *gin.Context) {
	userID := request.GetUserID(ctx)
	if userID == 0 {
		response.ErrorWithWError(werror.New(401, "用户未登录"), ctx)
		return
	}

	var req dto.BeautyTechnicianProfileRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithWError(werror.New(400, "参数错误: "+err.Error()), ctx)
		return
	}

	err := c.technicianService.UpdateTechnicianProfile(int64(userID), &req)
	if err != nil {
		response.ErrorWithWError(werror.New(500, "更新技师信息失败: "+err.Error()), ctx)
		return
	}

	response.SuccessWithMessage("更新成功", ctx)
}

// GetTechnicianWorkSettings 获取技师工作设置
func (c *TechnicianController) GetTechnicianWorkSettings(ctx *gin.Context) {
	userID := request.GetUserID(ctx)
	if userID == 0 {
		response.ErrorWithWError(werror.New(401, "用户未登录"), ctx)
		return
	}

	result, err := c.technicianService.GetTechnicianWorkSettings(int64(userID))
	if err != nil {
		response.ErrorWithWError(werror.New(500, "获取工作设置失败: "+err.Error()), ctx)
		return
	}

	response.SuccessWithData(result, ctx)
}

// UpdateTechnicianWorkSettings 更新技师工作设置
func (c *TechnicianController) UpdateTechnicianWorkSettings(ctx *gin.Context) {
	userID := request.GetUserID(ctx)
	if userID == 0 {
		response.ErrorWithWError(werror.New(401, "用户未登录"), ctx)
		return
	}

	var req dto.BeautyTechnicianWorkSettingsRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithWError(werror.New(400, "参数错误: "+err.Error()), ctx)
		return
	}

	err := c.technicianService.UpdateTechnicianWorkSettings(int64(userID), &req)
	if err != nil {
		response.ErrorWithWError(werror.New(500, "更新工作设置失败: "+err.Error()), ctx)
		return
	}

	response.SuccessWithMessage("更新成功", ctx)
}

// GetTechnicianList 获取技师列表
func (c *TechnicianController) GetTechnicianList(ctx *gin.Context) {
	var req dto.BeautyTechnicianListRequest

	// 手动处理参数，避免 undefined 字符串解析错误
	pageStr := ctx.Query("page")
	pageSizeStr := ctx.Query("page_size")
	serviceIDStr := ctx.Query("service_id")
	level := ctx.Query("level")
	sort := ctx.Query("sort")
	keyword := ctx.Query("keyword")

	// 解析页码
	page := 1
	if pageStr != "" && pageStr != "undefined" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	// 解析每页数量
	pageSize := 10
	if pageSizeStr != "" && pageSizeStr != "undefined" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 100 {
			pageSize = ps
		}
	}

	// 解析服务ID
	var serviceID uint
	if serviceIDStr != "" && serviceIDStr != "undefined" && serviceIDStr != "null" {
		if sid, err := strconv.ParseUint(serviceIDStr, 10, 32); err == nil {
			serviceID = uint(sid)
		}
	}

	req = dto.BeautyTechnicianListRequest{
		Page:      page,
		PageSize:  pageSize,
		ServiceID: serviceID,
		Level:     level,
		Sort:      sort,
		Keyword:   keyword,
	}

	result, err := c.technicianService.GetTechnicianList(&req)
	if err != nil {
		response.ErrorWithWError(werror.New(500, "获取技师列表失败: "+err.Error()), ctx)
		return
	}

	response.SuccessWithData(result, ctx)
}

// GetTechnicianDetail 获取技师详情
func (c *TechnicianController) GetTechnicianDetail(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.ErrorWithWError(werror.New(400, "技师ID不能为空"), ctx)
		return
	}

	technicianID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		response.ErrorWithWError(werror.New(400, "技师ID格式错误"), ctx)
		return
	}

	// 添加调试信息
	fmt.Printf("请求技师详情，ID: %d\n", technicianID)

	result, err := c.technicianService.GetTechnicianDetail(uint(technicianID))
	if err != nil {
		fmt.Printf("获取技师详情失败: %v\n", err)
		response.ErrorWithWError(werror.New(500, "获取技师详情失败: "+err.Error()), ctx)
		return
	}

	fmt.Printf("获取技师详情成功: %+v\n", result)
	response.SuccessWithData(result, ctx)
}

// GetTechnicianAvailableTime 获取技师可用时间
func (c *TechnicianController) GetTechnicianAvailableTime(ctx *gin.Context) {
	id := ctx.Param("id")
	if id == "" {
		response.ErrorWithWError(werror.New(400, "技师ID不能为空"), ctx)
		return
	}

	technicianID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		response.ErrorWithWError(werror.New(400, "技师ID格式错误"), ctx)
		return
	}

	date := ctx.Query("date")
	if date == "" {
		response.ErrorWithWError(werror.New(400, "日期不能为空"), ctx)
		return
	}

	serviceIDStr := ctx.Query("service_id")
	var serviceID uint
	if serviceIDStr != "" && serviceIDStr != "undefined" && serviceIDStr != "null" {
		serviceIDUint, err := strconv.ParseUint(serviceIDStr, 10, 32)
		if err != nil {
			response.ErrorWithWError(werror.New(400, "服务ID格式错误"), ctx)
			return
		}
		serviceID = uint(serviceIDUint)
	}

	result, err := c.technicianService.GetTechnicianAvailableTime(uint(technicianID), date, serviceID)
	if err != nil {
		response.ErrorWithWError(werror.New(500, "获取技师可用时间失败: "+err.Error()), ctx)
		return
	}

	response.SuccessWithData(result, ctx)
}
