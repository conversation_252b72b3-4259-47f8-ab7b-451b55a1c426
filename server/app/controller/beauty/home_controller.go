package beauty

import (
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/beauty"

	"github.com/gin-gonic/gin"
)

// HomeController 美容首页控制器
type HomeController struct {
	serviceService    *beauty.ServiceService
	technicianService *beauty.TechnicianService
}

// NewHomeController 创建首页控制器实例
func NewHomeController() *HomeController {
	return &HomeController{
		serviceService:    beauty.NewServiceService(),
		technicianService: beauty.NewTechnicianService(),
	}
}

// GetHomeData 获取首页数据
func (c *HomeController) GetHomeData(ctx *gin.Context) {
	// 从上下文获取用户ID
	userID := int64(0)
	if userId, exists := ctx.Get("userId"); exists {
		if id, ok := userId.(int64); ok {
			userID = id
		}
	}

	// 获取推荐服务（热门服务）
	hotServicesReq := &dto.BeautyServiceListRequest{
		Page:     1,
		PageSize: 6,
		IsHot:    1,
		Sort:     "booking_desc",
	}

	hotServices, err := c.serviceService.GetServiceList(hotServicesReq, userID)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	// 获取新品服务
	newServicesReq := &dto.BeautyServiceListRequest{
		Page:     1,
		PageSize: 6,
		IsNew:    1,
		Sort:     "id desc",
	}

	newServices, err := c.serviceService.GetServiceList(newServicesReq, userID)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	// 获取明星技师 - 从数据库查询
	featuredTechniciansReq := &dto.BeautyTechnicianListRequest{
		Page:     1,
		PageSize: 4,
		Sort:     "rating_desc",
	}

	featuredTechniciansRes, err := c.technicianService.GetTechnicianList(featuredTechniciansReq)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	// 转换为前端需要的格式
	featuredTechnicians := make([]map[string]interface{}, 0, len(featuredTechniciansRes.List))
	for _, technician := range featuredTechniciansRes.List {
		featuredTechnicians = append(featuredTechnicians, map[string]interface{}{
			"id":           technician.ID,
			"user_id":      technician.UserID, // 添加user_id字段
			"name":         technician.Name,
			"avatar":       technician.Avatar,
			"level":        technician.Level,
			"experience":   technician.Experience,
			"specialties":  technician.Specialties,
			"introduction": technician.Introduction,
			"rating_avg":   technician.RatingAvg,
			"rating_count": technician.RatingCount,
			"extra_fee":    technician.ExtraFee,
			"is_featured":  technician.IsFeatured,
		})
	}

	// 构建首页数据
	homeData := map[string]interface{}{
		"banners": []map[string]interface{}{
			{
				"id":    1,
				"image": "/uploads/beauty/banner1.jpg",
				"title": "专业美容护理",
				"url":   "/beauty/service/list?category_id=1",
			},
			{
				"id":    2,
				"image": "/uploads/beauty/banner2.jpg",
				"title": "焕肤美白套餐",
				"url":   "/beauty/service/detail/1",
			},
		},
		"categories": []map[string]interface{}{
			{
				"id":   1,
				"name": "面部护理",
				"icon": "/uploads/beauty/category_face.png",
				"desc": "深层清洁，补水保湿",
			},
			{
				"id":   2,
				"name": "身体护理",
				"icon": "/uploads/beauty/category_body.png",
				"desc": "全身放松，舒缓疲劳",
			},
			{
				"id":   3,
				"name": "美甲美睫",
				"icon": "/uploads/beauty/category_nail.png",
				"desc": "精致美甲，魅力美睫",
			},
			{
				"id":   4,
				"name": "美发造型",
				"icon": "/uploads/beauty/category_hair.png",
				"desc": "时尚造型，个性设计",
			},
		},
		"hot_services":         hotServices.List,
		"new_services":         newServices.List,
		"featured_technicians": featuredTechnicians,
		"total_bookings":       1268,
		"total_customers":      856,
		"satisfaction":         98.5,
	}

	response.SuccessWithData(homeData, ctx)
}
