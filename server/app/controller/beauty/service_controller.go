package beauty

import (
	"strconv"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/beauty"

	"github.com/gin-gonic/gin"
)

// ServiceController 美容服务控制器
type ServiceController struct {
	serviceService *beauty.ServiceService
}

// NewServiceController 创建服务控制器实例
func NewServiceController() *ServiceController {
	return &ServiceController{
		serviceService: beauty.NewServiceService(),
	}
}

// GetServiceList 获取服务列表
func (c *ServiceController) GetServiceList(ctx *gin.Context) {
	var req dto.BeautyServiceListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.ErrorWithWError(werror.New(400, "参数错误: "+err.Error()), ctx)
		return
	}

	// 从上下文获取用户ID
	userID := int64(0)
	if userId, exists := ctx.Get("userId"); exists {
		if id, ok := userId.(int64); ok {
			userID = id
		}
	}

	result, err := c.serviceService.GetServiceList(&req, userID)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithData(result, ctx)
}

// GetServiceCategories 获取服务分类列表
func (c *ServiceController) GetServiceCategories(ctx *gin.Context) {
	result, err := c.serviceService.GetServiceCategories()
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithData(result, ctx)
}

// GetServiceDetail 获取服务详情
func (c *ServiceController) GetServiceDetail(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorWithWError(werror.New(400, "服务ID格式错误"), ctx)
		return
	}

	result, err := c.serviceService.GetServiceDetail(uint(id))
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithData(result, ctx)
}
