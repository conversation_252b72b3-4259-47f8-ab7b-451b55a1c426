package beauty

import (
	"fmt"
	"strconv"
	"time"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/model/do"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/provider/db"
	"wnsys/shop/app/service/payment"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type CardController struct{}

func NewCardController() *CardController {
	return &CardController{}
}

// List 获取套餐卡列表
func (c *CardController) List(ctx *gin.Context) {
	var cards []do.CardDO
	err := (&do.CardDO{}).Query().Where("status = ? AND is_delete = 0", 1).Order("sort DESC, id DESC").Find(&cards).Error
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}
	response.SuccessWithData(cards, ctx)
}

// Detail 获取套餐卡详情
func (c *CardController) Detail(ctx *gin.Context) {
	// 获取ID参数
	idStr := ctx.Query("id")
	if idStr == "" {
		response.Error(400, "缺少套餐卡ID", ctx)
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.Error(400, "套餐卡ID格式错误", ctx)
		return
	}

	// 查询套餐卡详情
	var card do.CardDO
	err = (&do.CardDO{}).Query().Where("id = ? AND is_delete = 0", id).First(&card).Error
	if err != nil {
		response.Error(404, "套餐卡不存在", ctx)
		return
	}

	response.SuccessWithData(card, ctx)
}

// Purchase 购买套餐卡
func (c *CardController) Purchase(ctx *gin.Context) {
	// 获取用户ID
	userID, exists := ctx.Get("userId")
	if !exists {
		userID, exists = ctx.Get("user_id")
	}
	if !exists {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 获取请求参数
	var req struct {
		CardID   string `json:"card_id" binding:"required"`
		Quantity int    `json:"quantity" binding:"required,min=1"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(400, "参数错误: "+err.Error(), ctx)
		return
	}

	// 解析套餐卡ID
	cardID, err := strconv.ParseInt(req.CardID, 10, 64)
	if err != nil {
		response.Error(400, "套餐卡ID格式错误", ctx)
		return
	}

	// 查询套餐卡信息
	var card do.CardDO
	err = (&do.CardDO{}).Query().Where("id = ? AND status = 1 AND is_delete = 0", cardID).First(&card).Error
	if err != nil {
		response.Error(404, "套餐卡不存在或已下架", ctx)
		return
	}

	// 开始数据库事务
	tx := db.DB.Shop.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 创建统一订单
	orderNo := fmt.Sprintf("CARD%d", time.Now().Unix())
	totalAmount := card.Price * float64(req.Quantity)

	order := ShopDB.OrderDO{
		OrderNo:        orderNo,
		BusinessType:   "card",
		UserID:         userID.(int64),
		TotalAmount:    totalAmount,
		PayAmount:      totalAmount,
		FreightAmount:  0,
		DiscountAmount: 0,
		Status:         "unpaid",
		IsDelete:       0,
		CreateTime:     time.Now(),
		UpdateTime:     time.Now(),
	}

	err = tx.Create(&order).Error
	if err != nil {
		tx.Rollback()
		response.Error(500, "创建订单失败: "+err.Error(), ctx)
		return
	}

	// 2. 创建用户套餐卡记录（作为订单详情）
	expire := time.Now().AddDate(0, 0, card.ValidDays)
	remainingTimes := card.TotalTimes * req.Quantity
	remainingAmount := card.TotalAmount * float64(req.Quantity)
	cardNo := fmt.Sprintf("UC%d", time.Now().UnixNano()) // 生成唯一的卡号

	userCard := do.UserCardDO{
		OrderID:         &order.ID, // 关联订单ID
		UserID:          userID.(int64),
		CardID:          cardID,
		CardName:        card.Name,
		CardType:        card.CardType,
		TotalTimes:      card.TotalTimes * req.Quantity,
		TotalAmount:     card.TotalAmount * float64(req.Quantity),
		UsedTimes:       0,
		UsedAmount:      0,
		RemainingTimes:  &remainingTimes,
		RemainingAmount: &remainingAmount,
		ValidDays:       card.ValidDays,
		ExpireTime:      &expire,
		EndTime:         &expire, // 设置结束时间
		Status:          1,       // 1: 有效
		CreateTime:      time.Now(),
		UpdateTime:      time.Now(),
		IsDelete:        0,
		PurchasePrice:   card.Price * float64(req.Quantity),
		PaymentAmount:   card.Price * float64(req.Quantity), // 设置支付金额
		PurchaseTime:    time.Now(),                         // 设置购买时间
		StartTime:       time.Now(),
		CardNo:          cardNo, // 设置卡号
	}

	err = tx.Create(&userCard).Error
	if err != nil {
		tx.Rollback()
		response.Error(500, "创建用户套餐卡失败: "+err.Error(), ctx)
		return
	}

	// 3. 更新订单的业务ID
	err = tx.Model(&order).Update("business_id", userCard.ID).Error
	if err != nil {
		tx.Rollback()
		response.Error(500, "更新订单业务ID失败: "+err.Error(), ctx)
		return
	}

	// 4. 更新套餐卡销量
	err = tx.Model(&do.CardDO{}).Where("id = ?", cardID).Update("sales_count", card.SalesCount+req.Quantity).Error
	if err != nil {
		tx.Rollback()
		response.Error(500, "更新销量失败: "+err.Error(), ctx)
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		response.Error(500, "提交事务失败: "+err.Error(), ctx)
		return
	}

	response.SuccessWithData(map[string]interface{}{
		"user_card_id": userCard.ID,
		"order_id":     order.ID,
		"order_no":     orderNo,
		"message":      "购买成功",
	}, ctx)
}

// Pay 套餐卡支付
func (c *CardController) Pay(ctx *gin.Context) {
	// 获取用户ID
	userID, exists := ctx.Get("userId")
	if !exists {
		userID, exists = ctx.Get("user_id")
	}
	if !exists {
		response.Error(401, "请先登录", ctx)
		return
	}

	var req dto.BeautyCardPayRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Error(400, "参数错误: "+err.Error(), ctx)
		return
	}

	// 查询用户套餐卡是否存在且属于该用户
	var userCard do.UserCardDO
	err := (&do.UserCardDO{}).Query().Where("id = ? AND user_id = ? AND is_delete = 0", req.UserCardID, userID).First(&userCard).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.Error(404, "用户套餐卡不存在", ctx)
			return
		}
		response.Error(500, "查询用户套餐卡失败", ctx)
		return
	}

	// 检查套餐卡状态
	if userCard.Status != 1 {
		response.Error(400, "套餐卡状态异常", ctx)
		return
	}

	// 检查是否已过期
	if userCard.ExpireTime != nil && time.Now().After(*userCard.ExpireTime) {
		response.Error(400, "套餐卡已过期", ctx)
		return
	}

	// 查询关联的统一订单
	var order ShopDB.OrderDO
	err = (&ShopDB.OrderDO{}).Query().Where("id = ? AND user_id = ? AND business_type = 'card'", userCard.OrderID, userID).First(&order).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.Error(404, "关联订单不存在", ctx)
			return
		}
		response.Error(500, "查询订单失败", ctx)
		return
	}

	// 检查订单状态
	if order.Status != "unpaid" {
		response.Error(400, "订单状态异常，无法支付", ctx)
		return
	}

	// 使用订单的支付金额
	payAmount := order.PayAmount

	// 调用支付服务
	paymentService := payment.NewPaymentFactory().GetPaymentService(req.PayType)
	if paymentService == nil {
		response.Error(400, "不支持的支付方式", ctx)
		return
	}

	// 调用支付服务，使用统一订单的order_id和order_no
	result, err := paymentService.Pay(order.ID, order.OrderNo, payAmount, "card")
	if err != nil {
		response.Error(500, "创建支付订单失败: "+err.Error(), ctx)
		return
	}

	// 如果是余额支付，直接处理支付成功
	if req.PayType == 3 {
		// 构造回调数据
		notifyData := map[string]string{
			"user_id":      fmt.Sprintf("%d", userCard.UserID),
			"user_card_id": fmt.Sprintf("%d", userCard.ID),
			"order_id":     fmt.Sprintf("%d", order.ID),
			"order_no":     order.OrderNo,
			"amount":       fmt.Sprintf("%.2f", payAmount),
		}

		// 处理支付成功
		err = payment.HandlePaymentSuccessWithSkipCheck(order.OrderNo, int8(req.PayType), "", notifyData, "card", nil)
		if err != nil {
			response.Error(500, "处理支付成功失败: "+err.Error(), ctx)
			return
		}
	}

	response.SuccessWithData(&dto.BeautyCardPayResponse{
		Success: true,
		Data:    result,
		Message: "支付成功",
	}, ctx)
}
