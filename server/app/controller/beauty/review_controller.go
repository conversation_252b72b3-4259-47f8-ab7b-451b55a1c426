package beauty

import (
	"strconv"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/beauty"

	"github.com/gin-gonic/gin"
)

// ReviewController 评价控制器
type ReviewController struct {
	reviewService *beauty.ReviewService
}

// NewReviewController 创建评价控制器实例
func NewReviewController() *ReviewController {
	return &ReviewController{
		reviewService: beauty.NewReviewService(),
	}
}

// GetTechnicianReviews 获取技师评价列表
func (c *ReviewController) GetTechnicianReviews(ctx *gin.Context) {
	var req dto.BeautyTechnicianReviewListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.ErrorWithWError(werror.New(400, "参数错误: "+err.Error()), ctx)
		return
	}

	result, err := c.reviewService.GetTechnicianReviews(&req)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithData(result, ctx)
}

// GetServiceReviews 获取服务评价列表
func (c *ReviewController) GetServiceReviews(ctx *gin.Context) {
	var req dto.BeautyServiceReviewListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.ErrorWithWError(werror.New(400, "参数错误: "+err.Error()), ctx)
		return
	}

	result, err := c.reviewService.GetServiceReviews(&req)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithData(result, ctx)
}

// GetTechnicianReviewStats 获取技师评价统计
func (c *ReviewController) GetTechnicianReviewStats(ctx *gin.Context) {
	TechnicianUserIDStr := ctx.Param("id")
	if TechnicianUserIDStr == "" {
		response.ErrorWithWError(werror.New(400, "技师ID不能为空"), ctx)
		return
	}

	TechnicianUserID, err := strconv.ParseUint(TechnicianUserIDStr, 10, 32)
	if err != nil {
		response.ErrorWithWError(werror.New(400, "技师ID格式错误"), ctx)
		return
	}

	result, err := c.reviewService.GetTechnicianReviewStats(uint(TechnicianUserID))
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithData(result, ctx)
}

// GetServiceReviewStats 获取服务评价统计
func (c *ReviewController) GetServiceReviewStats(ctx *gin.Context) {
	serviceIDStr := ctx.Param("id")
	if serviceIDStr == "" {
		response.ErrorWithWError(werror.New(400, "服务ID不能为空"), ctx)
		return
	}

	serviceID, err := strconv.ParseUint(serviceIDStr, 10, 32)
	if err != nil {
		response.ErrorWithWError(werror.New(400, "服务ID格式错误"), ctx)
		return
	}

	result, err := c.reviewService.GetServiceReviewStats(uint(serviceID))
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithData(result, ctx)
}
