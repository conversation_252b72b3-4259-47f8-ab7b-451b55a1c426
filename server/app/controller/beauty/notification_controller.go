package beauty

import (
	"wnsys/shop/app/common/request"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/beauty"

	"github.com/gin-gonic/gin"
)

// NotificationController 通知控制器
type NotificationController struct {
	notificationService *beauty.NotificationService
}

// NewNotificationController 创建通知控制器实例
func NewNotificationController() *NotificationController {
	return &NotificationController{
		notificationService: beauty.NewNotificationService(),
	}
}

// GetNotificationList 获取通知列表
func (c *NotificationController) GetNotificationList(ctx *gin.Context) {
	// 获取用户ID，暂时使用固定用户ID用于测试
	userID := request.GetUserID(ctx)
	if userID == 0 {
		userID = 1 // 暂时使用用户ID 1进行测试
	}

	var req dto.BeautyNotificationListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.ErrorWithWError(werror.New(400, "参数错误: "+err.Error()), ctx)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	result, err := c.notificationService.GetNotificationList(int64(userID), &req)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithData(result, ctx)
}

// GetUnreadNotificationCount 获取未读通知数量
func (c *NotificationController) GetUnreadNotificationCount(ctx *gin.Context) {
	// 获取用户ID，暂时使用固定用户ID用于测试
	userID := request.GetUserID(ctx)
	if userID == 0 {
		userID = 1 // 暂时使用用户ID 1进行测试
	}

	result, err := c.notificationService.GetUnreadNotificationCount(int64(userID))
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithData(result, ctx)
}

// MarkNotificationRead 标记通知为已读
func (c *NotificationController) MarkNotificationRead(ctx *gin.Context) {
	// 获取用户ID，暂时使用固定用户ID用于测试
	userID := request.GetUserID(ctx)
	if userID == 0 {
		userID = 1 // 暂时使用用户ID 1进行测试
	}

	var req dto.BeautyNotificationReadRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithWError(werror.New(400, "参数错误: "+err.Error()), ctx)
		return
	}

	err := c.notificationService.MarkNotificationRead(int64(userID), &req)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithMessage("标记成功", ctx)
}
