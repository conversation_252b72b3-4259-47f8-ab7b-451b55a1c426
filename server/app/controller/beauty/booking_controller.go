package beauty

import (
	"fmt"
	"strconv"
	"wnsys/shop/app/common/request"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/beauty"

	"github.com/gin-gonic/gin"
)

// BookingController 预约控制器
type BookingController struct {
	bookingService *beauty.BookingService
}

// NewBookingController 创建预约控制器实例
func NewBookingController() *BookingController {
	return &BookingController{
		bookingService: beauty.NewBookingService(),
	}
}

// CreateBooking 创建预约
func (c *BookingController) CreateBooking(ctx *gin.Context) {
	fmt.Printf("收到预约创建请求\n")

	// 获取用户ID，暂时使用固定用户ID用于测试
	userID := request.GetUserID(ctx)
	if userID == 0 {
		userID = 1 // 暂时使用用户ID 1进行测试
	}
	fmt.Printf("用户ID: %d\n", userID)

	var req dto.BeautyBookingCreateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		fmt.Printf("参数绑定失败: %v\n", err)
		response.ErrorWithWError(werror.New(400, "参数错误: "+err.Error()), ctx)
		return
	}
	fmt.Printf("请求参数绑定成功: %+v\n", req)

	result, err := c.bookingService.CreateBooking(int64(userID), &req)
	if err != nil {
		fmt.Printf("服务层返回错误: %v\n", err)
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	fmt.Printf("预约创建成功: %+v\n", result)
	response.SuccessWithData(result, ctx)
}

// GetBookingList 获取预约列表
func (c *BookingController) GetBookingList(ctx *gin.Context) {
	// 获取用户ID，暂时使用固定用户ID用于测试
	userID := request.GetUserID(ctx)
	if userID == 0 {
		userID = 1 // 暂时使用用户ID 1进行测试
	}

	// 打印接收到的查询参数
	fmt.Printf("接收到的查询参数:\n")
	fmt.Printf("page: %s\n", ctx.Query("page"))
	fmt.Printf("page_size: %s\n", ctx.Query("page_size"))
	fmt.Printf("status: %s\n", ctx.Query("status"))
	fmt.Printf("date_from: %s\n", ctx.Query("date_from"))
	fmt.Printf("date_to: %s\n", ctx.Query("date_to"))

	var req dto.BeautyBookingListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		fmt.Printf("参数绑定失败: %v\n", err)
		response.ErrorWithWError(werror.New(400, "参数错误: "+err.Error()), ctx)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	fmt.Printf("参数绑定成功: %+v\n", req)

	result, err := c.bookingService.GetBookingList(int64(userID), &req)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithData(result, ctx)
}

// GetBookingDetail 获取预约详情
func (c *BookingController) GetBookingDetail(ctx *gin.Context) {
	// 获取用户ID，暂时使用固定用户ID用于测试
	userID := request.GetUserID(ctx)
	if userID == 0 {
		userID = 1 // 暂时使用用户ID 1进行测试
	}

	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.ErrorWithWError(werror.New(400, "预约ID格式错误"), ctx)
		return
	}

	result, err := c.bookingService.GetBookingDetail(int64(userID), int64(id))
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithData(result, ctx)
}

// CancelBooking 取消预约
func (c *BookingController) CancelBooking(ctx *gin.Context) {
	// 获取用户ID
	userID := request.GetUserID(ctx)
	if userID == 0 {
		response.ErrorWithWError(werror.New(401, "请先登录"), ctx)
		return
	}

	var req dto.BeautyBookingCancelRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithWError(werror.New(400, "参数错误: "+err.Error()), ctx)
		return
	}

	err := c.bookingService.CancelBooking(int64(userID), &req)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithMessage("取消预约成功", ctx)
}

// GetAvailableTime 获取可用时间
func (c *BookingController) GetAvailableTime(ctx *gin.Context) {
	var req dto.BeautyAvailableTimeRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.ErrorWithWError(werror.New(400, "参数错误: "+err.Error()), ctx)
		return
	}

	// TODO: 实现获取可用时间逻辑
	// 这里先返回模拟数据
	result := &dto.BeautyAvailableTimeResponse{
		Date: req.Date,
		AvailableSlots: []dto.TimeSlot{
			{StartTime: "09:00", EndTime: "10:30", Available: true},
			{StartTime: "10:30", EndTime: "12:00", Available: true},
			{StartTime: "14:00", EndTime: "15:30", Available: false, Reason: "已被预约"},
			{StartTime: "15:30", EndTime: "17:00", Available: true},
		},
	}

	response.SuccessWithData(result, ctx)
}

// GetBookingStats 获取预约统计
func (c *BookingController) GetBookingStats(ctx *gin.Context) {
	// 获取用户ID，暂时使用固定用户ID用于测试
	userID := request.GetUserID(ctx)
	if userID == 0 {
		userID = 1 // 暂时使用用户ID 1进行测试
	}

	result, err := c.bookingService.GetBookingStats(int64(userID))
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithData(result, ctx)
}

// PayBeautyBooking 美容预约支付
func (c *BookingController) PayBeautyBooking(ctx *gin.Context) {
	// 获取用户ID
	userID := request.GetUserID(ctx)
	if userID == 0 {
		response.ErrorWithWError(werror.New(401, "请先登录"), ctx)
		return
	}

	var req dto.BeautyBookingPayRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithWError(werror.New(400, "参数错误: "+err.Error()), ctx)
		return
	}

	result, err := c.bookingService.PayBeautyBooking(int64(userID), &req)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithData(result, ctx)
}

// GetTechnicianTodayBookings 获取技师今日预约列表
func (c *BookingController) GetTechnicianTodayBookings(ctx *gin.Context) {
	// 获取技师user_id
	userID := request.GetUserID(ctx)
	if userID == 0 {
		response.ErrorWithWError(werror.New(401, "请先登录"), ctx)
		return
	}
	result, err := c.bookingService.GetTechnicianTodayBookings(userID)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}
	if result == nil {
		result = []dto.BeautyBookingItem{} // 保证返回空数组而不是null
	}
	response.SuccessWithData(result, ctx)
}

// GetTechnicianTodayStats 获取技师今日统计
func (c *BookingController) GetTechnicianTodayStats(ctx *gin.Context) {
	userID := request.GetUserID(ctx)
	if userID == 0 {
		response.ErrorWithWError(werror.New(401, "请先登录"), ctx)
		return
	}
	result, err := c.bookingService.GetTechnicianTodayStats(userID)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}
	response.SuccessWithData(result, ctx)
}

// GetTechnicianDateBookings 获取技师指定日期预约列表
func (c *BookingController) GetTechnicianDateBookings(ctx *gin.Context) {
	userID := request.GetUserID(ctx)
	if userID == 0 {
		response.ErrorWithWError(werror.New(401, "请先登录"), ctx)
		return
	}

	date := ctx.Query("date")
	if date == "" {
		response.ErrorWithWError(werror.New(400, "日期参数不能为空"), ctx)
		return
	}

	result, err := c.bookingService.GetTechnicianDateBookings(userID, date)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}
	if result == nil {
		result = []dto.BeautyBookingItem{} // 保证返回空数组而不是null
	}
	response.SuccessWithData(result, ctx)
}

// GetTechnicianDateStats 获取技师指定日期统计
func (c *BookingController) GetTechnicianDateStats(ctx *gin.Context) {
	userID := request.GetUserID(ctx)
	if userID == 0 {
		response.ErrorWithWError(werror.New(401, "请先登录"), ctx)
		return
	}

	date := ctx.Query("date")
	if date == "" {
		response.ErrorWithWError(werror.New(400, "日期参数不能为空"), ctx)
		return
	}

	result, err := c.bookingService.GetTechnicianDateStats(userID, date)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}
	response.SuccessWithData(result, ctx)
}

// GetTechnicianBookingList 获取技师全部预约列表
func (c *BookingController) GetTechnicianBookingList(ctx *gin.Context) {
	userID := request.GetUserID(ctx)
	if userID == 0 {
		response.ErrorWithWError(werror.New(401, "请先登录"), ctx)
		return
	}
	var req dto.TechnicianBookingListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.ErrorWithWError(werror.New(400, "参数错误: "+err.Error()), ctx)
		return
	}
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	result, err := c.bookingService.GetTechnicianBookingList(int64(userID), &req)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}
	response.SuccessWithData(result, ctx)
}

// UpdateBookingStatus 更新预约状态
func (c *BookingController) UpdateBookingStatus(ctx *gin.Context) {
	userID := request.GetUserID(ctx)
	if userID == 0 {
		response.ErrorWithWError(werror.New(401, "请先登录"), ctx)
		return
	}

	var req dto.BeautyBookingUpdateStatusRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithWError(werror.New(400, "参数错误: "+err.Error()), ctx)
		return
	}

	err := c.bookingService.UpdateBookingStatus(int64(userID), &req)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithMessage("状态更新成功", ctx)
}
