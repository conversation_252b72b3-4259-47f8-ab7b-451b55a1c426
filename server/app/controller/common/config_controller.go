package common

import (
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/service/common"

	"github.com/gin-gonic/gin"
)

type ConfigController struct {
	configService *common.ConfigService
}

// NewConfigController 创建配置控制器
func NewConfigController() *ConfigController {
	return &ConfigController{
		configService: common.NewConfigService(),
	}
}

// GetConfigs 获取配置信息
func (c *ConfigController) GetConfigs(ctx *gin.Context) {
	var req common.ConfigRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	result, err := c.configService.GetConfigs(&req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(result, ctx)
}

// GetConfig 获取单个配置
func (c *ConfigController) GetConfig(ctx *gin.Context) {
	moduleKey := ctx.Query("moduleKey")
	configKey := ctx.Query("configKey")

	if moduleKey == "" || configKey == "" {
		response.Error(400, "moduleKey和configKey不能为空", ctx)
		return
	}

	value, err := c.configService.GetConfig(moduleKey, configKey)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(gin.H{
		"moduleKey":   moduleKey,
		"configKey":   configKey,
		"configValue": value,
	}, ctx)
}

// GetShopConfigs 获取商城配置
func (c *ConfigController) GetShopConfigs(ctx *gin.Context) {
	// 从查询参数获取配置键列表
	keys := ctx.QueryArray("keys")

	result, err := c.configService.GetShopConfigs(keys...)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(result, ctx)
}

// ClearCache 清除配置缓存
func (c *ConfigController) ClearCache(ctx *gin.Context) {
	moduleKey := ctx.Query("moduleKey")

	if moduleKey != "" {
		c.configService.ClearModuleCache(moduleKey)
		response.SuccessWithMessage("模块缓存清除成功", ctx)
	} else {
		c.configService.ClearCache()
		response.SuccessWithMessage("缓存清除成功", ctx)
	}
}
