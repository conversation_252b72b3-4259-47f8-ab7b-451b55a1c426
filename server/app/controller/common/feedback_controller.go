package common

import (
	"strconv"
	"wnsys/shop/app/common/request"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/common"

	"github.com/gin-gonic/gin"
)

type FeedbackController struct {
	feedbackService *common.FeedbackService
}

// NewFeedbackController 创建反馈控制器
func NewFeedbackController() *FeedbackController {
	return &FeedbackController{
		feedbackService: common.NewFeedbackService(),
	}
}

// SubmitFeedback 提交反馈
func (c *FeedbackController) SubmitFeedback(ctx *gin.Context) {
	var req dto.SubmitFeedbackRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取用户ID
	userID := request.GetUserID(ctx)
	if userID == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	if err := c.feedbackService.SubmitFeedback(userID, &req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("反馈提交成功，我们会尽快处理", ctx)
}

// GetUserFeedbacks 获取用户反馈列表
func (c *FeedbackController) GetUserFeedbacks(ctx *gin.Context) {
	var req dto.FeedbackListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取用户ID
	userID := request.GetUserID(ctx)
	if userID == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	feedbacks, total, err := c.feedbackService.GetUserFeedbacks(userID, &req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(gin.H{
		"list":      feedbacks,
		"total":     total,
		"page":      req.Page,
		"page_size": req.PageSize,
	}, ctx)
}

// GetFeedbackDetail 获取反馈详情
func (c *FeedbackController) GetFeedbackDetail(ctx *gin.Context) {
	feedbackIDStr := ctx.Param("id")
	feedbackID, err := strconv.ParseInt(feedbackIDStr, 10, 64)
	if err != nil {
		response.Error(400, "反馈ID格式错误", ctx)
		return
	}

	// 获取用户ID
	userID := request.GetUserID(ctx)
	if userID == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	feedback, err := c.feedbackService.GetFeedbackByID(userID, feedbackID)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(feedback, ctx)
}
