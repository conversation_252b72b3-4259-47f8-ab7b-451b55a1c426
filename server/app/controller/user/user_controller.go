package user

import (
	"wnsys/shop/app/common/request"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/common/upload"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/user"

	"github.com/gin-gonic/gin"
)

type Controller struct {
	service *user.Service
}

func NewController() *Controller {
	return &Controller{
		service: user.NewService(),
	}
}

// GetInfo 获取用户信息
func (c *Controller) GetInfo(ctx *gin.Context) {
	// 调用服务获取用户信息
	info, err := c.service.GetUserInfo(ctx.GetInt64("userId"))
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(info, ctx)
}

// GetCollectList 获取收藏列表
func (c *Controller) GetCollectList(ctx *gin.Context) {
	// 绑定参数
	var req dto.CollectListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取收藏列表
	data, err := c.service.GetCollectList(ctx.GetInt64("userId"), req.Page, req.Size)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(data, ctx)
}

// DeleteCollect 删除收藏
func (c *Controller) DeleteCollect(ctx *gin.Context) {
	// 绑定参数
	var req dto.DeleteCollectReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 删除收藏
	err := c.service.DeleteCollect(ctx.GetInt64("userId"), req.ID)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("删除成功", ctx)
}

// GetFootprintList 获取足迹列表
func (c *Controller) GetFootprintList(ctx *gin.Context) {
	// 绑定参数
	var req dto.FootprintListReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取足迹列表
	data, err := c.service.GetFootprintList(ctx.GetInt64("userId"), req.Page, req.Size)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(data, ctx)
}

// DeleteFootprint 删除足迹
func (c *Controller) DeleteFootprint(ctx *gin.Context) {
	// 绑定参数
	var req dto.DeleteFootprintReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 删除足迹
	err := c.service.DeleteFootprint(ctx.GetInt64("userId"), req.ID)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("删除成功", ctx)
}

// ClearFootprint 清空足迹
func (c *Controller) ClearFootprint(ctx *gin.Context) {
	// 清空足迹
	err := c.service.ClearFootprint(ctx.GetInt64("userId"))
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("清空成功", ctx)
}

// GetStats 获取用户数据统计
func (c *Controller) GetStats(ctx *gin.Context) {
	// 调用service获取统计数据
	data, err := c.service.GetStats(ctx.GetInt64("userId"))
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}
	response.SuccessWithData(data, ctx)
}

// GetOrderStats 获取订单统计
func (c *Controller) GetOrderStats(ctx *gin.Context) {
	// 调用service获取订单统计
	data, err := c.service.GetOrderStats(ctx.GetInt64("userId"))
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}
	response.SuccessWithData(data, ctx)
}

// GetUserInfo 获取用户信息
func (c *Controller) GetUserInfo(ctx *gin.Context) {
	// 从上下文中获取用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	// 获取用户信息
	info, err := c.service.GetUserInfo(userId)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(info, ctx)
}

// UpdateUserInfo 更新用户信息
func (c *Controller) UpdateUserInfo(ctx *gin.Context) {
	// 从上下文中获取用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	// 绑定请求参数
	var userInfo ShopDB.UserDO
	if err := ctx.ShouldBindJSON(&userInfo); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 更新用户信息
	err := c.service.UpdateUserInfo(userId, &userInfo)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("更新成功", ctx)
}

// UploadAvatar 上传头像
func (c *Controller) UploadAvatar(ctx *gin.Context) {
	// 从上下文中获取用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	// 获取上传的文件
	file, err := ctx.FormFile("file")
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 上传文件到OSS
	filePath, err := upload.UploadImageToOSS(file)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 更新用户头像
	err = c.service.UpdateAvatar(userId, filePath)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(gin.H{
		"avatar": filePath,
	}, ctx)
}

// ChangePassword 修改密码
func (c *Controller) ChangePassword(ctx *gin.Context) {
	// 从上下文中获取用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	// 绑定请求参数
	var req dto.ChangePasswordReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 修改密码
	err := c.service.ChangePassword(userId, req.OldPassword, req.NewPassword)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("密码修改成功", ctx)
}

// ResetPassword 重置密码（不需要验证当前密码）
func (c *Controller) ResetPassword(ctx *gin.Context) {
	// 从上下文中获取用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	// 绑定请求参数
	var req dto.ResetPasswordReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 重置密码
	err := c.service.ResetPassword(userId, req.NewPassword)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("密码重置成功", ctx)
}

// ApplyDistributor 申请成为分销员
func (c *Controller) ApplyDistributor(ctx *gin.Context) {
	// 从上下文中获取用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	// 申请成为分销员
	err := c.service.ApplyDistributor(userId)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("申请成功", ctx)
}

// GetInviteRelations 获取邀请关系列表
func (c *Controller) GetInviteRelations(ctx *gin.Context) {
	// 从上下文中获取用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	// 获取邀请关系列表
	relations, err := c.service.GetInviteRelations(userId)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(relations, ctx)
}

// GetInviteStats 获取邀请统计
func (c *Controller) GetInviteStats(ctx *gin.Context) {
	// 从上下文中获取用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	// 获取邀请统计
	stats, err := c.service.GetInviteStats(userId)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(stats, ctx)
}
