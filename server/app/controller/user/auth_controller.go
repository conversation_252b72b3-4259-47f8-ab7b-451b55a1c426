package user

import (
	"strings"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/user"

	"github.com/gin-gonic/gin"
)

type AuthController struct {
	service *user.Service
}

func NewAuthController() *AuthController {
	return &AuthController{
		service: user.NewService(),
	}
}

// Login 用户登录
func (c *AuthController) Login(ctx *gin.Context) {
	var req dto.LoginReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 调用登录服务
	resp, err := c.service.Login(&req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(resp, ctx)
}

// Logout 用户退出登录
func (c *AuthController) Logout(ctx *gin.Context) {
	// 获取token并去掉Bearer前缀
	token := strings.TrimPrefix(ctx.GetHeader("Authorization"), "Bearer ")

	// 调用service层处理退出登录
	err := c.service.Logout(token)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("退出成功", ctx)
}

// WechatPhoneLogin 微信手机号快捷登录
func (c *AuthController) WechatPhoneLogin(ctx *gin.Context) {
	var req dto.WechatPhoneLoginReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 调用服务进行登录
	resp, err := c.service.WechatPhoneLogin(&req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(resp, ctx)
}

// Register 用户注册
func (c *AuthController) Register(ctx *gin.Context) {
	var req dto.RegisterReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 调用注册服务
	resp, err := c.service.Register(&req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(resp, ctx)
}

// SendSms 发送短信验证码
func (c *AuthController) SendSms(ctx *gin.Context) {
	var req dto.SendSmsReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 调用发送短信服务
	err := c.service.SendSms(&req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("验证码发送成功", ctx)
}

// VerifySms 验证短信验证码
func (c *AuthController) VerifySms(ctx *gin.Context) {
	var req dto.VerifySmsReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 调用验证短信服务
	err := c.service.VerifySms(&req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("验证码验证成功", ctx)
}
