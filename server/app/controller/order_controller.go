package controller

import (
	"strconv"
	"wnsys/shop/app/common/request"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/order"

	"github.com/gin-gonic/gin"
)

// OrderController 统一订单控制器
type OrderController struct {
	orderService *order.OrderService
}

// NewOrderController 创建订单控制器实例
func NewOrderController() *OrderController {
	return &OrderController{
		orderService: order.NewOrderService(),
	}
}

// GetOrderList 获取订单列表
func (c *OrderController) GetOrderList(ctx *gin.Context) {
	// 获取用户ID
	userID := request.GetUserID(ctx)
	if userID == 0 {
		response.ErrorWithWError(werror.New(401, "请先登录"), ctx)
		return
	}

	var req dto.OrderListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.ErrorWithWError(werror.New(400, "参数错误: "+err.Error()), ctx)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	result, err := c.orderService.GetOrderList(int64(userID), &req)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithData(result, ctx)
}

// GetOrderDetail 获取订单详情
func (c *OrderController) GetOrderDetail(ctx *gin.Context) {
	// 获取用户ID
	userID := request.GetUserID(ctx)
	if userID == 0 {
		response.ErrorWithWError(werror.New(401, "请先登录"), ctx)
		return
	}

	// 获取订单ID
	orderIDStr := ctx.Param("id")
	orderID, err := strconv.ParseInt(orderIDStr, 10, 64)
	if err != nil {
		response.ErrorWithWError(werror.New(400, "订单ID格式错误"), ctx)
		return
	}

	result, err := c.orderService.GetOrderDetail(int64(userID), orderID)
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithData(result, ctx)
}

// GetOrderStats 获取订单统计
func (c *OrderController) GetOrderStats(ctx *gin.Context) {
	// 获取用户ID
	userID := request.GetUserID(ctx)
	if userID == 0 {
		response.ErrorWithWError(werror.New(401, "请先登录"), ctx)
		return
	}

	result, err := c.orderService.GetOrderStats(int64(userID))
	if err != nil {
		if werr, ok := werror.AsWError(err); ok {
			response.ErrorWithWError(werr, ctx)
		} else {
			response.ErrorWithWError(werror.New(500, err.Error()), ctx)
		}
		return
	}

	response.SuccessWithData(result, ctx)
}
