package relation

import (
	"strconv"
	"wnsys/shop/app/common/request"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/service/relation"

	"github.com/gin-gonic/gin"
)

type RelationController struct {
	relationService *relation.RelationService
}

func NewRelationController() *RelationController {
	return &RelationController{
		relationService: &relation.RelationService{},
	}
}

// CreateRelationReq 创建关系请求
type CreateRelationReq struct {
	RelatedUserId int64                  `json:"related_user_id" binding:"required"` // 关联用户ID
	RelationType  string                 `json:"relation_type" binding:"required"`   // 关系类型
	Direction     string                 `json:"direction" binding:"required"`       // 关系方向
	Source        string                 `json:"source"`                             // 关系来源
	Metadata      map[string]interface{} `json:"metadata"`                           // 扩展数据
}

// CreateRelation 创建用户关系
func (c *RelationController) CreateRelation(ctx *gin.Context) {
	var req CreateRelationReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取当前用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	// 不能和自己建立关系
	if userId == req.RelatedUserId {
		response.Error(400, "不能和自己建立关系", ctx)
		return
	}

	err := c.relationService.CreateRelation(userId, req.RelatedUserId, req.RelationType, req.Direction, req.Source, req.Metadata)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("创建关系成功", ctx)
}

// GetRelations 获取用户关系列表
func (c *RelationController) GetRelations(ctx *gin.Context) {
	// 获取当前用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	relationType := ctx.Query("relation_type")

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	relations, total, err := c.relationService.GetRelations(userId, relationType, page, pageSize)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	result := map[string]interface{}{
		"list":      relations,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	}

	response.SuccessWithData(result, ctx)
}

// GetInviteTree 获取邀请树
func (c *RelationController) GetInviteTree(ctx *gin.Context) {
	// 获取当前用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	maxLevel, _ := strconv.Atoi(ctx.DefaultQuery("max_level", "3"))

	if maxLevel < 1 || maxLevel > 10 {
		maxLevel = 3
	}

	tree, err := c.relationService.GetInviteTree(userId, maxLevel)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(tree, ctx)
}

// GetTeamCount 获取团队人数
func (c *RelationController) GetTeamCount(ctx *gin.Context) {
	// 获取当前用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	relationType := ctx.DefaultQuery("relation_type", ShopDB.RelationTypeInvite)

	count, err := c.relationService.GetTeamCount(userId, relationType)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	result := map[string]interface{}{
		"count": count,
	}

	response.SuccessWithData(result, ctx)
}

// CheckRelation 检查关系
func (c *RelationController) CheckRelation(ctx *gin.Context) {
	// 获取当前用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	relatedUserIdStr := ctx.Query("related_user_id")
	relationType := ctx.Query("relation_type")

	if relatedUserIdStr == "" || relationType == "" {
		response.Error(400, "参数不能为空", ctx)
		return
	}

	relatedUserId, err := strconv.ParseInt(relatedUserIdStr, 10, 64)
	if err != nil {
		response.Error(400, "用户ID格式错误", ctx)
		return
	}

	relationExists, err := c.relationService.CheckRelation(userId, relatedUserId, relationType)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	result := map[string]interface{}{
		"exists": relationExists,
	}

	response.SuccessWithData(result, ctx)
}

// DeleteRelation 删除关系
func (c *RelationController) DeleteRelation(ctx *gin.Context) {
	// 获取当前用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	relatedUserIdStr := ctx.Param("related_user_id")
	relationType := ctx.Query("relation_type")

	if relatedUserIdStr == "" || relationType == "" {
		response.Error(400, "参数不能为空", ctx)
		return
	}

	relatedUserId, err := strconv.ParseInt(relatedUserIdStr, 10, 64)
	if err != nil {
		response.Error(400, "用户ID格式错误", ctx)
		return
	}

	err = c.relationService.DeleteRelation(userId, relatedUserId, relationType)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("删除关系成功", ctx)
}

// GetAncestors 获取上级链
func (c *RelationController) GetAncestors(ctx *gin.Context) {
	// 获取当前用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	relationType := ctx.DefaultQuery("relation_type", ShopDB.RelationTypeInvite)

	ancestors, err := c.relationService.GetAncestors(userId, relationType)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	result := map[string]interface{}{
		"ancestors": ancestors,
	}

	response.SuccessWithData(result, ctx)
}

// AddFriend 添加好友
func (c *RelationController) AddFriend(ctx *gin.Context) {
	var req CreateRelationReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取当前用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	// 不能和自己建立关系
	if userId == req.RelatedUserId {
		response.Error(400, "不能添加自己为好友", ctx)
		return
	}

	// 固定为好友关系
	err := c.relationService.CreateRelation(userId, req.RelatedUserId, ShopDB.RelationTypeFriend, ShopDB.DirectionTwoWay, req.Source, req.Metadata)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("添加好友成功", ctx)
}

// FollowUser 关注用户
func (c *RelationController) FollowUser(ctx *gin.Context) {
	var req CreateRelationReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取当前用户ID
	userId := request.GetUserID(ctx)
	if userId == 0 {
		response.Error(401, "用户未登录", ctx)
		return
	}

	// 不能关注自己
	if userId == req.RelatedUserId {
		response.Error(400, "不能关注自己", ctx)
		return
	}

	// 固定为关注关系
	err := c.relationService.CreateRelation(userId, req.RelatedUserId, ShopDB.RelationTypeFollow, ShopDB.DirectionOneWay, req.Source, req.Metadata)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("关注成功", ctx)
}
