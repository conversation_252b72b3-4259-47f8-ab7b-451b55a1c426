package city

import (
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/service/city"

	"github.com/gin-gonic/gin"
)

type Controller struct {
	cityService *city.Service
}

func NewController() *Controller {
	return &Controller{
		cityService: city.NewService(),
	}
}

// GetCityList 获取城市列表
func (c *Controller) GetCityList(ctx *gin.Context) {
	// 获取父级ID参数
	parentId := ctx.DefaultQuery("parentId", "0")

	// 调用服务获取城市列表
	list, err := c.cityService.GetCityList(parentId)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(list, ctx)
}
