package category

import (
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/category"

	"github.com/gin-gonic/gin"
)

type Controller struct {
	service *category.Service
}

func NewController() *Controller {
	return &Controller{
		service: &category.Service{},
	}
}

// GetCategoryTree 获取分类树形结构
func (c *Controller) GetCategoryTree(ctx *gin.Context) {
	tree, err := c.service.GetCategoryTree()
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}
	response.SuccessWithData(tree, ctx)
}

// GetCategoryGoods 根据分类获取商品列表
func (c *Controller) GetCategoryGoods(ctx *gin.Context) {
	var req dto.CategoryGoodsReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 验证分类ID
	if req.CategoryID <= 0 {
		response.Error(400, "分类ID不能为空", ctx)
		return
	}

	resp, err := c.service.GetCategoryGoods(&req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}
	response.SuccessWithData(resp, ctx)
}
