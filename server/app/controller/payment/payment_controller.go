package payment

import (
	"crypto/md5"
	"fmt"
	"io/ioutil"
	"math/rand"
	"net/http"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/model/do/BeautyDB"
	"wnsys/shop/app/model/do/ShopDB"
	"wnsys/shop/app/provider/db"
	"wnsys/shop/app/provider/mylog"
	"wnsys/shop/app/service/payment"

	"github.com/gin-gonic/gin"
)

// PaymentController 支付控制器
type PaymentController struct{}

// NewPaymentController 创建支付控制器
func NewPaymentController() *PaymentController {
	return &PaymentController{}
}

// WechatNotify 微信支付回调通知
func (c *PaymentController) WechatNotify(ctx *gin.Context) {
	logger := mylog.GetDefaultLogger()
	logger.Info("收到微信支付回调通知")

	// 读取请求体
	body, err := ioutil.ReadAll(ctx.Request.Body)
	if err != nil {
		logger.Error(fmt.Sprintf("读取微信支付回调请求体失败: %v", err))
		ctx.String(http.StatusBadRequest, "FAIL")
		return
	}

	// 判断是V2还是V3回调（V2是XML格式，V3是JSON格式）
	var notifyData map[string]string

	// 尝试解析为XML（V2格式）
	if body[0] == '<' {
		// V2格式回调 - 解析XML
		notifyData = parseWechatV2XML(body)
		if len(notifyData) == 0 {
			logger.Error("解析微信V2回调XML失败")
			ctx.String(http.StatusBadRequest, "FAIL")
			return
		}
		logger.Info("检测到微信支付V2回调")
	} else {
		// V3格式回调（暂时不支持，因为需要特殊处理）
		logger.Warn("收到微信支付V3回调，暂不支持")
		ctx.String(http.StatusOK, "SUCCESS")
		return
	}

	// 获取支付服务
	factory := payment.NewPaymentFactory()
	paymentService := factory.GetPaymentService(1) // 微信支付
	if paymentService == nil {
		logger.Error("获取微信支付服务失败")
		ctx.String(http.StatusInternalServerError, "FAIL")
		return
	}

	// 处理回调
	err = paymentService.Notify(notifyData)
	if err != nil {
		logger.Error(fmt.Sprintf("处理微信支付回调失败: %v", err))
		ctx.String(http.StatusInternalServerError, "FAIL")
		return
	}

	// 返回成功响应
	logger.Info("微信支付回调处理成功")
	ctx.String(http.StatusOK, "SUCCESS")
}

// AlipayNotify 支付宝支付回调通知
func (c *PaymentController) AlipayNotify(ctx *gin.Context) {
	logger := mylog.GetDefaultLogger()
	logger.Info("收到支付宝支付回调通知")

	// 获取POST参数
	notifyData := make(map[string]string)
	for key, values := range ctx.Request.PostForm {
		if len(values) > 0 {
			notifyData[key] = values[0]
		}
	}

	// 获取支付服务
	factory := payment.NewPaymentFactory()
	paymentService := factory.GetPaymentService(2) // 支付宝支付
	if paymentService == nil {
		logger.Error("获取支付宝支付服务失败")
		ctx.String(http.StatusInternalServerError, "fail")
		return
	}

	// 处理回调
	err := paymentService.Notify(notifyData)
	if err != nil {
		logger.Error(fmt.Sprintf("处理支付宝支付回调失败: %v", err))
		ctx.String(http.StatusInternalServerError, "fail")
		return
	}

	// 返回成功响应
	logger.Info("支付宝支付回调处理成功")
	ctx.String(http.StatusOK, "success")
}

// TestNotify 测试回调接口
func (c *PaymentController) TestNotify(ctx *gin.Context) {
	response.SuccessWithData(gin.H{
		"message": "支付回调接口正常",
		"time":    fmt.Sprintf("%d", ctx.GetInt64("timestamp")),
	}, ctx)
}

// TestWechatNotify 测试微信支付回调
func (c *PaymentController) TestWechatNotify(ctx *gin.Context) {
	logger := mylog.GetDefaultLogger()
	logger.Info("开始测试微信支付回调")

	// 获取测试参数
	orderNo := ctx.DefaultQuery("order_no", "TEST20240101001")
	transactionId := ctx.DefaultQuery("transaction_id", fmt.Sprintf("4200001%d", time.Now().UnixMilli()))

	logger.Info(fmt.Sprintf("测试参数 - 订单号: %s, 交易号: %s", orderNo, transactionId))

	// 从数据库读取订单信息
	orderInfo, err := c.getOrderInfoByNo(orderNo)
	if err != nil {
		logger.Error(fmt.Sprintf("获取订单信息失败: %v", err))
		response.Error(500, fmt.Sprintf("获取订单信息失败: %v", err), ctx)
		return
	}

	// 计算微信支付金额（单位：分）
	totalFee := int64(orderInfo.PayAmount * 100)
	totalFeeStr := strconv.FormatInt(totalFee, 10)

	logger.Info(fmt.Sprintf("从订单读取的信息 - 订单ID: %d, 支付金额: %.2f元(%s分), 用户ID: %d",
		orderInfo.ID, orderInfo.PayAmount, totalFeeStr, orderInfo.UserID))

	// 生成模拟的微信支付回调通知数据
	notifyData := c.generateMockWechatNotifyData(orderNo, totalFeeStr, transactionId)
	logger.Info(fmt.Sprintf("生成的模拟回调数据: %+v", notifyData))

	// 获取支付服务
	factory := payment.NewPaymentFactory()
	paymentService := factory.GetPaymentService(1) // 微信支付
	if paymentService == nil {
		logger.Error("获取微信支付服务失败")
		response.Error(500, "获取支付服务失败", ctx)
		return
	}

	// 处理回调（包含签名验证）
	err = paymentService.Notify(notifyData)
	if err != nil {
		logger.Error(fmt.Sprintf("处理测试回调失败: %v", err))
		response.Error(500, fmt.Sprintf("处理回调失败: %v", err), ctx)
		return
	}

	logger.Info("测试微信支付回调处理成功")
	response.SuccessWithData(gin.H{
		"message":        "测试微信支付回调成功",
		"order_no":       orderNo,
		"order_id":       orderInfo.ID,
		"pay_amount":     orderInfo.PayAmount,
		"total_fee":      totalFeeStr,
		"transaction_id": transactionId,
		"notify_data":    notifyData,
		"note":           "从数据库读取订单信息，使用正确签名",
	}, ctx)
}

// TestAlipayNotify 测试支付宝支付回调
func (c *PaymentController) TestAlipayNotify(ctx *gin.Context) {
	logger := mylog.GetDefaultLogger()
	logger.Info("开始测试支付宝支付回调")

	// 获取测试参数
	orderNo := ctx.DefaultQuery("order_no", "TEST20240101001")
	tradeNo := ctx.DefaultQuery("trade_no", fmt.Sprintf("2024%d", time.Now().UnixMilli()))

	logger.Info(fmt.Sprintf("测试参数 - 订单号: %s, 交易号: %s", orderNo, tradeNo))

	// 从数据库读取订单信息
	orderInfo, err := c.getOrderInfoByNo(orderNo)
	if err != nil {
		logger.Error(fmt.Sprintf("获取订单信息失败: %v", err))
		response.Error(500, fmt.Sprintf("获取订单信息失败: %v", err), ctx)
		return
	}

	// 支付宝金额（单位：元）
	totalAmount := fmt.Sprintf("%.2f", orderInfo.PayAmount)

	logger.Info(fmt.Sprintf("从订单读取的信息 - 订单ID: %d, 支付金额: %s元, 用户ID: %d",
		orderInfo.ID, totalAmount, orderInfo.UserID))

	// 生成模拟的支付宝回调通知数据
	notifyData := c.generateMockAlipayNotifyData(orderNo, totalAmount, tradeNo)
	logger.Info(fmt.Sprintf("生成的模拟支付宝回调数据: %+v", notifyData))

	// 获取支付服务
	factory := payment.NewPaymentFactory()
	paymentService := factory.GetPaymentService(2) // 支付宝支付
	if paymentService == nil {
		logger.Error("获取支付宝支付服务失败")
		response.Error(500, "获取支付服务失败", ctx)
		return
	}

	// 处理回调（包含签名验证）
	err = paymentService.Notify(notifyData)
	if err != nil {
		logger.Error(fmt.Sprintf("处理支付宝测试回调失败: %v", err))
		response.Error(500, fmt.Sprintf("处理回调失败: %v", err), ctx)
		return
	}

	logger.Info("测试支付宝支付回调处理成功")
	response.SuccessWithData(gin.H{
		"message":      "测试支付宝支付回调成功",
		"order_no":     orderNo,
		"order_id":     orderInfo.ID,
		"pay_amount":   orderInfo.PayAmount,
		"total_amount": totalAmount,
		"trade_no":     tradeNo,
		"notify_data":  notifyData,
		"note":         "从数据库读取订单信息，使用模拟签名",
	}, ctx)
}

// generateMockWechatNotifyData 生成模拟的微信支付回调通知数据（参考Java实现）
func (c *PaymentController) generateMockWechatNotifyData(orderNo, totalFee, transactionId string) map[string]string {
	// 生成随机字符串
	nonceStr := c.getRandomString(32)

	// 构建回调参数（参考Java代码的字段）
	params := map[string]string{
		"appid":          "wx58459973f280f050",
		"bank_type":      "OTHERS",
		"cash_fee":       totalFee,
		"fee_type":       "CNY",
		"is_subscribe":   "N",
		"mch_id":         "**********",
		"nonce_str":      nonceStr,
		"openid":         "oUpF8uMuAJO_M2pxb1Q9zNjWeS6o",
		"out_trade_no":   orderNo,
		"result_code":    "SUCCESS",
		"return_code":    "SUCCESS",
		"time_end":       fmt.Sprintf("%d", time.Now().Unix()),
		"total_fee":      totalFee,
		"trade_type":     "JSAPI",
		"transaction_id": transactionId,
	}

	// 生成正确的签名
	sign := c.generateWechatSign(params)
	params["sign"] = sign

	return params
}

// generateWechatSign 生成微信支付签名（参考微信V2签名算法）
func (c *PaymentController) generateWechatSign(params map[string]string) string {
	// 微信支付API密钥
	apiKey := "jlNH9PW28B4CRkPoWMhwFfvzQa5VNE0G"

	// 1. 参数名按ASCII码从小到大排序
	var keys []string
	for k := range params {
		// 排除sign字段和空值字段
		if k != "sign" && params[k] != "" {
			keys = append(keys, k)
		}
	}

	// 排序
	sort.Strings(keys)

	// 2. 构建待签名字符串
	var signStr strings.Builder
	for i, k := range keys {
		if i > 0 {
			signStr.WriteString("&")
		}
		signStr.WriteString(k)
		signStr.WriteString("=")
		signStr.WriteString(params[k])
	}

	// 3. 拼接API密钥
	signStr.WriteString("&key=")
	signStr.WriteString(apiKey)

	// 4. MD5加密并转大写
	hash := md5.Sum([]byte(signStr.String()))
	return fmt.Sprintf("%X", hash)
}

// getRandomString 生成指定长度的随机字符串（参考Java实现）
func (c *PaymentController) getRandomString(length int) string {
	characters := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	var sb strings.Builder

	// 设置随机种子
	rand.Seed(time.Now().UnixNano())

	for i := 0; i < length; i++ {
		index := rand.Intn(len(characters))
		sb.WriteByte(characters[index])
	}

	return sb.String()
}

// getOrderInfoByNo 根据订单号获取订单信息（支持普通订单和美容预约订单）
func (c *PaymentController) getOrderInfoByNo(orderNo string) (*ShopDB.OrderDO, error) {
	// 先尝试查询普通订单
	var order ShopDB.OrderDO
	err := db.DB.Shop.Where("order_no = ?", orderNo).First(&order).Error
	if err == nil {
		// 检查订单状态
		if order.Status != "unpaid" {
			return nil, fmt.Errorf("订单状态不是待支付: %s, 当前状态: %s", orderNo, order.Status)
		}
		return &order, nil
	}

	// 如果普通订单不存在，尝试查询美容预约订单
	var booking BeautyDB.BeautyBookingDO
	err = db.DB.Shop.Where("booking_no = ?", orderNo).First(&booking).Error
	if err != nil {
		return nil, fmt.Errorf("订单不存在: %s", orderNo)
	}

	// 检查预约支付状态
	if booking.PaymentStatus == "paid" {
		return nil, fmt.Errorf("预约已支付: %s", orderNo)
	}

	// 将美容预约转换为订单格式
	order = ShopDB.OrderDO{
		ID:        booking.ID,
		OrderNo:   booking.BookingNo,
		UserID:    booking.UserID,
		PayAmount: booking.FinalPrice,
		Status:    "unpaid", // 美容预约的支付状态
	}

	return &order, nil
}

// generateMockAlipayNotifyData 生成模拟的支付宝回调通知数据
func (c *PaymentController) generateMockAlipayNotifyData(orderNo, totalAmount, tradeNo string) map[string]string {
	// 构建支付宝回调参数
	params := map[string]string{
		"gmt_create":       time.Now().Format("2006-01-02 15:04:05"),
		"charset":          "utf-8",
		"gmt_payment":      time.Now().Format("2006-01-02 15:04:05"),
		"notify_time":      time.Now().Format("2006-01-02 15:04:05"),
		"subject":          "测试商品",
		"buyer_id":         "2088123456789012345",
		"body":             "测试商品描述",
		"invoice_amount":   totalAmount,
		"version":          "1.0",
		"notify_id":        c.getRandomString(16),
		"fund_bill_list":   fmt.Sprintf("[{\"amount\":\"%s\",\"fundChannel\":\"ALIPAYACCOUNT\"}]", totalAmount),
		"notify_type":      "trade_status_sync",
		"out_trade_no":     orderNo,
		"total_amount":     totalAmount,
		"trade_status":     "TRADE_SUCCESS",
		"trade_no":         tradeNo,
		"auth_app_id":      "****************",
		"receipt_amount":   totalAmount,
		"point_amount":     "0.00",
		"app_id":           "****************",
		"buyer_pay_amount": totalAmount,
		"sign_type":        "RSA2",
		"seller_id":        "****************",
	}

	// 生成模拟签名（支付宝签名比较复杂，这里使用简化版本用于测试）
	sign := c.generateAlipayMockSign(params)
	params["sign"] = sign

	return params
}

// generateAlipayMockSign 生成支付宝模拟签名（简化版本，仅用于测试）
func (c *PaymentController) generateAlipayMockSign(params map[string]string) string {
	// 支付宝签名比较复杂，这里生成一个基于参数的简单签名用于测试
	var signStr strings.Builder

	// 获取所有参数键并排序
	var keys []string
	for k := range params {
		if k != "sign" && k != "sign_type" && params[k] != "" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 构建签名字符串
	for i, k := range keys {
		if i > 0 {
			signStr.WriteString("&")
		}
		signStr.WriteString(k)
		signStr.WriteString("=")
		signStr.WriteString(params[k])
	}

	// 生成MD5签名（实际支付宝使用RSA2，这里简化处理）
	hash := md5.Sum([]byte(signStr.String()))
	return fmt.Sprintf("MOCK_ALIPAY_SIGN_%X", hash)
}

// parseWechatV2XML 解析微信V2回调XML
func parseWechatV2XML(xmlData []byte) map[string]string {
	result := make(map[string]string)

	// 使用正则表达式解析XML
	re := regexp.MustCompile(`<([^>]+)><!\[CDATA\[([^\]]*)\]\]></[^>]+>|<([^>]+)>([^<]*)</[^>]+>`)
	matches := re.FindAllSubmatch(xmlData, -1)

	for _, match := range matches {
		if len(match) >= 3 {
			if len(match[1]) > 0 && len(match[2]) > 0 {
				// CDATA格式
				result[string(match[1])] = string(match[2])
			} else if len(match[3]) > 0 && len(match[4]) > 0 {
				// 普通格式
				result[string(match[3])] = string(match[4])
			}
		}
	}

	return result
}
