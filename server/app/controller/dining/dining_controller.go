package dining

import (
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/service/dining"

	"github.com/gin-gonic/gin"
)

type Controller struct {
	service *dining.Service
}

// NewController 创建堂食外卖控制器
func NewController() *Controller {
	return &Controller{
		service: dining.NewService(),
	}
}

// GetDiningTypes 获取用餐类型列表
func (c *Controller) GetDiningTypes(ctx *gin.Context) {
	list, err := c.service.GetDiningTypes()
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(list, ctx)
}

// GetDiningTables 获取餐桌列表
func (c *Controller) GetDiningTables(ctx *gin.Context) {
	list, err := c.service.GetDiningTables()
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(list, ctx)
}

// GetTableByNo 根据餐桌编号获取餐桌信息
func (c *Controller) GetTableByNo(ctx *gin.Context) {
	tableNo := ctx.Query("tableNo")
	if tableNo == "" {
		response.Error(400, "餐桌编号不能为空", ctx)
		return
	}

	table, err := c.service.GetTableByNo(tableNo)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(table, ctx)
}
