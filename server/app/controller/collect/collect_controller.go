package collect

import (
	"fmt"
	"strconv"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/collect"

	"github.com/gin-gonic/gin"
)

// Controller 收藏点赞控制器
type Controller struct {
	service *collect.Service
}

// NewController 创建收藏点赞控制器
func NewController() *Controller {
	return &Controller{
		service: collect.NewService(),
	}
}

// Collect 收藏/取消收藏
func (c *Controller) Collect(ctx *gin.Context) {
	var req dto.CommonCollectReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取用户ID
	userID := ctx.GetInt64("userId")
	fmt.Printf("[Collect] Request: %+v, UserID: %d\n", req, userID)

	if userID == 0 {
		fmt.Printf("[Collect] UserID is 0, user not authenticated\n")
		response.Error(401, "请先登录", ctx)
		return
	}

	// 执行收藏操作
	err := c.service.Collect(userID, &req)
	if err != nil {
		fmt.Printf("[Collect] Service error: %v\n", err)
		response.ErrorWithError(err, ctx)
		return
	}

	if req.Action == 1 {
		response.SuccessWithMessage("收藏成功", ctx)
	} else {
		response.SuccessWithMessage("取消收藏成功", ctx)
	}
}

// GetCollectStatus 获取收藏状态
func (c *Controller) GetCollectStatus(ctx *gin.Context) {
	// 获取参数
	targetIDStr := ctx.Query("targetId")
	targetType := ctx.Query("targetType")

	if targetIDStr == "" || targetType == "" {
		response.Error(400, "参数不完整", ctx)
		return
	}

	targetID, err := strconv.ParseInt(targetIDStr, 10, 64)
	if err != nil {
		response.Error(400, "目标ID格式错误", ctx)
		return
	}

	// 获取用户ID（可选）
	userID := ctx.GetInt64("userId")

	// 获取收藏状态
	status, err := c.service.GetCollectStatus(userID, targetID, targetType)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(status, ctx)
}

// Like 点赞/取消点赞
func (c *Controller) Like(ctx *gin.Context) {
	var req dto.CommonLikeReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取用户ID
	userID := ctx.GetInt64("userId")
	if userID == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 执行点赞操作
	err := c.service.Like(userID, &req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	if req.Action == 1 {
		response.SuccessWithMessage("点赞成功", ctx)
	} else {
		response.SuccessWithMessage("取消点赞成功", ctx)
	}
}

// GetLikeStatus 获取点赞状态
func (c *Controller) GetLikeStatus(ctx *gin.Context) {
	// 获取参数
	targetIDStr := ctx.Query("targetId")
	targetType := ctx.Query("targetType")

	if targetIDStr == "" || targetType == "" {
		response.Error(400, "参数不完整", ctx)
		return
	}

	targetID, err := strconv.ParseInt(targetIDStr, 10, 64)
	if err != nil {
		response.Error(400, "目标ID格式错误", ctx)
		return
	}

	// 获取用户ID（可选）
	userID := ctx.GetInt64("userId")

	// 获取点赞状态
	status, err := c.service.GetLikeStatus(userID, targetID, targetType)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(status, ctx)
}
