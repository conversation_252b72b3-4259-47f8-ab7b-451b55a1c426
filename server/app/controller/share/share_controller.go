package share

import (
	"fmt"
	"strconv"
	"wnsys/shop/app/common/request"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/share"

	"github.com/gin-gonic/gin"
)

type ShareController struct {
	shareService *share.ShareService
}

func NewController() *ShareController {
	return &ShareController{
		shareService: share.NewShareService(),
	}
}

// GetConfig 获取分销配置
func (c *ShareController) GetConfig(ctx *gin.Context) {
	config, err := c.shareService.GetShareSetting()
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}
	response.SuccessWithData(config, ctx)
}

// GetDistributorInfo 获取分销员信息
func (c *ShareController) GetDistributorInfo(ctx *gin.Context) {
	userId := request.GetUserID(ctx)
	info, err := c.shareService.GetDistributorInfo(userId)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}
	response.SuccessWithData(info, ctx)
}

// ApplyDistributor 申请成为分销员
func (c *ShareController) ApplyDistributor(ctx *gin.Context) {
	var req dto.ApplyDistributorReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	userId := request.GetUserID(ctx)
	req.UserId = userId

	err := c.shareService.ApplyDistributor(&req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}
	response.SuccessWithMessage("申请提交成功，请等待审核", ctx)
}

// CheckQualification 检查分销资格
func (c *ShareController) CheckQualification(ctx *gin.Context) {
	userId := request.GetUserID(ctx)
	qualified, err := c.shareService.CheckQualification(userId)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}
	response.SuccessWithData(gin.H{"qualified": qualified}, ctx)
}

// GetTeamStats 获取团队统计数据
func (c *ShareController) GetTeamStats(ctx *gin.Context) {
	userId := request.GetUserID(ctx)
	stats, err := c.shareService.GetTeamStats(userId)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}
	response.SuccessWithData(stats, ctx)
}

// GetTeamMembers 获取团队成员列表
func (c *ShareController) GetTeamMembers(ctx *gin.Context) {
	userId := request.GetUserID(ctx)

	// 获取分页参数
	pageStr := ctx.DefaultQuery("page", "1")
	sizeStr := ctx.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	list, err := c.shareService.GetTeamMembers(userId, page, size)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}
	response.SuccessWithData(list, ctx)
}

// GetDistributionOrders 获取分销订单列表
func (c *ShareController) GetDistributionOrders(ctx *gin.Context) {
	userId := request.GetUserID(ctx)

	// 获取分页参数
	pageStr := ctx.DefaultQuery("page", "1")
	sizeStr := ctx.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	list, err := c.shareService.GetDistributionOrders(userId, page, size)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}
	response.SuccessWithData(list, ctx)
}

// GetDistributionOrderStats 获取分销订单统计
func (c *ShareController) GetDistributionOrderStats(ctx *gin.Context) {
	userId := request.GetUserID(ctx)
	stats, err := c.shareService.GetDistributionOrderStats(userId)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}
	response.SuccessWithData(stats, ctx)
}

// GetBalanceInfo 获取余额信息
func (c *ShareController) GetBalanceInfo(ctx *gin.Context) {
	userId := request.GetUserID(ctx)
	info, err := c.shareService.GetDistributorInfo(userId)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 返回余额相关信息
	balance := gin.H{
		"availableBalance": info.AvailableBalance,
		"frozenBalance":    info.FrozenBalance,
		"totalWithdraw":    0.0, // TODO: 从提现记录计算
	}
	response.SuccessWithData(balance, ctx)
}

// GetWithdrawConfig 获取提现配置
func (c *ShareController) GetWithdrawConfig(ctx *gin.Context) {
	_, err := c.shareService.GetShareSetting()
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 返回提现相关配置
	withdrawConfig := gin.H{
		"minAmount":     10.0,
		"maxAmount":     5000.0,
		"feeRate":       0.01,
		"wechatEnabled": true,
		"alipayEnabled": true,
	}
	response.SuccessWithData(withdrawConfig, ctx)
}

// Withdraw 提现
func (c *ShareController) Withdraw(ctx *gin.Context) {
	var req dto.WithdrawReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	userId := request.GetUserID(ctx)
	req.UserId = userId

	err := c.shareService.Withdraw(ctx, &req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}
	response.SuccessWithMessage("提现申请成功", ctx)
}

// GetWithdrawList 获取提现记录
func (c *ShareController) GetWithdrawList(ctx *gin.Context) {
	userId := request.GetUserID(ctx)

	// 获取分页参数
	pageStr := ctx.DefaultQuery("page", "1")
	sizeStr := ctx.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	list, err := c.shareService.GetWithdrawList(userId, page, size)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}
	response.SuccessWithData(list, ctx)
}

// GetWithdrawStats 获取提现统计
func (c *ShareController) GetWithdrawStats(ctx *gin.Context) {
	userId := request.GetUserID(ctx)

	// 使用现有的提现列表方法来计算统计
	list, err := c.shareService.GetWithdrawList(userId, 1, 1000)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 计算统计数据
	stats := gin.H{
		"totalCount":    0,
		"totalAmount":   "0.00",
		"successAmount": "0.00",
	}

	if listData, ok := list["list"].([]interface{}); ok {
		totalCount := len(listData)
		var totalAmount, successAmount float64

		for _, item := range listData {
			if itemMap, ok := item.(map[string]interface{}); ok {
				if amount, ok := itemMap["amount"].(float64); ok {
					totalAmount += amount
					if status, ok := itemMap["status"].(string); ok && status == "success" {
						successAmount += amount
					}
				}
			}
		}

		stats = gin.H{
			"totalCount":    totalCount,
			"totalAmount":   fmt.Sprintf("%.2f", totalAmount),
			"successAmount": fmt.Sprintf("%.2f", successAmount),
		}
	}

	response.SuccessWithData(stats, ctx)
}
