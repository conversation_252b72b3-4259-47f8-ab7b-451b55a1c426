package cart

import (
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/model/dto"
	"wnsys/shop/app/service/cart"

	"github.com/gin-gonic/gin"
)

// Controller 购物车控制器
type Controller struct {
	service *cart.Service
}

// NewController 创建购物车控制器
func NewController() *Controller {
	return &Controller{
		service: cart.NewService(),
	}
}

// Add 添加商品到购物车
// @Summary 添加购物车
// @Description 添加商品到购物车
// @Tags 购物车
// @Accept json
// @Produce json
// @Param data body dto.AddCartReq true "请求参数"
// @Success 200 {object} response.Response
// @Router /api/cart/add [post]
func (c *Controller) Add(ctx *gin.Context) {
	var req dto.AddCartReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取用户ID
	userID := ctx.GetInt64("userId")
	if userID == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 添加到购物车
	cartId, err := c.service.AddCart(userID, &req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(map[string]interface{}{
		"cartId":  cartId,
		"message": "添加成功",
	}, ctx)
}

// GetCount 获取购物车商品数量
// @Summary 获取购物车数量
// @Description 获取当前用户购物车商品总数
// @Tags 购物车
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{data=vo.CartCountVO}
// @Router /api/cart/count [get]
func (c *Controller) GetCount(ctx *gin.Context) {
	// 获取用户ID
	userID := ctx.GetInt64("userId")
	if userID == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 获取购物车数量
	count, err := c.service.GetCartCount(userID)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(count, ctx)
}

// GetList 获取购物车列表
// @Summary 获取购物车列表
// @Description 获取当前用户购物车商品列表
// @Tags 购物车
// @Accept json
// @Produce json
// @Param selected query bool false "是否只查询已选中的商品"
// @Success 200 {object} response.Response{data=vo.CartListVO}
// @Router /api/cart/list [get]
func (c *Controller) GetList(ctx *gin.Context) {
	// 获取用户ID
	userID := ctx.GetInt64("userId")
	if userID == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 获取查询参数
	selected := ctx.Query("selected") == "true"

	// 获取购物车列表
	list, err := c.service.GetCartList(userID, selected)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithData(list, ctx)
}

// UpdateQuantity 更新购物车商品数量
// @Summary 修改购物车商品数量
// @Description 修改购物车商品数量
// @Tags 购物车
// @Accept json
// @Produce json
// @Param data body dto.UpdateCartReq true "请求参数"
// @Success 200 {object} response.Response
// @Router /api/cart/quantity [put]
func (c *Controller) UpdateQuantity(ctx *gin.Context) {
	var req dto.UpdateCartReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取用户ID
	userID := ctx.GetInt64("userId")
	if userID == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 更新数量
	err := c.service.UpdateCartQuantity(userID, &req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("更新成功", ctx)
}

// UpdateSelected 更新购物车商品选中状态
// @Summary 修改购物车商品选中状态
// @Description 修改购物车商品选中状态
// @Tags 购物车
// @Accept json
// @Produce json
// @Param data body dto.UpdateCartSelectedReq true "请求参数"
// @Success 200 {object} response.Response
// @Router /api/cart/selected [put]
func (c *Controller) UpdateSelected(ctx *gin.Context) {
	var req dto.UpdateCartSelectedReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取用户ID
	userID := ctx.GetInt64("userId")
	if userID == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 更新选中状态
	err := c.service.UpdateCartSelected(userID, &req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("更新成功", ctx)
}

// Delete 删除购物车商品
// @Summary 删除购物车商品
// @Description 删除购物车商品
// @Tags 购物车
// @Accept json
// @Produce json
// @Param data body dto.DeleteCartReq true "请求参数"
// @Success 200 {object} response.Response
// @Router /api/cart/delete [post]
func (c *Controller) Delete(ctx *gin.Context) {
	var req dto.DeleteCartReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	// 获取用户ID
	userID := ctx.GetInt64("userId")
	if userID == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 删除购物车商品
	err := c.service.DeleteCart(userID, &req)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("删除成功", ctx)
}

// Clear 清空购物车
// @Summary 清空购物车
// @Description 清空购物车
// @Tags 购物车
// @Accept json
// @Produce json
// @Success 200 {object} response.Response
// @Router /api/cart/clear [post]
func (c *Controller) Clear(ctx *gin.Context) {
	// 获取用户ID
	userID := ctx.GetInt64("userId")
	if userID == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 清空购物车
	err := c.service.ClearCart(userID)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("清空成功", ctx)
}

// ClearSelected 清空选中的购物车商品
// @Summary 清除已选中的购物车商品
// @Description 清除已选中的购物车商品
// @Tags 购物车
// @Accept json
// @Produce json
// @Success 200 {object} response.Response
// @Router /api/cart/clear-selected [post]
func (c *Controller) ClearSelected(ctx *gin.Context) {
	// 获取用户ID
	userID := ctx.GetInt64("userId")
	if userID == 0 {
		response.Error(401, "请先登录", ctx)
		return
	}

	// 清空选中商品
	err := c.service.ClearSelectedCart(userID)
	if err != nil {
		response.ErrorWithError(err, ctx)
		return
	}

	response.SuccessWithMessage("清空成功", ctx)
}
