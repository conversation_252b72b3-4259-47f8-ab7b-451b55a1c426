package config

// OSSConfig 阿里云OSS配置
type OSSConfig struct {
	Endpoint        string `yaml:"endpoint"`
	AccessKeyId     string `yaml:"accessKeyId"`
	AccessKeySecret string `yaml:"accessKeySecret"`
	BucketName      string `yaml:"bucketName"`
	Domain          string `yaml:"domain"`
}

// SmsConfig 阿里云短信配置
type SmsConfig struct {
	AccessKeyId     string            `yaml:"accessKeyId"`
	AccessKeySecret string            `yaml:"accessKeySecret"`
	SignName        string            `yaml:"signName"`
	TemplateCode    map[string]string `yaml:"templateCode"`
}

// AliyunConfig 阿里云配置
type AliyunConfig struct {
	OSS OSSConfig `yaml:"oss"`
	SMS SmsConfig `yaml:"sms"`
}

// MiniAppConfig 微信小程序配置
type MiniAppConfig struct {
	AppID  string `yaml:"appid"`
	Secret string `yaml:"secret"`
}

// WechatPayConfig 微信支付配置
type WechatPayConfig struct {
	Version        string `yaml:"version"`
	MchID          string `yaml:"mchid"`
	ApiKey         string `yaml:"apikey"`
	AppSecret      string `yaml:"appsecret"`
	NotifyUrl      string `yaml:"notify-url"`
	CertPath       string `yaml:"cert-path"`
	PrivateKeyPath string `yaml:"private-key-path"`
	SerialNo       string `yaml:"serial-no"`
}

// WechatConfig 微信配置
type WechatConfig struct {
	MiniApp MiniAppConfig   `yaml:"miniapp"`
	Pay     WechatPayConfig `yaml:"pay"`
}

type AppConfig struct {
	ENV         string       `yaml:"ENV"`
	Host        string       `yaml:"Host"`
	Port        int          `yaml:"Port"`
	Name        string       `yaml:"Name"`
	Aliyun      AliyunConfig `yaml:"aliyun"`
	UploadPath  string       `yaml:"UploadPath"`
	ImageDomain string       `yaml:"ImageDomain"` // 图片域名
	Wx          WechatConfig `yaml:"wx"`
}

var App AppConfig
