package server

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"wnsys/shop/app/provider/mylog"

	"github.com/gin-gonic/gin"
)

// SetupStaticFiles 配置静态文件服务
func SetupStaticFiles(engine *gin.Engine, uploadPath string) {
	if uploadPath == "" {
		mylog.GetDefaultLogger().Warn("上传文件路径 UploadPath 未配置, 跳过静态文件服务设置")
		return
	}

	// 检查目录是否存在，如果不存在则尝试创建
	if _, err := os.Stat(uploadPath); os.IsNotExist(err) {
		mylog.GetDefaultLogger().Warn(fmt.Sprintf("上传目录 %s 不存在，尝试创建...", uploadPath))
		if mkdirErr := os.MkdirAll(uploadPath, 0755); mkdirErr != nil {
			mylog.GetDefaultLogger().Error(fmt.Sprintf("创建上传目录 %s 失败: %v", uploadPath, mkdirErr))
			// 创建失败，不设置静态文件服务
			return
		} else {
			mylog.GetDefaultLogger().Info(fmt.Sprintf("成功创建上传目录: %s", uploadPath))
		}
	} else if err != nil {
		// 检查目录时发生其他错误
		mylog.GetDefaultLogger().Error(fmt.Sprintf("检查上传目录 %s 时出错: %v", uploadPath, err))
		// 检查出错，不设置静态文件服务
		return
	}

	// 目录存在或已成功创建
	engine.Static("/uploads", uploadPath)
	mylog.GetDefaultLogger().Info(fmt.Sprintf("静态文件服务已映射: /uploads -> %s", uploadPath))
}

// Run 启动 HTTP 服务器并处理优雅停机
func Run(engine *gin.Engine, addr string) error {
	srv := &http.Server{
		Addr:    addr,
		Handler: engine,
	}

	// 启动服务器 (goroutine)
	go func() {
		mylog.GetDefaultLogger().Info(fmt.Sprintf("服务器正在监听 %s", srv.Addr))
		if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			// ListenAndServe 失败通常是致命的，直接 panic 或使用 Fatal 记录
			mylog.GetDefaultLogger().Fatal(fmt.Sprintf("监听失败: %s\n", err))
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit // 阻塞在此，直到接收到信号
	mylog.GetDefaultLogger().Info("收到关闭信号，正在关闭服务器...")

	// 设置超时 context
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second) // 5秒超时
	defer cancel()

	// 优雅关闭服务器
	if err := srv.Shutdown(ctx); err != nil {
		mylog.GetDefaultLogger().Error(fmt.Sprintf("服务器关闭发生错误: %v", err)) // 这里使用 Error 而不是 Fatal，允许主程序继续执行清理
		return fmt.Errorf("服务器关闭发生错误: %w", err)
	}

	mylog.GetDefaultLogger().Info("服务器已成功关闭")
	return nil // 正常关闭返回 nil
}
