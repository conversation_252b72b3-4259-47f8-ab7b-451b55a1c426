package middleware

import (
	"bytes"
	"encoding/json"
	"io"
	"wnsys/shop/app/common/utils"
	"wnsys/shop/app/provider/mylog"

	"github.com/gin-gonic/gin"
)

// Logs 日志中间件
func Logs() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取请求ID
		requestID := c.<PERSON>ead<PERSON>("X-Request-ID")
		if requestID == "" {
			requestID = utils.GenID()
		}

		// 设置请求ID到响应头和上下文
		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)

		// 读取请求体
		var requestBody map[string]interface{}
		if c.Request.Body != nil {
			bodyBytes, _ := c.GetRawData()
			if len(bodyBytes) > 0 {
				_ = json.Unmarshal(bodyBytes, &requestBody)
				// 重新设置请求体，因为读取后需要重新设置
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
			}
		}

		// 记录请求日志
		mylog.GetDefaultLogger().Info(c, "start", map[string]interface{}{
			"path":        c.Request.URL.Path,
			"method":      c.Request.Method,
			"requestBody": requestBody,
			"ip":          c.ClientIP(),
			"params":      c.Request.URL.Query(),
		})

		// 创建自定义ResponseWriter以捕获响应数据
		blw := &bodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = blw

		c.Next()

		// 解析响应数据
		var responseBody map[string]interface{}
		if len(blw.body.Bytes()) > 0 {
			_ = json.Unmarshal(blw.body.Bytes(), &responseBody)
		}
		if responseBody["code"] == float64(200) {
			// 记录响应日志
			mylog.GetDefaultLogger().Info(c, "completed", map[string]interface{}{
				"status":       c.Writer.Status(),
				"responseBody": responseBody,
			})
		} else {
			mylog.GetDefaultLogger().Error(c, "completed", map[string]interface{}{
				"status":       c.Writer.Status(),
				"responseBody": responseBody,
			})
		}
	}
}

// bodyLogWriter 用于捕获响应体
type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *bodyLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

func (w *bodyLogWriter) WriteString(s string) (int, error) {
	w.body.WriteString(s)
	return w.ResponseWriter.WriteString(s)
}
