package middleware

import (
	"fmt"
	"strings"
	"wnsys/shop/app/common/response"
	"wnsys/shop/app/common/utils"
	"wnsys/shop/app/provider/db"

	"github.com/gin-gonic/gin"
)

// JWT 中间件，用于验证令牌
func JWT() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 从请求头中获取令牌
		authHeader := ctx.GetHeader("Authorization")
		fmt.Printf("[JWT] Authorization header: %s\n", authHeader)

		if authHeader == "" {
			fmt.Printf("[JWT] No Authorization header provided\n")
			response.Error(401, "未提供令牌", ctx)
			ctx.Abort()
			return
		}

		// 检查令牌格式
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			fmt.Printf("[JWT] Invalid token format: %s\n", authHeader)
			response.Error(401, "令牌格式错误", ctx)
			ctx.Abort()
			return
		}

		token := parts[1]
		fmt.Printf("[JWT] Token: %s...\n", token[:min(len(token), 20)])

		// 检查是否在黑名单中
		exists, _ := db.DB.Redis.Get("token_blacklist:" + token).Result()
		if exists != "" {
			fmt.Printf("[JWT] Token is blacklisted\n")
			response.Error(401, "令牌已失效", ctx)
			ctx.Abort()
			return
		}

		// 解析令牌
		claims, err := utils.ParseToken(parts[1])
		if err != nil {
			fmt.Printf("[JWT] Token parse failed: %v\n", err)
			response.Error(401, "无效的令牌", ctx)
			ctx.Abort()
			return
		}

		// 将用户信息存储到上下文中
		ctx.Set("userId", claims.UserID)
		ctx.Set("userName", claims.UserName)
		fmt.Printf("[JWT] User info set: userId=%d, userName=%s\n", claims.UserID, claims.UserName)

		ctx.Next()
	}
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// OptionalJWT 可选的JWT中间件，用于公开接口但需要获取用户状态的场景
// 如果用户提供了有效的token，则设置用户信息；如果没有或无效，则继续执行但不设置用户信息
func OptionalJWT() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 从请求头中获取令牌
		authHeader := ctx.GetHeader("Authorization")
		fmt.Printf("[OptionalJWT] Authorization header: %s\n", authHeader)

		if authHeader == "" {
			// 没有提供令牌，继续执行但不设置用户信息
			fmt.Printf("[OptionalJWT] No token provided, continuing without user info\n")
			ctx.Next()
			return
		}

		// 检查令牌格式
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			// 令牌格式错误，继续执行但不设置用户信息
			fmt.Printf("[OptionalJWT] Invalid token format, continuing without user info\n")
			ctx.Next()
			return
		}

		token := parts[1]

		// 检查是否在黑名单中
		exists, _ := db.DB.Redis.Get("token_blacklist:" + token).Result()
		if exists != "" {
			// 令牌已失效，继续执行但不设置用户信息
			fmt.Printf("[OptionalJWT] Token blacklisted, continuing without user info\n")
			ctx.Next()
			return
		}

		// 解析令牌
		claims, err := utils.ParseToken(parts[1])
		if err != nil {
			// 令牌无效，继续执行但不设置用户信息
			fmt.Printf("[OptionalJWT] Token parse failed: %v, continuing without user info\n", err)
			ctx.Next()
			return
		}

		// 将用户信息存储到上下文中
		ctx.Set("userId", claims.UserID)
		ctx.Set("userName", claims.UserName)
		fmt.Printf("[OptionalJWT] User info set: userId=%d, userName=%s\n", claims.UserID, claims.UserName)

		ctx.Next()
	}
}
