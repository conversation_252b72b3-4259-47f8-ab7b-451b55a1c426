package request

import (
	"strconv"

	"github.com/gin-gonic/gin"
)

// GetPage 获取页码
func GetPage(c *gin.Context) int {
	page, _ := strconv.Atoi(c<PERSON><PERSON><PERSON>("page", "1"))
	if page <= 0 {
		page = 1
	}
	return page
}

// GetSize 获取每页数量
func GetSize(c *gin.Context) int {
	size, _ := strconv.Atoi(c<PERSON><PERSON>("size", "10"))
	if size <= 0 {
		size = 10
	}
	return size
}

// GetInt64 获取int64参数
func GetInt64(c *gin.Context, key string) int64 {
	value, _ := strconv.ParseInt(c.<PERSON>Form(key), 10, 64)
	return value
}

// GetUserID 从上下文中获取用户ID
func GetUserID(ctx *gin.Context) int64 {
	value, exists := ctx.Get("userId")
	if !exists {
		return 0
	}
	userId, ok := value.(int64)
	if !ok {
		return 0
	}
	return userId
}
