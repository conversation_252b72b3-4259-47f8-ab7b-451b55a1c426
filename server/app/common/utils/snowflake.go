package utils

import (
	"fmt"
	"sync"
	"time"
)

const (
	workerBits  uint8 = 10                      // 机器ID的位数
	numberBits  uint8 = 12                      // 序列号的位数
	workerMax   int64 = -1 ^ (-1 << workerBits) // 机器ID的最大值
	numberMax   int64 = -1 ^ (-1 << numberBits) // 序列号的最大值
	timeShift   uint8 = workerBits + numberBits // 时间戳向左的偏移量
	workerShift uint8 = numberBits              // 机器ID向左的偏移量
	epoch       int64 = 1640995200000           // 起始时间戳 (2022-01-01 00:00:00 +0800 CST)
)

// Snowflake 结构体
type Snowflake struct {
	sync.Mutex
	timestamp int64
	workerId  int64
	number    int64
}

var snowflake *Snowflake

func init() {
	InitSnowflake(1) // 默认使用workerId=1初始化
}

// 初始化雪花算法
func InitSnowflake(workerId int64) {
	if workerId < 0 || workerId > workerMax {
		workerId = workerId & workerMax
	}
	snowflake = &Snowflake{
		timestamp: 0,
		workerId:  workerId,
		number:    0,
	}
}

// 生成15位数字字符串ID
func GenID() string {
	if snowflake == nil {
		InitSnowflake(1) // 如果还未初始化，则进行初始化
	}

	snowflake.Lock()
	defer snowflake.Unlock()

	now := time.Now().UnixMilli()
	if snowflake.timestamp == now {
		snowflake.number++
		if snowflake.number > numberMax {
			for now <= snowflake.timestamp {
				now = time.Now().UnixMilli()
			}
		}
	} else {
		snowflake.number = 0
		snowflake.timestamp = now
	}

	// 生成ID
	id := (now-epoch)<<timeShift | (snowflake.workerId << workerShift) | (snowflake.number)

	// 格式化为15位字符串
	return fmt.Sprintf("%015d", id%1000000000000000)
}

// 格式化ID为15位字符串
func formatID(id int64) string {
	idStr := ""
	if id < 100000000000000 {
		idStr = "0" + formatID(id*10)
	} else if id > 999999999999999 {
		idStr = formatID(id / 10)
	} else {
		return time.Now().Format("050102") + time.Now().Format("150405") + "001"
	}
	return idStr
}
