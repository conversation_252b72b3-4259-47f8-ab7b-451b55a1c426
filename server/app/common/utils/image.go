package utils

import (
	"strings"
	"wnsys/shop/app/config"
)

// BuildImageURL 构建完整的图片URL
// 如果图片路径已经是完整的URL（包含http或https），则直接返回
// 否则添加配置的图片域名前缀
func BuildImageURL(imagePath string) string {
	if imagePath == "" {
		return ""
	}

	// 检查是否已经是完整的URL
	if strings.HasPrefix(imagePath, "http://") || strings.HasPrefix(imagePath, "https://") {
		return imagePath
	}

	// 如果没有配置图片域名，使用默认的localhost
	imageDomain := config.App.ImageDomain
	if imageDomain == "" {
		imageDomain = "http://localhost:8080"
	}

	// 确保域名不以/结尾，路径以/开头
	imageDomain = strings.TrimSuffix(imageDomain, "/")
	if !strings.HasPrefix(imagePath, "/") {
		imagePath = "/" + imagePath
	}

	return imageDomain + imagePath
}

// BuildImageURLs 批量处理图片URL列表
func BuildImageURLs(imagePaths []string) []string {
	if len(imagePaths) == 0 {
		return imagePaths
	}

	result := make([]string, len(imagePaths))
	for i, path := range imagePaths {
		result[i] = BuildImageURL(path)
	}
	return result
}
