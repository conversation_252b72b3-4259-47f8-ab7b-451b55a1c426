package myhttp

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"
	"wnsys/shop/app/provider/mylog"

	"github.com/parnurzeal/gorequest"
)

// HTTPClient HTTP客户端结构体
type HTTPClient struct {
	timeout time.Duration
	headers map[string]string
}

// NewHTTPClient 创建新的HTTP客户端
func NewHTTPClient() *HTTPClient {
	return &HTTPClient{
		timeout: 30 * time.Second,
		headers: make(map[string]string),
	}
}

// SetTimeout 设置超时时间
func (c *HTTPClient) SetTimeout(timeout time.Duration) *HTTPClient {
	c.timeout = timeout
	return c
}

// SetHeader 设置请求头
func (c *HTTPClient) SetHeader(key, value string) *HTTPClient {
	c.headers[key] = value
	return c
}

// SetHeaders 批量设置请求头
func (c *HTTPClient) SetHeaders(headers map[string]string) *HTTPClient {
	for k, v := range headers {
		c.headers[k] = v
	}
	return c
}

// Get 发送GET请求
func (c *HTTPClient) Get(reqUrl string, params map[string]string) ([]byte, error) {
	// 构建完整URL
	fullUrl := reqUrl
	if len(params) > 0 {
		if strings.Contains(reqUrl, "?") {
			fullUrl += "&" + concatenateURLParams(params)
		} else {
			fullUrl += "?" + concatenateURLParams(params)
		}
	}

	mylog.GetLogger("request").Info(nil, map[string]interface{}{
		"method": "GET",
		"url":    fullUrl,
		"params": params,
	}, "HTTP GET请求")

	// 创建请求
	req, err := http.NewRequest("GET", fullUrl, nil)
	if err != nil {
		mylog.GetLogger("request").Error(nil, map[string]interface{}{
			"error": err.Error(),
		}, "创建GET请求失败")
		return nil, err
	}

	// 设置请求头
	for k, v := range c.headers {
		req.Header.Set(k, v)
	}

	// 发送请求
	client := &http.Client{Timeout: c.timeout}
	resp, err := client.Do(req)
	if err != nil {
		mylog.GetLogger("request").Error(nil, map[string]interface{}{
			"error": err.Error(),
		}, "HTTP GET请求失败")
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		mylog.GetLogger("request").Error(nil, map[string]interface{}{
			"error": err.Error(),
		}, "读取GET响应失败")
		return nil, err
	}

	mylog.GetLogger("request").Info(nil, map[string]interface{}{
		"status": resp.Status,
		"body":   string(body),
	}, "HTTP GET响应")

	return body, nil
}

// Post 发送POST请求（保持向后兼容）
func (c *HTTPClient) Post(reqUrl string, requestBody map[string]string) ([]byte, error) {
	return c.PostForm(reqUrl, requestBody)
}

// PostForm 发送POST表单请求
func (c *HTTPClient) PostForm(reqUrl string, requestBody map[string]string) ([]byte, error) {
	mylog.GetLogger("request").Info(nil, map[string]interface{}{
		"method":      "POST",
		"url":         reqUrl,
		"requestBody": requestBody,
	}, "HTTP POST表单请求")

	queryString := concatenateURLParams(requestBody)

	// 创建请求
	req, err := http.NewRequest("POST", reqUrl, strings.NewReader(queryString))
	if err != nil {
		mylog.GetLogger("request").Error(nil, map[string]interface{}{
			"error": err.Error(),
		}, "创建POST请求失败")
		return nil, err
	}

	// 设置Content-Type
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// 设置其他请求头
	for k, v := range c.headers {
		req.Header.Set(k, v)
	}

	// 发送请求
	client := &http.Client{Timeout: c.timeout}
	resp, err := client.Do(req)
	if err != nil {
		mylog.GetLogger("request").Error(nil, map[string]interface{}{
			"error": err.Error(),
		}, "HTTP POST请求失败")
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		mylog.GetLogger("request").Error(nil, map[string]interface{}{
			"error": err.Error(),
		}, "读取POST响应失败")
		return nil, err
	}

	mylog.GetLogger("request").Info(nil, map[string]interface{}{
		"status": resp.Status,
		"body":   string(body),
	}, "HTTP POST响应")

	return body, nil
}

// PostJSON 发送POST JSON请求
func (c *HTTPClient) PostJSON(reqUrl string, requestBody interface{}) ([]byte, error) {
	mylog.GetLogger("request").Info(nil, map[string]interface{}{
		"method":      "POST",
		"url":         reqUrl,
		"requestBody": requestBody,
	}, "HTTP POST JSON请求")

	// 序列化JSON
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		mylog.GetLogger("request").Error(nil, map[string]interface{}{
			"error": err.Error(),
		}, "JSON序列化失败")
		return nil, err
	}

	// 创建请求
	req, err := http.NewRequest("POST", reqUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		mylog.GetLogger("request").Error(nil, map[string]interface{}{
			"error": err.Error(),
		}, "创建POST JSON请求失败")
		return nil, err
	}

	// 设置Content-Type
	req.Header.Set("Content-Type", "application/json")

	// 设置其他请求头
	for k, v := range c.headers {
		req.Header.Set(k, v)
	}

	// 发送请求
	client := &http.Client{Timeout: c.timeout}
	resp, err := client.Do(req)
	if err != nil {
		mylog.GetLogger("request").Error(nil, map[string]interface{}{
			"error": err.Error(),
		}, "HTTP POST JSON请求失败")
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		mylog.GetLogger("request").Error(nil, map[string]interface{}{
			"error": err.Error(),
		}, "读取POST JSON响应失败")
		return nil, err
	}

	mylog.GetLogger("request").Info(nil, map[string]interface{}{
		"status": resp.Status,
		"body":   string(body),
	}, "HTTP POST JSON响应")

	return body, nil
}

// --- 全局便捷方法（保持向后兼容） ---

// Post 全局POST方法（保持向后兼容）
func Post(reqUrl string, requestBody map[string]string) ([]byte, error) {
	mylog.GetLogger("request").Info(nil, map[string]interface{}{
		"reqUrl":      reqUrl,
		"requestBody": requestBody,
	}, "HTTP POST请求")

	queryString := concatenateURLParams(requestBody)
	request := gorequest.New()
	resp, body, errs := request.Post(reqUrl).
		Send(queryString).
		End()
	if errs != nil {
		mylog.GetLogger("request").Error(nil, map[string]interface{}{
			"errs": errs,
		}, "HTTP请求失败")
		return nil, errs[0]
	}
	mylog.GetLogger("request").Info(nil, map[string]interface{}{
		"resp": resp,
		"body": body,
		"errs": errs,
	}, "HTTP响应")
	return []byte(body), nil
}

// Get 全局GET方法
func Get(reqUrl string, params map[string]string) ([]byte, error) {
	client := NewHTTPClient()
	return client.Get(reqUrl, params)
}

// PostForm 全局POST表单方法
func PostForm(reqUrl string, requestBody map[string]string) ([]byte, error) {
	client := NewHTTPClient()
	return client.PostForm(reqUrl, requestBody)
}

// PostJSON 全局POST JSON方法
func PostJSON(reqUrl string, requestBody interface{}) ([]byte, error) {
	client := NewHTTPClient()
	return client.PostJSON(reqUrl, requestBody)
}

// concatenateURLParams 拼接URL参数
func concatenateURLParams(params map[string]string) string {
	var queryString []string

	for k, v := range params {
		// URL编码键和值，确保特殊字符被正确处理
		encodedKey := url.QueryEscape(k)
		encodedValue := url.QueryEscape(v)
		// 拼接成 "key=value" 形式的字符串并添加到结果列表中
		queryString = append(queryString, fmt.Sprintf("%s=%s", encodedKey, encodedValue))
	}

	// 使用 "&" 连接所有参数对
	return strings.Join(queryString, "&")
}
