package werror

import (
	"encoding/json"
	"fmt"
	"time"
)

// ErrorCode 错误码类型
type ErrorCode int

// ErrorLevel 错误级别
type ErrorLevel string

const (
	LevelInfo    ErrorLevel = "info"    // 信息级别
	LevelWarning ErrorLevel = "warning" // 警告级别
	LevelError   ErrorLevel = "error"   // 错误级别
	LevelFatal   ErrorLevel = "fatal"   // 致命错误级别
)

// WError 自定义错误类型
type WError struct {
	Code      ErrorCode              `json:"code"`      // 错误码
	Message   string                 `json:"message"`   // 错误信息
	Data      interface{}            `json:"data"`      // 响应数据
	Context   map[string]interface{} `json:"context"`   // 上下文数据（用于日志记录）
	Level     ErrorLevel             `json:"level"`     // 错误级别
	Cause     error                  `json:"-"`         // 原始错误（不序列化）
	Timestamp int64                  `json:"timestamp"` // 错误时间戳
}

// New 创建新的错误（支持多种参数形式）
func New(args ...interface{}) *WError {
	// 默认值
	var code ErrorCode = 500
	var message string = "未知错误"
	var context map[string]interface{}

	// 根据参数数量和类型进行处理
	switch len(args) {
	case 1:
		// New(message)
		if msg, ok := args[0].(string); ok {
			message = msg
		}
	case 2:
		// New(code, message)
		if c, ok := args[0].(int); ok {
			code = ErrorCode(c)
		} else if c, ok := args[0].(ErrorCode); ok {
			code = c
		}
		if msg, ok := args[1].(string); ok {
			message = msg
		}
	case 3:
		// New(code, message, context) - 向后兼容
		if c, ok := args[0].(int); ok {
			code = ErrorCode(c)
		} else if c, ok := args[0].(ErrorCode); ok {
			code = c
		}
		if msg, ok := args[1].(string); ok {
			message = msg
		}
		if ctx, ok := args[2].(map[string]interface{}); ok {
			context = ctx
		}
	}

	if context == nil {
		context = make(map[string]interface{})
	}

	return &WError{
		Code:      code,
		Message:   message,
		Data:      nil,
		Context:   context,
		Level:     LevelError,
		Timestamp: getCurrentTimestamp(),
	}
}

// NewLegacy 向后兼容的错误创建方法（支持旧的API）
func NewLegacy(code int, message string, context map[string]interface{}) *WError {
	if context == nil {
		context = make(map[string]interface{})
	}
	return &WError{
		Code:      ErrorCode(code),
		Message:   message,
		Data:      nil,
		Context:   context,
		Level:     LevelError,
		Timestamp: getCurrentTimestamp(),
	}
}

// NewWithData 创建带数据的错误
func NewWithData(code ErrorCode, message string, data interface{}) *WError {
	return &WError{
		Code:      code,
		Message:   message,
		Data:      data,
		Context:   make(map[string]interface{}),
		Level:     LevelError,
		Timestamp: getCurrentTimestamp(),
	}
}

// NewWithContext 创建带上下文的错误
func NewWithContext(code ErrorCode, message string, context map[string]interface{}) *WError {
	if context == nil {
		context = make(map[string]interface{})
	}
	return &WError{
		Code:      code,
		Message:   message,
		Data:      nil,
		Context:   context,
		Level:     LevelError,
		Timestamp: getCurrentTimestamp(),
	}
}

// NewFull 创建完整的错误
func NewFull(code ErrorCode, message string, data interface{}, context map[string]interface{}, level ErrorLevel) *WError {
	if context == nil {
		context = make(map[string]interface{})
	}
	return &WError{
		Code:      code,
		Message:   message,
		Data:      data,
		Context:   context,
		Level:     level,
		Timestamp: getCurrentTimestamp(),
	}
}

// Wrap 包装原始错误
func Wrap(err error, code ErrorCode, message string) *WError {
	return &WError{
		Code:      code,
		Message:   message,
		Data:      nil,
		Context:   make(map[string]interface{}),
		Level:     LevelError,
		Cause:     err,
		Timestamp: getCurrentTimestamp(),
	}
}

// WrapWithContext 包装原始错误并添加上下文
func WrapWithContext(err error, code ErrorCode, message string, context map[string]interface{}) *WError {
	if context == nil {
		context = make(map[string]interface{})
	}
	return &WError{
		Code:      code,
		Message:   message,
		Data:      nil,
		Context:   context,
		Level:     LevelError,
		Cause:     err,
		Timestamp: getCurrentTimestamp(),
	}
}

// 链式方法

// WithData 设置响应数据
func (e *WError) WithData(data interface{}) *WError {
	e.Data = data
	return e
}

// WithContext 添加上下文数据
func (e *WError) WithContext(key string, value interface{}) *WError {
	if e.Context == nil {
		e.Context = make(map[string]interface{})
	}
	e.Context[key] = value
	return e
}

// WithContextMap 批量添加上下文数据
func (e *WError) WithContextMap(context map[string]interface{}) *WError {
	if e.Context == nil {
		e.Context = make(map[string]interface{})
	}
	for k, v := range context {
		e.Context[k] = v
	}
	return e
}

// WithLevel 设置错误级别
func (e *WError) WithLevel(level ErrorLevel) *WError {
	e.Level = level
	return e
}

// WithCause 设置原始错误
func (e *WError) WithCause(err error) *WError {
	e.Cause = err
	return e
}

// 实现 error 接口
func (e *WError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("[%d] %s: %v", e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("[%d] %s", e.Code, e.Message)
}

// GetCode 获取错误码
func (e *WError) GetCode() ErrorCode {
	return e.Code
}

// GetMessage 获取错误信息
func (e *WError) GetMessage() string {
	return e.Message
}

// GetData 获取响应数据
func (e *WError) GetData() interface{} {
	return e.Data
}

// GetContext 获取上下文数据
func (e *WError) GetContext() map[string]interface{} {
	return e.Context
}

// GetLevel 获取错误级别
func (e *WError) GetLevel() ErrorLevel {
	return e.Level
}

// GetCause 获取原始错误
func (e *WError) GetCause() error {
	return e.Cause
}

// IsLevel 检查错误级别
func (e *WError) IsLevel(level ErrorLevel) bool {
	return e.Level == level
}

// ToJSON 转换为JSON字符串（用于日志记录）
func (e *WError) ToJSON() string {
	data := map[string]interface{}{
		"code":      e.Code,
		"message":   e.Message,
		"data":      e.Data,
		"context":   e.Context,
		"level":     e.Level,
		"timestamp": e.Timestamp,
	}
	if e.Cause != nil {
		data["cause"] = e.Cause.Error()
	}
	jsonData, _ := json.Marshal(data)
	return string(jsonData)
}

// 便捷方法

// IsWError 检查是否为WError类型
func IsWError(err error) bool {
	_, ok := err.(*WError)
	return ok
}

// AsWError 转换为WError类型
func AsWError(err error) (*WError, bool) {
	werr, ok := err.(*WError)
	return werr, ok
}

// FromError 从标准错误创建WError
func FromError(err error) *WError {
	if werr, ok := AsWError(err); ok {
		return werr
	}
	return Wrap(err, 500, err.Error())
}

// 工具函数

// getCurrentTimestamp 获取当前时间戳（毫秒）
func getCurrentTimestamp() int64 {
	return time.Now().UnixMilli()
}
