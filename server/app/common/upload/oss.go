package upload

import (
	"fmt"
	"mime/multipart"
	"path"
	"strings"
	"time"
	"wnsys/shop/app/config"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/google/uuid"
)

// OSSUploadService 阿里云OSS上传服务
type OSSUploadService struct {
	client *oss.Client
	bucket *oss.Bucket
}

// NewOSSUploadService 创建OSS上传服务
func NewOSSUploadService() (*OSSUploadService, error) {
	cfg := config.App.Aliyun.OSS

	// 创建OSS客户端
	client, err := oss.New(cfg.Endpoint, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		return nil, fmt.Errorf("创建OSS客户端失败: %v", err)
	}

	// 获取存储空间
	bucket, err := client.Bucket(cfg.BucketName)
	if err != nil {
		return nil, fmt.Errorf("获取OSS存储空间失败: %v", err)
	}

	return &OSSUploadService{
		client: client,
		bucket: bucket,
	}, nil
}

// UploadImage 上传图片到OSS
func (s *OSSUploadService) UploadImage(file *multipart.FileHeader) (string, error) {
	// 检查文件大小（限制为5MB）
	if file.Size > 5<<20 {
		return "", fmt.Errorf("文件大小不能超过5MB")
	}

	// 检查文件格式
	ext := strings.ToLower(path.Ext(file.Filename))
	if !imageExts[ext] {
		return "", fmt.Errorf("不支持的图片格式")
	}

	// 生成文件名
	fileName := fmt.Sprintf("%s%s", uuid.New().String(), ext)

	// 按年月日生成目录
	now := time.Now()
	objectKey := fmt.Sprintf("images/%d/%02d/%02d/%s", now.Year(), now.Month(), now.Day(), fileName)

	// 打开文件
	src, err := file.Open()
	if err != nil {
		return "", fmt.Errorf("打开文件失败: %v", err)
	}
	defer src.Close()

	// 上传到OSS
	err = s.bucket.PutObject(objectKey, src)
	if err != nil {
		return "", fmt.Errorf("上传到OSS失败: %v", err)
	}

	// 返回完整的访问URL
	cfg := config.App.Aliyun.OSS
	if cfg.Domain != "" {
		// 使用自定义域名
		return fmt.Sprintf("%s/%s", strings.TrimRight(cfg.Domain, "/"), objectKey), nil
	} else {
		// 使用默认域名
		return fmt.Sprintf("https://%s.%s/%s", cfg.BucketName, cfg.Endpoint, objectKey), nil
	}
}

// UploadImageToOSS 全局函数，用于替代原来的UploadImage
func UploadImageToOSS(file *multipart.FileHeader) (string, error) {
	service, err := NewOSSUploadService()
	if err != nil {
		// 如果OSS服务初始化失败，回退到本地上传
		return UploadImage(file)
	}

	return service.UploadImage(file)
}
