package upload

import (
	"fmt"
	"mime/multipart"
	"os"
	"path"
	"strings"
	"time"

	"github.com/google/uuid"
)

// 支持的图片格式
var imageExts = map[string]bool{
	".jpg":  true,
	".jpeg": true,
	".png":  true,
	".gif":  true,
	".bmp":  true,
	".webp": true,
}

// UploadImage 上传图片
func UploadImage(file *multipart.FileHeader) (string, error) {
	// 检查文件大小（限制为5MB）
	if file.Size > 5<<20 {
		return "", fmt.Errorf("文件大小不能超过5MB")
	}

	// 检查文件格式
	ext := strings.ToLower(path.Ext(file.Filename))
	if !imageExts[ext] {
		return "", fmt.Errorf("不支持的图片格式")
	}

	// 生成文件名
	fileName := fmt.Sprintf("%s%s", uuid.New().String(), ext)

	// 按年月日生成目录
	now := time.Now()
	dir := fmt.Sprintf("uploads/images/%d/%02d/%02d", now.Year(), now.Month(), now.Day())

	// 创建目录
	if err := os.MkdirAll(dir, 0755); err != nil {
		return "", fmt.Errorf("创建目录失败: %v", err)
	}

	// 完整的文件路径
	filePath := path.Join(dir, fileName)

	// 保存文件
	if err := os.MkdirAll(path.Dir(filePath), 0755); err != nil {
		return "", fmt.Errorf("创建目录失败: %v", err)
	}

	src, err := file.Open()
	if err != nil {
		return "", fmt.Errorf("打开文件失败: %v", err)
	}
	defer src.Close()

	dst, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("创建文件失败: %v", err)
	}
	defer dst.Close()

	// 读取文件内容并写入
	buffer := make([]byte, 1024*1024) // 1MB buffer
	for {
		n, err := src.Read(buffer)
		if err != nil {
			break
		}
		if _, err := dst.Write(buffer[:n]); err != nil {
			return "", fmt.Errorf("写入文件失败: %v", err)
		}
	}

	// 返回文件的相对路径
	return "/" + filePath, nil
}
