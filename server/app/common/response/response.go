package response

import (
	"net/http"
	"time"
	"wnsys/shop/app/common/werror"
	"wnsys/shop/app/provider/mylog"

	"github.com/gin-gonic/gin"
)

// 向后兼容的常量
const (
	ERROR   = 500
	SUCCESS = 200
)

// Response 统一响应结构
type Response struct {
	Code      int         `json:"code"`      // 响应码
	Message   string      `json:"message"`   // 响应消息
	Data      interface{} `json:"data"`      // 响应数据
	Timestamp int64       `json:"timestamp"` // 响应时间戳
}

// ResponseBuilder 响应构建器
type ResponseBuilder struct {
	code      int
	message   string
	data      interface{}
	timestamp int64
}

// NewResponse 创建新的响应构建器
func NewResponse() *ResponseBuilder {
	return &ResponseBuilder{
		code:      200,
		message:   "success",
		data:      nil,
		timestamp: getCurrentTimestamp(),
	}
}

// WithCode 设置响应码
func (r *ResponseBuilder) WithCode(code int) *ResponseBuilder {
	r.code = code
	return r
}

// WithMessage 设置响应消息
func (r *ResponseBuilder) WithMessage(message string) *ResponseBuilder {
	r.message = message
	return r
}

// WithData 设置响应数据
func (r *ResponseBuilder) WithData(data interface{}) *ResponseBuilder {
	r.data = data
	return r
}

// Build 构建响应对象
func (r *ResponseBuilder) Build() *Response {
	return &Response{
		Code:      r.code,
		Message:   r.message,
		Data:      r.data,
		Timestamp: r.timestamp,
	}
}

// Send 发送响应
func (r *ResponseBuilder) Send(c *gin.Context) {
	response := r.Build()
	c.JSON(http.StatusOK, response)
}

// SendWithStatus 发送带HTTP状态码的响应
func (r *ResponseBuilder) SendWithStatus(c *gin.Context, httpStatus int) {
	response := r.Build()
	c.JSON(httpStatus, response)
}

// 便捷方法

// Success 成功响应
func Success(c *gin.Context) {
	NewResponse().Send(c)
}

// SuccessWithMessage 带消息的成功响应
func SuccessWithMessage(message string, c *gin.Context) {
	NewResponse().WithMessage(message).Send(c)
}

// SuccessWithData 带数据的成功响应
func SuccessWithData(data interface{}, c *gin.Context) {
	NewResponse().WithData(data).Send(c)
}

// SuccessWithDetailed 详细成功响应
func SuccessWithDetailed(data interface{}, message string, c *gin.Context) {
	NewResponse().WithData(data).WithMessage(message).Send(c)
}

// Error 错误响应
func Error(code int, message string, c *gin.Context) {
	NewResponse().WithCode(code).WithMessage(message).Send(c)
}

// ErrorWithData 带数据的错误响应
func ErrorWithData(code int, message string, data interface{}, c *gin.Context) {
	NewResponse().WithCode(code).WithMessage(message).WithData(data).Send(c)
}

// ErrorWithWError 使用WError的错误响应
func ErrorWithWError(err *werror.WError, c *gin.Context) {
	// 记录错误日志
	if err.GetLevel() == werror.LevelError || err.GetLevel() == werror.LevelFatal {
		mylog.GetLogger("error").Error(c, err.GetMessage(), err.GetContext())
	} else if err.GetLevel() == werror.LevelWarning {
		mylog.GetLogger("error").Warn(c, err.GetMessage(), err.GetContext())
	} else {
		mylog.GetLogger("error").Info(c, err.GetMessage(), err.GetContext())
	}

	// 发送响应
	NewResponse().
		WithCode(int(err.GetCode())).
		WithMessage(err.GetMessage()).
		WithData(err.GetData()).
		Send(c)
}

// ErrorWithError 处理标准错误
func ErrorWithError(err error, c *gin.Context) {
	if werr, ok := werror.AsWError(err); ok {
		ErrorWithWError(werr, c)
		return
	}

	// 处理标准错误
	mylog.GetLogger("error").Error(c, err.Error(), nil)
	NewResponse().WithCode(500).WithMessage(err.Error()).Send(c)
}

// 向后兼容的方法（保持原有API）

// 工具函数

// getCurrentTimestamp 获取当前时间戳
func getCurrentTimestamp() int64 {
	return time.Now().UnixMilli()
}
