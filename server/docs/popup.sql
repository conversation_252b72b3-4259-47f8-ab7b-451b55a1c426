/*
 Navicat Premium Dump SQL

 Source Server         : mysql80
 Source Server Type    : MySQL
 Source Server Version : 80042 (8.0.42)
 Source Host           : localhost:3307
 Source Schema         : wnsys

 Target Server Type    : MySQL
 Target Server Version : 80042 (8.0.42)
 File Encoding         : 65001

 Date: 18/06/2025 11:38:48
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for popup
-- ----------------------------
DROP TABLE IF EXISTS `popup`;
CREATE TABLE `popup` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '弹窗ID',
  `title` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '弹窗标题',
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '弹窗内容',
  `title_style` json DEFAULT NULL COMMENT '标题样式JSON',
  `buttons` json DEFAULT NULL COMMENT '按钮配置JSON',
  `trigger_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'manual' COMMENT '触发类型：manual/auto/schedule',
  `trigger_rule` json DEFAULT NULL COMMENT '触发规则JSON',
  `priority` int NOT NULL DEFAULT '0' COMMENT '优先级',
  `auto_close` int NOT NULL DEFAULT '0' COMMENT '自动关闭时间(毫秒)',
  `delay` int NOT NULL DEFAULT '0' COMMENT '延迟显示时间(毫秒)',
  `mask_closable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '点击遮罩是否关闭',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用 1=启用',
  `show_count` bigint NOT NULL DEFAULT '0' COMMENT '显示次数',
  `click_count` bigint NOT NULL DEFAULT '0' COMMENT '点击次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0=否 1=是',
  PRIMARY KEY (`id`),
  KEY `idx_status_time` (`status`,`start_time`,`end_time`),
  KEY `idx_trigger_type` (`trigger_type`),
  KEY `idx_priority` (`priority`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='弹窗表';

-- ----------------------------
-- Records of popup
-- ----------------------------
BEGIN;
INSERT INTO `popup` (`id`, `title`, `content`, `title_style`, `buttons`, `trigger_type`, `trigger_rule`, `priority`, `auto_close`, `delay`, `mask_closable`, `start_time`, `end_time`, `status`, `show_count`, `click_count`, `create_time`, `update_time`, `is_delete`) VALUES (1, '欢迎使用wn商城', '感谢您使用我们的应用，祝您购物愉快！', '{\"color\": \"#fff\", \"fontSize\": \"16px\", \"textAlign\": \"left\", \"background\": \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\", \"fontWeight\": \"normal\", \"backgroundSize\": \"cover\", \"backgroundColor\": \"rgba(214, 12, 12, 1)\", \"backgroundImage\": \"\"}', '[{\"text\": \"开始购物\", \"type\": \"primary\"}]', 'manual', '{\"pages\": [], \"userType\": \"\", \"frequency\": \"\", \"scheduleTime\": \"22:00\", \"scheduleType\": \"once\"}', 10, 0, 1000, 1, '2025-06-18 00:00:00', '2025-06-25 00:00:00', 1, 7, 4, '2025-06-18 00:49:33', '2025-06-18 03:37:53', 0);
INSERT INTO `popup` (`id`, `title`, `content`, `title_style`, `buttons`, `trigger_type`, `trigger_rule`, `priority`, `auto_close`, `delay`, `mask_closable`, `start_time`, `end_time`, `status`, `show_count`, `click_count`, `create_time`, `update_time`, `is_delete`) VALUES (2, '限时优惠活动', '全场商品8折起，满100元立减20元，活动有限期！', '{\"color\": \"#fff\", \"background\": \"#ff4757\"}', '[{\"text\": \"立即查看\", \"type\": \"primary\"}, {\"text\": \"稍后再说\", \"type\": \"default\"}]', 'auto', '{\"pages\": [\"/pages/index/index\"], \"userTypes\": [\"new\"], \"maxShowTimes\": 3}', 8, 10000, 2000, 1, NULL, NULL, 1, 3, 0, '2025-06-18 00:49:33', '2025-06-18 03:19:12', 0);
INSERT INTO `popup` (`id`, `title`, `content`, `title_style`, `buttons`, `trigger_type`, `trigger_rule`, `priority`, `auto_close`, `delay`, `mask_closable`, `start_time`, `end_time`, `status`, `show_count`, `click_count`, `create_time`, `update_time`, `is_delete`) VALUES (3, '系统维护通知', '系统将于今晚23:00-01:00进行维护升级，期间服务可能暂时中断。', '{\"color\": \"#fff\", \"fontSize\": \"16px\", \"textAlign\": \"center\", \"background\": \"#ffa502\", \"fontWeight\": \"normal\", \"backgroundSize\": \"cover\", \"backgroundColor\": \"rgba(182, 62, 62, 1)\", \"backgroundImage\": \"https://blog-page.oss-cn-hangzhou.aliyuncs.com/uploads/2025/06/10/12ecf8e6-0c6e-4b7a-92a0-a11bfe72056d.png\"}', '[{\"text\": \"我知道了\", \"type\": \"warning\"}]', 'schedule', '{\"pages\": [], \"userType\": \"\", \"frequency\": \"\", \"scheduleTime\": \"22:00\", \"scheduleType\": \"once\"}', 15, 0, 0, 0, '2025-06-17 00:00:00', '2025-06-26 00:00:00', 1, 7, 7, '2025-06-18 00:49:33', '2025-06-18 03:37:52', 0);
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
