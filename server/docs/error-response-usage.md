# 错误类和响应类使用文档

## 概述

本项目重构了错误类（WError）和响应类（Response），使其更加通用和抽象。错误码和消息在使用时动态定义，提供了更大的灵活性。

## 错误类（WError）使用

### 1. 基本错误创建

```go
import "wnsys/shop/app/common/werror"

// 创建基本错误
err := werror.New(400, "参数错误")

// 创建带数据的错误
err := werror.NewWithData(404, "用户不存在", map[string]interface{}{
    "userId": 123,
})

// 创建带上下文的错误
err := werror.NewWithContext(500, "数据库连接失败", map[string]interface{}{
    "database": "mysql",
    "host": "localhost",
})

// 创建完整错误
err := werror.NewFull(
    1001,                    // 错误码
    "业务处理失败",            // 错误消息
    map[string]interface{}{  // 响应数据
        "retryAfter": 30,
    },
    map[string]interface{}{  // 上下文数据（用于日志）
        "userId": 123,
        "operation": "createOrder",
    },
    werror.LevelWarning,     // 错误级别
)
```

### 2. 包装标准错误

```go
// 包装标准错误
dbErr := errors.New("connection timeout")
err := werror.Wrap(dbErr, 500, "数据库操作失败")

// 包装错误并添加上下文
err := werror.WrapWithContext(dbErr, 500, "数据库操作失败", map[string]interface{}{
    "table": "users",
    "operation": "select",
})
```

### 3. 链式调用

```go
err := werror.New(400, "参数验证失败").
    WithData(map[string]interface{}{
        "field": "email",
        "value": "invalid-email",
    }).
    WithContext("userId", 123).
    WithContext("ip", "***********").
    WithLevel(werror.LevelWarning)
```

### 4. 错误级别

```go
// 不同级别的错误
infoErr := werror.New(200, "操作成功").WithLevel(werror.LevelInfo)
warnErr := werror.New(400, "参数可能有问题").WithLevel(werror.LevelWarning)
errorErr := werror.New(500, "系统错误").WithLevel(werror.LevelError)
fatalErr := werror.New(500, "系统崩溃").WithLevel(werror.LevelFatal)

// 检查错误级别
if err.IsLevel(werror.LevelError) {
    // 处理错误级别的错误
}
```

## 响应类（Response）使用

### 1. 成功响应

```go
import "wnsys/shop/app/common/response"

// 基本成功响应
response.Success(c)

// 带消息的成功响应
response.SuccessWithMessage("操作成功", c)

// 带数据的成功响应
response.SuccessWithData(userData, c)

// 详细成功响应
response.SuccessWithDetailed(userData, "用户信息获取成功", c)
```

### 2. 错误响应

```go
// 基本错误响应
response.Error(400, "参数错误", c)

// 带数据的错误响应
response.ErrorWithData(400, "验证失败", map[string]interface{}{
    "errors": []string{"邮箱格式错误", "密码长度不足"},
}, c)

// 使用WError的错误响应
err := werror.New(404, "用户不存在").WithData(map[string]interface{}{
    "userId": 123,
})
response.ErrorWithWError(err, c)

// 处理标准错误
if err != nil {
    response.ErrorWithError(err, c)
    return
}
```

### 3. 响应构建器

```go
// 使用构建器模式
response.NewResponse().
    WithCode(201).
    WithMessage("创建成功").
    WithData(newUser).
    Send(c)

// 发送带HTTP状态码的响应
response.NewResponse().
    WithCode(400).
    WithMessage("请求格式错误").
    SendWithStatus(c, http.StatusBadRequest)
```

## 业务代码示例

### 1. 用户服务示例

```go
package user

import (
    "wnsys/shop/app/common/werror"
    "wnsys/shop/app/common/response"
)

// 用户登录
func (s *Service) Login(req *dto.LoginReq) (*dto.LoginResp, error) {
    // 参数验证
    if req.Username == "" {
        return nil, werror.New(400, "用户名不能为空").
            WithContext("field", "username")
    }
    
    // 查询用户
    user, err := s.getUserByUsername(req.Username)
    if err != nil {
        return nil, werror.Wrap(err, 500, "查询用户失败").
            WithContext("username", req.Username)
    }
    
    if user == nil {
        return nil, werror.New(404, "用户不存在").
            WithContext("username", req.Username).
            WithLevel(werror.LevelWarning)
    }
    
    // 验证密码
    if !s.verifyPassword(req.Password, user.Password) {
        return nil, werror.New(401, "密码错误").
            WithContext("userId", user.ID).
            WithLevel(werror.LevelWarning)
    }
    
    // 生成token
    token, err := s.generateToken(user.ID)
    if err != nil {
        return nil, werror.Wrap(err, 500, "生成token失败").
            WithContext("userId", user.ID)
    }
    
    return &dto.LoginResp{
        Token: token,
        User:  user,
    }, nil
}

// 控制器中的使用
func (ctrl *UserController) Login(c *gin.Context) {
    var req dto.LoginReq
    if err := c.ShouldBindJSON(&req); err != nil {
        response.ErrorWithError(err, c)
        return
    }
    
    resp, err := ctrl.userService.Login(&req)
    if err != nil {
        response.ErrorWithError(err, c)
        return
    }
    
    response.SuccessWithData(resp, c)
}
```

### 2. 商品服务示例

```go
// 获取商品详情
func (s *Service) GetGoodsDetail(goodsId int64) (*vo.GoodsDetailVO, error) {
    // 查询商品
    goods, err := s.getGoodsById(goodsId)
    if err != nil {
        return nil, werror.Wrap(err, 500, "查询商品失败").
            WithContext("goodsId", goodsId)
    }
    
    if goods == nil {
        return nil, werror.New(404, "商品不存在").
            WithData(map[string]interface{}{
                "goodsId": goodsId,
            }).
            WithContext("goodsId", goodsId)
    }
    
    // 检查商品状态
    if goods.Status == 0 {
        return nil, werror.New(400, "商品已下架").
            WithData(map[string]interface{}{
                "goodsId": goodsId,
                "status": goods.Status,
            }).
            WithContext("goodsId", goodsId).
            WithLevel(werror.LevelWarning)
    }
    
    return s.buildGoodsDetailVO(goods), nil
}
```

### 3. 短信服务示例

```go
// 发送短信验证码
func (s *Service) SendSmsCode(phone string) error {
    // 验证手机号
    if !isValidPhone(phone) {
        return werror.New(400, "手机号格式不正确").
            WithContext("phone", phone)
    }
    
    // 检查发送频率
    if s.isRateLimited(phone) {
        return werror.New(429, "发送过于频繁，请稍后再试").
            WithData(map[string]interface{}{
                "retryAfter": 60,
            }).
            WithContext("phone", phone).
            WithLevel(werror.LevelWarning)
    }
    
    // 调用阿里云短信服务
    code := generateCode()
    err := s.aliyunSms.SendSmsCode(phone, code)
    if err != nil {
        return werror.Wrap(err, 500, "短信发送失败").
            WithContext("phone", phone).
            WithContext("provider", "aliyun")
    }
    
    // 保存验证码
    err = s.saveVerifyCode(phone, code)
    if err != nil {
        return werror.Wrap(err, 500, "保存验证码失败").
            WithContext("phone", phone)
    }
    
    return nil
}
```

## 错误码规范建议

虽然错误码在使用时定义，但建议遵循一定的规范：

```go
// 系统级错误码 (1000-1999)
const (
    CodeInternalError     = 1000  // 内部错误
    CodeInvalidParams     = 1001  // 参数错误
    CodeUnauthorized      = 1002  // 未授权
    CodeForbidden         = 1003  // 禁止访问
    CodeNotFound          = 1004  // 资源不存在
    CodeMethodNotAllowed  = 1005  // 方法不允许
    CodeTooManyRequests   = 1006  // 请求过于频繁
)

// 用户相关错误码 (2000-2999)
const (
    CodeUserNotFound      = 2001  // 用户不存在
    CodeUserExists        = 2002  // 用户已存在
    CodePasswordError     = 2003  // 密码错误
    CodeTokenInvalid      = 2004  // Token无效
)

// 商品相关错误码 (3000-3999)
const (
    CodeGoodsNotFound     = 3001  // 商品不存在
    CodeGoodsOutOfStock   = 3002  // 商品库存不足
    CodeGoodsOffline      = 3003  // 商品已下架
)
```

## 最佳实践

1. **错误码分类**: 按业务模块分配错误码范围
2. **错误级别**: 合理使用错误级别，便于日志分析
3. **上下文数据**: 添加有助于调试的上下文信息
4. **响应数据**: 为前端提供有用的错误数据
5. **日志记录**: 错误会自动记录日志，包含上下文信息
6. **向后兼容**: 保持原有API的兼容性

## 响应格式

重构后的响应格式：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": 123,
    "username": "admin"
  },
  "timestamp": 1640995200000
}
```

错误响应格式：

```json
{
  "code": 404,
  "message": "用户不存在",
  "data": {
    "userId": 123
  },
  "timestamp": 1640995200000
}
``` 