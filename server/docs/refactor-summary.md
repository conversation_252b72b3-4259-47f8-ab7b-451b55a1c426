# 错误类和响应类重构总结

## 重构概述

本次重构对项目中的错误处理和响应处理进行了全面优化，使其更加通用、抽象和灵活。主要重构了以下两个核心组件：

1. **错误类（WError）** - `server/app/common/werror/error.go`
2. **响应类（Response）** - `server/app/common/response/response.go`
3. **HTTP客户端** - `server/app/common/myhttp/client.go`

## 主要改进

### 1. 错误类（WError）改进

#### 移除预定义错误码
- **之前**: 错误码在类中预定义，限制了灵活性
- **现在**: 错误码在使用时动态定义，提供更大的灵活性

#### 增强的错误信息
```go
type WError struct {
    Code      ErrorCode              // 错误码
    Message   string                 // 错误信息
    Data      interface{}            // 响应数据（返回给前端）
    Context   map[string]interface{} // 上下文数据（用于日志记录）
    Level     ErrorLevel             // 错误级别
    Cause     error                  // 原始错误
    Timestamp int64                  // 错误时间戳
}
```

#### 多种创建方式
```go
// 基本创建
err := werror.New(404, "用户不存在")

// 带数据创建
err := werror.NewWithData(400, "参数错误", validationErrors)

// 带上下文创建
err := werror.NewWithContext(500, "数据库错误", map[string]interface{}{
    "table": "users",
    "operation": "select",
})

// 完整创建
err := werror.NewFull(code, message, data, context, level)

// 包装标准错误
err := werror.Wrap(dbErr, 500, "数据库操作失败")
```

#### 链式调用支持
```go
err := werror.New(400, "参数验证失败").
    WithData(validationErrors).
    WithContext("userId", 123).
    WithLevel(werror.LevelWarning)
```

#### 错误级别
- `LevelInfo` - 信息级别
- `LevelWarning` - 警告级别  
- `LevelError` - 错误级别
- `LevelFatal` - 致命错误级别

#### 向后兼容
保持了原有API的兼容性，支持旧的调用方式：
```go
// 旧的调用方式仍然有效
err := werror.New(500, "错误信息", map[string]interface{}{"key": "value"})
```

### 2. 响应类（Response）改进

#### 增强的响应结构
```go
type Response struct {
    Code      int         // 响应码
    Message   string      // 响应消息
    Data      interface{} // 响应数据
    Timestamp int64       // 响应时间戳
}
```

#### 构建器模式
```go
// 使用构建器模式
response.NewResponse().
    WithCode(201).
    WithMessage("创建成功").
    WithData(newUser).
    Send(c)
```

#### 智能错误处理
```go
// 自动处理WError
response.ErrorWithWError(werr, c)

// 自动处理标准错误
response.ErrorWithError(err, c)
```

#### 自动日志记录
根据错误级别自动记录相应的日志：
- `LevelError/LevelFatal` → Error日志
- `LevelWarning` → Warning日志
- `LevelInfo` → Info日志

#### 向后兼容
保持了所有原有的API方法：
```go
// 原有方法仍然有效
response.Ok(c)
response.OkWithData(data, c)
response.FailWithMessage(code, message, c)
```

### 3. HTTP客户端改进

#### 面向对象设计
```go
// 创建客户端实例
client := myhttp.NewHTTPClient().
    SetTimeout(30 * time.Second).
    SetHeader("Authorization", "Bearer token")

// 发送请求
body, err := client.Get(url, params)
```

#### 多种请求方式
- `Get()` - GET请求
- `PostForm()` - POST表单请求
- `PostJSON()` - POST JSON请求

#### 灵活配置
- 自定义超时时间
- 自定义请求头
- 链式调用

#### 完整日志记录
自动记录请求和响应的详细日志

## 使用示例

### 1. 业务服务中的错误处理

```go
func (s *UserService) Login(req *LoginReq) (*LoginResp, error) {
    // 参数验证
    if req.Username == "" {
        return nil, werror.New(400, "用户名不能为空").
            WithContext("field", "username")
    }
    
    // 查询用户
    user, err := s.getUserByUsername(req.Username)
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, werror.New(404, "用户不存在").
                WithContext("username", req.Username).
                WithLevel(werror.LevelWarning)
        }
        return nil, werror.Wrap(err, 500, "查询用户失败").
            WithContext("username", req.Username)
    }
    
    // 验证密码
    if !s.verifyPassword(req.Password, user.Password) {
        return nil, werror.New(401, "密码错误").
            WithContext("userId", user.ID).
            WithLevel(werror.LevelWarning)
    }
    
    return &LoginResp{Token: token, User: user}, nil
}
```

### 2. 控制器中的响应处理

```go
func (ctrl *UserController) Login(c *gin.Context) {
    var req LoginReq
    if err := c.ShouldBindJSON(&req); err != nil {
        response.ErrorWithError(err, c)
        return
    }
    
    resp, err := ctrl.userService.Login(&req)
    if err != nil {
        response.ErrorWithError(err, c) // 自动处理WError和标准错误
        return
    }
    
    response.SuccessWithData(resp, c)
}
```

### 3. HTTP客户端使用

```go
// 阿里云短信服务
type AliyunSmsService struct {
    httpClient *myhttp.HTTPClient
}

func NewAliyunSmsService() *AliyunSmsService {
    client := myhttp.NewHTTPClient().SetTimeout(10 * time.Second)
    return &AliyunSmsService{httpClient: client}
}

func (s *AliyunSmsService) SendSms(params map[string]string) error {
    body, err := s.httpClient.Get("https://dysmsapi.aliyuncs.com/", params)
    if err != nil {
        return werror.Wrap(err, 500, "短信发送失败")
    }
    // 处理响应...
    return nil
}
```

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": 123,
    "username": "admin"
  },
  "timestamp": 1640995200000
}
```

### 错误响应
```json
{
  "code": 404,
  "message": "用户不存在",
  "data": {
    "userId": 123
  },
  "timestamp": 1640995200000
}
```

## 最佳实践

### 1. 错误码规范
建议按业务模块分配错误码范围：
- 系统级错误码: 1000-1999
- 用户相关: 2000-2999
- 商品相关: 3000-3999
- 订单相关: 4000-4999
- 第三方服务: 5000-5999

### 2. 错误级别使用
- `LevelInfo`: 正常的业务信息
- `LevelWarning`: 业务异常但不影响系统运行
- `LevelError`: 系统错误，需要关注
- `LevelFatal`: 致命错误，需要立即处理

### 3. 上下文数据
添加有助于调试和监控的上下文信息：
```go
err := werror.New(500, "数据库连接失败").
    WithContext("database", "mysql").
    WithContext("host", "localhost").
    WithContext("userId", userId)
```

### 4. 响应数据
为前端提供有用的错误数据：
```go
err := werror.New(400, "验证失败").
    WithData(map[string]interface{}{
        "errors": validationErrors,
        "retryAfter": 30,
    })
```

## 兼容性

本次重构完全保持了向后兼容性：
- 所有原有的API调用方式仍然有效
- 原有的常量（`response.ERROR`、`response.SUCCESS`）仍然可用
- 原有的错误创建方式仍然支持

## 总结

通过本次重构，我们实现了：

1. **更高的灵活性** - 错误码和消息在使用时定义
2. **更好的抽象** - 分离了业务逻辑和错误处理
3. **更强的功能** - 支持错误级别、上下文数据、响应数据
4. **更好的日志** - 自动记录结构化日志
5. **更易维护** - 统一的错误处理和响应格式
6. **完全兼容** - 保持原有API的兼容性

这些改进使得错误处理更加规范化、标准化，同时提供了更好的开发体验和调试能力。 