# HTTP客户端使用文档

## 概述

本项目封装了一个功能完善的HTTP客户端，支持GET、POST表单、POST JSON等多种请求方式，并提供了灵活的配置选项。

## 功能特性

### 1. 支持多种请求方式
- GET请求
- POST表单请求
- POST JSON请求

### 2. 灵活的配置
- 自定义超时时间
- 自定义请求头
- 链式调用

### 3. 完整的日志记录
- 请求日志
- 响应日志
- 错误日志

### 4. 向后兼容
- 保持原有全局方法的兼容性

## 使用方法

### 1. 创建HTTP客户端实例

```go
import "wnsys/shop/app/common/myhttp"

// 创建默认客户端
client := myhttp.NewHTTPClient()

// 链式配置
client := myhttp.NewHTTPClient().
    SetTimeout(30 * time.Second).
    SetHeader("User-Agent", "MyApp/1.0").
    SetHeader("Accept", "application/json")
```

### 2. GET请求

```go
// 使用客户端实例
params := map[string]string{
    "key1": "value1",
    "key2": "value2",
}
body, err := client.Get("https://api.example.com/data", params)
if err != nil {
    log.Printf("GET请求失败: %v", err)
    return
}

// 使用全局方法
body, err := myhttp.Get("https://api.example.com/data", params)
```

### 3. POST表单请求

```go
// 使用客户端实例
formData := map[string]string{
    "username": "admin",
    "password": "123456",
}
body, err := client.PostForm("https://api.example.com/login", formData)

// 使用全局方法
body, err := myhttp.PostForm("https://api.example.com/login", formData)

// 兼容原有Post方法
body, err := client.Post("https://api.example.com/login", formData)
body, err := myhttp.Post("https://api.example.com/login", formData)
```

### 4. POST JSON请求

```go
// 使用客户端实例
requestData := map[string]interface{}{
    "name": "张三",
    "age":  25,
    "email": "<EMAIL>",
}
body, err := client.PostJSON("https://api.example.com/users", requestData)

// 使用全局方法
body, err := myhttp.PostJSON("https://api.example.com/users", requestData)
```

### 5. 设置请求头

```go
// 单个设置
client.SetHeader("Authorization", "Bearer token123")

// 批量设置
headers := map[string]string{
    "Authorization": "Bearer token123",
    "Content-Type":  "application/json",
    "User-Agent":    "MyApp/1.0",
}
client.SetHeaders(headers)
```

### 6. 设置超时时间

```go
// 设置30秒超时
client.SetTimeout(30 * time.Second)

// 链式调用
client := myhttp.NewHTTPClient().
    SetTimeout(10 * time.Second).
    SetHeader("Accept", "application/json")
```

## 实际应用示例

### 1. 阿里云短信服务

```go
type AliyunSmsService struct {
    httpClient *myhttp.HTTPClient
}

func NewAliyunSmsService() *AliyunSmsService {
    // 创建HTTP客户端并设置超时时间
    client := myhttp.NewHTTPClient().SetTimeout(10 * time.Second)
    
    return &AliyunSmsService{
        httpClient: client,
    }
}

func (s *AliyunSmsService) SendSms(params map[string]string) error {
    // 使用自己的HTTP客户端发送GET请求
    body, err := s.httpClient.Get("https://dysmsapi.aliyuncs.com/", params)
    if err != nil {
        return fmt.Errorf("发送HTTP请求失败: %w", err)
    }
    
    // 处理响应...
    return nil
}
```

### 2. 微信API调用

```go
func CallWechatAPI(code string) (*WechatResponse, error) {
    client := myhttp.NewHTTPClient().
        SetTimeout(15 * time.Second).
        SetHeader("User-Agent", "WechatBot/1.0")
    
    params := map[string]string{
        "appid":      config.App.Wechat.AppID,
        "secret":     config.App.Wechat.AppSecret,
        "js_code":    code,
        "grant_type": "authorization_code",
    }
    
    body, err := client.Get("https://api.weixin.qq.com/sns/jscode2session", params)
    if err != nil {
        return nil, err
    }
    
    var resp WechatResponse
    err = json.Unmarshal(body, &resp)
    return &resp, err
}
```

### 3. 第三方支付API

```go
func CallPaymentAPI(orderData map[string]interface{}) error {
    client := myhttp.NewHTTPClient().
        SetTimeout(30 * time.Second).
        SetHeader("Authorization", "Bearer "+token).
        SetHeader("Content-Type", "application/json")
    
    body, err := client.PostJSON("https://api.payment.com/orders", orderData)
    if err != nil {
        return fmt.Errorf("支付API调用失败: %w", err)
    }
    
    // 处理响应...
    return nil
}
```

## 错误处理

HTTP客户端会返回详细的错误信息，包括：

1. **网络错误**: 连接超时、DNS解析失败等
2. **HTTP错误**: 4xx、5xx状态码
3. **序列化错误**: JSON序列化/反序列化失败
4. **参数错误**: 无效的URL、参数等

```go
body, err := client.Get("https://api.example.com/data", params)
if err != nil {
    log.Printf("HTTP请求失败: %v", err)
    // 根据错误类型进行不同处理
    return
}
```

## 日志记录

HTTP客户端会自动记录详细的请求和响应日志：

```
[INFO] HTTP GET请求 - method: GET, url: https://api.example.com/data?key=value
[INFO] HTTP GET响应 - status: 200 OK, body: {"result": "success"}
[ERROR] HTTP GET请求失败 - error: context deadline exceeded
```

## 性能优化建议

1. **复用客户端实例**: 避免频繁创建新的客户端实例
2. **合理设置超时**: 根据API响应时间设置合适的超时时间
3. **连接池**: HTTP客户端内部使用连接池，支持并发请求
4. **错误重试**: 对于网络错误，可以实现重试机制

```go
// 推荐：复用客户端实例
var globalHTTPClient = myhttp.NewHTTPClient().SetTimeout(30 * time.Second)

func CallAPI(url string, params map[string]string) ([]byte, error) {
    return globalHTTPClient.Get(url, params)
}
```

## 注意事项

1. **线程安全**: HTTP客户端是线程安全的，可以在多个goroutine中并发使用
2. **内存管理**: 响应体会被完全读取到内存中，注意大文件的处理
3. **超时设置**: 默认超时时间为30秒，可根据需要调整
4. **请求头**: 某些API可能需要特定的请求头，注意设置
5. **错误处理**: 务必检查返回的错误，进行适当的错误处理 