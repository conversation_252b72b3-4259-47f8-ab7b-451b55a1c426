# 阿里云短信服务集成文档

## 概述

本项目集成了阿里云短信服务，用于发送短信验证码。支持注册、登录、重置密码等场景的短信验证码发送。

## 配置说明

### 1. 阿里云短信服务开通

1. 登录阿里云控制台
2. 开通短信服务
3. 创建短信签名
4. 创建短信模板
5. 获取AccessKey

### 2. 配置文件设置

在 `server/etc/app.yaml` 中配置阿里云短信服务参数：

```yaml
# 阿里云短信服务配置
AliyunSms:
  AccessKeyId: "YOUR_ACCESS_KEY_ID"         # 阿里云 AccessKeyId
  AccessKeySecret: "YOUR_ACCESS_KEY_SECRET" # 阿里云 AccessKeySecret
  SignName: "wn商城"                        # 短信签名名称
  TemplateCode: "SMS_123456789"             # 短信模板CODE
  Region: "cn-hangzhou"                     # 地域节点
```

### 3. 短信模板要求

短信模板需要包含验证码参数，模板示例：
```
您的验证码是：${code}，5分钟内有效，请勿泄露给他人。
```

模板参数：
- `code`: 验证码参数

## 功能特性

### 1. 验证码发送

- **支持场景**: 注册、登录、重置密码
- **验证码长度**: 6位数字
- **有效期**: 5分钟
- **发送频率限制**: 60秒内只能发送一次

### 2. 业务验证

- **注册**: 检查手机号是否已注册
- **登录**: 检查手机号是否存在
- **重置密码**: 检查手机号是否存在

### 3. 错误处理

- 开发环境：短信发送失败时在控制台打印验证码
- 生产环境：短信发送失败时返回错误

## API接口

### 发送短信验证码

**接口地址**: `POST /api/user/sms/send`

**请求参数**:
```json
{
  "phone": "13800138000",
  "type": "register"
}
```

**参数说明**:
- `phone`: 手机号（11位）
- `type`: 验证码类型（register/login/reset）

**响应示例**:
```json
{
  "code": 200,
  "message": "验证码发送成功",
  "data": null
}
```

### 验证短信验证码

**接口地址**: `POST /api/user/sms/verify`

**请求参数**:
```json
{
  "phone": "13800138000",
  "code": "123456",
  "type": "register"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "验证码验证成功",
  "data": null
}
```

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 手机号格式不正确 | 手机号不是11位或格式错误 |
| 400 | 发送过于频繁，请稍后再试 | 60秒内重复发送 |
| 400 | 手机号已注册 | 注册时手机号已存在 |
| 400 | 手机号未注册 | 登录/重置时手机号不存在 |
| 400 | 验证码类型不正确 | type参数错误 |
| 400 | 验证码已过期或不存在 | 验证码超过5分钟或不存在 |
| 400 | 验证码错误 | 验证码不匹配 |
| 500 | 短信发送失败 | 阿里云短信服务调用失败 |

## 开发调试

### 1. 开发环境配置

开发环境下，如果阿里云短信配置不正确，系统会：
1. 在控制台打印验证码
2. 继续正常流程
3. 不会返回错误

### 2. 生产环境配置

生产环境下，必须正确配置阿里云短信服务，否则会返回错误。

### 3. 日志查看

短信发送的详细日志会输出到控制台，包括：
- 发送成功日志
- 发送失败日志
- 开发环境验证码打印

## 安全注意事项

1. **AccessKey安全**: 不要将AccessKey提交到代码仓库
2. **验证码有效期**: 验证码5分钟后自动过期
3. **发送频率限制**: 防止恶意刷短信
4. **业务验证**: 根据不同场景验证手机号状态
5. **错误信息**: 不要在错误信息中暴露敏感信息

## 费用说明

阿里云短信服务按条收费，具体费用请参考阿里云官方定价。建议：
1. 设置合理的发送频率限制
2. 监控短信发送量
3. 设置费用预警

## 故障排查

### 1. 短信发送失败

检查项目：
- AccessKey是否正确
- 短信签名是否审核通过
- 短信模板是否审核通过
- 账户余额是否充足
- 网络连接是否正常

### 2. 验证码收不到

检查项目：
- 手机号是否正确
- 短信是否被拦截
- 是否在黑名单中
- 运营商是否正常

### 3. 接口调用失败

检查项目：
- 请求参数是否正确
- 手机号格式是否正确
- 验证码类型是否正确
- Redis连接是否正常 