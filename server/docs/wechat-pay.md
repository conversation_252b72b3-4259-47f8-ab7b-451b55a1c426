# 微信支付集成说明

本项目同时支持微信支付V2和V3版本，可以通过配置文件灵活选择。

## 配置说明

### 1. 基础配置 (server/etc/app.yaml)

```yaml
# 微信支付配置
WechatPay:
  Version: "v3" # 支付版本选择 (v2|v3)
  MchID: "YOUR_MERCHANT_ID_HERE" # 商户号
  NotifyUrl: "http://yourdomain.com/api/payment/wechat/notify" # 回调地址
  
  # V3版本配置 (当Version为v3时使用)
  ApiV3Key: "YOUR_API_V3_KEY_HERE" # APIv3密钥
  PrivateKeyPath: "/path/to/your/apiclient_key.pem" # 私钥文件路径
  MerchantCertificateSerialNo: "YOUR_MERCHANT_CERT_SERIAL_NO_HERE" # 证书序列号
  
  # V2版本配置 (当Version为v2时使用)
  ApiKey: "YOUR_API_KEY_HERE" # API密钥
  SignType: "MD5" # 签名类型 (MD5|HMAC-SHA256)
```

### 2. 版本切换

只需要修改 `Version` 字段即可：
- `v3`: 使用微信支付V3 API (推荐)
- `v2`: 使用微信支付V2 API (兼容性)

## 版本对比

| 特性 | V2版本 | V3版本 |
|------|--------|--------|
| 数据格式 | XML | JSON |
| 签名算法 | MD5/HMAC-SHA256 | RSA |
| 证书要求 | 无需证书 | 需要商户证书 |
| API复杂度 | 简单 | 复杂 |
| 安全性 | 较低 | 高 |
| 官方推荐 | 不推荐 | 推荐 |

## 使用示例

### V3版本 (推荐)

```yaml
WechatPay:
  Version: "v3"
  MchID: "1900009191"
  ApiV3Key: "WECHATPAY2-SHA256-RSA2048"
  PrivateKeyPath: "./cert/apiclient_key.pem"
  MerchantCertificateSerialNo: "3775B6A45ACD588826D15E583A95F5DD40344DC"
  NotifyUrl: "https://yourdomain.com/api/payment/wechat/notify"
```

**特点：**
- 自动处理平台证书下载和更新
- 安全性高，官方推荐
- 支持更多高级功能

### V2版本 (兼容)

```yaml
WechatPay:
  Version: "v2"
  MchID: "1900009191"
  ApiKey: "192006250b4c09247ec02edce69f6a2d"
  SignType: "MD5"
  NotifyUrl: "https://yourdomain.com/api/payment/wechat/notify"
```

**特点：**
- 配置简单，无需证书
- 适合快速集成和测试
- 兼容老项目

## 开发建议

1. **新项目推荐使用V3版本**
   - 安全性更高
   - 功能更完整
   - 官方长期支持

2. **V2版本适用场景**
   - 快速原型开发
   - 兼容老项目
   - 证书配置困难的环境

3. **迁移建议**
   - 可以先使用V2版本快速上线
   - 后续逐步迁移到V3版本
   - 两个版本可以无缝切换

## 注意事项

1. **配置文件安全**
   - 不要将真实的密钥提交到代码仓库
   - 生产环境使用环境变量或加密配置

2. **证书管理**
   - V3版本的私钥文件需要妥善保管
   - 定期检查证书有效期

3. **回调地址**
   - 确保回调地址可公网访问
   - 支持HTTPS协议

4. **测试环境**
   - 可以使用微信支付沙箱环境测试
   - 切换版本后需要重新测试支付流程

## 故障排查

### V3版本常见问题
- 证书序列号不正确
- 私钥文件路径错误
- APIv3密钥配置错误

### V2版本常见问题
- API密钥配置错误
- 签名类型不匹配
- XML格式解析错误

更多技术支持请参考微信支付官方文档。 