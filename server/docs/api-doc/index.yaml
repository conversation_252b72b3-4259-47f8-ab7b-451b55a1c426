openapi: 3.0.0
info:
  title: 非遗商城首页接口文档
  version: 1.0.0
  description: 包含首页所需的所有接口

servers:
  - url: /api/v1
    description: 开发环境

paths:
  /home/<USER>
    get:
      summary: 获取首页轮播图
      description: 返回首页轮播图列表
      parameters:
        - name: position
          in: query
          description: 轮播图位置(1:首页顶部 2:分类页 3:活动页)
          required: false
          schema:
            type: integer
            enum: [1, 2, 3]
            default: 1
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Banner'

  /home/<USER>
    get:
      summary: 获取首页分类导航
      description: 返回首页顶部分类导航列表
      parameters:
        - name: level
          in: query
          description: 分类层级(1:一级分类 2:二级分类 3:三级分类)
          required: false
          schema:
            type: integer
            enum: [1, 2, 3]
            default: 1
        - name: parentId
          in: query
          description: 父级分类ID,获取子分类时传入
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Category'

  /home/<USER>
    get:
      summary: 获取首页商品列表
      description: 返回首页商品列表,支持分页和分类筛选
      parameters:
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: pageSize
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 50
        - name: categoryId
          in: query
          description: 分类ID
          required: false
          schema:
            type: integer
        - name: keyword
          in: query
          description: 搜索关键词
          required: false
          schema:
            type: string
        - name: sort
          in: query
          description: 排序方式(1:默认 2:销量 3:价格升序 4:价格降序)
          required: false
          schema:
            type: integer
            enum: [1, 2, 3, 4]
            default: 1
        - name: priceMin
          in: query
          description: 最低价格
          required: false
          schema:
            type: number
        - name: priceMax
          in: query
          description: 最高价格
          required: false
          schema:
            type: number
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: object
                    properties:
                      total:
                        type: integer
                        description: 总数量
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/Goods'

  /home/<USER>
    get:
      summary: 获取首页活动列表
      description: 返回首页活动专区数据
      parameters:
        - name: type
          in: query
          description: 活动类型(1:限时特惠 2:新品首发 3:主题活动)
          required: false
          schema:
            type: integer
            enum: [1, 2, 3]
        - name: status
          in: query
          description: 活动状态(0:未开始 1:进行中 2:已结束)
          required: false
          schema:
            type: integer
            enum: [0, 1, 2]
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: pageSize
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 50
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Activity'

components:
  schemas:
    Banner:
      type: object
      properties:
        id:
          type: integer
          description: 轮播图ID
        image:
          type: string
          description: 图片地址
        url:
          type: string
          description: 跳转链接
        type:
          type: integer
          description: 跳转类型(1:商品 2:活动 3:外链)
        title:
          type: string
          description: 标题
        sort:
          type: integer
          description: 排序

    Category:
      type: object
      properties:
        id:
          type: integer
          description: 分类ID
        name:
          type: string
          description: 分类名称
        icon:
          type: string
          description: 分类图标
        sort:
          type: integer
          description: 排序

    Goods:
      type: object
      properties:
        id:
          type: integer
          description: 商品ID
        name:
          type: string
          description: 商品名称
        desc:
          type: string
          description: 商品描述
        price:
          type: string
          description: 商品价格
        originalPrice:
          type: string
          description: 原价
        image:
          type: string
          description: 商品主图
        sales:
          type: integer
          description: 销量
        stock:
          type: integer
          description: 库存
        tags:
          type: array
          description: 商品标签
          items:
            type: string

    Activity:
      type: object
      properties:
        id:
          type: integer
          description: 活动ID
        name:
          type: string
          description: 活动名称
        desc:
          type: string
          description: 活动描述
        image:
          type: string
          description: 活动图片
        startTime:
          type: string
          format: date-time
          description: 开始时间
        endTime:
          type: string
          format: date-time
          description: 结束时间
        type:
          type: integer
          description: 活动类型(1:限时特惠 2:新品首发 3:主题活动)
        status:
          type: integer
          description: 活动状态(0:未开始 1:进行中 2:已结束) 