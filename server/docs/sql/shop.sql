-- 创建数据库
CREATE DATABASE IF NOT EXISTS shop DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE shop;

-- 轮播图表
CREATE TABLE shop_banner (
    id INT UNSIGNED AUTO_INCREMENT COMMENT '轮播图ID',
    title VARCHAR(100) NOT NULL DEFAULT '' COMMENT '标题',
    image VARCHAR(255) NOT NULL COMMENT '图片地址',
    url VARCHAR(255) NOT NULL DEFAULT '' COMMENT '跳转链接',
    type TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '跳转类型(1:商品 2:活动 3:外链)',
    position TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '位置(1:首页顶部 2:分类页 3:活动页)',
    sort INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序(数字越大越靠前)',
    status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='轮播图表';

-- 商品分类表
CREATE TABLE shop_category (
    id INT UNSIGNED AUTO_INCREMENT COMMENT '分类ID',
    parent_id INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '父级ID',
    name VARCHAR(50) NOT NULL COMMENT '分类名称',
    icon VARCHAR(255) NOT NULL DEFAULT '' COMMENT '分类图标',
    image VARCHAR(255) NOT NULL DEFAULT '' COMMENT '分类图片',
    level TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '层级(1:一级 2:二级 3:三级)',
    sort INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
    status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    is_delete TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_parent_id (parent_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分类表';

-- 商品表
CREATE TABLE shop_goods (
    id INT UNSIGNED AUTO_INCREMENT COMMENT '商品ID',
    category_id INT UNSIGNED NOT NULL COMMENT '分类ID',
    name VARCHAR(200) NOT NULL COMMENT '商品名称',
    goods_type TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '商品类型(1:普通商品)',
    unit VARCHAR(20) NOT NULL DEFAULT '件' COMMENT '单位',
    price DECIMAL(10,2) UNSIGNED NOT NULL COMMENT '售价',
    original_price DECIMAL(10,2) UNSIGNED NOT NULL COMMENT '原价',
    cost_price DECIMAL(10,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '成本价',
    weight DECIMAL(10,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '重量(kg)',
    volume DECIMAL(10,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '体积(m³)',
    sales INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '销量',
    virtual_sales INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '虚拟销量',
    stock INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '库存',
    warn_stock INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '库存预警值',
    status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态(0:下架 1:上架)',
    sort INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
    is_delete TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_category_id (category_id),
    KEY idx_sales (sales)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

-- 商品详情表
CREATE TABLE shop_goods_detail (
    id INT UNSIGNED AUTO_INCREMENT COMMENT 'ID',
    goods_id INT UNSIGNED NOT NULL COMMENT '商品ID',
    video_url VARCHAR(255) DEFAULT '' COMMENT '主视频',
    detail_desc TEXT COMMENT '详细描述',
    spec_desc TEXT COMMENT '规格说明',
    service_desc TEXT COMMENT '服务说明',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_goods_id (goods_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品详情表';

-- 商品图片表
CREATE TABLE shop_goods_image (
    id INT UNSIGNED AUTO_INCREMENT COMMENT '图片ID',
    goods_id INT UNSIGNED NOT NULL COMMENT '商品ID',
    image VARCHAR(255) NOT NULL COMMENT '图片地址',
    is_main TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否主图(0:否 1:是)',
    sort INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_goods_id (goods_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品图片表';

-- 商品规格表
CREATE TABLE shop_goods_spec (
    id INT UNSIGNED AUTO_INCREMENT COMMENT '规格ID',
    goods_id INT UNSIGNED NOT NULL COMMENT '商品ID',
    name VARCHAR(50) NOT NULL COMMENT '规格名称(如:颜色、尺码)',
    sort INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_goods_id (goods_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品规格表';

-- 规格值表
CREATE TABLE shop_goods_spec_value (
    id INT UNSIGNED AUTO_INCREMENT COMMENT '规格值ID',
    spec_id INT UNSIGNED NOT NULL COMMENT '规格ID',
    value VARCHAR(50) NOT NULL COMMENT '规格值(如:红色、XL)',
    sort INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_spec_id (spec_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规格值表';

-- SKU表
CREATE TABLE shop_goods_sku (
    id INT UNSIGNED AUTO_INCREMENT COMMENT 'SKU ID',
    goods_id INT UNSIGNED NOT NULL COMMENT '商品ID',
    spec_value_ids VARCHAR(255) NOT NULL COMMENT '规格值ID组合(如:1,3,5)',
    spec_value_str VARCHAR(255) NOT NULL COMMENT '规格值字符串(如:红色,XL)',
    image VARCHAR(255) DEFAULT '' COMMENT 'SKU图片',
    price DECIMAL(10,2) UNSIGNED NOT NULL COMMENT '售价',
    original_price DECIMAL(10,2) UNSIGNED NOT NULL COMMENT '原价',
    cost_price DECIMAL(10,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '成本价',
    stock INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '库存',
    code VARCHAR(100) DEFAULT '' COMMENT '商品编码',
    weight DECIMAL(10,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '重量(kg)',
    volume DECIMAL(10,2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '体积(m³)',
    status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_goods_id (goods_id),
    UNIQUE KEY uk_spec_values (goods_id, spec_value_ids)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品SKU表';

-- 商品标签表
CREATE TABLE shop_goods_tag (
    id INT UNSIGNED AUTO_INCREMENT COMMENT '标签ID',
    name VARCHAR(20) NOT NULL COMMENT '标签名称',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品标签表';

-- 商品标签关联表
CREATE TABLE shop_goods_tag_relation (
    id INT UNSIGNED AUTO_INCREMENT COMMENT 'ID',
    goods_id INT UNSIGNED NOT NULL COMMENT '商品ID',
    tag_id INT UNSIGNED NOT NULL COMMENT '标签ID',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_goods_tag (goods_id, tag_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品标签关联表';

-- 活动表
CREATE TABLE shop_activity (
    id INT UNSIGNED AUTO_INCREMENT COMMENT '活动ID',
    name VARCHAR(100) NOT NULL COMMENT '活动名称',
    desc TEXT COMMENT '活动描述',
    image VARCHAR(255) NOT NULL COMMENT '活动图片',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    type TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '活动类型(1:限时特惠 2:新品首发 3:主题活动)',
    status TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态(0:未开始 1:进行中 2:已结束)',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_start_time (start_time),
    KEY idx_end_time (end_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动表';

-- 活动商品关联表
CREATE TABLE shop_activity_goods (
    id INT UNSIGNED AUTO_INCREMENT COMMENT 'ID',
    activity_id INT UNSIGNED NOT NULL COMMENT '活动ID',
    goods_id INT UNSIGNED NOT NULL COMMENT '商品ID',
    activity_price DECIMAL(10,2) UNSIGNED NOT NULL COMMENT '活动价格',
    stock INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '活动库存',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_activity_goods (activity_id, goods_id),
    KEY idx_goods_id (goods_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动商品关联表';

-- 用户表
CREATE TABLE user (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    nickname VARCHAR(50) DEFAULT NULL COMMENT '昵称',
    avatar VARCHAR(255) DEFAULT NULL COMMENT '头像',
    phone VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    email VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0-禁用 1-启用',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_username (username),
    UNIQUE KEY uk_phone (phone),
    UNIQUE KEY uk_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 插入管理员账号
INSERT INTO user (username, password, nickname, status) VALUES ('admin', '$2a$10$N.ZOn9G6/YLFixAOPMg/h.z7pCu6v2XyFDtC4q.jeeGm/TEZyj15C', '管理员', 1);
