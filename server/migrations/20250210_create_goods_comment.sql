-- 创建商品评论表
CREATE TABLE IF NOT EXISTS shop_goods_comment (
  id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  goods_id bigint(20) NOT NULL COMMENT '商品ID',
  order_id bigint(20) NOT NULL COMMENT '订单ID',
  user_id bigint(20) NOT NULL COMMENT '用户ID',
  content text NOT NULL COMMENT '评论内容',
  images text DEFAULT NULL COMMENT '评论图片，JSON数组',
  rate tinyint(4) NOT NULL DEFAULT 5 COMMENT '评分(1-5星)',
  is_anonymous tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否匿名',
  reply_content text DEFAULT NULL COMMENT '商家回复内容',
  reply_time datetime NULL DEFAULT NULL COMMENT '商家回复时间',
  is_delete tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除',
  create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  KEY idx_goods_id (goods_id),
  KEY idx_order_id (order_id),
  KEY idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品评论表'; 