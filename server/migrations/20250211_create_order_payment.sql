-- 创建支付订单表
CREATE TABLE IF NOT EXISTS shop_order_payment (
  id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '支付订单ID',
  order_id bigint(20) NOT NULL COMMENT '订单ID',
  order_no varchar(64) NOT NULL COMMENT '订单编号',
  pay_type tinyint(4) NOT NULL COMMENT '支付方式(1:微信 2:支付宝 3:余额)',
  pay_amount decimal(10,2) NOT NULL COMMENT '支付金额',
  pay_status tinyint(4) NOT NULL DEFAULT 0 COMMENT '支付状态(0:未支付 1:支付成功 2:支付失败)',
  trade_no varchar(64) DEFAULT NULL COMMENT '支付平台交易号',
  pay_time datetime DEFAULT NULL COMMENT '支付时间',
  notify_time datetime DEFAULT NULL COMMENT '回调时间',
  notify_data text DEFAULT NULL COMMENT '回调数据',
  create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  UNIQUE KEY uk_order_id (order_id),
  KEY idx_order_no (order_no),
  KEY idx_trade_no (trade_no)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付订单表'; 