-- 创建弹窗表
CREATE TABLE IF NOT EXISTS `popup` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '弹窗ID',
  `title` varchar(100) DEFAULT '' COMMENT '弹窗标题',
  `content` text NOT NULL COMMENT '弹窗内容',
  `title_style` json DEFAULT NULL COMMENT '标题样式JSON',
  `buttons` json DEFAULT NULL COMMENT '按钮配置JSON',
  `trigger_type` varchar(20) NOT NULL DEFAULT 'manual' COMMENT '触发类型：manual/auto/schedule',
  `trigger_rule` json DEFAULT NULL COMMENT '触发规则JSON',
  `priority` int(11) NOT NULL DEFAULT '0' COMMENT '优先级',
  `auto_close` int(11) NOT NULL DEFAULT '0' COMMENT '自动关闭时间(毫秒)',
  `delay` int(11) NOT NULL DEFAULT '0' COMMENT '延迟显示时间(毫秒)',
  `mask_closable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '点击遮罩是否关闭',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用 1=启用',
  `show_count` bigint(20) NOT NULL DEFAULT '0' COMMENT '显示次数',
  `click_count` bigint(20) NOT NULL DEFAULT '0' COMMENT '点击次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0=否 1=是',
  PRIMARY KEY (`id`),
  KEY `idx_status_time` (`status`, `start_time`, `end_time`),
  KEY `idx_trigger_type` (`trigger_type`),
  KEY `idx_priority` (`priority`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='弹窗表';

-- 创建弹窗用户日志表
CREATE TABLE IF NOT EXISTS `popup_user_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `popup_id` bigint(20) NOT NULL COMMENT '弹窗ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `action` varchar(20) NOT NULL COMMENT '操作类型：show/click/close',
  `button_text` varchar(50) DEFAULT '' COMMENT '点击的按钮文字',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_popup_user` (`popup_id`, `user_id`),
  KEY `idx_user_action` (`user_id`, `action`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='弹窗用户日志表';

-- 插入示例数据
INSERT INTO `popup` (`title`, `content`, `title_style`, `buttons`, `trigger_type`, `trigger_rule`, `priority`, `auto_close`, `delay`, `mask_closable`, `start_time`, `end_time`, `status`) VALUES
('欢迎使用wn商城', '感谢您使用我们的应用，祝您购物愉快！', 
 '{"color": "#fff", "background": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"}',
 '[{"text": "开始购物", "type": "primary"}]',
 'manual', '{}', 10, 0, 1000, 1, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 1),

('限时优惠活动', '全场商品8折起，满100元立减20元，活动有限期！', 
 '{"color": "#fff", "background": "#ff4757"}',
 '[{"text": "立即查看", "type": "primary"}, {"text": "稍后再说", "type": "default"}]',
 'auto', '{"pages": ["/pages/index/index"], "userTypes": ["new"], "maxShowTimes": 3}', 8, 10000, 2000, 1, NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY), 1),

('系统维护通知', '系统将于今晚23:00-01:00进行维护升级，期间服务可能暂时中断。', 
 '{"color": "#fff", "background": "#ffa502"}',
 '[{"text": "我知道了", "type": "warning"}]',
 'schedule', '{"scheduleType": "once", "scheduleTime": "22:00"}', 15, 0, 0, 0, NOW(), DATE_ADD(NOW(), INTERVAL 1 DAY), 1); 