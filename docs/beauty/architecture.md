# 美容院预约系统 - 技术架构设计

## 1. 系统架构概览

### 1.1 整体架构
```
┌─────────────────────────────────────────────────────┐
│                    用户层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │
│  │ 用户端(uniapp)│ │ 技师端(H5)  │ │ 管理端(Web) │   │
│  └─────────────┘ └─────────────┘ └─────────────┘   │
└─────────────────────────────────────────────────────┘
                          │
┌─────────────────────────────────────────────────────┐
│                   接口层                           │
│  ┌─────────────────────────────────────────────────┐ │
│  │              API Gateway (Nginx)               │ │
│  └─────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────┘
                          │
┌─────────────────────────────────────────────────────┐
│                   应用层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │
│  │   美容模块   │ │   用户模块   │ │   支付模块   │   │
│  │ (新增)      │ │  (复用)     │ │  (复用)     │   │
│  └─────────────┘ └─────────────┘ └─────────────┘   │
└─────────────────────────────────────────────────────┘
                          │
┌─────────────────────────────────────────────────────┐
│                   数据层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │
│  │    MySQL    │ │    Redis    │ │   文件存储   │   │
│  │   (主从)     │ │   (缓存)     │ │    (OSS)    │   │
│  └─────────────┘ └─────────────┘ └─────────────┘   │
└─────────────────────────────────────────────────────┘
```

### 1.2 模块复用策略
- **完全复用**: 用户管理、支付系统、分类管理、配置管理
- **部分复用**: 订单流程(参考)、消息通知、文件上传
- **新增模块**: 美容服务、技师管理、预约管理、排班系统

### 1.3 技术选型理由
- **Go语言**: 高性能、简洁、并发支持好，与现有技术栈一致
- **uniapp**: 跨平台支持，开发效率高，维护成本低
- **MySQL**: 关系型数据库，事务支持强，数据一致性好
- **Redis**: 内存缓存，高性能，支持复杂数据结构

## 2. 后端架构设计

### 2.1 分层架构
```
┌─────────────────────────────────────────┐
│              Controller Layer           │
│  ┌─────────────┐ ┌─────────────┐       │
│  │BeautyController│ │UserController│     │
│  └─────────────┘ └─────────────┘       │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│               Service Layer             │
│  ┌─────────────┐ ┌─────────────┐       │
│  │BeautyService│ │ UserService │       │
│  └─────────────┘ └─────────────┘       │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│                Model Layer              │
│  ┌─────────────┐ ┌─────────────┐       │
│  │  BeautyDO   │ │   UserDO    │       │
│  └─────────────┘ └─────────────┘       │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│               Database Layer            │
│  ┌─────────────┐ ┌─────────────┐       │
│  │    MySQL    │ │    Redis    │       │
│  └─────────────┘ └─────────────┘       │
└─────────────────────────────────────────┘
```

### 2.2 模块目录结构
```
server/app/
├── controller/beauty/           # 美容模块控制器
│   ├── service_controller.go    # 服务管理
│   ├── technician_controller.go # 技师管理
│   ├── booking_controller.go    # 预约管理
│   └── admin_controller.go      # 管理端
├── service/beauty/              # 美容模块服务层
│   ├── service_service.go       # 服务业务逻辑
│   ├── technician_service.go    # 技师业务逻辑
│   ├── booking_service.go       # 预约业务逻辑
│   ├── schedule_service.go      # 排班业务逻辑
│   └── analytics_service.go     # 数据分析
├── model/do/BeautyDB/           # 美容模块数据模型
│   ├── service.go               # 服务模型
│   ├── technician.go            # 技师模型
│   ├── booking.go               # 预约模型
│   └── schedule.go              # 排班模型
├── model/dto/                   # 数据传输对象
│   ├── beauty_service_dto.go    # 服务DTO
│   ├── beauty_technician_dto.go # 技师DTO
│   └── beauty_booking_dto.go    # 预约DTO
└── route/                       # 路由配置
    └── beauty.go                # 美容模块路由
```

### 2.3 核心服务设计

#### 2.3.1 预约服务 (BookingService)
```go
type BookingService struct {
    db              *gorm.DB
    scheduleService *ScheduleService
    notifyService   *NotifyService
    paymentService  *PaymentService
}

// 核心方法
func (s *BookingService) CreateBooking(userId int64, req *CreateBookingReq) (*BookingResp, error)
func (s *BookingService) CancelBooking(bookingId int64, reason string) error
func (s *BookingService) ConfirmBooking(bookingId int64) error
func (s *BookingService) CheckTimeConflict(TechnicianUserIDint64, date, startTime, endTime string) error
```

#### 2.3.2 排班服务 (ScheduleService)
```go
type ScheduleService struct {
    db *gorm.DB
}

// 核心方法
func (s *ScheduleService) GetAvailableSlots(TechnicianUserID, serviceId int64, date string) ([]TimeSlot, error)
func (s *ScheduleService) GenerateSchedule(TechnicianUserIDint64, template ScheduleTemplate) error
func (s *ScheduleService) CheckConflict(TechnicianUserIDint64, startTime, endTime time.Time) bool
```

### 2.4 数据库连接管理
```go
// 复用现有数据库连接
func InitBeautyDB() {
    beautyDB = db.DB.Shop // 复用现有Shop数据库连接
    
    // 美容模块表初始化
    beautyDB.AutoMigrate(
        &BeautyDB.ServiceDO{},
        &BeautyDB.TechnicianDO{},
        &BeautyDB.BookingDO{},
        &BeautyDB.ScheduleDO{},
    )
}
```

## 3. 前端架构设计

### 3.1 技术架构
```
┌─────────────────────────────────────────┐
│               View Layer                │
│  ┌─────────────┐ ┌─────────────┐       │
│  │  Pages      │ │ Components  │       │
│  └─────────────┘ └─────────────┘       │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│               Logic Layer               │
│  ┌─────────────┐ ┌─────────────┐       │
│  │  Stores     │ │   Utils     │       │
│  │  (Pinia)    │ │             │       │
│  └─────────────┘ └─────────────┘       │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│                API Layer                │
│  ┌─────────────┐ ┌─────────────┐       │
│  │ HTTP Client │ │ Interceptors│       │
│  └─────────────┘ └─────────────┘       │
└─────────────────────────────────────────┘
```

### 3.2 状态管理设计
```javascript
// stores/beauty.js
export const useBeautyStore = defineStore('beauty', {
  state: () => ({
    services: [],
    technicians: [],
    bookings: [],
    currentBooking: null
  }),
  
  actions: {
    async fetchServices(params) {
      const result = await beautyApi.getServices(params)
      this.services = result.data.list
      return result
    },
    
    async createBooking(bookingData) {
      const result = await beautyApi.createBooking(bookingData)
      this.currentBooking = result.data
      return result
    }
  }
})
```

### 3.3 组件设计规范
```javascript
// 组件命名规范
// 业务组件: Beauty + 功能名
// BeautyServiceCard, BeautyTechnicianCard, BeautyBookingForm

// 通用组件: Common + 功能名  
// CommonTimeSlots, CommonDatePicker, CommonRating

// 页面组件: 直接使用页面名
// ServiceList, BookingConfirm, TechnicianDetail
```

### 3.4 API封装设计
```javascript
// api/beauty.js
export const beautyApi = {
  // 服务相关
  getServices: (params) => request.get('/beauty/service/list', { params }),
  getServiceDetail: (id) => request.get(`/beauty/service/${id}`),
  
  // 技师相关
  getTechnicians: (params) => request.get('/beauty/technician/list', { params }),
  getTechnicianSlots: (id, params) => request.get(`/beauty/technician/${id}/slots`, { params }),
  
  // 预约相关
  createBooking: (data) => request.post('/beauty/booking/create', data),
  getMyBookings: (params) => request.get('/beauty/booking/my-list', { params }),
  cancelBooking: (id, data) => request.post(`/beauty/booking/${id}/cancel`, data)
}
```

## 4. 数据架构设计

### 4.1 数据库架构
```
┌─────────────────────────────────────────┐
│              Application                │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│            Connection Pool              │
│         (GORM + 连接池管理)             │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│              MySQL Cluster              │
│  ┌─────────────┐ ┌─────────────┐       │
│  │   Master    │ │   Slave     │       │
│  │  (读写)      │ │   (只读)     │       │
│  └─────────────┘ └─────────────┘       │
└─────────────────────────────────────────┘
```

### 4.2 缓存架构
```go
// 缓存策略设计
type CacheStrategy struct {
    // 服务信息缓存 (30分钟)
    ServiceCache time.Duration = 30 * time.Minute
    
    // 技师信息缓存 (1小时)
    TechnicianCache time.Duration = 1 * time.Hour
    
    // 可预约时间缓存 (5分钟)
    AvailableSlotsCache time.Duration = 5 * time.Minute
    
    // 用户预约列表缓存 (10分钟)
    UserBookingsCache time.Duration = 10 * time.Minute
}
```

### 4.3 数据同步策略
```go
// 数据一致性保证
func (s *BookingService) CreateBookingWithTransaction(req *CreateBookingReq) error {
    return s.db.Transaction(func(tx *gorm.DB) error {
        // 1. 检查时间冲突
        if err := s.checkTimeConflict(tx, req); err != nil {
            return err
        }
        
        // 2. 创建预约记录
        booking := &BeautyDB.BookingDO{...}
        if err := tx.Create(booking).Error; err != nil {
            return err
        }
        
        // 3. 更新技师排班
        if err := s.updateTechnicianSchedule(tx, req); err != nil {
            return err
        }
        
        // 4. 清除相关缓存
        s.clearRelatedCache(req.TechnicianUserID, req.Date)
        
        return nil
    })
}
```

## 5. 安全架构设计

### 5.1 认证授权
```go
// JWT Token验证 (复用现有中间件)
func JWTAuth() gin.HandlerFunc {
    return jwt.New(jwt.Config{
        SigningKey: []byte(config.App.JWTSecret),
        TokenLookup: "header:Authorization",
        AuthScheme: "Bearer",
    })
}

// 角色权限验证
func RequireRole(roles ...string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userRole := c.GetString("userRole")
        if !utils.Contains(roles, userRole) {
            c.JSON(403, gin.H{"error": "权限不足"})
            c.Abort()
            return
        }
        c.Next()
    }
}
```

### 5.2 数据安全
```go
// 敏感数据加密
func EncryptSensitiveData(data string) string {
    // AES加密实现
    return aes.Encrypt(data, config.App.EncryptKey)
}

// 个人信息脱敏
func MaskPersonalInfo(phone string) string {
    if len(phone) < 7 {
        return phone
    }
    return phone[:3] + "****" + phone[7:]
}
```

## 6. 性能优化设计

### 6.1 数据库优化
```sql
-- 关键索引设计
-- 预约查询优化
CREATE INDEX idx_booking_user_date ON beauty_booking(user_id, booking_date, booking_status);
CREATE INDEX idx_booking_technician_date ON beauty_booking(technician_id, booking_date);

-- 技师可用时间查询优化
CREATE INDEX idx_schedule_technician_date ON beauty_technician_schedule(technician_id, date);
```

### 6.2 缓存优化
```go
// 多级缓存策略
type CacheManager struct {
    localCache  *sync.Map     // 本地缓存
    redisCache  redis.Client  // 分布式缓存
}

func (cm *CacheManager) Get(key string) (interface{}, bool) {
    // 1. 先查本地缓存
    if value, ok := cm.localCache.Load(key); ok {
        return value, true
    }
    
    // 2. 查Redis缓存
    if value, err := cm.redisCache.Get(key).Result(); err == nil {
        cm.localCache.Store(key, value) // 回写本地缓存
        return value, true
    }
    
    return nil, false
}
```

### 6.3 接口优化
```go
// 批量操作优化
func (s *BookingService) BatchGetBookings(userIds []int64) (map[int64][]BookingDO, error) {
    var bookings []BookingDO
    err := s.db.Where("user_id IN ?", userIds).Find(&bookings).Error
    if err != nil {
        return nil, err
    }
    
    // 按用户ID分组
    result := make(map[int64][]BookingDO)
    for _, booking := range bookings {
        result[booking.UserId] = append(result[booking.UserId], booking)
    }
    
    return result, nil
}
```

## 7. 监控告警设计

### 7.1 性能监控
```go
// 接口性能监控
func PerformanceMonitor() gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        
        c.Next()
        
        duration := time.Since(start)
        if duration > 500*time.Millisecond {
            log.Warn("Slow API", map[string]interface{}{
                "path":     c.Request.URL.Path,
                "method":   c.Request.Method,
                "duration": duration,
            })
        }
    }
}
```

### 7.2 业务监控
```go
// 预约成功率监控
func MonitorBookingSuccess(booking *BookingDO, success bool) {
    metrics.Counter("booking_total").Inc()
    if success {
        metrics.Counter("booking_success").Inc()
    } else {
        metrics.Counter("booking_failure").Inc()
    }
}
```

## 8. 部署架构设计

### 8.1 容器化部署
```dockerfile
# Dockerfile for Beauty Booking Service
FROM golang:1.19-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod download
RUN go build -o beauty-booking-service main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/beauty-booking-service .
CMD ["./beauty-booking-service"]
```

### 8.2 负载均衡
```nginx
# nginx配置
upstream beauty_backend {
    server beauty-service-1:8080;
    server beauty-service-2:8080;
    server beauty-service-3:8080;
}

server {
    listen 80;
    location /api/beauty/ {
        proxy_pass http://beauty_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

---

*文档版本: v1.0*
*更新时间: 2025-01-13*
*负责人: 架构师*
