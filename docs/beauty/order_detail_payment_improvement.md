# 订单详情页面支付功能改进

## 改进目标
参考美业支付页面的实现，将订单详情页面的支付功能改为直接在页面中弹出微信支付，而不是跳转到单独的支付页面。

## 改进内容

### 1. 前端页面修改

#### 1.1 修改支付按钮
```vue
<!-- 修改前 -->
<button v-if="order.status === 'unpaid'" class="btn btn-primary" @click="payOrder">立即支付</button>

<!-- 修改后 -->
<button 
  v-if="order.status === 'unpaid'" 
  class="btn btn-primary" 
  :class="{ disabled: paying }"
  @click="payOrder"
  :disabled="paying"
>
  {{ paying ? '支付中...' : `立即支付 ¥${order.payAmount}` }}
</button>
```

#### 1.2 添加支付状态变量
```javascript
data() {
  return {
    order: null,
    loading: false,
    paying: false  // 新增支付状态
  }
}
```

#### 1.3 重写支付方法
```javascript
// 修改前：跳转到支付页面
payOrder() {
  uni.navigateTo({
    url: `/pages/pay/pay?orderId=${this.order.id}&amount=${this.order.payAmount}`
  })
}

// 修改后：直接处理微信支付
async payOrder() {
  if (this.paying) return
  
  this.paying = true
  try {
    const response = await payOrder({
      order_id: this.order.id,
      pay_type: 1 // 微信支付
    })

    if (response.code === 200) {
      // 微信支付
      this.handleWechatPayment(response.data)
    } else if (response.code === 400 && response.message === '订单已支付') {
      // 订单已经支付，刷新页面
      this.fetchDetail(this.order.id)
      uni.showToast({
        title: '订单已支付',
        icon: 'success'
      })
    } else {
      throw new Error(response.message || '支付失败')
    }
  } catch (error) {
    console.error('支付失败:', error)
    uni.showToast({
      title: error.message || '支付失败',
      icon: 'none'
    })
  } finally {
    this.paying = false
  }
}
```

#### 1.4 添加微信支付处理方法
```javascript
// 处理微信支付
handleWechatPayment(payData) {
  console.log('支付数据:', payData)
  
  // 后端返回的数据结构是 {success: true, data: {...}, message: "支付成功"}
  // 实际的支付参数在 payData.data 中
  const wechatPayData = payData.data || payData
  
  // 检查支付数据是否包含必要的参数
  if (!wechatPayData.appId || !wechatPayData.timeStamp || !wechatPayData.nonceStr || !wechatPayData.package || !wechatPayData.signType || !wechatPayData.paySign) {
    console.error('微信支付参数不完整:', wechatPayData)
    uni.showToast({
      title: '支付参数错误',
      icon: 'none'
    })
    return
  }
  
  uni.requestPayment({
    provider: 'wxpay',
    appId: wechatPayData.appId,
    timeStamp: wechatPayData.timeStamp,
    nonceStr: wechatPayData.nonceStr,
    package: wechatPayData.package,
    signType: wechatPayData.signType,
    paySign: wechatPayData.paySign,
    success: () => {
      console.log('微信支付成功')
      this.paySuccess()
    },
    fail: (err) => {
      console.error('微信支付失败:', err)
      uni.showToast({
        title: '支付失败',
        icon: 'none'
      })
    }
  })
}

// 支付成功
paySuccess() {
  uni.showToast({
    title: '支付成功',
    icon: 'success'
  })
  
  setTimeout(() => {
    // 刷新订单详情
    this.fetchDetail(this.order.id)
  }, 1500)
}
```

#### 1.5 改进按钮样式
```scss
.btn-primary {
  background: linear-gradient(135deg, #FF6B81 0%, #FF8E9E 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 129, 0.3);
  transition: all 0.3s ease;
}

.btn-primary:active {
  transform: scale(0.98);
}

.btn-primary.disabled {
  background: #ccc;
  box-shadow: none;
}
```

### 2. 后端接口

#### 2.1 支付接口
后端已经提供了完整的支付接口：

```go
// 路由配置
orderGroup.POST("/pay", orderController.Pay)  // 支付订单

// 控制器方法
func (c *OrderController) Pay(ctx *gin.Context) {
    var req dto.PayOrderReq
    if err := ctx.ShouldBindJSON(&req); err != nil {
        response.ErrorWithError(err, ctx)
        return
    }

    // 获取用户ID
    userID := ctx.GetInt64("userId")

    // 支付订单
    result, err := c.orderService.PayOrder(userID, &req)
    if err != nil {
        response.ErrorWithError(err, ctx)
        return
    }

    response.SuccessWithData(result, ctx)
}
```

#### 2.2 前端API调用
```javascript
// 支付订单
export function payOrder(data) {
  return request.post('/order/pay', data)
}
```

## 功能特点

### 1. 用户体验改进
- **无需跳转**：直接在订单详情页面进行支付
- **即时反馈**：支付按钮显示加载状态和金额
- **状态管理**：防止重复点击支付按钮

### 2. 支付流程
1. 用户点击"立即支付"按钮
2. 按钮显示"支付中..."状态
3. 调用后端支付接口获取微信支付参数
4. 弹出微信支付界面
5. 支付成功后刷新订单详情
6. 显示支付成功提示

### 3. 错误处理
- **参数验证**：检查微信支付参数完整性
- **状态检查**：处理订单已支付的情况
- **异常处理**：支付失败时显示错误信息

### 4. 视觉改进
- **渐变背景**：使用渐变色提升视觉效果
- **阴影效果**：添加按钮阴影增强立体感
- **动画效果**：按钮点击时的缩放动画
- **状态样式**：禁用状态的灰色样式

## 技术实现

### 1. 状态管理
```javascript
paying: false  // 支付状态，防止重复点击
```

### 2. 条件渲染
```vue
:class="{ disabled: paying }"  // 根据支付状态设置样式
:disabled="paying"             // 根据支付状态禁用按钮
```

### 3. 动态文本
```vue
{{ paying ? '支付中...' : `立即支付 ¥${order.payAmount}` }}
```

### 4. 异步处理
```javascript
async payOrder() {
  // 异步支付处理
}
```

## 与美业支付页面的对比

| 功能 | 美业支付页面 | 订单详情页面 |
|------|-------------|-------------|
| 支付方式 | 微信支付 | 微信支付 |
| 支付流程 | 直接弹出 | 直接弹出 |
| 页面跳转 | 无 | 无 |
| 状态管理 | 有 | 有 |
| 错误处理 | 完善 | 完善 |
| 视觉样式 | 美观 | 美观 |

## 总结

通过参考美业支付页面的实现，成功改进了订单详情页面的支付功能：

- ✅ **用户体验提升**：无需跳转页面，直接在订单详情中支付
- ✅ **功能完整性**：支持微信支付，包含完整的错误处理
- ✅ **视觉优化**：采用渐变背景和动画效果
- ✅ **状态管理**：防止重复点击，提供即时反馈
- ✅ **技术实现**：使用异步处理和条件渲染

订单详情页面的支付功能现在与美业支付页面保持一致，提供了更好的用户体验！ 