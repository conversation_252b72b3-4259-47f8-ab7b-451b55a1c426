# 美容院预约系统

## 项目概述

基于现有电商系统架构，新增美容院预约模块，实现美容服务的在线预约、支付、管理等功能。

## 文档目录

- [业务需求分析](./business-analysis.md) - 业务流程、用户角色、需求分析
- [技术架构设计](./architecture.md) - 系统架构、技术选型、模块设计
- [数据库设计](./database-design.md) - 数据模型、表结构设计
- [API接口设计](./api-design.md) - 接口规范、数据格式
- [前端页面设计](./frontend-design.md) - 页面结构、用户体验设计
- [开发计划](./development-plan.md) - 开发阶段、时间安排、任务分工

## 核心特性

- ✅ 复用现有用户、支付、分类系统
- ✅ 独立的美容预约业务模块  
- ✅ 智能排班和时间冲突检测
- ✅ 技师管理和服务匹配
- ✅ 会员系统和营销活动支持
- ✅ 完整的预约状态流转
- ✅ 移动端优化的用户体验

## 技术栈

### 后端
- **语言**: Go 1.19+
- **框架**: Gin + GORM
- **数据库**: MySQL 8.0
- **缓存**: Redis

### 前端
- **框架**: uniapp + Vue.js 3
- **UI库**: uView 2.0
- **状态管理**: Pinia

## 项目结构

```
├── server/app/
│   ├── controller/beauty/     # 美容预约控制器
│   ├── service/beauty/        # 美容预约服务层
│   ├── model/do/BeautyDB/     # 美容预约数据模型
│   └── route/beauty.go        # 美容预约路由
├── bookify/pages/beauty/      # 前端美容预约页面
└── docs/beauty/               # 美容预约文档
```

---

*最后更新: 2025-01-13*
