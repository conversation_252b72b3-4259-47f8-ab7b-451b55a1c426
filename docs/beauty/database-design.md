# 美容院预约系统 - 数据库设计

## 1. 设计原则

### 1.1 复用策略
- 复用现有 `category` 表，通过 `module='beauty'` 区分美容服务分类
- 复用现有 `user` 表作为客户用户
- 复用现有 `sys_config` 表存储美容院配置
- 复用现有 `feedback` 表处理投诉建议

### 1.2 设计规范
- 遵循现有项目的数据库命名规范
- 使用 `beauty_` 前缀标识美容业务表
- 统一使用 `is_delete` 字段实现软删除
- 统一使用 `create_time` 和 `update_time` 记录时间

## 2. 核心表结构

### 2.1 美容服务表 (beauty_service)

```sql
CREATE TABLE beauty_service (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    category_id BIGINT NOT NULL COMMENT '分类ID(关联category表，module=beauty)',
    name VARCHAR(200) NOT NULL COMMENT '服务名称',
    subtitle VARCHAR(300) COMMENT '服务副标题',
    description TEXT COMMENT '服务描述',
    images JSON COMMENT '服务图片数组',
    
    -- 价格信息
    price DECIMAL(10,2) NOT NULL COMMENT '基础价格',
    original_price DECIMAL(10,2) COMMENT '原价（用于显示折扣）',
    
    -- 服务属性
    duration INT NOT NULL COMMENT '标准服务时长(分钟)',
    
    -- 预约规则
    need_technician TINYINT DEFAULT 1 COMMENT '是否需要指定技师(0:否 1:是)',
    max_advance_days INT DEFAULT 30 COMMENT '最大提前预约天数',
    min_advance_hours INT DEFAULT 2 COMMENT '最小提前预约小时数',
    allow_cancel_hours INT DEFAULT 24 COMMENT '允许取消的提前小时数',
    
    -- 适用条件
    gender_limit TINYINT DEFAULT 0 COMMENT '性别限制(0:不限 1:仅男性 2:仅女性)',
    age_min INT DEFAULT 0 COMMENT '最小年龄限制',
    age_max INT DEFAULT 100 COMMENT '最大年龄限制',
    
    -- 服务特性
    tags JSON COMMENT '服务标签["补水","美白","抗衰"]',
    contraindications TEXT COMMENT '禁忌症说明',
    preparation_notes TEXT COMMENT '预约前准备事项',
    aftercare_notes TEXT COMMENT '护理后注意事项',
    
    -- 统计信息
    booking_count INT DEFAULT 0 COMMENT '累计预约次数',
    rating DECIMAL(3,2) DEFAULT 0 COMMENT '平均评分(0-5)',
    rating_count INT DEFAULT 0 COMMENT '评价总数',
    
    -- 状态管理
    status TINYINT DEFAULT 1 COMMENT '状态(0:下架 1:上架 2:维护中)',
    sort INT DEFAULT 0 COMMENT '排序权重',
    is_delete TINYINT DEFAULT 0 COMMENT '是否删除(0:否 1:是)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_category (category_id),
    INDEX idx_status (status),
    INDEX idx_price (price)
) COMMENT='美容服务表';
```

### 2.2 技师表 (beauty_technician)

```sql
CREATE TABLE beauty_technician (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    emp_no VARCHAR(50) UNIQUE COMMENT '员工编号',
    name VARCHAR(100) NOT NULL COMMENT '技师姓名',
    avatar VARCHAR(255) COMMENT '头像URL',
    gender TINYINT COMMENT '性别(1:男 2:女)',
    phone VARCHAR(20) COMMENT '联系电话',
    
    -- 专业信息
    level TINYINT DEFAULT 1 COMMENT '技师等级(1:初级 2:中级 3:高级 4:专家 5:首席)',
    experience_years INT DEFAULT 0 COMMENT '从业年限',
    introduction TEXT COMMENT '个人简介',
    specialties JSON COMMENT '专长领域["面部护理","身体护理"]',
    
    -- 工作信息  
    entry_date DATE COMMENT '入职日期',
    work_type TINYINT DEFAULT 1 COMMENT '工作类型(1:全职 2:兼职)',
    commission_rate DECIMAL(5,2) DEFAULT 0 COMMENT '提成比例(%)',
    
    -- 统计信息
    total_bookings INT DEFAULT 0 COMMENT '累计服务次数',
    rating DECIMAL(3,2) DEFAULT 0 COMMENT '平均评分(0-5)',
    rating_count INT DEFAULT 0 COMMENT '评价总数',
    
    -- 状态管理
    status TINYINT DEFAULT 1 COMMENT '状态(0:离职 1:在职 2:休假)',
    is_delete TINYINT DEFAULT 0 COMMENT '是否删除',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_level (level),
    INDEX idx_status (status)
) COMMENT='美容技师表';
```

### 2.3 预约订单表 (beauty_booking)

```sql
CREATE TABLE beauty_booking (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    booking_no VARCHAR(32) UNIQUE NOT NULL COMMENT '预约编号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    service_id BIGINT NOT NULL COMMENT '服务ID',
    technician_id BIGINT COMMENT '技师ID',
    
    -- 预约时间信息
    booking_date DATE NOT NULL COMMENT '预约日期',
    start_time TIME NOT NULL COMMENT '开始时间',
    end_time TIME NOT NULL COMMENT '结束时间',
    duration INT NOT NULL COMMENT '服务时长(分钟)',
    
    -- 客户信息
    contact_name VARCHAR(100) NOT NULL COMMENT '联系人姓名',
    contact_phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    gender TINYINT COMMENT '性别',
    age INT COMMENT '年龄',
    
    -- 个性化需求
    skin_type VARCHAR(50) COMMENT '肌肤类型',
    skin_concerns JSON COMMENT '肌肤问题',
    special_requests TEXT COMMENT '特殊要求',
    
    -- 价格信息
    original_price DECIMAL(10,2) NOT NULL COMMENT '原价',
    final_price DECIMAL(10,2) NOT NULL COMMENT '实付金额',
    
    -- 状态管理
    booking_status VARCHAR(20) DEFAULT 'pending' COMMENT '预约状态',
    payment_status VARCHAR(20) DEFAULT 'unpaid' COMMENT '支付状态',
    
    -- 评价信息
    rating TINYINT COMMENT '评分(1-5)',
    review TEXT COMMENT '评价内容',
    
    -- 系统字段
    is_delete TINYINT DEFAULT 0 COMMENT '是否删除',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_booking_date (booking_date),
    INDEX idx_booking_status (booking_status)
) COMMENT='美容预约订单表';
```

## 3. 状态定义

### 3.1 预约状态 (booking_status)
- `pending` - 待确认：客户提交预约，等待技师或管理员确认
- `confirmed` - 已确认：技师或管理员确认预约，客户可以按时到店
- `in_service` - 服务中：技师正在为客户提供服务
- `completed` - 已完成：服务已完成，可以进行评价和支付
- `cancelled` - 已取消：预约被取消，可能是客户取消或商家取消

### 3.2 支付状态 (payment_status)
- `unpaid` - 未支付：预约尚未支付
- `paid` - 已支付：预约已完成支付

### 3.3 服务状态 (service_status)
- `waiting` - 等待中：等待服务开始
- `in_progress` - 进行中：服务正在进行
- `completed` - 已完成：服务已完成

## 4. 初始化数据

### 4.1 分类数据
```sql
-- 插入美容服务分类
INSERT INTO category (module, parent_id, name, level, sort, status) VALUES
('beauty', 0, '面部护理', 1, 100, 1),
('beauty', 0, '身体护理', 1, 90, 1),
('beauty', 0, '美甲美睫', 1, 80, 1);
```

---

*文档版本: v1.0*
*更新时间: 2025-01-13*
