# 美容院预约系统 - API接口设计

## 1. 接口规范

### 1.1 基础信息
- **Base URL**: `http://localhost:8080/api/beauty`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **认证方式**: JWT <PERSON> (Bearer)

### 1.2 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": 1673598234
}
```

### 1.3 状态码定义
- `200` - 请求成功
- `400` - 请求参数错误
- `401` - 未授权访问
- `404` - 资源不存在
- `500` - 服务器内部错误

## 2. 美容服务相关接口

### 2.1 获取服务分类列表
**接口路径**: `GET /service/categories`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "面部护理",
      "icon": "face-care.png",
      "description": "专业面部护理服务",
      "children": [
        {
          "id": 11,
          "name": "补水保湿"
        }
      ]
    }
  ]
}
```

### 2.2 获取服务列表
**接口路径**: `GET /service/list`

**请求参数**:
```json
{
  "category_id": 1,        // 分类ID，可选
  "keyword": "补水",       // 搜索关键词，可选
  "page": 1,               // 页码，默认1
  "page_size": 10          // 每页数量，默认10
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "深层补水护理",
        "price": 299.00,
        "duration": 90,
        "images": ["service1.jpg"],
        "rating": 4.8,
        "booking_count": 300
      }
    ],
    "total": 25,
    "page": 1,
    "page_size": 10
  }
}
```

### 2.3 获取服务详情
**接口路径**: `GET /service/:id`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "深层补水护理",
    "description": "使用进口精华，深层补水...",
    "price": 299.00,
    "duration": 90,
    "images": ["service1.jpg"],
    "rating": 4.8,
    "contraindications": "孕妇及过敏体质慎用",
    "available_technicians": [
      {
        "id": 1,
        "name": "张美美",
        "level": 3,
        "rating": 4.9
      }
    ]
  }
}
```

## 3. 技师相关接口

### 3.1 获取技师列表
**接口路径**: `GET /technician/list`

**请求参数**:
```json
{
  "service_id": 1,         // 服务ID筛选
  "level": 3,              // 技师等级筛选
  "page": 1,
  "page_size": 10
}
```

### 3.2 获取技师可预约时间
**接口路径**: `GET /technician/:id/available-slots`

**请求参数**:
```json
{
  "service_id": 1,         // 服务ID
  "date": "2025-01-15"     // 查询日期
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "available_dates": [
      {
        "date": "2025-01-15",
        "slots": [
          {
            "start_time": "09:00",
            "end_time": "10:30",
            "available": true,
            "price": 328.90
          }
        ]
      }
    ]
  }
}
```

## 4. 预约相关接口

### 4.1 创建预约订单
**接口路径**: `POST /booking/create`

**请求头**: `Authorization: Bearer {token}`

**请求体**:
```json
{
  "service_id": 1,
  "technician_id": 1,
  "booking_date": "2025-01-15",
  "start_time": "09:00",
  "contact_name": "李小姐",
  "contact_phone": "13800138000",
  "special_requests": "希望技师轻一点",
  "payment_method": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "预约创建成功",
  "data": {
    "booking_id": 123,
    "booking_no": "BK202501150001",
    "final_price": 328.90,
    "payment_info": {
      "pay_type": 1,
      "prepay_id": "wx_prepay_123456"
    }
  }
}
```

### 4.2 获取我的预约列表
**接口路径**: `GET /booking/my-list`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 123,
        "booking_no": "BK202501150001",
        "service_name": "深层补水护理",
        "technician_name": "张美美",
        "booking_date": "2025-01-15",
        "start_time": "09:00",
        "booking_status": "confirmed",
        "final_price": 328.90,
        "can_cancel": true
      }
    ],
    "total": 8
  }
}
```

### 4.3 取消预约
**接口路径**: `POST /booking/:id/cancel`

**请求体**:
```json
{
  "cancel_reason": "临时有事，无法前来"
}
```

### 4.4 评价预约
**接口路径**: `POST /booking/:id/review`

**请求体**:
```json
{
  "rating": 5,
  "review": "服务很好，技师很专业"
}
```

## 5. 管理端接口

### 5.1 预约管理列表
**接口路径**: `GET /admin/booking/list`

**请求头**: `Authorization: Bearer {admin_token}`

### 5.2 技师管理
**接口路径**: `GET /admin/technician/list`

### 5.3 营收统计
**接口路径**: `GET /admin/analytics/revenue`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_revenue": 52800.00,
    "total_bookings": 180,
    "chart_data": [
      {
        "date": "2025-01-01",
        "revenue": 1200.00,
        "bookings": 4
      }
    ]
  }
}
```

## 6. 错误处理

### 6.1 业务错误码
- `40001` - 预约时间冲突
- `40002` - 技师不可用
- `40003` - 服务已下架
- `40004` - 超出预约时间范围

### 6.2 错误响应示例
```json
{
  "code": 40001,
  "message": "该时间段已被预约，请选择其他时间",
  "data": {
    "suggested_times": ["10:30", "14:00", "16:00"]
  }
}
```

---

*文档版本: v1.0*
*更新时间: 2025-01-13*
