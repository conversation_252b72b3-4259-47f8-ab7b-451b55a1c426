# 美容院预约系统 - 前端页面设计

## 1. 整体设计原则

### 1.1 设计理念
- **用户体验优先**: 简洁直观的预约流程
- **移动端优化**: 响应式设计，适配各种屏幕
- **品牌一致性**: 统一的视觉风格和交互体验
- **性能优先**: 快速加载，流畅交互

### 1.2 技术选型
- **框架**: uniapp + Vue.js 3
- **UI组件库**: uView 2.0
- **状态管理**: Pinia
- **样式方案**: SCSS + CSS变量
- **图标库**: uni-icons + 自定义图标

### 1.3 设计规范
- **色彩主题**: 优雅粉色系为主，温馨舒适
- **字体大小**: 主要文字28rpx，标题32rpx，小字24rpx
- **间距规范**: 基础间距20rpx，组件间距30rpx
- **圆角规范**: 按钮12rpx，卡片16rpx，图片20rpx

## 2. 页面架构设计

### 2.1 整体导航结构
```
美容预约系统
├── 首页 (beauty/index)
├── 服务分类 (beauty/category)
├── 服务列表 (beauty/service/list)
├── 服务详情 (beauty/service/detail)
├── 技师列表 (beauty/technician/list)
├── 技师详情 (beauty/technician/detail)
├── 预约流程
│   ├── 选择技师 (beauty/booking/technician)
│   ├── 选择时间 (beauty/booking/time)
│   ├── 填写信息 (beauty/booking/form)
│   └── 确认预约 (beauty/booking/confirm)
├── 我的预约 (beauty/booking/list)
├── 预约详情 (beauty/booking/detail)
└── 管理后台 (beauty/admin)
    ├── 预约管理 (beauty/admin/booking)
    ├── 技师管理 (beauty/admin/technician)
    └── 数据统计 (beauty/admin/analytics)
```

### 2.2 页面路由配置
```javascript
// pages.json 配置示例
{
  "pages": [
    {
      "path": "pages/beauty/index/index",
      "style": {
        "navigationBarTitleText": "美容预约",
        "navigationBarBackgroundColor": "#FFB6C1",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/beauty/service/list",
      "style": {
        "navigationBarTitleText": "选择服务",
        "enablePullDownRefresh": true
      }
    }
  ]
}
```

## 3. 核心页面设计

### 3.1 美容首页 (beauty/index/index.vue)

#### 3.1.1 页面布局
```
┌─────────────────────────────┐
│ 顶部搜索栏 + 消息图标        │
├─────────────────────────────┤
│ Banner轮播图 (活动推广)      │
├─────────────────────────────┤
│ 服务分类导航 (4x2网格)       │
├─────────────────────────────┤
│ 推荐服务 (横向滚动卡片)      │
├─────────────────────────────┤
│ 明星技师 (横向滚动卡片)      │
├─────────────────────────────┤
│ 用户快捷入口                │
│ [我的预约] [会员中心] [客服] │
└─────────────────────────────┘
```

#### 3.1.2 核心功能
- **搜索功能**: 全局服务搜索，支持关键词和语音搜索
- **Banner管理**: 活动推广、新服务介绍、节日特惠
- **分类导航**: 
  - 面部护理 (补水、美白、抗衰、清洁)
  - 身体护理 (SPA、按摩、减肥、护理)
  - 美甲美睫 (美甲、美睫、纹绣)
  - 特色项目 (中医养生、产后修复)
- **个性化推荐**: 基于用户历史和偏好的服务推荐
- **技师展示**: 评分最高、预约最多的明星技师

#### 3.1.3 代码结构
```vue
<template>
  <view class="beauty-home">
    <!-- 顶部搜索 -->
    <view class="search-header">
      <u-search placeholder="搜索服务或技师" @search="handleSearch"></u-search>
      <u-icon name="bell" @click="showNotifications"></u-icon>
    </view>
    
    <!-- Banner轮播 -->
    <u-swiper :list="bannerList" height="300" border-radius="16"></u-swiper>
    
    <!-- 分类导航 -->
    <view class="category-grid">
      <view class="category-item" v-for="item in categories" :key="item.id">
        <image :src="item.icon" class="category-icon"></image>
        <text class="category-name">{{item.name}}</text>
      </view>
    </view>
    
    <!-- 推荐服务 -->
    <RecommendServices :services="recommendServices" />
    
    <!-- 明星技师 -->
    <StarTechnicians :technicians="starTechnicians" />
    
    <!-- 快捷入口 -->
    <QuickActions />
  </view>
</template>
```

### 3.2 服务详情页 (beauty/service/detail.vue)

#### 3.2.1 页面布局
```
┌─────────────────────────────┐
│ 服务图片轮播                │
├─────────────────────────────┤
│ 服务名称 + 价格 + 评分       │
├─────────────────────────────┤
│ 服务标签 [补水] [保湿] [舒缓] │
├─────────────────────────────┤
│ 服务详情描述                │
├─────────────────────────────┤
│ 适用人群 + 注意事项          │
├─────────────────────────────┤
│ 可选技师列表                │
├─────────────────────────────┤
│ 用户评价 (评分 + 评论)       │
├─────────────────────────────┤
│ [立即预约] 底部固定按钮      │
└─────────────────────────────┘
```

#### 3.2.2 核心功能
- **图片展示**: 高清服务图片，支持放大查看
- **价格显示**: 原价、现价、会员价多层级显示
- **技师选择**: 可选择指定技师或让系统推荐
- **时间预览**: 显示最近可预约时间
- **评价系统**: 真实用户评价，支持图片评价
- **收藏分享**: 收藏服务，分享给朋友

#### 3.2.3 交互设计
```vue
<template>
  <view class="service-detail">
    <!-- 图片轮播 -->
    <u-swiper :list="service.images" height="500"></u-swiper>
    
    <!-- 服务信息 -->
    <view class="service-info">
      <view class="service-header">
        <text class="service-name">{{service.name}}</text>
        <view class="service-actions">
          <u-icon name="heart" @click="toggleFavorite"></u-icon>
          <u-icon name="share" @click="shareService"></u-icon>
        </view>
      </view>
      
      <!-- 价格和评分 -->
      <view class="price-rating">
        <view class="price-info">
          <text class="current-price">¥{{service.price}}</text>
          <text class="original-price">¥{{service.originalPrice}}</text>
        </view>
        <u-rate :value="service.rating" readonly></u-rate>
      </view>
      
      <!-- 服务标签 -->
      <view class="service-tags">
        <u-tag v-for="tag in service.tags" :key="tag" size="mini">{{tag}}</u-tag>
      </view>
    </view>
    
    <!-- 技师选择 -->
    <TechnicianSelection 
      :technicians="availableTechnicians"
      @select="selectTechnician"
    />
    
    <!-- 固定底部按钮 -->
    <view class="fixed-bottom">
      <u-button type="primary" @click="startBooking">立即预约</u-button>
    </view>
  </view>
</template>
```

### 3.3 预约时间选择页 (beauty/booking/time.vue)

#### 3.3.1 页面设计
```
┌─────────────────────────────┐
│ 选择日期 (日历组件)          │
├─────────────────────────────┤
│ 可选时间段                  │
│ ┌─────┐ ┌─────┐ ┌─────┐     │
│ │09:00│ │10:30│ │14:00│     │
│ │可约 │ │已约 │ │可约 │     │
│ └─────┘ └─────┘ └─────┘     │
├─────────────────────────────┤
│ 技师信息 (如已选择)          │
├─────────────────────────────┤
│ 服务时长: 90分钟            │
│ 预估价格: ¥299              │
├─────────────────────────────┤
│ [确认时间] 按钮             │
└─────────────────────────────┘
```

#### 3.3.2 核心功能
- **日历选择**: 可视化日历，禁用不可预约日期
- **时段展示**: 清晰显示可约/已约/休息时段
- **实时更新**: 实时获取最新的可预约时间
- **智能推荐**: 根据用户习惯推荐最佳时间
- **冲突检测**: 防止选择已被预约的时间

#### 3.3.3 组件实现
```vue
<template>
  <view class="time-selection">
    <!-- 日期选择器 -->
    <u-calendar 
      :show="showCalendar"
      :min-date="minDate"
      :max-date="maxDate"
      @confirm="selectDate"
    ></u-calendar>
    
    <!-- 时间段网格 -->
    <view class="time-slots">
      <view class="date-header">
        <text>选择时间 - {{selectedDate}}</text>
        <text class="change-date" @click="showCalendar = true">更换日期</text>
      </view>
      
      <view class="slots-grid">
        <view 
          v-for="slot in timeSlots" 
          :key="slot.startTime"
          class="time-slot"
          :class="{
            'available': slot.available,
            'selected': selectedSlot?.startTime === slot.startTime,
            'disabled': !slot.available
          }"
          @click="selectTimeSlot(slot)"
        >
          <text class="time-text">{{slot.startTime}}</text>
          <text class="status-text">{{slot.available ? '可约' : '已约'}}</text>
        </view>
      </view>
    </view>
    
    <!-- 预约信息确认 -->
    <BookingConfirmInfo 
      :service="service"
      :technician="technician"
      :datetime="selectedDateTime"
      :price="calculatedPrice"
    />
  </view>
</template>
```

### 3.4 预约确认页 (beauty/booking/confirm.vue)

#### 3.4.1 页面布局
```
┌─────────────────────────────┐
│ 预约信息卡片                │
│ ┌─────────────────────────┐ │
│ │ 服务: 深层补水护理      │ │
│ │ 技师: 张美美 (高级)     │ │
│ │ 时间: 1月15日 09:00    │ │
│ │ 时长: 90分钟           │ │
│ └─────────────────────────┘ │
├─────────────────────────────┤
│ 个人信息填写                │
│ [姓名] [手机] [年龄] [性别]  │
├─────────────────────────────┤
│ 肌肤信息                    │
│ [肌肤类型] [肌肤问题]       │
├─────────────────────────────┤
│ 特殊需求 (文本框)           │
├─────────────────────────────┤
│ 价格明细                    │
│ 服务费用: ¥299             │
│ 技师费用: ¥30              │
│ 优惠金额: -¥20             │
│ ─────────────────          │
│ 实付金额: ¥309             │
├─────────────────────────────┤
│ [确认预约并支付] 按钮        │
└─────────────────────────────┘
```

#### 3.4.2 表单设计
```vue
<template>
  <view class="booking-confirm">
    <!-- 预约信息卡片 -->
    <BookingInfoCard :booking="bookingInfo" />
    
    <!-- 个人信息表单 -->
    <u-form :model="form" ref="uForm">
      <u-form-item label="联系姓名" prop="contactName" required>
        <u-input v-model="form.contactName" placeholder="请输入您的姓名"></u-input>
      </u-form-item>
      
      <u-form-item label="手机号码" prop="contactPhone" required>
        <u-input v-model="form.contactPhone" placeholder="请输入手机号码"></u-input>
      </u-form-item>
      
      <u-form-item label="年龄">
        <u-number-box v-model="form.age" :min="16" :max="80"></u-number-box>
      </u-form-item>
      
      <u-form-item label="性别">
        <u-radio-group v-model="form.gender">
          <u-radio name="1">男</u-radio>
          <u-radio name="2">女</u-radio>
        </u-radio-group>
      </u-form-item>
    </u-form>
    
    <!-- 肌肤信息 -->
    <SkinInfoForm v-model="form.skinInfo" />
    
    <!-- 特殊需求 -->
    <u-textarea 
      v-model="form.specialRequests" 
      placeholder="请描述您的特殊需求或注意事项"
      maxlength="200"
    ></u-textarea>
    
    <!-- 价格明细 -->
    <PriceBreakdown :pricing="priceDetail" />
    
    <!-- 确认按钮 -->
    <view class="confirm-action">
      <u-button type="primary" @click="confirmBooking">
        确认预约并支付 ¥{{finalPrice}}
      </u-button>
    </view>
  </view>
</template>
```

### 3.5 我的预约页 (beauty/booking/list.vue)

#### 3.5.1 页面设计
```
┌─────────────────────────────┐
│ 状态筛选 Tab                │
│ [全部] [待付款] [已确认] [已完成] │
├─────────────────────────────┤
│ 预约卡片列表                │
│ ┌─────────────────────────┐ │
│ │ [服务图] 深层补水护理   │ │
│ │          技师: 张美美    │ │
│ │          时间: 1月15日   │ │
│ │          状态: 已确认    │ │
│ │ [取消] [改期] [评价]    │ │
│ └─────────────────────────┘ │
├─────────────────────────────┤
│ 分页加载更多                │
└─────────────────────────────┘
```

#### 3.5.2 状态管理
```vue
<template>
  <view class="my-bookings">
    <!-- 状态筛选 -->
    <u-tabs :list="statusTabs" @click="changeStatus"></u-tabs>
    
    <!-- 预约列表 -->
    <view class="booking-list">
      <BookingCard 
        v-for="booking in bookingList" 
        :key="booking.id"
        :booking="booking"
        @cancel="cancelBooking"
        @reschedule="rescheduleBooking"
        @review="reviewBooking"
        @pay="payBooking"
      />
    </view>
    
    <!-- 空状态 -->
    <u-empty v-if="bookingList.length === 0" text="暂无预约记录"></u-empty>
    
    <!-- 加载更多 -->
    <u-loadmore :status="loadStatus" @loadmore="loadMore"></u-loadmore>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useBookingStore } from '@/stores/booking'

const bookingStore = useBookingStore()

const statusTabs = ref([
  { name: '全部', value: '' },
  { name: '待付款', value: 'unpaid' },
  { name: '已确认', value: 'confirmed' },
  { name: '服务中', value: 'in_service' },
  { name: '已完成', value: 'completed' }
])

const currentStatus = ref('')
const bookingList = ref([])
const loadStatus = ref('loadmore')

const loadBookings = async () => {
  try {
    const result = await bookingStore.getMyBookings({
      status: currentStatus.value,
      page: 1,
      pageSize: 10
    })
    bookingList.value = result.list
  } catch (error) {
    uni.showToast({ title: '加载失败', icon: 'error' })
  }
}

onMounted(() => {
  loadBookings()
})
</script>
```

## 4. 公共组件设计

### 4.1 服务卡片组件 (ServiceCard.vue)
```vue
<template>
  <view class="service-card" @click="goToDetail">
    <image :src="service.image" class="service-image"></image>
    <view class="service-info">
      <text class="service-name">{{service.name}}</text>
      <view class="service-tags">
        <u-tag v-for="tag in service.tags" size="mini">{{tag}}</u-tag>
      </view>
      <view class="service-footer">
        <view class="price-info">
          <text class="current-price">¥{{service.price}}</text>
          <text class="original-price">¥{{service.originalPrice}}</text>
        </view>
        <view class="rating-info">
          <u-rate :value="service.rating" size="12" readonly></u-rate>
          <text class="booking-count">{{service.bookingCount}}人预约</text>
        </view>
      </view>
    </view>
  </view>
</template>
```

### 4.2 技师卡片组件 (TechnicianCard.vue)
```vue
<template>
  <view class="technician-card">
    <image :src="technician.avatar" class="technician-avatar"></image>
    <view class="technician-info">
      <text class="technician-name">{{technician.name}}</text>
      <text class="technician-level">{{technician.levelName}}</text>
      <view class="technician-stats">
        <u-rate :value="technician.rating" size="12" readonly></u-rate>
        <text class="experience">{{technician.experienceYears}}年经验</text>
      </view>
      <view class="specialties">
        <text v-for="specialty in technician.specialties" class="specialty-tag">
          {{specialty}}
        </text>
      </view>
    </view>
    <view class="action-buttons">
      <u-button size="mini" @click="viewProfile">查看详情</u-button>
      <u-button type="primary" size="mini" @click="selectTechnician">选择</u-button>
    </view>
  </view>
</template>
```

## 5. 交互设计规范

### 5.1 页面跳转动画
```scss
// 页面切换动画
.page-enter-active, .page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-100%);
}
```

### 5.2 加载状态设计
- **页面加载**: 骨架屏 + 加载动画
- **数据加载**: Loading组件 + 进度提示
- **操作反馈**: Toast提示 + 成功/失败状态

### 5.3 错误处理
- **网络错误**: 重试按钮 + 错误提示
- **数据为空**: 空状态页面 + 引导操作
- **表单验证**: 实时验证 + 错误提示

## 6. 响应式设计

### 6.1 屏幕适配
```scss
// 响应式断点
$phone: 750rpx;
$tablet: 1024rpx;
$desktop: 1200rpx;

// 适配不同屏幕
@media screen and (max-width: #{$phone}) {
  .service-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (min-width: #{$tablet}) {
  .service-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
```

### 6.2 字体适配
```scss
// 动态字体大小
@function rpx($px) {
  @return ($px / 750) * 100vw;
}

.title-text {
  font-size: rpx(32);
  line-height: rpx(44);
}
```

## 7. 性能优化

### 7.1 图片优化
- 使用WebP格式图片
- 实现图片懒加载
- 压缩和缓存策略

### 7.2 代码分割
```javascript
// 路由懒加载
const ServiceDetail = () => import('@/pages/beauty/service/detail.vue')
const BookingConfirm = () => import('@/pages/beauty/booking/confirm.vue')
```

### 7.3 缓存策略
```javascript
// 数据缓存
export const useCacheData = (key, fetcher, ttl = 300000) => {
  const cache = ref(uni.getStorageSync(key))
  const timestamp = ref(uni.getStorageSync(`${key}_timestamp`))
  
  const isExpired = () => {
    return Date.now() - timestamp.value > ttl
  }
  
  const fetchData = async () => {
    if (!cache.value || isExpired()) {
      const data = await fetcher()
      cache.value = data
      timestamp.value = Date.now()
      uni.setStorageSync(key, data)
      uni.setStorageSync(`${key}_timestamp`, timestamp.value)
    }
    return cache.value
  }
  
  return { data: cache, fetchData }
}
```

---

*文档版本: v1.0*
*更新时间: 2025-01-13*
*负责人: uniapp前端工程师*
