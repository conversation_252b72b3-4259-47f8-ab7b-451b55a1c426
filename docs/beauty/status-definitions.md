# 美容预约系统状态定义

## 1. 预约状态 (booking_status)

### 状态流转图
```
pending → confirmed → in_service → completed
    ↓         ↓           ↓
cancelled  cancelled   cancelled
```

### 详细说明

#### `pending` - 待确认
- **含义**：客户提交预约申请，等待技师或管理员确认
- **可执行操作**：
  - 技师/管理员：确认预约、取消预约
  - 客户：取消预约、修改预约信息、支付费用
- **状态流转**：可转为 `confirmed` 或 `cancelled`

#### `confirmed` - 已确认
- **含义**：技师或管理员已确认预约，客户可以按时到店
- **可执行操作**：
  - 技师：开始服务、取消预约
  - 客户：取消预约（在允许时间内）、支付费用
- **状态流转**：可转为 `in_service` 或 `cancelled`

#### `in_service` - 服务中
- **含义**：技师正在为客户提供服务
- **可执行操作**：
  - 技师：完成服务、取消预约（特殊情况）
  - 客户：支付费用
- **状态流转**：可转为 `completed` 或 `cancelled`

#### `completed` - 已完成
- **含义**：服务已完成，可以进行评价和支付
- **可执行操作**：
  - 客户：支付费用、评价服务
  - 技师：查看评价
- **状态流转**：终态，不可再转换

#### `cancelled` - 已取消
- **含义**：预约被取消，可能是客户取消或商家取消
- **可执行操作**：
  - 客户：重新预约
- **注意**：已取消的预约不能支付
- **状态流转**：终态，不可再转换

## 2. 支付状态 (payment_status)

### 状态流转图
```
unpaid → paid
```

### 详细说明

#### `unpaid` - 未支付
- **含义**：预约尚未支付费用
- **触发条件**：预约创建时默认为未支付
- **可执行操作**：客户支付费用
- **状态流转**：可转为 `paid`

#### `paid` - 已支付
- **含义**：预约已完成支付
- **触发条件**：客户成功支付费用后
- **可执行操作**：查看支付记录
- **状态流转**：终态，不可再转换

## 3. 服务状态 (service_status)

### 状态流转图
```
waiting → in_progress → completed
```

### 详细说明

#### `waiting` - 等待中
- **含义**：等待服务开始
- **触发条件**：预约创建时默认为等待中
- **可执行操作**：技师开始服务
- **状态流转**：可转为 `in_progress`

#### `in_progress` - 进行中
- **含义**：服务正在进行
- **触发条件**：技师开始服务后
- **可执行操作**：技师完成服务
- **状态流转**：可转为 `completed`

#### `completed` - 已完成
- **含义**：服务已完成
- **触发条件**：技师完成服务后
- **可执行操作**：客户评价服务
- **状态流转**：终态，不可再转换

## 4. 状态组合规则

### 4.1 预约状态与支付状态的关系
- `pending` + `unpaid`：正常状态（等待支付）
- `pending` + `paid`：正常状态（提前支付）
- `confirmed` + `unpaid`：正常状态（等待支付）
- `confirmed` + `paid`：正常状态（提前支付）
- `in_service` + `unpaid`：正常状态（服务中支付）
- `in_service` + `paid`：正常状态（已提前支付）
- `completed` + `unpaid`：正常状态（等待支付）
- `completed` + `paid`：正常状态（已支付）
- `cancelled` + `unpaid`：正常状态（取消后无需支付）
- `cancelled` + `paid`：异常状态（已取消的预约不应有支付记录）

### 4.2 预约状态与服务状态的关系
- `pending` + `waiting`：正常状态
- `confirmed` + `waiting`：正常状态
- `in_service` + `in_progress`：正常状态
- `completed` + `completed`：正常状态
- `cancelled` + `waiting`：正常状态

## 5. 业务场景示例

### 5.1 标准预约流程（服务后支付）
1. 客户提交预约 → `pending` + `unpaid` + `waiting`
2. 技师确认预约 → `confirmed` + `unpaid` + `waiting`
3. 技师开始服务 → `in_service` + `unpaid` + `in_progress`
4. 技师完成服务 → `completed` + `unpaid` + `completed`
5. 客户支付费用 → `completed` + `paid` + `completed`

### 5.2 提前支付流程
1. 客户提交预约 → `pending` + `unpaid` + `waiting`
2. 客户提前支付 → `pending` + `paid` + `waiting`
3. 技师确认预约 → `confirmed` + `paid` + `waiting`
4. 技师开始服务 → `in_service` + `paid` + `in_progress`
5. 技师完成服务 → `completed` + `paid` + `completed`

### 5.3 服务中支付流程
1. 客户提交预约 → `pending` + `unpaid` + `waiting`
2. 技师确认预约 → `confirmed` + `unpaid` + `waiting`
3. 技师开始服务 → `in_service` + `unpaid` + `in_progress`
4. 客户服务中支付 → `in_service` + `paid` + `in_progress`
5. 技师完成服务 → `completed` + `paid` + `completed`

### 5.4 预约取消流程
1. 客户提交预约 → `pending` + `unpaid` + `waiting`
2. 客户取消预约 → `cancelled` + `unpaid` + `waiting`

## 6. 注意事项

1. **状态一致性**：系统应确保三个状态字段的组合符合业务逻辑
2. **状态流转验证**：每次状态变更都应验证流转的合法性
3. **时间记录**：状态变更时应记录相应的时间字段
4. **权限控制**：不同角色只能执行其权限范围内的状态变更操作
5. **数据完整性**：状态变更应在事务中进行，确保数据一致性
6. **支付策略**：除了取消状态外，其他所有预约状态都允许支付
7. **支付时机**：客户可以在预约的任何阶段进行支付（待确认、已确认、服务中、已完成）
8. **取消限制**：已取消的预约不能进行支付操作 