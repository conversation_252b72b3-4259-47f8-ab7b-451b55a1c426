根据当前的开发进度和已完成的功能，我来为您制定接下来的开发计划。让我先查看一下当前的项目状态和已完成的功能。
Search files...
Ran tool
Ran tool
Ran tool
基于当前的项目状态和已完成的功能，我为您制定以下接下来的开发计划：

## 🎯 接下来的开发计划

### 📋 当前项目状态分析

**已完成的功能：**
- ✅ 美容预约系统前端基础架构
- ✅ 服务详情页面与API集成
- ✅ 技师选择和预约流程优化
- ✅ 服务评价和收藏系统
- ✅ 预约表单页面bug修复
- ✅ 完整的测试验证体系

**当前问题：**
- ⚠️ 主要使用Mock数据，缺少真实后端API
- ⚠️ 预约流程测试通过但需要更多实际场景验证
- ⚠️ 缺少用户认证和权限管理
- ⚠️ 缺少支付功能集成

---

## 🚀 第一阶段：核心功能完善 (2-3周)

### 1. 后端API开发 (优先级：🔥 高)
```go
// 需要开发的核心API
POST /api/beauty/booking/create     // 创建预约
GET  /api/beauty/booking/list       // 预约列表
GET  /api/beauty/service/list       // 服务列表
GET  /api/beauty/technician/list    // 技师列表
GET  /api/beauty/schedule/available // 可预约时间
```

**具体任务：**
- [ ] 创建数据库表结构（参考已有的database-design.md）
- [ ] 实现预约订单服务 (BookingService)
- [ ] 实现排班管理服务 (ScheduleService)
- [ ] 实现服务管理API
- [ ] 实现技师管理API

### 2. 前端与后端API集成 (优先级：🔥 高)
- [ ] 替换Mock数据为真实API调用
- [ ] 完善错误处理和加载状态
- [ ] 优化用户体验和交互反馈
- [ ] 添加数据验证和格式化

### 3. 用户认证集成 (优先级：🔥 高)
- [ ] 集成现有用户系统
- [ ] 实现登录状态检查
- [ ] 添加用户信息预填充
- [ ] 实现用户预约历史

---

## 🎨 第二阶段：用户体验优化 (2周)

### 1. 页面交互优化
- [ ] 实现真实的页面跳转逻辑
- [ ] 完善服务详情页面
- [ ] 优化技师选择流程
- [ ] 改善预约确认页面

### 2. 移动端适配
- [ ] 响应式布局优化
- [ ] 触摸交互改善
- [ ] 性能优化（图片懒加载、虚拟滚动）
- [ ] 微信小程序适配

### 3. 错误处理和用户提示
- [ ] 统一错误处理机制
- [ ] 友好的错误提示页面
- [ ] 网络异常处理
- [ ] 加载状态优化

---

## 💰 第三阶段：支付和订单管理 (2周)

### 1. 支付功能集成
- [ ] 集成现有支付系统
- [ ] 实现微信支付/支付宝支付
- [ ] 支付状态管理
- [ ] 退款处理机制

### 2. 订单管理完善
- [ ] 预约状态流转
- [ ] 订单详情页面
- [ ] 预约变更功能（改期、取消）
- [ ] 预约提醒功能

### 3. 评价系统完善
- [ ] 服务完成后评价
- [ ] 评价图片上传
- [ ] 评价管理和展示
- [ ] 评分统计分析

---

## 📊 第四阶段：管理后台开发 (2-3周)

### 1. 预约管理后台
- [ ] 预约列表管理
- [ ] 预约状态处理
- [ ] 预约冲突解决
- [ ] 预约数据统计

### 2. 技师管理后台
- [ ] 技师信息管理
- [ ] 排班管理
- [ ] 业绩统计
- [ ] 技师评价管理

### 3. 服务管理后台
- [ ] 服务项目管理
- [ ] 价格管理
- [ ] 分类管理
- [ ] 营销活动管理

---

## 🎯 第五阶段：高级功能开发 (2-3周)

### 1. 智能推荐系统
- [ ] 基于用户历史的服务推荐
- [ ] 技师匹配算法
- [ ] 个性化首页内容
- [ ] 智能排班算法

### 2. 营销功能
- [ ] 优惠券系统
- [ ] 会员积分系统
- [ ] 推荐奖励机制
- [ ] 营销活动管理

### 3. 数据分析
- [ ] 用户行为分析
- [ ] 营收数据统计
- [ ] 服务热度分析
- [ ] 技师绩效分析

---

## 🔧 第六阶段：系统优化和部署 (1-2周)

### 1. 性能优化
- [ ] 数据库查询优化
- [ ] 缓存策略实施
- [ ] 前端性能优化
- [ ] 接口响应时间优化

### 2. 安全加固
- [ ] 数据验证加强
- [ ] 权限控制完善
- [ ] 敏感信息加密
- [ ] API安全防护

### 3. 部署和监控
- [ ] 生产环境部署
- [ ] 监控系统搭建
- [ ] 日志管理
- [ ] 备份策略

---

## 📋 近期具体行动计划

### 本周重点任务：
1. **数据库表结构创建**
   - 根据database-design.md创建beauty相关表
   - 编写数据库迁移脚本

2. **核心API开发**
   - 实现预约创建API
   - 实现服务列表API
   - 实现技师列表API

3. **前端API集成**
   - 修改api/beauty/mock.js为真实API调用
   - 测试API集成效果

### 下周重点任务：
1. **预约流程完善**
   - 完善时间冲突检测
   - 实现预约确认流程
   - 添加用户认证检查

2. **用户体验优化**
   - 优化页面加载速度
   - 改善错误提示
   - 完善交互反馈

## 🎯 成功指标

- [ ] 用户可以完整完成预约流程
- [ ] 后端API响应时间 < 500ms
- [ ] 前端页面加载时间 < 3秒
- [ ] 预约成功率 > 95%
- [ ] 用户满意度 > 4.5/5

您希望从哪个阶段开始？我建议优先从**第一阶段的后端API开发**开始，这样可以让整个系统真正运行起来。

## 🗄️ 美容预约系统数据库设计

### 📊 1. 表结构设计

#### 1.1 复用现有表
```sql
-- 复用现有用户表
user (现有)
├── id (用户ID)
├── username (用户名)
├── phone (手机号)
├── nickname (昵称)
├── avatar (头像)
├── gender (性别)
├── birthday (生日)
├── balance (余额)
├── points (积分)
└── level (会员等级)

-- 复用现有分类表
category (现有)
├── id (分类ID)
├── module = 'beauty' (模块标识)
├── parent_id (父级ID)
├── name (分类名称)
├── icon (分类图标)
├── level (层级)
└── sort (排序)
```

#### 1.2 新增美容业务表

**美容服务表 (beauty_service)**
```sql
CREATE TABLE beauty_service (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '服务ID',
    category_id BIGINT NOT NULL COMMENT '分类ID(关联category表)',
    name VARCHAR(200) NOT NULL COMMENT '服务名称',
    subtitle VARCHAR(300) COMMENT '服务副标题',
    description TEXT COMMENT '服务描述',
    images JSON COMMENT '服务图片数组',
    
    -- 价格信息
    price DECIMAL(10,2) NOT NULL COMMENT '基础价格',
    original_price DECIMAL(10,2) COMMENT '原价',
    
    -- 服务属性
    duration INT NOT NULL COMMENT '标准服务时长(分钟)',
    
    -- 预约规则
    need_technician TINYINT DEFAULT 1 COMMENT '是否需要指定技师',
    max_advance_days INT DEFAULT 30 COMMENT '最大提前预约天数',
    min_advance_hours INT DEFAULT 2 COMMENT '最小提前预约小时数',
    allow_cancel_hours INT DEFAULT 24 COMMENT '允许取消的提前小时数',
    
    -- 适用条件
    gender_limit TINYINT DEFAULT 0 COMMENT '性别限制(0:不限 1:男 2:女)',
    age_min INT DEFAULT 0 COMMENT '最小年龄限制',
    age_max INT DEFAULT 100 COMMENT '最大年龄限制',
    
    -- 服务特性
    tags JSON COMMENT '服务标签',
    contraindications TEXT COMMENT '禁忌症说明',
    preparation_notes TEXT COMMENT '预约前准备事项',
    
    -- 统计字段
    booking_count INT DEFAULT 0 COMMENT '预约次数',
    rating_avg DECIMAL(3,2) DEFAULT 0 COMMENT '平均评分',
    rating_count INT DEFAULT 0 COMMENT '评价数量',
    
    -- 状态控制
    status TINYINT DEFAULT 1 COMMENT '状态(0:下架 1:上架)',
    sort INT DEFAULT 0 COMMENT '排序权重',
    is_hot TINYINT DEFAULT 0 COMMENT '是否热门',
    is_new TINYINT DEFAULT 0 COMMENT '是否新品',
    
    -- 系统字段
    is_delete TINYINT DEFAULT 0 COMMENT '是否删除',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category_id (category_id),
    INDEX idx_status (status),
    INDEX idx_price (price),
    INDEX idx_duration (duration),
    INDEX idx_hot_new (is_hot, is_new)
) COMMENT='美容服务表';
```

**技师表 (beauty_technician)**
```sql
CREATE TABLE beauty_technician (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '技师ID',
    name VARCHAR(100) NOT NULL COMMENT '技师姓名',
    avatar VARCHAR(255) COMMENT '技师头像',
    gender TINYINT COMMENT '性别(1:男 2:女)',
    age INT COMMENT '年龄',
    phone VARCHAR(20) COMMENT '联系电话',
    
    -- 专业信息
    level VARCHAR(50) NOT NULL COMMENT '技师等级',
    experience INT DEFAULT 0 COMMENT '从业经验(年)',
    specialties JSON COMMENT '专长领域',
    certificates JSON COMMENT '资格证书',
    introduction TEXT COMMENT '个人简介',
    
    -- 服务能力
    service_ids JSON COMMENT '可提供的服务ID数组',
    work_hours JSON COMMENT '工作时间配置',
    
    -- 统计数据
    total_bookings INT DEFAULT 0 COMMENT '总预约次数',
    completed_bookings INT DEFAULT 0 COMMENT '完成预约次数',
    rating_avg DECIMAL(3,2) DEFAULT 0 COMMENT '平均评分',
    rating_count INT DEFAULT 0 COMMENT '评价数量',
    
    -- 价格设置
    extra_fee DECIMAL(10,2) DEFAULT 0 COMMENT '技师附加费',
    
    -- 状态控制
    status TINYINT DEFAULT 1 COMMENT '状态(0:离职 1:在职 2:请假)',
    is_featured TINYINT DEFAULT 0 COMMENT '是否明星技师',
    sort INT DEFAULT 0 COMMENT '排序权重',
    
    -- 系统字段
    is_delete TINYINT DEFAULT 0 COMMENT '是否删除',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_status (status),
    INDEX idx_level (level),
    INDEX idx_rating (rating_avg),
    INDEX idx_featured (is_featured)
) COMMENT='美容技师表';
```

**技师排班表 (beauty_schedule)**
```sql
CREATE TABLE beauty_schedule (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '排班ID',
    technician_id BIGINT NOT NULL COMMENT '技师ID',
    work_date DATE NOT NULL COMMENT '工作日期',
    
    -- 时间段配置
    start_time TIME NOT NULL COMMENT '开始时间',
    end_time TIME NOT NULL COMMENT '结束时间',
    break_start TIME COMMENT '休息开始时间',
    break_end TIME COMMENT '休息结束时间',
    
    -- 排班类型
    schedule_type VARCHAR(20) DEFAULT 'normal' COMMENT '排班类型(normal:正常 overtime:加班 leave:请假)',
    
    -- 状态控制
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    
    -- 系统字段
    is_delete TINYINT DEFAULT 0 COMMENT '是否删除',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_technician_date (technician_id, work_date),
    INDEX idx_work_date (work_date),
    INDEX idx_technician_id (technician_id)
) COMMENT='技师排班表';
```

**预约订单表 (beauty_booking)**
```sql
CREATE TABLE beauty_booking (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '预约ID',
    booking_no VARCHAR(32) UNIQUE NOT NULL COMMENT '预约编号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    service_id BIGINT NOT NULL COMMENT '服务ID',
    technician_id BIGINT COMMENT '技师ID',
    
    -- 预约时间信息
    booking_date DATE NOT NULL COMMENT '预约日期',
    start_time TIME NOT NULL COMMENT '开始时间',
    end_time TIME NOT NULL COMMENT '结束时间',
    duration INT NOT NULL COMMENT '服务时长(分钟)',
    
    -- 客户信息
    contact_name VARCHAR(100) NOT NULL COMMENT '联系人姓名',
    contact_phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    customer_gender TINYINT COMMENT '客户性别',
    customer_age INT COMMENT '客户年龄',
    
    -- 个性化需求
    skin_type VARCHAR(50) COMMENT '肌肤类型',
    skin_concerns JSON COMMENT '肌肤问题',
    allergies TEXT COMMENT '过敏史',
    special_requests TEXT COMMENT '特殊要求',
    
    -- 价格信息
    service_price DECIMAL(10,2) NOT NULL COMMENT '服务价格',
    technician_fee DECIMAL(10,2) DEFAULT 0 COMMENT '技师费用',
    discount_amount DECIMAL(10,2) DEFAULT 0 COMMENT '优惠金额',
    final_price DECIMAL(10,2) NOT NULL COMMENT '实付金额',
    
    -- 优惠信息
    coupon_id BIGINT COMMENT '优惠券ID',
    coupon_amount DECIMAL(10,2) DEFAULT 0 COMMENT '优惠券金额',
    
    -- 状态管理
    booking_status VARCHAR(20) DEFAULT 'pending' COMMENT '预约状态',
    payment_status VARCHAR(20) DEFAULT 'unpaid' COMMENT '支付状态',
    service_status VARCHAR(20) DEFAULT 'waiting' COMMENT '服务状态',
    
    -- 时间记录
    confirmed_time DATETIME COMMENT '确认时间',
    checkin_time DATETIME COMMENT '签到时间',
    service_start_time DATETIME COMMENT '服务开始时间',
    service_end_time DATETIME COMMENT '服务结束时间',
    cancelled_time DATETIME COMMENT '取消时间',
    cancel_reason TEXT COMMENT '取消原因',
    
    -- 评价信息
    rating TINYINT COMMENT '评分(1-5)',
    review TEXT COMMENT '评价内容',
    review_images JSON COMMENT '评价图片',
    review_time DATETIME COMMENT '评价时间',
    
    -- 系统字段
    is_delete TINYINT DEFAULT 0 COMMENT '是否删除',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_service_id (service_id),
    INDEX idx_technician_id (technician_id),
    INDEX idx_booking_date (booking_date),
    INDEX idx_booking_status (booking_status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_booking_no (booking_no)
) COMMENT='美容预约订单表';
```

**服务评价表 (beauty_review)**
```sql
CREATE TABLE beauty_review (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '评价ID',
    booking_id BIGINT NOT NULL COMMENT '预约订单ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    service_id BIGINT NOT NULL COMMENT '服务ID',
    technician_id BIGINT COMMENT '技师ID',
    
    -- 评价内容
    service_rating TINYINT NOT NULL COMMENT '服务评分(1-5)',
    technician_rating TINYINT COMMENT '技师评分(1-5)',
    environment_rating TINYINT COMMENT '环境评分(1-5)',
    overall_rating TINYINT NOT NULL COMMENT '综合评分(1-5)',
    
    review_content TEXT COMMENT '评价内容',
    review_images JSON COMMENT '评价图片',
    
    -- 评价标签
    review_tags JSON COMMENT '评价标签',
    
    -- 状态控制
    status TINYINT DEFAULT 1 COMMENT '状态(0:隐藏 1:显示)',
    is_anonymous TINYINT DEFAULT 0 COMMENT '是否匿名',
    
    -- 系统字段
    is_delete TINYINT DEFAULT 0 COMMENT '是否删除',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_booking_id (booking_id),
    INDEX idx_user_id (user_id),
    INDEX idx_service_id (service_id),
    INDEX idx_technician_id (technician_id),
    INDEX idx_overall_rating (overall_rating)
) COMMENT='服务评价表';
```

**优惠券表 (beauty_coupon)**
```sql
CREATE TABLE beauty_coupon (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '优惠券ID',
    name VARCHAR(200) NOT NULL COMMENT '优惠券名称',
    description TEXT COMMENT '使用说明',
    
    -- 优惠券类型
    type VARCHAR(20) NOT NULL COMMENT '类型(amount:满减 percent:折扣 service:服务券)',
    value DECIMAL(10,2) NOT NULL COMMENT '优惠值',
    min_amount DECIMAL(10,2) DEFAULT 0 COMMENT '最低消费金额',
    max_discount DECIMAL(10,2) COMMENT '最大优惠金额(折扣券用)',
    
    -- 适用范围
    applicable_services JSON COMMENT '适用服务ID数组',
    applicable_technicians JSON COMMENT '适用技师ID数组',
    
    -- 发放规则
    total_quantity INT DEFAULT 0 COMMENT '发放总量(0:不限)',
    used_quantity INT DEFAULT 0 COMMENT '已使用数量',
    user_limit INT DEFAULT 1 COMMENT '每用户限领数量',
    
    -- 时间限制
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    validity_days INT COMMENT '有效天数(领取后)',
    
    -- 状态控制
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    
    -- 系统字段
    is_delete TINYINT DEFAULT 0 COMMENT '是否删除',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_time_range (start_time, end_time)
) COMMENT='优惠券表';
```

**用户优惠券表 (beauty_user_coupon)**
```sql
CREATE TABLE beauty_user_coupon (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    coupon_id BIGINT NOT NULL COMMENT '优惠券ID',
    
    -- 使用状态
    status TINYINT DEFAULT 1 COMMENT '状态(1:未使用 2:已使用 3:已过期)',
    used_time DATETIME COMMENT '使用时间',
    booking_id BIGINT COMMENT '使用的预约订单ID',
    
    -- 时间信息
    receive_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
    expire_time DATETIME NOT NULL COMMENT '过期时间',
    
    -- 系统字段
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_coupon_id (coupon_id),
    INDEX idx_status (status),
    INDEX idx_expire_time (expire_time)
) COMMENT='用户优惠券表';
```

### 📈 2. 数据库关系图