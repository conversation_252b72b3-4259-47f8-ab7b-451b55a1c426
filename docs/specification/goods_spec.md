# 商品规格与购物车开发规范

## 一、商品规格类型

### 1.1 规格类型定义
- `specType = 1`: 无规格商品
- `specType = 2`: 多规格商品

### 1.2 规格类型处理规范
#### 1.2.1 无规格商品 (specType = 1)
- 库存判断：使用商品总库存 `goods.stock`
- 加入购物车：不需要选择规格，直接使用商品ID
- 请求参数：
  ```javascript
  {
    goodsId: xxx,
    quantity: 1
  }
  ```

#### 1.2.2 多规格商品 (specType = 2)
- 库存判断：使用选中规格对应的 SKU 库存
- 加入购物车：必须选择完所有规格
- 请求参数：
  ```javascript
  {
    goodsId: xxx,
    quantity: 1,
    skuCode: "xxx",
    specsJson: "{\"颜色\":\"蓝色\",\"尺寸\":\"M\"}"
  }
  ```

## 二、规格数据结构

### 2.1 商品规格数据
```javascript
goods.specs = [
  {
    name: "颜色",
    values: ["红色", "蓝色", "黑色"]
  },
  {
    name: "尺寸",
    values: ["S", "M", "L"]
  }
]
```

### 2.2 SKU 库存数据
```javascript
goods.specStock = [
  {
    skuCode: "sku1",
    specValueStr: "{\"颜色\":\"红色\",\"尺寸\":\"S\"}",
    stock: 100,
    price: 199
  }
]
```

## 三、规格选择处理

### 3.1 规格匹配逻辑
```javascript
// 查找对应的SKU
const skuItem = goods.specStock.find(item => {
    const itemSpecs = JSON.parse(item.specValueStr)
    return Object.keys(selectedSpecs).every(key => 
        itemSpecs[key] === selectedSpecs[key]
    )
})
```

### 3.2 库存判断规范
1. 无规格商品：
```javascript
if (goods.stock < 1) {
    return "商品库存不足"
}
```

2. 多规格商品：
```javascript
if (!skuItem || skuItem.stock < 1) {
    return "该规格库存不足"
}
```

## 四、购物车操作规范

### 4.1 加入购物车前置检查
1. 登录状态检查
```javascript
if (!checkLogin()) {
    提示并跳转登录页
    return
}
```

2. 规格选择检查（多规格商品）
```javascript
if (!hasSelectedAllSpecs) {
    提示选择规格
    return
}
```

3. 库存检查
```javascript
if (库存不足) {
    提示库存不足
    return
}
```

### 4.2 错误处理规范
| 错误类型 | 提示信息 | 处理方式 |
|---------|---------|---------|
| 未登录 | 请先登录 | 跳转登录页 |
| 规格未选择 | 请选择商品规格 | 停留当前页 |
| 库存不足 | 商品库存不足 | 停留当前页 |
| 规格错误 | 规格信息错误 | 停留当前页 |
| 接口错误 | 添加失败 | 显示具体错误 |

### 4.3 成功处理规范
1. 显示成功提示
2. 更新购物车数量
3. 重置相关状态（如需要）

## 五、接口规范

### 5.1 添加购物车接口
- 路径：`/cart/add`
- 方法：POST
- 请求头：需要携带 token
- 参数格式：
```javascript
{
    goodsId: number,   // 商品ID
    quantity: number,  // 数量
    skuCode?: string, // 规格商品必传
    specsJson?: string // 规格商品必传
}
```

### 5.2 购物车数量接口
- 路径：`/cart/count`
- 方法：GET
- 请求头：需要携带 token
- 返回格式：
```javascript
{
    count: number  // 购物车商品总数
}
```

## 六、注意事项

1. 接口地址配置：
   - 确保 BASE_URL 配置正确
   - 避免重复的 `/api` 前缀

2. 请求方法使用：
   - 使用统一封装的 request 方法
   - 正确使用 request.post/get 等方法

3. 数据格式：
   - 规格数据序列化必须使用 JSON.stringify
   - 确保规格数据格式与后端一致

4. 状态管理：
   - 及时更新购物车数量
   - 正确处理规格选择状态

5. 用户体验：
   - 添加操作要有加载状态
   - 错误提示要明确具体原因
   - 成功提示要简洁明了 