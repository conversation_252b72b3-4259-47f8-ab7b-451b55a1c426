-- 数据迁移脚本：将现有分销关系迁移到通用用户关系表
-- 执行前请先备份数据库

-- 1. 创建通用用户关系表（如果不存在）
CREATE TABLE IF NOT EXISTS `user_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `related_user_id` bigint(20) NOT NULL COMMENT '关联用户ID',
  `relation_type` varchar(20) NOT NULL COMMENT '关系类型：invite-邀请，friend-好友，share-分享裂变，follow-关注，block-拉黑',
  `direction` varchar(10) NOT NULL COMMENT '关系方向：one_way-单向，two_way-双向',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-有效 0-无效 2-待确认',
  `source` varchar(20) DEFAULT NULL COMMENT '关系来源：register-注册，manual-手动添加，share-分享，qr_code-二维码，search-搜索，import-导入',
  `metadata` json DEFAULT NULL COMMENT '扩展数据（JSON格式）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间（可选，用于临时关系）',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_related_user_id` (`related_user_id`),
  KEY `idx_relation_type` (`relation_type`),
  KEY `idx_status` (`status`),
  KEY `idx_user_relation_type` (`user_id`, `relation_type`),
  KEY `idx_related_relation_type` (`related_user_id`, `relation_type`),
  KEY `idx_create_time` (`create_time`),
  UNIQUE KEY `uk_user_related_type` (`user_id`, `related_user_id`, `relation_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通用用户关系表';

-- 2. 备份现有分销关系数据
CREATE TABLE IF NOT EXISTS `share_relation_backup` AS SELECT * FROM `share_relation`;

-- 3. 迁移分销关系数据到通用关系表
INSERT INTO `user_relation` (
    `user_id`, 
    `related_user_id`, 
    `relation_type`, 
    `direction`, 
    `status`, 
    `source`, 
    `metadata`, 
    `create_time`, 
    `update_time`
)
SELECT 
    sr.inviter_id AS user_id,           -- 邀请者作为user_id
    sr.user_id AS related_user_id,      -- 被邀请者作为related_user_id
    'invite' AS relation_type,          -- 关系类型为邀请
    'one_way' AS direction,             -- 单向关系
    CASE 
        WHEN sr.status = 1 THEN 1       -- 有效
        ELSE 0                          -- 无效
    END AS status,
    CASE 
        WHEN sr.bind_type = 'register' THEN 'register'
        WHEN sr.bind_type = 'share' THEN 'share'
        ELSE 'manual'
    END AS source,
    JSON_OBJECT(
        'bind_type', sr.bind_type,
        'level', 1
    ) AS metadata,
    sr.create_time,
    NOW() AS update_time
FROM `share_relation` sr
WHERE sr.inviter_id IS NOT NULL 
  AND sr.inviter_id > 0
  AND sr.user_id IS NOT NULL 
  AND sr.user_id > 0
  AND sr.inviter_id != sr.user_id       -- 排除自己邀请自己的异常数据
ON DUPLICATE KEY UPDATE
    status = VALUES(status),
    source = VALUES(source),
    metadata = VALUES(metadata),
    update_time = VALUES(update_time);

-- 4. 验证迁移结果
SELECT 
    '原分销关系表记录数' AS description,
    COUNT(*) AS count
FROM `share_relation`
WHERE inviter_id IS NOT NULL AND inviter_id > 0

UNION ALL

SELECT 
    '新关系表邀请关系记录数' AS description,
    COUNT(*) AS count
FROM `user_relation`
WHERE relation_type = 'invite';

-- 5. 检查数据一致性
SELECT 
    sr.user_id,
    sr.inviter_id,
    sr.status AS old_status,
    ur.status AS new_status,
    sr.create_time AS old_create_time,
    ur.create_time AS new_create_time
FROM `share_relation` sr
LEFT JOIN `user_relation` ur ON (
    ur.user_id = sr.inviter_id 
    AND ur.related_user_id = sr.user_id 
    AND ur.relation_type = 'invite'
)
WHERE ur.id IS NULL  -- 找出未成功迁移的记录
LIMIT 10;

-- 6. 创建视图以兼容旧的查询方式（可选）
CREATE OR REPLACE VIEW `share_relation_view` AS
SELECT 
    ur.id,
    ur.related_user_id AS user_id,
    ur.user_id AS inviter_id,
    ur.status,
    CASE 
        WHEN ur.source = 'register' THEN 'register'
        WHEN ur.source = 'share' THEN 'share'
        ELSE 'manual'
    END AS bind_type,
    ur.create_time
FROM `user_relation` ur
WHERE ur.relation_type = 'invite';

-- 7. 统计信息
SELECT 
    '迁移完成统计' AS title,
    (SELECT COUNT(*) FROM share_relation WHERE inviter_id IS NOT NULL) AS original_count,
    (SELECT COUNT(*) FROM user_relation WHERE relation_type = 'invite') AS migrated_count,
    (SELECT COUNT(*) FROM user_relation WHERE relation_type = 'invite') - 
    (SELECT COUNT(*) FROM share_relation WHERE inviter_id IS NOT NULL) AS difference;

-- 注意事项：
-- 1. 执行前请备份数据库
-- 2. 建议在测试环境先执行验证
-- 3. 迁移完成后，可以考虑重命名或删除旧的 share_relation 表
-- 4. 需要更新应用代码以使用新的通用关系表
-- 5. 如果有其他系统依赖旧表结构，请保留视图或做相应调整 