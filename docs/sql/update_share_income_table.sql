-- 更新share_income表结构，使其符合佣金记录表需求
-- 参考referrer_commission_record表结构，去掉activity_id字段

-- 先备份现有数据（如果有的话）
-- CREATE TABLE share_income_backup AS SELECT * FROM share_income;

-- 删除现有表
DROP TABLE IF EXISTS `share_income`;

-- 创建新的share_income表
CREATE TABLE `share_income` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `referrer_id` bigint(20) DEFAULT NULL COMMENT '推荐官ID',
  `user_id` bigint(20) NOT NULL COMMENT '推荐官用户ID',
  `invitee_id` bigint(20) DEFAULT NULL COMMENT '被邀请人ID',
  `invitee_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '被邀请人姓名',
  `commission_amount` decimal(10,2) NOT NULL COMMENT '佣金金额',
  `commission_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '佣金类型(register:注册,purchase:购买,order:订单)',
  `commission_status` tinyint(1) DEFAULT '0' COMMENT '佣金状态(0-待结算,1-已结算,2-已取消)',
  `source_order_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '来源订单号',
  `order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '订单金额，注册佣金时为0',
  `commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '佣金比例(%)',
  `level` tinyint(1) NOT NULL DEFAULT '1' COMMENT '分销层级(1-一级 2-二级)',
  `unfreeze_time` datetime DEFAULT NULL COMMENT '解冻时间',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_referrer_id` (`referrer_id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_commission_status` (`commission_status`) USING BTREE,
  KEY `idx_commission_type` (`commission_type`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_invitee_id` (`invitee_id`) USING BTREE,
  KEY `idx_source_order_no` (`source_order_no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='分销收益记录表'; 