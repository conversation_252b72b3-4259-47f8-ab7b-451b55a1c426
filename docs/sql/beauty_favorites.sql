-- 美容服务收藏表
DROP TABLE IF EXISTS `beauty_service_favorites`;
CREATE TABLE `beauty_service_favorites` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `service_id` bigint(20) NOT NULL COMMENT '服务ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除(0:否 1:是)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_service` (`user_id`, `service_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_service_id` (`service_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='美容服务收藏表';

-- 插入测试数据
INSERT INTO `beauty_service_favorites` (`user_id`, `service_id`, `create_time`) VALUES
(1, 1, '2024-01-10 10:00:00'),
(1, 3, '2024-01-12 14:30:00'),
(1, 5, '2024-01-15 16:20:00'),
(2, 2, '2024-01-11 09:15:00'),
(2, 4, '2024-01-13 11:45:00'); 