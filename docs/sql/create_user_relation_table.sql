-- 通用用户关系表
CREATE TABLE `user_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `related_user_id` bigint(20) NOT NULL COMMENT '关联用户ID',
  `relation_type` varchar(20) NOT NULL COMMENT '关系类型：invite-邀请，friend-好友，share-分享裂变，follow-关注，block-拉黑',
  `direction` varchar(10) NOT NULL COMMENT '关系方向：one_way-单向，two_way-双向',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-有效 0-无效 2-待确认',
  `source` varchar(20) DEFAULT NULL COMMENT '关系来源：register-注册，manual-手动添加，share-分享，qr_code-二维码，search-搜索，import-导入',
  `metadata` json DEFAULT NULL COMMENT '扩展数据（JSON格式）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间（可选，用于临时关系）',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_related_user_id` (`related_user_id`),
  KEY `idx_relation_type` (`relation_type`),
  KEY `idx_status` (`status`),
  KEY `idx_user_relation_type` (`user_id`, `relation_type`),
  KEY `idx_related_relation_type` (`related_user_id`, `relation_type`),
  KEY `idx_create_time` (`create_time`),
  UNIQUE KEY `uk_user_related_type` (`user_id`, `related_user_id`, `relation_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通用用户关系表';

-- 插入示例数据
INSERT INTO `user_relation` (`user_id`, `related_user_id`, `relation_type`, `direction`, `status`, `source`, `metadata`, `create_time`, `update_time`) VALUES
(1, 2, 'invite', 'one_way', 1, 'register', '{"level": 1, "commission_rate": 0.1}', NOW(), NOW()),
(1, 3, 'friend', 'two_way', 1, 'manual', '{"nickname": "好友"}', NOW(), NOW()),
(3, 1, 'friend', 'two_way', 1, 'manual', '{"nickname": "好友"}', NOW(), NOW()),
(2, 4, 'invite', 'one_way', 1, 'share', '{"level": 2, "commission_rate": 0.05}', NOW(), NOW()),
(1, 5, 'follow', 'one_way', 1, 'manual', NULL, NOW(), NOW());

-- 关系类型说明
-- invite: 邀请关系（分销/推荐）- 通常是单向的，A邀请B，A是B的上级
-- friend: 好友关系 - 通常是双向的，需要双方确认
-- share: 分享裂变关系 - 通过分享链接建立的关系
-- follow: 关注关系 - 单向的，A关注B
-- block: 拉黑关系 - 单向的，A拉黑B

-- 关系方向说明
-- one_way: 单向关系，只有一条记录
-- two_way: 双向关系，需要创建两条记录（正向和反向）

-- 状态说明
-- 0: 无效/已删除
-- 1: 有效/正常
-- 2: 待确认（如好友申请）

-- 来源说明
-- register: 注册时建立（如邀请码注册）
-- manual: 手动添加（如搜索添加好友）
-- share: 分享建立（如分享商品链接）
-- qr_code: 二维码扫描
-- search: 搜索添加
-- import: 导入通讯录 