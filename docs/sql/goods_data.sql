-- 商品测试数据
-- 插入商品数据到 goods 表

INSERT INTO `goods` (`id`, `name`, `description`, `image`, `price`, `original_price`, `stock`, `sales`, `status`, `category_id`, `created_at`, `updated_at`) VALUES
(1, '苹果iPhone 15 Pro Max', '全新苹果iPhone 15 Pro Max，搭载A17 Pro芯片，钛金属设计', '/static/images/category/mobile.png', 9999.00, 11999.00, 100, 256, 1, 1, NOW(), NOW()),
(2, '华为Mate 60 Pro', '华为Mate 60 Pro，麒麟9000S芯片，卫星通话功能', '/static/images/category/mobile.png', 6999.00, 7999.00, 80, 189, 1, 1, NOW(), NOW()),
(3, '小米14 Ultra', '小米14 Ultra，徕卡影像，骁龙8 Gen3处理器', '/static/images/category/mobile.png', 5999.00, 6999.00, 120, 345, 1, 1, NOW(), NOW()),
(4, 'MacBook Pro 16英寸', 'Apple MacBook Pro 16英寸，M3 Max芯片，专业级性能', '/static/images/category/computer.png', 19999.00, 22999.00, 50, 78, 1, 2, NOW(), NOW()),
(5, '联想ThinkPad X1', '联想ThinkPad X1 Carbon，商务办公首选', '/static/images/category/computer.png', 12999.00, 14999.00, 60, 123, 1, 2, NOW(), NOW()),
(6, '戴尔XPS 13', '戴尔XPS 13，轻薄便携，高性能笔记本', '/static/images/category/computer.png', 8999.00, 10999.00, 40, 89, 1, 2, NOW(), NOW()),
(7, 'AirPods Pro 2', '苹果AirPods Pro 2代，主动降噪，空间音频', '/static/images/category/accessories.png', 1899.00, 2199.00, 200, 567, 1, 3, NOW(), NOW()),
(8, '索尼WH-1000XM5', '索尼WH-1000XM5头戴式降噪耳机，音质出众', '/static/images/category/accessories.png', 2399.00, 2799.00, 150, 234, 1, 3, NOW(), NOW()),
(9, 'PlayStation 5', '索尼PlayStation 5游戏主机，次世代游戏体验', '/static/images/category/game.png', 3999.00, 4599.00, 30, 456, 1, 4, NOW(), NOW()),
(10, 'Nintendo Switch OLED', '任天堂Switch OLED版，便携游戏机', '/static/images/category/game.png', 2599.00, 2999.00, 80, 678, 1, 4, NOW(), NOW()),
(11, '小米智能手环8', '小米智能手环8，健康监测，运动追踪', '/static/images/category/smart.png', 299.00, 399.00, 500, 1234, 1, 5, NOW(), NOW()),
(12, '华为Watch GT 4', '华为Watch GT 4智能手表，专业运动指导', '/static/images/category/smart.png', 1688.00, 1988.00, 300, 789, 1, 5, NOW(), NOW()),
(13, '罗技MX Master 3S', '罗技MX Master 3S无线鼠标，办公利器', '/static/images/category/accessories.png', 699.00, 799.00, 400, 345, 1, 3, NOW(), NOW()),
(14, '机械革命键盘', '机械革命机械键盘，青轴手感，游戏办公两用', '/static/images/category/accessories.png', 399.00, 499.00, 250, 567, 1, 3, NOW(), NOW()),
(15, '小米电视65英寸', '小米电视65英寸4K智能电视，影院级体验', '/static/images/category/smart.png', 3299.00, 3799.00, 100, 234, 1, 5, NOW(), NOW()),
(16, '海尔冰箱', '海尔智能冰箱，变频节能，大容量设计', '/static/images/category/smart.png', 4999.00, 5999.00, 50, 123, 1, 5, NOW(), NOW()),
(17, '戴森吸尘器V15', '戴森V15无线吸尘器，强劲吸力，智能检测', '/static/images/category/smart.png', 4690.00, 5290.00, 80, 189, 1, 5, NOW(), NOW()),
(18, '苹果iPad Pro 12.9', '苹果iPad Pro 12.9英寸，M2芯片，专业创作', '/static/images/category/computer.png', 8999.00, 10999.00, 70, 345, 1, 2, NOW(), NOW()),
(19, '三星Galaxy S24 Ultra', '三星Galaxy S24 Ultra，AI摄影，S Pen手写笔', '/static/images/category/mobile.png', 8999.00, 9999.00, 90, 456, 1, 1, NOW(), NOW()),
(20, 'OPPO Find X7 Ultra', 'OPPO Find X7 Ultra，哈苏影像，旗舰性能', '/static/images/category/mobile.png', 5999.00, 6999.00, 110, 678, 1, 1, NOW(), NOW());

-- 插入分类数据（如果不存在）
INSERT IGNORE INTO `category` (`id`, `name`, `image`, `sort`, `status`, `level`, `parent_id`, `created_at`, `updated_at`) VALUES
(1, '手机数码', '/static/images/category/mobile.png', 1, 1, 1, 0, NOW(), NOW()),
(2, '电脑办公', '/static/images/category/computer.png', 2, 1, 1, 0, NOW(), NOW()),
(3, '数码配件', '/static/images/category/accessories.png', 3, 1, 1, 0, NOW(), NOW()),
(4, '游戏设备', '/static/images/category/game.png', 4, 1, 1, 0, NOW(), NOW()),
(5, '智能家居', '/static/images/category/smart.png', 5, 1, 1, 0, NOW(), NOW()); 