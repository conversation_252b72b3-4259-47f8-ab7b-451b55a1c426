-- Banner test data
USE wnsys;

-- Clear existing banner data
DELETE FROM shop_banner WHERE position = 1;

-- Insert homepage banner data
INSERT INTO shop_banner (title, image, url, position, type, target_id, sort, status, is_delete) VALUES
('Featured Products', '/static/images/category/smart.png', '', 1, 0, NULL, 100, 1, 0),
('Hot Recommendations', '/static/images/category/mobile.png', '', 1, 0, NULL, 90, 1, 0),
('New Arrivals', '/static/images/category/computer.png', '', 1, 0, NULL, 80, 1, 0),
('Gaming Zone', '/static/images/category/game.png', '', 1, 0, NULL, 70, 1, 0),
('Accessories', '/static/images/category/accessories.png', '', 1, 0, NULL, 60, 1, 0);

-- View inserted results
SELECT * FROM shop_banner WHERE position = 1 ORDER BY sort DESC; 