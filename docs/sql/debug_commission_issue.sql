-- 调试注册佣金问题的SQL查询

-- 1. 检查用户信息
SELECT 'User Info' as type, id, nickname, phone, create_time 
FROM user 
WHERE id IN (17, 25)
ORDER BY id;

-- 2. 检查分销员信息
SELECT 'Distributor Info' as type, id, user_id, status, level, total_income, create_time
FROM share_user 
WHERE user_id IN (17, 25)
ORDER BY user_id;

-- 3. 检查邀请关系
SELECT 'Invite Relations' as type, id, user_id as inviter_id, related_user_id as invitee_id, 
       relation_type, status, create_time
FROM user_relation 
WHERE (user_id IN (17, 25) OR related_user_id IN (17, 25))
  AND relation_type = 'invite'
ORDER BY create_time DESC;

-- 4. 检查分销设置（假设在sys_config表中）
SELECT 'Share Settings' as type, config_key, config_value, remark
FROM sys_config 
WHERE config_key LIKE '%share%' OR config_key LIKE '%distribution%' OR config_key LIKE '%commission%'
ORDER BY config_key;

-- 5. 检查现有的收益记录
SELECT 'Income Records' as type, id, user_id, invitee_id, invitee_name, 
       commission_amount, commission_type, commission_status, create_time
FROM share_income 
WHERE user_id IN (17, 25) OR invitee_id IN (17, 25)
ORDER BY create_time DESC;

-- 6. 检查表结构是否正确
SHOW CREATE TABLE share_income;

-- 7. 如果需要手动创建分销员
-- INSERT INTO share_user (user_id, level, status, total_income, available_balance, frozen_balance, team_count, create_time, update_time)
-- VALUES (17, 1, 1, 0.00, 0.00, 0.00, 0, NOW(), NOW());

-- 8. 如果需要手动创建邀请关系
-- INSERT INTO user_relation (user_id, related_user_id, relation_type, direction, status, source, create_time, update_time)
-- VALUES (17, 25, 'invite', 'one_way', 1, 'register', NOW(), NOW());

-- 9. 检查分销设置表
SELECT 'Share Setting Table' as type, * FROM share_setting LIMIT 1; 