-- 堂食外卖相关表结构

-- 用餐类型表
CREATE TABLE `shop_dining_type` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(50) NOT NULL COMMENT '类型名称(堂食/外卖)',
  `code` varchar(20) NOT NULL COMMENT '类型编码(dine_in/takeout)',
  `icon` varchar(255) DEFAULT NULL COMMENT '图标',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(0:禁用 1:启用)',
  `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用餐类型表';

-- 餐桌表
CREATE TABLE `shop_dining_table` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '餐桌ID',
  `table_no` varchar(20) NOT NULL COMMENT '餐桌编号',
  `table_name` varchar(50) NOT NULL COMMENT '餐桌名称',
  `capacity` int(11) NOT NULL COMMENT '容纳人数',
  `qr_code` varchar(255) DEFAULT NULL COMMENT '二维码',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(0:禁用 1:空闲 2:使用中 3:预订)',
  `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_table_no` (`table_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='餐桌表';

-- 修改订单表，增加用餐类型和餐桌信息
ALTER TABLE `shop_order` 
ADD COLUMN `dining_type` varchar(20) DEFAULT 'takeout' COMMENT '用餐类型(dine_in:堂食 takeout:外卖)' AFTER `status`,
ADD COLUMN `table_id` bigint(20) DEFAULT NULL COMMENT '餐桌ID(堂食时使用)' AFTER `dining_type`,
ADD COLUMN `table_no` varchar(20) DEFAULT NULL COMMENT '餐桌编号(堂食时使用)' AFTER `table_id`;

-- 插入默认用餐类型数据
INSERT INTO `shop_dining_type` (`name`, `code`, `icon`, `description`, `sort`, `status`) VALUES
('堂食', 'dine_in', '/static/images/dining/dine-in.png', '店内用餐，享受舒适环境', 1, 1),
('外卖', 'takeout', '/static/images/dining/takeout.png', '外卖配送，方便快捷', 2, 1);

-- 插入示例餐桌数据
INSERT INTO `shop_dining_table` (`table_no`, `table_name`, `capacity`, `status`) VALUES
('T001', '1号桌', 2, 1),
('T002', '2号桌', 4, 1),
('T003', '3号桌', 6, 1),
('T004', '4号桌', 4, 1),
('T005', '5号桌', 8, 1),
('T006', '6号桌', 2, 1),
('T007', '7号桌', 4, 1),
('T008', '8号桌', 6, 1); 