-- 在系统配置表中添加分享功能配置
-- 使用层级结构管理分享相关配置

-- 1. 分享功能主配置
INSERT INTO `sys_config` (`parent_id`, `config_name`, `config_key`, `config_value`, `config_type`, `module_key`, `module_name`, `level`, `sort`, `status`, `remark`) VALUES 
(0, '分享功能', '', '', 'group', 'shareConfig', '分享配置', 1, 100, 1, '分享功能相关配置');

-- 获取刚插入的分享功能配置ID（假设为22，实际使用时需要查询获取）
SET @share_parent_id = LAST_INSERT_ID();

-- 2. 基础设置
INSERT INTO `sys_config` (`parent_id`, `config_name`, `config_key`, `config_value`, `config_type`, `module_key`, `module_name`, `level`, `sort`, `status`, `remark`) VALUES 
(@share_parent_id, '功能启用', 'share.enabled', '1', 'boolean', 'shareConfig', '分享配置', 2, 1, 1, '分享功能总开关'),
(@share_parent_id, '注册佣金', '', '', 'group', 'shareConfig', '分享配置', 2, 2, 1, '注册佣金相关配置'),
(@share_parent_id, '购买佣金', '', '', 'group', 'shareConfig', '分享配置', 2, 3, 1, '购买佣金相关配置'),
(@share_parent_id, '佣金管理', '', '', 'group', 'shareConfig', '分享配置', 2, 4, 1, '佣金管理相关配置'),
(@share_parent_id, '提现设置', '', '', 'group', 'shareConfig', '分享配置', 2, 5, 1, '提现相关配置');

-- 获取各个分组的ID
SET @register_group_id = (SELECT id FROM sys_config WHERE config_name = '注册佣金' AND parent_id = @share_parent_id);
SET @purchase_group_id = (SELECT id FROM sys_config WHERE config_name = '购买佣金' AND parent_id = @share_parent_id);
SET @commission_group_id = (SELECT id FROM sys_config WHERE config_name = '佣金管理' AND parent_id = @share_parent_id);
SET @withdraw_group_id = (SELECT id FROM sys_config WHERE config_name = '提现设置' AND parent_id = @share_parent_id);

-- 3. 注册佣金配置
INSERT INTO `sys_config` (`parent_id`, `config_name`, `config_key`, `config_value`, `config_type`, `module_key`, `module_name`, `level`, `sort`, `status`, `remark`) VALUES 
(@register_group_id, '注册佣金开关', 'share.register.enabled', '1', 'boolean', 'shareConfig', '分享配置', 3, 1, 1, '是否启用注册佣金'),
(@register_group_id, '注册佣金金额', 'share.register.amount', '5.00', 'number', 'shareConfig', '分享配置', 3, 2, 1, '新用户注册奖励金额(元)');

-- 4. 购买佣金配置
INSERT INTO `sys_config` (`parent_id`, `config_name`, `config_key`, `config_value`, `config_type`, `module_key`, `module_name`, `level`, `sort`, `status`, `remark`) VALUES 
(@purchase_group_id, '购买佣金开关', 'share.purchase.enabled', '1', 'boolean', 'shareConfig', '分享配置', 3, 1, 1, '是否启用购买佣金'),
(@purchase_group_id, '一级返佣比例', 'share.purchase.level1_rate', '10.00', 'number', 'shareConfig', '分享配置', 3, 2, 1, '一级分销员返佣比例(%)'),
(@purchase_group_id, '二级返佣比例', 'share.purchase.level2_rate', '5.00', 'number', 'shareConfig', '分享配置', 3, 3, 1, '二级分销员返佣比例(%)');

-- 5. 佣金管理配置
INSERT INTO `sys_config` (`parent_id`, `config_name`, `config_key`, `config_value`, `config_type`, `module_key`, `module_name`, `level`, `sort`, `status`, `remark`) VALUES 
(@commission_group_id, '佣金冻结天数', 'share.commission.freeze_days', '7', 'number', 'shareConfig', '分享配置', 3, 1, 1, '佣金冻结天数，0表示不冻结');

-- 6. 提现设置配置
INSERT INTO `sys_config` (`parent_id`, `config_name`, `config_key`, `config_value`, `config_type`, `module_key`, `module_name`, `level`, `sort`, `status`, `remark`) VALUES 
(@withdraw_group_id, '最低提现金额', 'share.withdraw.min_amount', '10.00', 'number', 'shareConfig', '分享配置', 3, 1, 1, '最低提现金额(元)'),
(@withdraw_group_id, '最高提现金额', 'share.withdraw.max_amount', '5000.00', 'number', 'shareConfig', '分享配置', 3, 2, 1, '最高提现金额(元)'),
(@withdraw_group_id, '提现手续费率', 'share.withdraw.fee_rate', '0.00', 'number', 'shareConfig', '分享配置', 3, 3, 1, '提现手续费率(%)'),
(@withdraw_group_id, '微信提现开关', 'share.withdraw.wechat_enabled', '1', 'boolean', 'shareConfig', '分享配置', 3, 4, 1, '是否启用微信提现'),
(@withdraw_group_id, '支付宝提现开关', 'share.withdraw.alipay_enabled', '0', 'boolean', 'shareConfig', '分享配置', 3, 5, 1, '是否启用支付宝提现');

-- 查看插入的配置
SELECT 
    c1.config_name as '一级配置',
    c2.config_name as '二级配置', 
    c3.config_name as '三级配置',
    c3.config_key as '配置键',
    c3.config_value as '配置值',
    c3.config_type as '类型',
    c3.remark as '说明'
FROM sys_config c1
LEFT JOIN sys_config c2 ON c2.parent_id = c1.id
LEFT JOIN sys_config c3 ON c3.parent_id = c2.id
WHERE c1.module_key = 'shareConfig'
ORDER BY c1.sort, c2.sort, c3.sort; 