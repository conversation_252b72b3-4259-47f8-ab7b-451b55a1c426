-- 迁移收藏点赞表名称
-- 如果存在 shop_collect 表，重命名为 collect

-- 检查是否存在 shop_collect 表
SET @table_exists = 0;
SELECT COUNT(*) INTO @table_exists 
FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'shop_collect';

-- 如果 shop_collect 表存在，重命名为 collect
SET @sql = IF(@table_exists > 0, 'RENAME TABLE shop_collect TO collect', 'SELECT "shop_collect table does not exist" as result');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 确保 collect 表存在且结构正确
CREATE TABLE IF NOT EXISTS `collect` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `target_id` bigint(20) NOT NULL COMMENT '目标ID（商品ID、活动ID等）',
  `target_type` varchar(32) NOT NULL COMMENT '目标类型（goods、activity等）',
  `action_type` tinyint(1) NOT NULL COMMENT '操作类型（1:收藏 2:点赞）',
  `is_delete` tinyint(1) DEFAULT '0' COMMENT '是否删除 0:正常 1:删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_target_action` (`user_id`,`target_id`,`target_type`,`action_type`),
  KEY `idx_target` (`target_id`,`target_type`,`action_type`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='统一收藏点赞表'; 