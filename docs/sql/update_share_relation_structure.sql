-- 更新分销关系表结构
-- 将多级关系存储改为只存储直接邀请关系

-- 1. 备份原有数据
CREATE TABLE IF NOT EXISTS share_relation_backup AS SELECT * FROM share_relation;

-- 2. 删除原有的多级关系数据，只保留一级关系（直接邀请关系）
DELETE FROM share_relation WHERE level != 1;

-- 3. 修改表结构
-- 删除不需要的字段
ALTER TABLE share_relation DROP COLUMN level;
ALTER TABLE share_relation DROP COLUMN ancestor_id;

-- 添加新字段
ALTER TABLE share_relation ADD COLUMN inviter_id BIGINT NOT NULL COMMENT '邀请者ID（直接上级）';

-- 4. 更新数据：将原来的ancestor_id数据迁移到inviter_id
UPDATE share_relation SET inviter_id = (
    SELECT ancestor_id FROM share_relation_backup 
    WHERE share_relation_backup.id = share_relation.id
);

-- 5. 添加索引
CREATE INDEX idx_share_relation_user_id ON share_relation(user_id);
CREATE INDEX idx_share_relation_inviter_id ON share_relation(inviter_id);
CREATE INDEX idx_share_relation_status ON share_relation(status);

-- 6. 添加外键约束（可选）
-- ALTER TABLE share_relation ADD CONSTRAINT fk_share_relation_user FOREIGN KEY (user_id) REFERENCES shop_user(id);
-- ALTER TABLE share_relation ADD CONSTRAINT fk_share_relation_inviter FOREIGN KEY (inviter_id) REFERENCES shop_user(id);

-- 7. 更新表注释
ALTER TABLE share_relation COMMENT = '分销关系表（只记录直接邀请关系）';

-- 注意：执行此脚本前请确保：
-- 1. 已备份数据库
-- 2. 已停止应用服务
-- 3. 测试环境验证无误
-- 4. 如需回滚，可使用备份表 share_relation_backup 