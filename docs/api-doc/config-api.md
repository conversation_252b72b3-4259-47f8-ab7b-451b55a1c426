# 系统配置 API 文档

## 概述

系统配置模块提供了灵活的配置管理功能，支持多模块、多层级的配置管理，并提供缓存机制提升性能。

## 接口列表

### 1. 获取配置列表

**接口地址：** `POST /api/config/list`

**请求参数：**
```json
{
  "moduleKey": "shopConfig",  // 必填，模块标识
  "keys": ["shop.yunfei,shunfeng", "shop.name"]  // 可选，配置键列表，为空则获取模块下所有配置
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "moduleKey": "shopConfig",
    "configs": {
      "shop.yunfei,shunfeng": 5.0,
      "shop.name": "万能商城"
    }
  }
}
```

### 2. 获取单个配置

**接口地址：** `GET /api/config/get`

**请求参数：**
- `moduleKey`: 模块标识（必填）
- `configKey`: 配置键名（必填）

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "moduleKey": "shopConfig",
    "configKey": "shop.yunfei,shunfeng",
    "configValue": 5.0
  }
}
```

### 3. 获取商城配置

**接口地址：** `GET /api/config/shop`

**请求参数：**
- `keys`: 配置键列表（可选，数组格式）

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "moduleKey": "shopConfig",
    "configs": {
      "shop.yunfei,shunfeng": 5.0,
      "shop.name": "万能商城",
      "payment.wechat_enabled": true,
      "order.auto_cancel_time": 30
    }
  }
}
```

### 4. 清除配置缓存

**接口地址：** `POST /api/config/cache/clear`

**请求参数：**
- `moduleKey`: 模块标识（可选，为空则清除所有缓存）

**响应示例：**
```json
{
  "code": 200,
  "message": "缓存清除成功",
  "data": null
}
```

## 配置类型说明

系统支持以下配置类型：

- `string`: 字符串类型
- `number`: 数字类型（整数或浮点数）
- `boolean`: 布尔类型
- `json`: JSON对象类型

## 商城配置说明

### 运费配置
- `shop.yunfei,shunfeng`: 顺丰快递运费
- `shop.yunfei,zhongtong`: 中通快递运费
- `shop.yunfei,yuantong`: 圆通快递运费
- `shop.yunfei,yunda`: 韵达快递运费
- `shop.yunfei,shentong`: 申通快递运费

### 基础配置
- `shop.name`: 商城名称
- `shop.description`: 商城描述
- `shop.service_phone`: 客服电话

### 订单配置
- `order.auto_cancel_time`: 订单自动取消时间（分钟）
- `order.auto_confirm_time`: 订单自动确认收货时间（天）
- `order.free_freight_amount`: 免运费金额

### 支付配置
- `payment.wechat_enabled`: 是否启用微信支付
- `payment.alipay_enabled`: 是否启用支付宝支付
- `payment.balance_enabled`: 是否启用余额支付

### 分销配置
- `distribution.enabled`: 是否启用分销功能
- `distribution.level1_rate`: 一级分销比例
- `distribution.level2_rate`: 二级分销比例

## 使用示例

### 前端调用示例

```javascript
import { getShopConfigs, getConfig } from '@/api/config'

// 获取运费配置
const freightConfig = await getConfig('shopConfig', 'shop.yunfei,shunfeng')

// 获取多个商城配置
const shopConfigs = await getShopConfigs(['shop.name', 'payment.wechat_enabled'])

// 获取所有商城配置
const allConfigs = await getShopConfigs()
```

### 后端服务调用示例

```go
// 在服务中使用配置服务
configService := common.NewConfigService()

// 获取运费配置
freight := configService.GetConfigFloat("shopConfig", "shop.yunfei,shunfeng", 12.0)

// 获取商城名称
shopName := configService.GetConfigString("shopConfig", "shop.name", "默认商城")

// 获取是否启用微信支付
wechatEnabled := configService.GetConfigBool("shopConfig", "payment.wechat_enabled", true)
```

## 缓存机制

- 配置数据会自动缓存5分钟
- 支持按模块清除缓存
- 支持清除全部缓存
- 缓存失效时会自动从数据库重新加载

## 错误码说明

- `400`: 请求参数错误
- `404`: 配置不存在
- `500`: 服务器内部错误 