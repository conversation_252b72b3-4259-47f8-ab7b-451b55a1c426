# 收藏点赞功能API文档

## 概述

收藏点赞模块提供了统一的收藏和点赞功能，使用统一的数据表`collect`存储，通过`action_type`字段区分点赞（2）和收藏（1）操作，支持多种目标类型（商品、活动等），具有良好的扩展性。

## 数据库设计

### 统一收藏点赞表（collect）

| 字段 | 类型 | 说明 |
|------|------|------|
| id | bigint | 主键ID |
| user_id | bigint | 用户ID |
| target_id | bigint | 目标ID（商品ID、活动ID等） |
| target_type | varchar(32) | 目标类型（goods、activity等） |
| action_type | tinyint(1) | 操作类型（1:收藏 2:点赞） |
| is_delete | tinyint(1) | 是否删除（0:正常 1:删除） |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

**索引设计：**
- 主键：id
- 唯一索引：uk_user_target_action (user_id, target_id, target_type, action_type)
- 普通索引：idx_target (target_id, target_type, action_type)
- 普通索引：idx_user_id (user_id)

## 接口列表

### 1. 收藏相关接口

#### 1.1 收藏/取消收藏

**接口地址：** `POST /api/collect/action`

**请求参数：**
```json
{
  "targetId": 123,           // 目标ID（必填）
  "targetType": "goods",     // 目标类型（必填：goods、activity）
  "action": 1                // 操作类型（必填：1-收藏，0-取消收藏）
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "收藏成功",
  "data": null
}
```

#### 1.2 获取收藏状态

**接口地址：** `GET /api/collect/status`

**请求参数：**
- `targetId`: 目标ID（必填）
- `targetType`: 目标类型（必填）

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "isCollected": true,     // 是否已收藏
    "count": 156            // 收藏总数
  }
}
```

### 2. 点赞相关接口

#### 2.1 点赞/取消点赞

**接口地址：** `POST /api/collect/like`

**请求参数：**
```json
{
  "targetId": 123,           // 目标ID（必填）
  "targetType": "goods",     // 目标类型（必填：goods、activity）
  "action": 1                // 操作类型（必填：1-点赞，0-取消点赞）
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "点赞成功",
  "data": null
}
```

#### 2.2 获取点赞状态

**接口地址：** `GET /api/collect/like/status`

**请求参数：**
- `targetId`: 目标ID（必填）
- `targetType`: 目标类型（必填）

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "isLiked": true,         // 是否已点赞
    "count": 89             // 点赞总数
  }
}
```

## 前端使用示例

### 1. 引入收藏混入

```javascript
import collectMixin from '@/mixins/collectMixin.js'

export default {
  mixins: [collectMixin],
  // ...
}
```

### 2. 使用点赞功能

```javascript
// 切换点赞状态
async handleLike(item) {
  const result = await this.toggleLike(item.id, 'goods', item.isLiked)
  item.isLiked = result.isLiked
  item.likes = result.count
}
```

### 3. 使用收藏功能

```javascript
// 切换收藏状态
async handleCollect(item) {
  const newStatus = await this.toggleCollect(item.id, 'goods', item.isCollected)
  item.isCollected = newStatus
}
```

### 4. 批量加载状态

```javascript
// 批量加载商品状态（用于列表页面）
const goodsWithStatus = await this.batchLoadGoodsStatus(goodsList)
this.goodsList = goodsWithStatus
```

## 目标类型说明

- `goods`: 商品
- `activity`: 活动（预留扩展）

## 操作类型说明

- `action_type = 1`: 收藏操作
- `action_type = 2`: 点赞操作

## 注意事项

1. 所有操作都需要用户登录
2. 使用统一表存储，通过字段区分不同操作类型
3. 支持扩展其他目标类型，只需在服务层添加相应的处理逻辑
4. 前端混入提供了完整的错误处理和用户提示
5. 唯一索引确保同一用户对同一目标的同类操作不会重复
6. 软删除设计，支持数据恢复和统计分析 