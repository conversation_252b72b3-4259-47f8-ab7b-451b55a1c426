# 弹窗API接口文档

## 概述

弹窗管理系统API，提供弹窗的获取、日志记录和统计功能。

## 接口列表

### 1. 获取生效弹窗列表

**接口地址：** `GET /api/popup/active`

**接口描述：** 获取当前用户可以显示的弹窗列表

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | int64 | 是 | 用户ID |

**请求示例：**
```http
GET /api/popup/active?userId=123
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "1",
      "title": "欢迎使用wn商城",
      "content": "感谢您使用我们的应用，祝您购物愉快！",
      "titleStyle": {
        "color": "#fff",
        "background": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
      },
      "buttons": [
        {
          "text": "开始购物",
          "type": "primary"
        }
      ],
      "priority": 10,
      "autoClose": 0,
      "delay": 1000,
      "maskClosable": true
    }
  ],
  "timestamp": 1705123456789
}
```

### 2. 记录弹窗操作日志

**接口地址：** `POST /api/popup/log`

**接口描述：** 记录用户对弹窗的操作（显示、点击、关闭）

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| popupId | int64 | 是 | 弹窗ID |
| userId | int64 | 是 | 用户ID |
| action | string | 是 | 操作类型：show/click/close |
| buttonText | string | 否 | 点击的按钮文字 |

**请求示例：**
```http
POST /api/popup/log
Content-Type: application/json

{
  "popupId": 1,
  "userId": 123,
  "action": "click",
  "buttonText": "开始购物"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": null,
  "timestamp": 1705123456789
}
```

### 3. 获取弹窗统计信息

**接口地址：** `GET /api/popup/stats`

**接口描述：** 获取弹窗的统计数据（总数、显示次数、点击率等）

**请求参数：** 无

**请求示例：**
```http
GET /api/popup/stats
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalPopups": 10,
    "activePopups": 5,
    "totalShows": 1250,
    "totalClicks": 380,
    "clickRate": 30.4
  },
  "timestamp": 1705123456789
}
```

## 数据结构说明

### PopupActiveResp - 生效弹窗响应

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | string | 弹窗ID |
| title | string | 弹窗标题 |
| content | string | 弹窗内容 |
| titleStyle | object | 标题样式配置 |
| titleStyle.color | string | 标题文字颜色 |
| titleStyle.background | string | 标题背景色 |
| titleStyle.backgroundImage | string | 标题背景图片 |
| buttons | array | 按钮配置列表 |
| buttons[].text | string | 按钮文字 |
| buttons[].type | string | 按钮类型：default/primary/success/warning/danger |
| buttons[].style | object | 自定义按钮样式 |
| priority | int | 优先级 |
| autoClose | int | 自动关闭时间(毫秒) |
| delay | int | 延迟显示时间(毫秒) |
| maskClosable | bool | 点击遮罩是否关闭 |

### PopupStatsResp - 弹窗统计响应

| 字段名 | 类型 | 说明 |
|--------|------|------|
| totalPopups | int64 | 总弹窗数 |
| activePopups | int64 | 生效弹窗数 |
| totalShows | int64 | 总显示次数 |
| totalClicks | int64 | 总点击次数 |
| clickRate | float64 | 点击率(百分比) |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 参数错误 |
| 500 | 服务器内部错误 |

## 使用示例

### JavaScript/UniApp示例

```javascript
import { getActivePopups, recordPopupLog, getPopupStats } from '@/api/popup'

// 获取弹窗列表
async function loadPopups(userId) {
  try {
    const res = await getActivePopups(userId)
    if (res.code === 200) {
      return res.data
    }
  } catch (error) {
    console.error('获取弹窗失败:', error)
  }
  return []
}

// 记录弹窗显示
async function logPopupShow(popupId, userId) {
  try {
    await recordPopupLog({
      popupId: popupId,
      userId: userId,
      action: 'show'
    })
  } catch (error) {
    console.error('记录日志失败:', error)
  }
}

// 记录按钮点击
async function logPopupClick(popupId, userId, buttonText) {
  try {
    await recordPopupLog({
      popupId: popupId,
      userId: userId,
      action: 'click',
      buttonText: buttonText
    })
  } catch (error) {
    console.error('记录日志失败:', error)
  }
}

// 获取统计信息
async function getStats() {
  try {
    const res = await getPopupStats()
    if (res.code === 200) {
      return res.data
    }
  } catch (error) {
    console.error('获取统计失败:', error)
  }
  return null
}
```

## 注意事项

1. **用户ID验证**：所有接口都需要传入有效的用户ID
2. **弹窗显示逻辑**：后端会根据触发规则、时间范围、频次控制等条件过滤弹窗
3. **日志记录**：建议在弹窗显示、用户点击按钮时及时记录日志，用于统计分析
4. **错误处理**：请妥善处理接口调用失败的情况，避免影响用户体验
5. **性能优化**：避免频繁调用接口，可以在应用启动时一次性获取弹窗列表

## 更新日志

- **v1.0.0** (2025-01-13)
  - 初始版本
  - 支持获取生效弹窗列表
  - 支持记录弹窗操作日志
  - 支持获取弹窗统计信息 