# 商品相关接口文档

## 获取商品详情

### 基本信息

- 接口URL：`/api/goods/detail`
- 请求方式：GET
- 接口说明：获取商品详细信息

### 请求参数

| 参数名 | 类型   | 必填 | 说明    |
|--------|--------|------|---------|
| id     | number | 是   | 商品ID  |

### 响应参数

```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1,                    // 商品ID
        "title": "商品标题",         // 商品标题
        "price": 999,               // 当前价格
        "originalPrice": 1299,      // 原价
        "sales": 100,               // 销量
        "collect": 2580,            // 收藏数
        "desc": "商品描述",          // 商品描述
        "images": [                 // 商品轮播图
            "https://example.com/image1.jpg",
            "https://example.com/image2.jpg"
        ],
        "tags": [                   // 商品标签
            "新品",
            "限时特惠",
            "包邮"
        ],
        "specs": [                  // 商品规格
            {
                "name": "颜色",
                "values": ["红色", "蓝色", "黑色"]
            },
            {
                "name": "尺码",
                "values": ["S", "M", "L", "XL"]
            }
        ],
        "detail": "<p>商品详情富文本内容</p>",  // 商品详情（富文本）
        "detailImages": [           // 商品详情图片
            "/static/images/detail/1.png",
            "/static/images/detail/2.png"
        ],
        "services": [               // 服务承诺
            "正品保障",
            "7天退换",
            "顺丰发货"
        ],
        "isCollected": false        // 是否已收藏
    }
}
```

## 获取商品评价列表

### 基本信息

- 接口URL：`/api/goods/comments`
- 请求方式：GET
- 接口说明：获取商品评价列表

### 请求参数

| 参数名 | 类型   | 必填 | 说明    |
|--------|--------|------|---------|
| goodsId | number | 是   | 商品ID  |
| page    | number | 是   | 页码，从1开始 |
| size    | number | 是   | 每页数量 |

### 响应参数

```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "total": 100,              // 总评论数
        "list": [
            {
                "id": 1,           // 评论ID
                "nickname": "用户昵称", // 用户昵称
                "avatar": "/static/images/avatar.png", // 用户头像
                "rate": 5,         // 评分（1-5）
                "content": "评价内容", // 评价内容
                "images": [        // 评价图片
                    "/static/images/comment/1.png",
                    "/static/images/comment/2.png"
                ],
                "time": "2024-01-20" // 评价时间
            }
        ]
    }
}
```

## 获取购物车数量

### 基本信息

- 接口URL：`/api/cart/count`
- 请求方式：GET
- 接口说明：获取当前用户购物车商品总数

### 请求参数

无

### 响应参数

```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "count": 5  // 购物车商品总数
    }
}
```

## 收藏/取消收藏商品

### 基本信息

- 接口URL：`/api/goods/collect`
- 请求方式：POST
- 接口说明：收藏或取消收藏商品

### 请求参数

| 参数名  | 类型    | 必填 | 说明    |
|---------|---------|------|---------|
| goodsId | number  | 是   | 商品ID  |
| type    | number  | 是   | 操作类型：1-收藏，2-取消收藏 |

### 响应参数

```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
``` 