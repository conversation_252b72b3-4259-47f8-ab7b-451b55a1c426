openapi: 3.0.0
info:
  title: wn商城首页接口文档
  version: 1.0.0
  description: 包含首页所需的所有接口

servers:
  - url: /api/v1
    description: 开发环境

paths:
  /home/<USER>
    get:
      summary: 获取首页轮播图
      description: 返回首页轮播图列表
      parameters:
        - name: position
          in: query
          description: 轮播图位置(1:首页顶部 2:分类页 3:活动页)
          required: false
          schema:
            type: integer
            enum: [1, 2, 3]
            default: 1
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Banner'

  /home/<USER>
    get:
      summary: 获取首页分类导航
      description: 返回首页顶部分类导航列表
      parameters:
        - name: level
          in: query
          description: 分类层级(1:一级分类 2:二级分类 3:三级分类)
          required: false
          schema:
            type: integer
            enum: [1, 2, 3]
            default: 1
        - name: parentId
          in: query
          description: 父级分类ID,获取子分类时传入
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Category'

  /home/<USER>
    get:
      summary: 获取首页商品列表
      description: 返回首页商品列表,支持分页和分类筛选
      parameters:
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: pageSize
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 50
        - name: categoryId
          in: query
          description: 分类ID
          required: false
          schema:
            type: integer
        - name: keyword
          in: query
          description: 搜索关键词
          required: false
          schema:
            type: string
        - name: sort
          in: query
          description: 排序方式(1:默认 2:销量 3:价格升序 4:价格降序)
          required: false
          schema:
            type: integer
            enum: [1, 2, 3, 4]
            default: 1
        - name: priceMin
          in: query
          description: 最低价格
          required: false
          schema:
            type: number
        - name: priceMax
          in: query
          description: 最高价格
          required: false
          schema:
            type: number
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: object
                    properties:
                      total:
                        type: integer
                        description: 总数量
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/Goods'

  /home/<USER>
    get:
      summary: 获取首页活动列表
      description: 返回首页活动专区数据
      parameters:
        - name: type
          in: query
          description: 活动类型(1:限时特惠 2:新品首发 3:主题活动)
          required: false
          schema:
            type: integer
            enum: [1, 2, 3]
        - name: status
          in: query
          description: 活动状态(0:未开始 1:进行中 2:已结束)
          required: false
          schema:
            type: integer
            enum: [0, 1, 2]
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: pageSize
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 50
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Activity'

  /category/tree:
    get:
      summary: 获取分类树形结构
      description: 返回完整的分类树形结构，包含一级分类和其下的所有子分类
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/CategoryTree'

  /category/goods:
    get:
      summary: 获取分类商品列表
      description: 根据分类ID获取商品列表，支持分页
      parameters:
        - name: categoryId
          in: query
          description: 分类ID
          required: true
          schema:
            type: integer
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: pageSize
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 50
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: object
                    properties:
                      total:
                        type: integer
                        description: 总数量
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/Goods'

  /goods/detail/{id}:
    get:
      summary: 获取商品详情
      description: 返回商品的详细信息，包括基本信息、规格、图片等
      parameters:
        - name: id
          in: path
          required: true
          description: 商品ID
          schema:
            type: integer
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    $ref: '#/components/schemas/GoodsDetail'
        '404':
          description: 商品不存在
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 404
                  message:
                    type: string
                    example: 商品不存在

  /goods/comments/{id}:
    get:
      summary: 获取商品评论列表
      description: 返回商品的评论列表，支持分页
      parameters:
        - name: id
          in: path
          required: true
          description: 商品ID
          schema:
            type: integer
        - name: page
          in: query
          description: 页码
          required: false
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: page_size
          in: query
          description: 每页数量
          required: false
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 50
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: object
                    properties:
                      total:
                        type: integer
                        description: 总评论数
                      list:
                        type: array
                        items:
                          $ref: '#/components/schemas/Comment'

  /cart/add:
    post:
      summary: 加入购物车
      description: 将商品加入购物车
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                goods_id:
                  type: integer
                  description: 商品ID
                specs:
                  type: object
                  description: 选择的规格
                  example:
                    颜色: "红色"
                    尺码: "M"
                quantity:
                  type: integer
                  description: 数量
                  minimum: 1
              required:
                - goods_id
                - specs
                - quantity
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: object
                    properties:
                      cart_count:
                        type: integer
                        description: 购物车商品总数

  /cart/count:
    get:
      summary: 获取购物车商品数量
      description: 返回当前用户购物车中的商品总数
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: object
                    properties:
                      count:
                        type: integer
                        description: 购物车商品总数

  /goods/collect/{id}:
    post:
      summary: 收藏/取消收藏商品
      description: 收藏或取消收藏商品
      parameters:
        - name: id
          in: path
          required: true
          description: 商品ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                is_collect:
                  type: boolean
                  description: true-收藏 false-取消收藏
              required:
                - is_collect
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  data:
                    type: object
                    properties:
                      collect_count:
                        type: integer
                        description: 更新后的收藏数
                      is_collected:
                        type: boolean
                        description: 当前收藏状态

components:
  schemas:
    Banner:
      type: object
      properties:
        id:
          type: integer
          description: 轮播图ID
        image:
          type: string
          description: 图片地址
        url:
          type: string
          description: 跳转链接
        type:
          type: integer
          description: 跳转类型(1:商品 2:活动 3:外链)
        title:
          type: string
          description: 标题
        sort:
          type: integer
          description: 排序

    Category:
      type: object
      properties:
        id:
          type: integer
          description: 分类ID
        name:
          type: string
          description: 分类名称
        icon:
          type: string
          description: 分类图标
        sort:
          type: integer
          description: 排序

    Goods:
      type: object
      properties:
        id:
          type: integer
          description: 商品ID
        name:
          type: string
          description: 商品名称
        desc:
          type: string
          description: 商品描述
        price:
          type: string
          description: 商品价格
        originalPrice:
          type: string
          description: 原价
        image:
          type: string
          description: 商品主图
        sales:
          type: integer
          description: 销量
        stock:
          type: integer
          description: 库存
        tags:
          type: array
          description: 商品标签

    Activity:
      type: object
      properties:
        id:
          type: integer
          description: 活动ID
        name:
          type: string
          description: 活动名称
        desc:
          type: string
          description: 活动描述
        image:
          type: string
          description: 活动图片
        startTime:
          type: string
          format: date-time
          description: 开始时间
        endTime:
          type: string
          format: date-time
          description: 结束时间
        type:
          type: integer
          description: 活动类型(1:限时特惠 2:新品首发 3:主题活动)
        status:
          type: integer
          description: 活动状态(0:未开始 1:进行中 2:已结束)

    CategoryTree:
      type: object
      properties:
        id:
          type: integer
          description: 分类ID
        name:
          type: string
          description: 分类名称
        icon:
          type: string
          description: 分类图标
        image:
          type: string
          description: 分类图片
        level:
          type: integer
          description: 层级(1:一级 2:二级 3:三级)
        sort:
          type: integer
          description: 排序
        children:
          type: array
          description: 子分类
          items:
            $ref: '#/components/schemas/CategoryTree'

    Goods:
      type: object
      properties:
        id:
          type: integer
          description: 商品ID
        name:
          type: string
          description: 商品名称
        price:
          type: number
          description: 商品价格
        original_price:
          type: number
          description: 原价
        image:
          type: string
          description: 商品主图
        sales:
          type: integer
          description: 销量
        stock:
          type: integer
          description: 库存
        status:
          type: integer
          description: 状态(0:下架 1:上架)

    GoodsDetail:
      allOf:
        - $ref: '#/components/schemas/Goods'
        - type: object
          properties:
            images:
              type: array
              items:
                type: string
              description: 商品图片列表
            desc:
              type: string
              description: 商品描述
            detail:
              type: string
              description: 商品详情(富文本)
            detail_images:
              type: array
              items:
                type: string
              description: 详情图片列表
            collect_count:
              type: integer
              description: 收藏数量
            is_collected:
              type: boolean
              description: 当前用户是否已收藏
            specs:
              type: array
              items:
                type: object
                properties:
                  name:
                    type: string
                    description: 规格名称
                  values:
                    type: array
                    items:
                      type: string
                    description: 规格值列表
              description: 商品规格
            services:
              type: array
              items:
                type: string
              description: 服务承诺列表
            tags:
              type: array
              items:
                type: string
              description: 商品标签列表

    Comment:
      type: object
      properties:
        id:
          type: integer
          description: 评论ID
        user_id:
          type: integer
          description: 用户ID
        nickname:
          type: string
          description: 用户昵称
        avatar:
          type: string
          description: 用户头像
        rate:
          type: integer
          description: 评分(1-5)
        content:
          type: string
          description: 评论内容
        images:
          type: array
          items:
            type: string
          description: 评论图片列表
        create_time:
          type: string
          format: date-time
          description: 评论时间 