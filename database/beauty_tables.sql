-- 美容预约系统数据库表结构
-- 数据库: wnsys
-- 创建时间: 2025-01-20

USE wnsys;

-- 1. 美容服务表
CREATE TABLE beauty_service (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '服务ID',
    category_id BIGINT NOT NULL COMMENT '分类ID(关联category表)',
    name VARCHAR(200) NOT NULL COMMENT '服务名称',
    subtitle VARCHAR(300) COMMENT '服务副标题',
    description TEXT COMMENT '服务描述',
    images JSON COMMENT '服务图片数组',
    
    -- 价格信息
    price DECIMAL(10,2) NOT NULL COMMENT '基础价格',
    original_price DECIMAL(10,2) COMMENT '原价',
    
    -- 服务属性
    duration INT NOT NULL COMMENT '标准服务时长(分钟)',
    
    -- 预约规则
    need_technician TINYINT DEFAULT 1 COMMENT '是否需要指定技师',
    max_advance_days INT DEFAULT 30 COMMENT '最大提前预约天数',
    min_advance_hours INT DEFAULT 2 COMMENT '最小提前预约小时数',
    allow_cancel_hours INT DEFAULT 24 COMMENT '允许取消的提前小时数',
    
    -- 适用条件
    gender_limit TINYINT DEFAULT 0 COMMENT '性别限制(0:不限 1:男 2:女)',
    age_min INT DEFAULT 0 COMMENT '最小年龄限制',
    age_max INT DEFAULT 100 COMMENT '最大年龄限制',
    
    -- 服务特性
    tags JSON COMMENT '服务标签',
    contraindications TEXT COMMENT '禁忌症说明',
    preparation_notes TEXT COMMENT '预约前准备事项',
    
    -- 统计字段
    booking_count INT DEFAULT 0 COMMENT '预约次数',
    rating_avg DECIMAL(3,2) DEFAULT 0 COMMENT '平均评分',
    rating_count INT DEFAULT 0 COMMENT '评价数量',
    
    -- 状态控制
    status TINYINT DEFAULT 1 COMMENT '状态(0:下架 1:上架)',
    sort INT DEFAULT 0 COMMENT '排序权重',
    is_hot TINYINT DEFAULT 0 COMMENT '是否热门',
    is_new TINYINT DEFAULT 0 COMMENT '是否新品',
    
    -- 系统字段
    is_delete TINYINT DEFAULT 0 COMMENT '是否删除',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category_id (category_id),
    INDEX idx_status (status),
    INDEX idx_price (price),
    INDEX idx_duration (duration),
    INDEX idx_hot_new (is_hot, is_new)
) COMMENT='美容服务表';

-- 2. 技师表
CREATE TABLE beauty_technician (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '技师ID',
    user_id BIGINT NOT NULL COMMENT '用户ID(关联user表)',
    name VARCHAR(100) NOT NULL COMMENT '技师姓名',
    
    -- 专业信息
    level VARCHAR(50) NOT NULL COMMENT '技师等级',
    experience INT DEFAULT 0 COMMENT '从业经验(年)',
    specialties JSON COMMENT '专长领域',
    certificates JSON COMMENT '资格证书',
    introduction TEXT COMMENT '个人简介',
    
    -- 服务能力
    service_ids JSON COMMENT '可提供的服务ID数组',
    work_hours JSON COMMENT '工作时间配置',
    
    -- 统计数据
    total_bookings INT DEFAULT 0 COMMENT '总预约次数',
    completed_bookings INT DEFAULT 0 COMMENT '完成预约次数',
    rating_avg DECIMAL(3,2) DEFAULT 0 COMMENT '平均评分',
    rating_count INT DEFAULT 0 COMMENT '评价数量',
    
    -- 价格设置
    extra_fee DECIMAL(10,2) DEFAULT 0 COMMENT '技师附加费',
    
    -- 状态控制
    status TINYINT DEFAULT 1 COMMENT '状态(0:离职 1:在职 2:请假)',
    is_featured TINYINT DEFAULT 0 COMMENT '是否明星技师',
    sort INT DEFAULT 0 COMMENT '排序权重',
    
    -- 系统字段
    is_delete TINYINT DEFAULT 0 COMMENT '是否删除',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_level (level),
    INDEX idx_rating (rating_avg),
    INDEX idx_featured (is_featured)
) COMMENT='美容技师表';

-- 3. 技师排班表
CREATE TABLE beauty_schedule (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '排班ID',
    technician_id BIGINT NOT NULL COMMENT '技师ID',
    work_date DATE NOT NULL COMMENT '工作日期',
    
    -- 时间段配置
    start_time TIME NOT NULL COMMENT '开始时间',
    end_time TIME NOT NULL COMMENT '结束时间',
    break_start TIME COMMENT '休息开始时间',
    break_end TIME COMMENT '休息结束时间',
    
    -- 排班类型
    schedule_type VARCHAR(20) DEFAULT 'normal' COMMENT '排班类型(normal:正常 overtime:加班 leave:请假)',
    
    -- 状态控制
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    
    -- 系统字段
    is_delete TINYINT DEFAULT 0 COMMENT '是否删除',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_technician_date (technician_id, work_date),
    INDEX idx_work_date (work_date),
    INDEX idx_technician_id (technician_id)
) COMMENT='技师排班表';

-- 4. 预约订单表
CREATE TABLE beauty_booking (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '预约ID',
    booking_no VARCHAR(32) UNIQUE NOT NULL COMMENT '预约编号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    service_id BIGINT NOT NULL COMMENT '服务ID',
    technician_id BIGINT COMMENT '技师ID',
    
    -- 预约时间信息
    booking_date DATE NOT NULL COMMENT '预约日期',
    start_time TIME NOT NULL COMMENT '开始时间',
    end_time TIME NOT NULL COMMENT '结束时间',
    duration INT NOT NULL COMMENT '服务时长(分钟)',
    
    -- 客户信息
    contact_name VARCHAR(100) NOT NULL COMMENT '联系人姓名',
    contact_phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    customer_gender TINYINT COMMENT '客户性别',
    customer_age INT COMMENT '客户年龄',
    
    -- 个性化需求
    skin_type VARCHAR(50) COMMENT '肌肤类型',
    skin_concerns JSON COMMENT '肌肤问题',
    allergies TEXT COMMENT '过敏史',
    special_requests TEXT COMMENT '特殊要求',
    
    -- 价格信息
    service_price DECIMAL(10,2) NOT NULL COMMENT '服务价格',
    technician_fee DECIMAL(10,2) DEFAULT 0 COMMENT '技师费用',
    discount_amount DECIMAL(10,2) DEFAULT 0 COMMENT '优惠金额',
    final_price DECIMAL(10,2) NOT NULL COMMENT '实付金额',
    
    -- 优惠信息
    coupon_id BIGINT COMMENT '优惠券ID',
    coupon_amount DECIMAL(10,2) DEFAULT 0 COMMENT '优惠券金额',
    
    -- 状态管理
    booking_status VARCHAR(20) DEFAULT 'pending' COMMENT '预约状态(pending:待确认 confirmed:已确认 in_service:服务中 completed:已完成 cancelled:已取消)',
    payment_status VARCHAR(20) DEFAULT 'unpaid' COMMENT '支付状态(unpaid:未支付 paid:已支付)',
    service_status VARCHAR(20) DEFAULT 'waiting' COMMENT '服务状态(waiting:等待中 in_progress:进行中 completed:已完成)',
    
    -- 时间记录
    confirmed_time DATETIME COMMENT '确认时间',
    checkin_time DATETIME COMMENT '签到时间',
    service_start_time DATETIME COMMENT '服务开始时间',
    service_end_time DATETIME COMMENT '服务结束时间',
    cancelled_time DATETIME COMMENT '取消时间',
    cancel_reason TEXT COMMENT '取消原因',
    
    -- 评价信息
    rating TINYINT COMMENT '评分(1-5)',
    review TEXT COMMENT '评价内容',
    review_images JSON COMMENT '评价图片',
    review_time DATETIME COMMENT '评价时间',
    
    -- 系统字段
    is_delete TINYINT DEFAULT 0 COMMENT '是否删除',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_service_id (service_id),
    INDEX idx_technician_id (technician_id),
    INDEX idx_booking_date (booking_date),
    INDEX idx_booking_status (booking_status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_booking_no (booking_no)
) COMMENT='美容预约订单表';

-- 5. 服务评价表
CREATE TABLE beauty_review (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '评价ID',
    booking_id BIGINT NOT NULL COMMENT '预约订单ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    service_id BIGINT NOT NULL COMMENT '服务ID',
    technician_id BIGINT COMMENT '技师ID',
    
    -- 评价内容
    service_rating TINYINT NOT NULL COMMENT '服务评分(1-5)',
    technician_rating TINYINT COMMENT '技师评分(1-5)',
    environment_rating TINYINT COMMENT '环境评分(1-5)',
    overall_rating TINYINT NOT NULL COMMENT '综合评分(1-5)',
    
    review_content TEXT COMMENT '评价内容',
    review_images JSON COMMENT '评价图片',
    
    -- 评价标签
    review_tags JSON COMMENT '评价标签',
    
    -- 状态控制
    status TINYINT DEFAULT 1 COMMENT '状态(0:隐藏 1:显示)',
    is_anonymous TINYINT DEFAULT 0 COMMENT '是否匿名',
    
    -- 系统字段
    is_delete TINYINT DEFAULT 0 COMMENT '是否删除',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_booking_id (booking_id),
    INDEX idx_user_id (user_id),
    INDEX idx_service_id (service_id),
    INDEX idx_technician_id (technician_id),
    INDEX idx_overall_rating (overall_rating)
) COMMENT='服务评价表';

-- 6. 优惠券表
CREATE TABLE beauty_coupon (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '优惠券ID',
    name VARCHAR(200) NOT NULL COMMENT '优惠券名称',
    description TEXT COMMENT '使用说明',
    
    -- 优惠券类型
    type VARCHAR(20) NOT NULL COMMENT '类型(amount:满减 percent:折扣 service:服务券)',
    value DECIMAL(10,2) NOT NULL COMMENT '优惠值',
    min_amount DECIMAL(10,2) DEFAULT 0 COMMENT '最低消费金额',
    max_discount DECIMAL(10,2) COMMENT '最大优惠金额(折扣券用)',
    
    -- 适用范围
    applicable_services JSON COMMENT '适用服务ID数组',
    applicable_technicians JSON COMMENT '适用技师ID数组',
    
    -- 发放规则
    total_quantity INT DEFAULT 0 COMMENT '发放总量(0:不限)',
    used_quantity INT DEFAULT 0 COMMENT '已使用数量',
    user_limit INT DEFAULT 1 COMMENT '每用户限领数量',
    
    -- 时间限制
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    validity_days INT COMMENT '有效天数(领取后)',
    
    -- 状态控制
    status TINYINT DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
    
    -- 系统字段
    is_delete TINYINT DEFAULT 0 COMMENT '是否删除',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_time_range (start_time, end_time)
) COMMENT='优惠券表';

-- 7. 用户优惠券表
CREATE TABLE beauty_user_coupon (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    coupon_id BIGINT NOT NULL COMMENT '优惠券ID',
    
    -- 使用状态
    status TINYINT DEFAULT 1 COMMENT '状态(1:未使用 2:已使用 3:已过期)',
    used_time DATETIME COMMENT '使用时间',
    booking_id BIGINT COMMENT '使用的预约订单ID',
    
    -- 时间信息
    receive_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
    expire_time DATETIME NOT NULL COMMENT '过期时间',
    
    -- 系统字段
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_coupon_id (coupon_id),
    INDEX idx_status (status),
    INDEX idx_expire_time (expire_time)
) COMMENT='用户优惠券表';

-- 8. 创建美容分类数据
INSERT INTO category (module, parent_id, name, icon, level, sort, create_time) VALUES
('beauty', 0, '面部护理', 'icon-face', 1, 1, NOW()),
('beauty', 0, '身体护理', 'icon-body', 1, 2, NOW()),
('beauty', 0, '美甲美睫', 'icon-nail', 1, 3, NOW()),
('beauty', 0, '美发造型', 'icon-hair', 1, 4, NOW());

-- 获取分类ID用于后续插入
SET @face_category_id = (SELECT id FROM category WHERE module = 'beauty' AND name = '面部护理');
SET @body_category_id = (SELECT id FROM category WHERE module = 'beauty' AND name = '身体护理');
SET @nail_category_id = (SELECT id FROM category WHERE module = 'beauty' AND name = '美甲美睫');
SET @hair_category_id = (SELECT id FROM category WHERE module = 'beauty' AND name = '美发造型'); 