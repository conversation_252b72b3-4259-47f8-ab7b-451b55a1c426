-- 技师表结构迁移脚本
-- 将基本信息字段迁移到user表，保留工作相关字段在beauty_technician表

USE wnsys;

-- 1. 备份现有技师数据
CREATE TABLE beauty_technician_backup AS SELECT * FROM beauty_technician;

-- 2. 检查user_id字段是否存在，如果不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'wnsys' 
     AND TABLE_NAME = 'beauty_technician' 
     AND COLUMN_NAME = 'user_id') = 0,
    'ALTER TABLE beauty_technician ADD COLUMN user_id BIGINT COMMENT "用户ID(关联user表)";',
    'SELECT "user_id字段已存在" as message;'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 为现有技师创建对应的用户记录（如果不存在）
INSERT IGNORE INTO user (username, phone, nickname, avatar, gender, birthday, balance, points, level, create_time) 
SELECT 
    CONCAT('tech_', id) as username,
    phone,
    name as nickname,
    avatar,
    gender,
    NULL as birthday,
    0.00 as balance,
    0 as points,
    1 as level,
    NOW() as create_time
FROM beauty_technician_backup 
WHERE phone IS NOT NULL AND phone != '';

-- 4. 更新技师表的user_id字段
UPDATE beauty_technician t 
JOIN user u ON t.phone = u.phone 
SET t.user_id = u.id 
WHERE t.phone IS NOT NULL AND t.phone != '';

-- 5. 删除手机号字段
ALTER TABLE beauty_technician DROP COLUMN phone;

-- 6. 添加唯一索引
ALTER TABLE beauty_technician ADD UNIQUE KEY uk_user_id (user_id);

-- 7. 验证迁移结果
SELECT 
    t.id as technician_id,
    t.user_id,
    t.name as technician_name,
    u.phone,
    u.email
FROM beauty_technician t
LEFT JOIN user u ON t.user_id = u.id
WHERE t.is_delete = 0;

-- 8. 清理备份表（可选，确认无误后执行）
-- DROP TABLE beauty_technician_backup; 